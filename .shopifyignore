# 开发文件
*.md
docs/*
.git/*
.gitignore
.vscode/*
.idea/*

# Node.js
node_modules/*
package*.json
.npmrc

# 配置文件
.prettierrc
.eslintrc.js
.editorconfig

# 临时文件
*.tmp
*.log
*.swp
*.swo
*~

# 系统文件
.DS_Store
Thumbs.db
desktop.ini

# 备份文件
*.bak
*.backup

# 构建文件
dist/*
build/*

# 测试文件
test/*
tests/*
*.test.js
*.spec.js

# 环境文件
.env
.env.local
.env.development
.env.production

# IDE文件
*.sublime-project
*.sublime-workspace

# 压缩文件
*.zip
*.tar.gz
*.rar

# 图片源文件（保留优化后的版本）
*.psd
*.ai
*.sketch

# 脚本文件
scripts/*
deploy.sh
backup.sh

# 生成的资产文件（避免冲突）
assets/ico-select.svg.liquid
assets/ico-select-drawer.svg.liquid
assets/ico-select-footer.svg.liquid
assets/ico-select-menu.svg.liquid
assets/ico-select-white.svg.liquid
assets/country-flags.css.liquid
assets/theme.css.liquid
