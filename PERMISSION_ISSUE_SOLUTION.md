# 🔐 Shopify CLI 权限问题解决方案

## 🚨 问题描述

您遇到的错误信息：
```
You are not authorized to use the CLI to develop in the provided store: 0.myshopify.com

You can't use Shopify CLI with development stores if you only have Partner staff member access.
```

## 🎯 问题分析

这个错误表明：
1. ✅ **认证成功** - Shopify CLI 已经成功连接到您的账户
2. ❌ **权限不足** - 您对目标商店没有足够的开发权限
3. 🏪 **商店类型** - 这是一个开发商店（Development Store）

## 🛠️ 解决方案

### 方案1: 直接登录商店 Admin（推荐）

这是最简单快速的解决方法：

1. **获取商店 URL**
   ```
   https://your-actual-store-name.myshopify.com/admin
   ```
   注意：不是 `0.myshopify.com`，而是您实际的商店名称

2. **直接登录**
   - 在浏览器中访问上述 URL
   - 使用您的 Shopify 账户登录
   - 这会将开发商店与您的登录关联

3. **重新运行 CLI**
   ```bash
   shopify theme dev --store=your-actual-store-name.myshopify.com
   ```

### 方案2: 在 Shopify Partners 中管理商店

1. **访问 Shopify Partners**
   - 登录 https://partners.shopify.com/
   - 进入"Stores"部分

2. **找到您的开发商店**
   - 查看商店列表
   - 找到您要使用的开发商店

3. **检查权限**
   - 确保您是商店的所有者
   - 或者有足够的开发权限

### 方案3: 创建新的开发商店

如果上述方法不行，创建一个新的开发商店：

1. **在 Partners 后台创建**
   - 登录 Shopify Partners
   - 点击"Create store"
   - 选择"Development store"

2. **设置商店**
   - 填写商店信息
   - 确保您是所有者

3. **使用新商店**
   ```bash
   shopify theme dev --store=new-store-name.myshopify.com
   ```

## 🔍 诊断步骤

### 1. 确认商店名称

```bash
# 列出您有权限的商店
shopify theme list
```

### 2. 检查当前认证状态

```bash
# 查看当前认证信息
shopify auth whoami
```

### 3. 重新认证（如果需要）

```bash
# 登出当前认证
shopify auth logout

# 重新认证
shopify theme dev
```

## 📋 常见问题

### Q: 为什么显示 `0.myshopify.com`？
A: 这通常表示 CLI 没有正确识别商店名称。请明确指定商店名称：
```bash
shopify theme dev --store=your-store-name.myshopify.com
```

### Q: 我是 Partner，但没有商店权限？
A: Partner 账户和商店所有权是分开的。您需要：
- 成为特定商店的所有者，或
- 被添加为商店的员工并获得主题权限

### Q: 如何找到我的商店名称？
A: 
1. 登录 Shopify Partners
2. 查看"Stores"列表
3. 商店名称通常显示为 `store-name.myshopify.com`

## 🚀 快速测试

完成权限设置后，测试以下命令：

```bash
# 1. 列出主题（测试基本权限）
shopify theme list --store=your-store.myshopify.com

# 2. 启动开发服务器
shopify theme dev --store=your-store.myshopify.com

# 3. 查看主题信息
shopify theme info --store=your-store.myshopify.com
```

## 🔧 高级解决方案

### 使用 Theme Access 应用

如果权限问题持续存在：

1. **安装 Theme Access 应用**
   - 在商店 Admin 中搜索"Theme Access"
   - 安装并配置应用

2. **生成访问密码**
   - 在应用中生成主题访问密码
   - 复制生成的密码

3. **使用密码认证**
   ```bash
   shopify theme dev --password=your-theme-access-password --store=your-store.myshopify.com
   ```

### 环境变量配置

为了避免每次输入商店名称：

1. **创建 .env 文件**
   ```bash
   # .env
   SHOPIFY_STORE_URL=your-store.myshopify.com
   SHOPIFY_THEME_PASSWORD=your-theme-access-password
   ```

2. **更新 package.json 脚本**
   ```json
   {
     "scripts": {
       "dev": "shopify theme dev --store=$SHOPIFY_STORE_URL",
       "pull": "shopify theme pull --store=$SHOPIFY_STORE_URL",
       "push": "shopify theme push --store=$SHOPIFY_STORE_URL"
     }
   }
   ```

## 📞 获取帮助

如果问题仍然存在：

1. **检查 Shopify Partners 后台**
   - 确认商店状态
   - 检查权限设置

2. **联系 Shopify 支持**
   - 通过 Partners 后台提交支持请求
   - 说明具体的错误信息

3. **社区支持**
   - Shopify Community Forums
   - GitHub Issues (Shopify CLI)

## ✅ 成功标志

当权限问题解决后，您应该看到：

```bash
shopify theme dev --store=your-store.myshopify.com

✓ Syncing theme #123456789 on your-store.myshopify.com
✓ Serving . at http://127.0.0.1:9292
✓ Serving your-store.myshopify.com at https://your-store.myshopify.com/?preview_theme_id=123456789

Your theme is being served at:
• http://127.0.0.1:9292 (local development server)
• https://your-store.myshopify.com/?preview_theme_id=123456789 (online store preview)
```

## 🎉 下一步

权限问题解决后，您可以：

1. **开始主题开发**
   ```bash
   npm run dev
   ```

2. **拉取现有主题**
   ```bash
   npm run pull
   ```

3. **使用开发助手**
   ```bash
   npm run helper
   ```

---

**记住**: 权限问题是 Shopify 开发中的常见问题，通常通过正确的商店访问设置就能解决。如果您按照上述步骤操作后仍有问题，请随时寻求帮助！
