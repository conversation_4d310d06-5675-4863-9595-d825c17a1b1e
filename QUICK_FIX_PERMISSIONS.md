# 🚀 快速解决 Shopify CLI 权限问题

## 🎯 您遇到的问题

```
You are not authorized to use the CLI to develop in the provided store: 0.myshopify.com
```

## ⚡ 3分钟快速解决方案

### 步骤1: 找到您的真实商店名称

1. 登录 [Shopify Partners](https://partners.shopify.com/)
2. 点击左侧菜单的 "Stores"
3. 找到您要使用的开发商店
4. 记下完整的商店名称，格式如：`your-store-name.myshopify.com`

### 步骤2: 直接登录商店 Admin

1. 在浏览器中访问：`https://your-store-name.myshopify.com/admin`
2. 使用您的 Shopify 账户登录
3. 这一步很重要！它会将开发商店与您的账户关联

### 步骤3: 使用正确的商店名称运行命令

```bash
# 替换 your-store-name 为您的实际商店名称
shopify theme dev --store=your-store-name.myshopify.com
```

## 🔧 永久解决方案

### 更新您的 package.json 脚本

编辑 `package.json` 文件，更新 scripts 部分：

```json
{
  "scripts": {
    "dev": "shopify theme dev --store=your-store-name.myshopify.com",
    "pull": "shopify theme pull --store=your-store-name.myshopify.com",
    "push": "shopify theme push --store=your-store-name.myshopify.com",
    "check": "shopify theme check --store=your-store-name.myshopify.com"
  }
}
```

然后您就可以使用：
```bash
npm run dev    # 启动开发服务器
npm run pull   # 拉取主题文件
npm run push   # 推送主题文件
```

## 🆘 如果还是不行

### 方案A: 使用 Theme Access 应用

1. 在您的商店 Admin 中搜索并安装 "Theme Access" 应用
2. 在应用中生成一个访问密码
3. 使用密码运行命令：

```bash
shopify theme dev --password=your-generated-password --store=your-store-name.myshopify.com
```

### 方案B: 检查商店权限

确保您：
- 是商店的所有者，或
- 被添加为员工并有"Themes"权限

在 Shopify Partners 中：
1. 找到您的商店
2. 检查您的角色和权限
3. 如果需要，请联系商店所有者添加权限

## 🧪 测试解决方案

运行以下命令测试是否解决：

```bash
# 1. 测试基本连接
shopify theme list --store=your-store-name.myshopify.com

# 2. 测试开发服务器
shopify theme dev --store=your-store-name.myshopify.com
```

如果看到类似这样的输出，说明成功了：
```
✓ Syncing theme #123456789 on your-store-name.myshopify.com
✓ Serving . at http://127.0.0.1:9292
```

## 📞 仍需帮助？

1. **运行诊断脚本**：
   ```bash
   npm run fix-permissions
   ```

2. **查看详细指南**：
   - `docs/authentication-guide.md`
   - `PERMISSION_ISSUE_SOLUTION.md`

3. **使用开发助手**：
   ```bash
   npm run helper
   ```

## ✅ 成功检查清单

- [ ] 找到了正确的商店名称
- [ ] 直接登录了商店 Admin
- [ ] 使用完整商店名称运行命令
- [ ] 更新了 package.json 脚本
- [ ] 测试了 `shopify theme list` 命令
- [ ] 成功启动了开发服务器

---

**记住**: 大多数权限问题都是因为：
1. 使用了错误的商店名称（如 `0.myshopify.com`）
2. 没有直接登录过商店 Admin
3. 缺少必要的开发权限

按照上述步骤，99% 的权限问题都能解决！🎉
