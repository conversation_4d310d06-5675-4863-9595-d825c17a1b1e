# 🚀 快速开始指南

## 环境验证

您的开发环境已经安装完成！运行以下命令验证：

```bash
npm run verify
```

## 🔧 已安装的工具

### ✅ 核心工具
- **Node.js** v20.15.1 - JavaScript运行环境
- **npm** v10.7.0 - 包管理器
- **Shopify CLI** v3.82.0 - Shopify主题开发工具
- **Git** v2.45.1 - 版本控制工具

### ✅ 开发工具
- **Prettier** v3.6.2 - 代码格式化工具
- **ESLint** v8.57.1 - 代码质量检查工具

### ✅ 配置文件
- `package.json` - 项目配置和脚本
- `.prettierrc` - 代码格式化配置
- `.eslintrc.js` - 代码质量检查配置
- `.shopifyignore` - Shopify上传忽略文件

## 🎯 下一步操作

### 1. 连接到Shopify商店

**重要提示**: 新版Shopify CLI不再使用`shopify auth login`命令。认证现在通过主题命令自动处理。

```bash
# 直接启动开发服务器，系统会自动提示认证
shopify theme dev

# 或者指定特定商店
shopify theme dev --store=your-store.myshopify.com
```

当您首次运行主题命令时，CLI会：
1. 显示用户验证码
2. 自动打开浏览器进行认证
3. 或者您可以手动访问认证链接

### 2. 启动开发环境

```bash
# 启动开发服务器
npm run dev

# 或者指定商店
shopify theme dev --store=your-store.myshopify.com
```

### 3. 常用开发命令

```bash
# 从商店拉取最新主题
npm run pull

# 推送本地更改到商店
npm run push

# 检查代码质量
npm run lint

# 格式化代码
npm run format

# 验证主题
npm run check
```

## 📁 项目结构

```
├── assets/                 # 静态资源 (CSS, JS, 图片)
├── config/                 # 主题配置文件
├── layout/                 # 布局模板
├── locales/                # 多语言文件
├── sections/               # 页面区块
├── snippets/               # 代码片段
├── templates/              # 页面模板
├── docs/                   # 项目文档
├── scripts/                # 开发脚本
├── package.json            # 项目配置
└── README.md              # 项目说明
```

## 🎨 开发工作流

### 日常开发
1. **启动开发服务器**: `npm run dev`
2. **编辑代码**: 修改主题文件
3. **实时预览**: 浏览器自动刷新
4. **代码检查**: `npm run lint`
5. **格式化代码**: `npm run format`

### 部署流程
1. **测试部署**: `npm run deploy:test`
2. **验证功能**: 在测试主题中验证
3. **生产部署**: `npm run deploy:live`

## 🔍 故障排除

### 常见问题

#### 1. 权限不足问题
```bash
# 错误: "You are not authorized to use the CLI"
# 解决方案1: 直接登录商店 Admin
# 访问: https://your-store.myshopify.com/admin

# 解决方案2: 指定正确的商店名称
shopify theme dev --store=your-actual-store.myshopify.com

# 解决方案3: 重新认证
shopify auth logout
shopify theme dev  # 会自动重新认证
```

#### 2. 主题推送失败
```bash
# 强制推送
shopify theme push --force
```

#### 3. 代码格式问题
```bash
# 自动修复格式
npm run format
```

#### 4. 权限问题
```bash
# 以管理员身份运行PowerShell
# 或者使用 --force 参数
```

## 📚 学习资源

### 官方文档
- [Shopify主题开发文档](https://shopify.dev/themes)
- [Liquid模板语言](https://shopify.github.io/liquid/)
- [Shopify CLI文档](https://shopify.dev/themes/tools/cli)

### 项目文档
- [项目概览](docs/README.md)
- [动画系统](docs/animation-system.md)
- [API参考](docs/api-reference.md)
- [部署指南](docs/deployment-guide.md)

## 🛠️ 开发技巧

### VS Code扩展推荐
安装以下扩展提升开发体验：
- Shopify Liquid
- Theme Check for Shopify Liquid
- Prettier - Code formatter
- ESLint

### 快捷命令
```bash
# 快速设置新环境
npm run setup

# 验证环境
npm run verify

# 查看主题信息
shopify theme info

# 列出所有主题
shopify theme list
```

## 🎉 开始开发

现在您可以开始开发了！

1. **连接商店**: `shopify auth login`
2. **启动开发**: `npm run dev`
3. **开始编码**: 编辑主题文件
4. **查看效果**: 在浏览器中预览

祝您开发愉快！🚀

---

*如有问题，请查看 [docs/](docs/) 目录中的详细文档或联系技术支持。*
