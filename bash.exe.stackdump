Stack trace:
Frame         Function      Args
0007FFFF9800  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF8700) msys-2.0.dll+0x1FE8E
0007FFFF9800  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF9AD8) msys-2.0.dll+0x67F9
0007FFFF9800  000210046832 (000210286019, 0007FFFF96B8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF9800  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF9800  000210068E24 (0007FFFF9810, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFF9AE0  00021006A225 (0007FFFF9810, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFAF51B0000 ntdll.dll
7FFAF3DE0000 KERNEL32.DLL
7FFAF28F0000 KERNELBASE.dll
7FFAF3770000 USER32.dll
7FFAF2CD0000 win32u.dll
000210040000 msys-2.0.dll
7FFAF3F40000 GDI32.dll
7FFAF2790000 gdi32full.dll
7FFAF2320000 msvcp_win.dll
7FFAF2500000 ucrtbase.dll
7FFAF2EF0000 advapi32.dll
7FFAF3060000 msvcrt.dll
7FFAF2FB0000 sechost.dll
7FFAF28C0000 bcrypt.dll
7FFAF3630000 RPCRT4.dll
7FFAF1AC0000 CRYPTBASE.DLL
7FFAF2DC0000 bcryptPrimitives.dll
7FFAF4390000 IMM32.DLL
