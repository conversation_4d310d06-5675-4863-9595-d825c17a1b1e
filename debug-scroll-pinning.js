// Debug script for IR3 Key Features ScrollTrigger pin functionality
// Run this in browser console to test the refactored scroll pinning

console.log('🔍 IR3 Key Features Debug Script');
console.log('================================');

// Check if GSAP and ScrollTrigger are available
console.log('✅ GSAP available:', typeof gsap !== 'undefined');
console.log('✅ ScrollTrigger available:', typeof ScrollTrigger !== 'undefined');

// Find the key features section
const keyFeaturesSection = document.querySelector('[id^="key-features-"]');
console.log('✅ Key Features Section found:', !!keyFeaturesSection);

if (keyFeaturesSection) {
    console.log('📍 Section ID:', keyFeaturesSection.id);
    console.log('📍 Section dimensions:', keyFeaturesSection.getBoundingClientRect());
}

// Check ScrollTrigger instances
if (typeof ScrollTrigger !== 'undefined') {
    const triggers = ScrollTrigger.getAll();
    console.log('📊 Total ScrollTrigger instances:', triggers.length);
    
    triggers.forEach((trigger, index) => {
        console.log(`📌 Trigger ${index + 1}:`, {
            trigger: trigger.trigger,
            start: trigger.vars.start,
            end: trigger.vars.end,
            pin: trigger.vars.pin,
            pinSpacing: trigger.vars.pinSpacing
        });
    });
}

// Test feature navigation
function testFeatureNavigation() {
    console.log('🧪 Testing feature navigation...');
    
    const prevButton = keyFeaturesSection?.querySelector('.prev-button');
    const nextButton = keyFeaturesSection?.querySelector('.next-button');
    const featureTags = keyFeaturesSection?.querySelectorAll('.feature-tag');
    
    console.log('🔘 Prev button found:', !!prevButton);
    console.log('🔘 Next button found:', !!nextButton);
    console.log('🏷️ Feature tags found:', featureTags?.length || 0);
    
    // Simulate button clicks
    if (nextButton) {
        console.log('🖱️ Simulating next button click...');
        nextButton.click();
        
        setTimeout(() => {
            console.log('✅ Next button click completed');
        }, 1000);
    }
}

// Test scroll wheel simulation
function testScrollWheel() {
    console.log('🎡 Testing scroll wheel simulation...');
    
    if (keyFeaturesSection) {
        // Create a wheel event
        const wheelEvent = new WheelEvent('wheel', {
            deltaY: 100,
            bubbles: true,
            cancelable: true
        });
        
        console.log('🎡 Dispatching wheel event...');
        keyFeaturesSection.dispatchEvent(wheelEvent);
        
        setTimeout(() => {
            console.log('✅ Wheel event completed');
        }, 1000);
    }
}

// Monitor scroll position
function monitorScrollPosition() {
    console.log('📊 Starting scroll position monitoring...');
    
    let lastScrollY = window.scrollY;
    
    const monitor = setInterval(() => {
        const currentScrollY = window.scrollY;
        if (currentScrollY !== lastScrollY) {
            console.log('📍 Scroll position changed:', {
                from: lastScrollY,
                to: currentScrollY,
                delta: currentScrollY - lastScrollY
            });
            lastScrollY = currentScrollY;
        }
    }, 100);
    
    // Stop monitoring after 30 seconds
    setTimeout(() => {
        clearInterval(monitor);
        console.log('⏹️ Scroll monitoring stopped');
    }, 30000);
    
    return monitor;
}

// Check for JavaScript errors
function checkForErrors() {
    console.log('🔍 Checking for JavaScript errors...');
    
    const originalError = window.onerror;
    const errors = [];
    
    window.onerror = function(message, source, lineno, colno, error) {
        errors.push({
            message,
            source,
            lineno,
            colno,
            error: error?.stack
        });
        
        console.error('❌ JavaScript Error:', {
            message,
            source,
            lineno,
            colno
        });
        
        if (originalError) {
            originalError.apply(this, arguments);
        }
    };
    
    // Restore original error handler after 30 seconds
    setTimeout(() => {
        window.onerror = originalError;
        console.log('📊 Error monitoring completed. Total errors:', errors.length);
        if (errors.length > 0) {
            console.table(errors);
        }
    }, 30000);
}

// Performance monitoring
function monitorPerformance() {
    console.log('⚡ Starting performance monitoring...');
    
    let frameCount = 0;
    let lastTime = performance.now();
    
    function countFrames() {
        frameCount++;
        const currentTime = performance.now();
        
        if (currentTime - lastTime >= 1000) {
            const fps = frameCount;
            console.log(`📊 FPS: ${fps}`);
            
            if (fps < 30) {
                console.warn('⚠️ Low FPS detected:', fps);
            }
            
            frameCount = 0;
            lastTime = currentTime;
        }
        
        requestAnimationFrame(countFrames);
    }
    
    requestAnimationFrame(countFrames);
}

// Run all tests
function runAllTests() {
    console.log('🚀 Running all debug tests...');
    
    checkForErrors();
    monitorPerformance();
    
    setTimeout(() => {
        testFeatureNavigation();
    }, 1000);
    
    setTimeout(() => {
        testScrollWheel();
    }, 3000);
    
    setTimeout(() => {
        monitorScrollPosition();
    }, 5000);
    
    console.log('✅ All tests initiated. Check console for results.');
}

// Export functions for manual testing
window.debugIR3 = {
    testFeatureNavigation,
    testScrollWheel,
    monitorScrollPosition,
    checkForErrors,
    monitorPerformance,
    runAllTests
};

console.log('🎯 Debug functions available:');
console.log('- debugIR3.testFeatureNavigation()');
console.log('- debugIR3.testScrollWheel()');
console.log('- debugIR3.monitorScrollPosition()');
console.log('- debugIR3.checkForErrors()');
console.log('- debugIR3.monitorPerformance()');
console.log('- debugIR3.runAllTests()');

console.log('🚀 Run debugIR3.runAllTests() to start comprehensive testing');
