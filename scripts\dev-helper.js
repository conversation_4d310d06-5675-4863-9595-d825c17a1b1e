#!/usr/bin/env node

/**
 * 开发助手脚本
 * 提供交互式的开发环境管理
 */

const { execSync, spawn } = require('child_process');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

console.log('🎨 Shopify Motion主题开发助手');
console.log('═'.repeat(40));

function showMenu() {
  console.log('\n📋 请选择操作:');
  console.log('1. 🚀 启动开发服务器');
  console.log('2. 📥 从商店拉取主题');
  console.log('3. 📤 推送主题到商店');
  console.log('4. 🔍 检查代码质量');
  console.log('5. ✨ 格式化代码');
  console.log('6. 📊 查看主题信息');
  console.log('7. 📋 列出所有主题');
  console.log('8. 🔧 验证开发环境');
  console.log('9. 📚 打开文档');
  console.log('0. 🚪 退出');
  console.log('─'.repeat(40));
}

function executeCommand(command, description) {
  console.log(`\n⏳ ${description}...`);
  try {
    execSync(command, { stdio: 'inherit' });
    console.log(`✅ ${description}完成`);
  } catch (error) {
    console.log(`❌ ${description}失败`);
    console.error(error.message);
  }
}

function openDocs() {
  console.log('\n📚 可用文档:');
  console.log('• docs/README.md - 项目概览');
  console.log('• docs/animation-system.md - 动画系统');
  console.log('• docs/api-reference.md - API参考');
  console.log('• docs/deployment-guide.md - 部署指南');
  console.log('• QUICK_START.md - 快速开始');
  
  rl.question('\n📖 输入文档名称打开 (或按回车返回): ', (doc) => {
    if (doc.trim()) {
      try {
        // Windows系统使用start命令打开文件
        execSync(`start ${doc}`, { stdio: 'ignore' });
        console.log(`📖 已打开 ${doc}`);
      } catch (error) {
        console.log(`❌ 无法打开 ${doc}`);
      }
    }
    handleChoice();
  });
}

function handleChoice() {
  rl.question('\n🎯 请输入选项 (0-9): ', (choice) => {
    console.log('');
    
    switch (choice.trim()) {
      case '1':
        rl.question('🏪 输入商店名称 (可选，格式: store.myshopify.com): ', (store) => {
          const command = store.trim()
            ? `shopify theme dev --store=${store.trim()}`
            : 'shopify theme dev';

          console.log('🚀 启动开发服务器...');
          console.log('💡 首次运行会自动打开浏览器进行认证');
          console.log('💡 按 Ctrl+C 停止服务器');

          const child = spawn(command, { shell: true, stdio: 'inherit' });
          child.on('close', (code) => {
            console.log(`\n🛑 开发服务器已停止 (退出码: ${code})`);
            handleChoice();
          });
        });
        break;
        
      case '2':
        executeCommand('npm run pull', '拉取主题');
        handleChoice();
        break;
        
      case '3':
        rl.question('🎯 选择推送类型 (1=测试环境, 2=生产环境): ', (type) => {
          if (type === '1') {
            executeCommand('npm run deploy:test', '推送到测试环境');
          } else if (type === '2') {
            rl.question('⚠️  确认推送到生产环境? (y/N): ', (confirm) => {
              if (confirm.toLowerCase() === 'y') {
                executeCommand('npm run deploy:live', '推送到生产环境');
              } else {
                console.log('❌ 已取消推送');
              }
              handleChoice();
            });
            return;
          } else {
            console.log('❌ 无效选择');
          }
          handleChoice();
        });
        break;
        
      case '4':
        executeCommand('npm run lint', '检查代码质量');
        handleChoice();
        break;
        
      case '5':
        executeCommand('npm run format', '格式化代码');
        handleChoice();
        break;
        
      case '6':
        rl.question('🏪 输入商店名称 (可选): ', (store) => {
          const command = store.trim()
            ? `shopify theme info --store=${store.trim()}`
            : 'shopify theme info';
          executeCommand(command, '获取主题信息');
          handleChoice();
        });
        break;
        
      case '7':
        rl.question('🏪 输入商店名称 (可选): ', (store) => {
          const command = store.trim()
            ? `shopify theme list --store=${store.trim()}`
            : 'shopify theme list';
          executeCommand(command, '列出主题');
          handleChoice();
        });
        break;
        
      case '8':
        executeCommand('npm run verify', '验证开发环境');
        handleChoice();
        break;
        
      case '9':
        openDocs();
        break;
        
      case '0':
        console.log('👋 再见！');
        rl.close();
        process.exit(0);
        break;
        
      default:
        console.log('❌ 无效选择，请输入 0-9');
        handleChoice();
        break;
    }
  });
}

// 启动程序
console.log('🔍 检查开发环境...');
try {
  execSync('npm run verify', { stdio: 'pipe' });
  console.log('✅ 开发环境正常');
  showMenu();
  handleChoice();
} catch (error) {
  console.log('⚠️  开发环境检查失败，请先运行: npm run setup');
  rl.close();
  process.exit(1);
}
