#!/usr/bin/env node

/**
 * Shopify CLI 权限问题修复助手
 * 帮助用户诊断和解决常见的权限问题
 */

const { execSync } = require('child_process');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

console.log('🔐 Shopify CLI 权限问题修复助手');
console.log('=====================================\n');

function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer.trim());
    });
  });
}

async function checkShopifyAuth() {
  try {
    console.log('🔍 检查当前认证状态...');
    // 使用 theme list 命令来测试认证状态
    const result = execSync('shopify theme list', { encoding: 'utf8', stdio: 'pipe' });
    console.log('✅ 认证状态正常');
    return true;
  } catch (error) {
    console.log('❌ 未认证或认证已过期');
    return false;
  }
}

async function listStores() {
  try {
    console.log('🏪 获取可用商店列表...');
    const result = execSync('shopify theme list', { encoding: 'utf8' });
    console.log('✅ 可用商店:');
    console.log(result);
    return true;
  } catch (error) {
    console.log('❌ 无法获取商店列表');
    console.log('错误信息:', error.message);
    return false;
  }
}

async function testStoreAccess(storeName) {
  try {
    console.log(`🧪 测试商店访问权限: ${storeName}`);
    const result = execSync(`shopify theme list --store=${storeName}`, { encoding: 'utf8' });
    console.log('✅ 商店访问成功!');
    return true;
  } catch (error) {
    console.log('❌ 商店访问失败');
    console.log('错误信息:', error.message);
    return false;
  }
}

async function main() {
  try {
    // 1. 检查认证状态
    const isAuthenticated = await checkShopifyAuth();
    
    if (!isAuthenticated) {
      console.log('\n🔄 需要重新认证...');
      const reauth = await askQuestion('是否现在重新认证? (y/n): ');
      
      if (reauth.toLowerCase() === 'y') {
        console.log('🚀 启动认证流程...');
        console.log('提示: 运行以下命令进行认证:');
        console.log('shopify theme dev');
        console.log('\n认证完成后请重新运行此脚本。');
        process.exit(0);
      }
    }

    // 2. 尝试列出商店
    console.log('\n' + '='.repeat(40));
    const canListStores = await listStores();
    
    if (!canListStores) {
      console.log('\n🔧 权限问题诊断:');
      console.log('1. 您可能没有足够的权限访问任何商店');
      console.log('2. 或者需要指定特定的商店名称');
      
      const storeName = await askQuestion('\n请输入您的商店名称 (格式: store.myshopify.com): ');
      
      if (storeName) {
        console.log('\n' + '='.repeat(40));
        const hasAccess = await testStoreAccess(storeName);
        
        if (hasAccess) {
          console.log('\n🎉 太好了! 您有权限访问这个商店。');
          console.log('\n📝 建议的解决方案:');
          console.log(`1. 使用完整的商店名称运行命令:`);
          console.log(`   shopify theme dev --store=${storeName}`);
          console.log(`\n2. 或者更新您的 package.json 脚本:`);
          console.log(`   "dev": "shopify theme dev --store=${storeName}"`);
          
          const updateScripts = await askQuestion('\n是否要自动更新 package.json 脚本? (y/n): ');
          
          if (updateScripts.toLowerCase() === 'y') {
            await updatePackageScripts(storeName);
          }
        } else {
          console.log('\n🚨 权限问题确认');
          console.log('\n可能的解决方案:');
          console.log('1. 直接登录商店 Admin:');
          console.log(`   https://${storeName}/admin`);
          console.log('\n2. 检查您是否是商店所有者或有主题开发权限');
          console.log('\n3. 如果是开发商店，确保您在 Shopify Partners 中有正确权限');
          console.log('\n4. 考虑使用 Theme Access 应用生成访问密码');
        }
      }
    } else {
      console.log('\n🎉 太好了! 您的权限设置正常。');
      console.log('您可以正常使用 Shopify CLI 进行主题开发。');
    }

    // 3. 提供额外帮助
    console.log('\n' + '='.repeat(40));
    console.log('📚 更多帮助资源:');
    console.log('• 权限问题详细指南: docs/authentication-guide.md');
    console.log('• 快速解决方案: PERMISSION_ISSUE_SOLUTION.md');
    console.log('• 开发助手: npm run helper');
    
  } catch (error) {
    console.error('❌ 脚本执行出错:', error.message);
  } finally {
    rl.close();
  }
}

async function updatePackageScripts(storeName) {
  try {
    const fs = require('fs');
    const packagePath = './package.json';
    
    if (!fs.existsSync(packagePath)) {
      console.log('❌ 未找到 package.json 文件');
      return;
    }
    
    const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
    
    // 更新脚本
    packageJson.scripts = packageJson.scripts || {};
    packageJson.scripts.dev = `shopify theme dev --store=${storeName}`;
    packageJson.scripts.pull = `shopify theme pull --store=${storeName}`;
    packageJson.scripts.push = `shopify theme push --store=${storeName}`;
    packageJson.scripts.check = `shopify theme check --store=${storeName}`;
    
    fs.writeFileSync(packagePath, JSON.stringify(packageJson, null, 2));
    
    console.log('✅ package.json 脚本已更新!');
    console.log('\n现在您可以使用:');
    console.log('• npm run dev    - 启动开发服务器');
    console.log('• npm run pull   - 拉取主题文件');
    console.log('• npm run push   - 推送主题文件');
    console.log('• npm run check  - 检查主题');
    
  } catch (error) {
    console.error('❌ 更新 package.json 失败:', error.message);
  }
}

// 处理程序退出
process.on('SIGINT', () => {
  console.log('\n\n👋 再见!');
  rl.close();
  process.exit(0);
});

// 运行主程序
main().catch(console.error);
