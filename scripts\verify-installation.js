#!/usr/bin/env node

/**
 * 验证开发环境安装脚本
 * 检查所有必需的工具是否正确安装
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔍 验证开发环境安装...\n');

const checks = [
  {
    name: 'Node.js',
    command: 'node --version',
    minVersion: '14.0.0',
    required: true,
  },
  {
    name: 'npm',
    command: 'npm --version',
    minVersion: '6.0.0',
    required: true,
  },
  {
    name: 'Shopify CLI',
    command: 'shopify version',
    minVersion: '3.0.0',
    required: true,
  },
  {
    name: 'Git',
    command: 'git --version',
    minVersion: '2.0.0',
    required: false,
  },
  {
    name: 'Prettier',
    command: 'npx prettier --version',
    minVersion: '2.0.0',
    required: false,
  },
];

const files = [
  {
    name: 'package.json',
    path: './package.json',
    required: true,
  },
  {
    name: 'Prettier配置',
    path: './.prettierrc',
    required: false,
  },
  {
    name: 'ESLint配置',
    path: './.eslintrc.js',
    required: false,
  },
  {
    name: 'Shopify忽略文件',
    path: './.shopifyignore',
    required: false,
  },
];

let allPassed = true;

// 检查命令工具
console.log('📦 检查工具安装状态:');
console.log('─'.repeat(50));

for (const check of checks) {
  try {
    const output = execSync(check.command, { encoding: 'utf8', stdio: 'pipe' });
    const version = output.trim();
    
    console.log(`✅ ${check.name.padEnd(15)} ${version}`);
  } catch (error) {
    if (check.required) {
      console.log(`❌ ${check.name.padEnd(15)} 未安装 (必需)`);
      allPassed = false;
    } else {
      console.log(`⚠️  ${check.name.padEnd(15)} 未安装 (可选)`);
    }
  }
}

console.log('\n📄 检查配置文件:');
console.log('─'.repeat(50));

// 检查配置文件
for (const file of files) {
  const exists = fs.existsSync(file.path);
  
  if (exists) {
    console.log(`✅ ${file.name.padEnd(15)} 存在`);
  } else {
    if (file.required) {
      console.log(`❌ ${file.name.padEnd(15)} 缺失 (必需)`);
      allPassed = false;
    } else {
      console.log(`⚠️  ${file.name.padEnd(15)} 缺失 (可选)`);
    }
  }
}

// 检查主题结构
console.log('\n🏗️  检查主题结构:');
console.log('─'.repeat(50));

const themeDirectories = [
  'assets',
  'config',
  'layout',
  'locales',
  'sections',
  'snippets',
  'templates',
];

for (const dir of themeDirectories) {
  const exists = fs.existsSync(dir);
  console.log(`${exists ? '✅' : '❌'} ${dir.padEnd(15)} ${exists ? '存在' : '缺失'}`);
  
  if (!exists) {
    allPassed = false;
  }
}

// 检查关键文件
console.log('\n🔑 检查关键主题文件:');
console.log('─'.repeat(50));

const keyFiles = [
  'layout/theme.liquid',
  'config/settings_schema.json',
  'assets/theme.js',
  'assets/theme.css',
];

for (const file of keyFiles) {
  const exists = fs.existsSync(file);
  console.log(`${exists ? '✅' : '❌'} ${file.padEnd(25)} ${exists ? '存在' : '缺失'}`);
}

// 测试Shopify CLI主题命令
console.log('\n🛠️  测试Shopify CLI功能:');
console.log('─'.repeat(50));

try {
  execSync('shopify theme --help', { stdio: 'pipe' });
  console.log('✅ Shopify主题命令    可用');
} catch (error) {
  console.log('❌ Shopify主题命令    不可用');
  allPassed = false;
}

// 显示结果
console.log('\n' + '='.repeat(50));

if (allPassed) {
  console.log('🎉 所有检查通过！开发环境已准备就绪。');
  console.log('\n📋 下一步操作:');
  console.log('1. 连接到Shopify商店: shopify auth login');
  console.log('2. 启动开发服务器: npm run dev');
  console.log('3. 查看文档: docs/README.md');
} else {
  console.log('⚠️  部分检查未通过，请安装缺失的工具或文件。');
  process.exit(1);
}

console.log('\n📚 有用的命令:');
console.log('• npm run dev      - 启动开发服务器');
console.log('• npm run pull     - 从商店拉取主题');
console.log('• npm run push     - 推送主题到商店');
console.log('• npm run lint     - 检查代码质量');
console.log('• npm run format   - 格式化代码');
console.log('• shopify theme --help - 查看所有主题命令');
