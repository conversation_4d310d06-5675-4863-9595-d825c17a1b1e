{% comment %}
  Hero Section for Ideaformer IR3 V2 3D Printer - Enhanced Version
  File: sections/IR3-Hero-Section-1.liquid
{% endcomment %}

{{ 'IR3-hero-section-1.css' | asset_url | stylesheet_tag }}

<section
  class="hero-section"
  id="hero-{{ section.id }}"
  style="margin-top: {{ section.settings.margin_top }}px; margin-bottom: {{ section.settings.margin_bottom }}px;"
>
  <!-- Hero Container -->
  <div class="hero-container">
    <!-- Enhanced Background Layers -->
    <div class="background-layer">
      <div class="gradient-overlay"></div>
      <div class="animated-gradient"></div>
      <div class="grid-pattern"></div>
      <div class="tech-lines"></div>
      <div class="floating-shapes">
        <div class="shape shape-1"></div>
        <div class="shape shape-2"></div>
        <div class="shape shape-3"></div>
      </div>
      <div class="particle-field"></div>
    </div>

    <!-- Content Layer -->
    <div class="content-layer">
      <div class="container">
        <div class="hero-content">
          <!-- Title Group -->
          <div class="title-group animate-fade-in" data-delay="0">
            <span class="pre-title animate-slide-in">Next Generation Technology</span>
            <h1 class="main-title glitch" data-text="{{ section.settings.main_title | default: 'Ideaformer IR3 V2' }}">
              {{ section.settings.main_title | default: 'Ideaformer IR3 V2' }}
            </h1>
            <h2 class="sub-title">
              {{ section.settings.sub_title | default: 'Professional Conveyor Belt 3D Printer' }}
            </h2>
            <p class="tagline animate-fade-in" data-delay="0.3">
              {{ section.settings.tagline | default: 'Endless Printing Possibilities with Automatic Belt System' }}
            </p>
          </div>

          <!-- Feature Pills -->
          <div class="feature-pills animate-fade-in" data-delay="0.4">
            <span class="pill">⚡ Ultra Fast</span>
            <span class="pill">🎯 0.01mm Precision</span>
            <span class="pill">♾️ Infinite Z-Axis</span>
          </div>


        </div>

        <!-- Product Showcase -->
        <div class="product-showcase animate-fade-in" data-delay="0.3">
          <!-- 3D Stage -->
          <div class="product-stage">
            <!-- Product Image with Effects -->
            <div class="product-image-wrapper">
              <div class="product-glow"></div>
              <div class="product-image">
                {%- if section.settings.product_image != blank -%}
                  {{
                    section.settings.product_image
                    | image_url: width: 1200
                    | image_tag: loading: 'eager', alt: section.settings.product_image.alt
                    | default: 'Ideaformer IR3 V2 3D Printer', class: 'hero-product-img'
                  }}
                {%- else -%}
                  {{ 'product-1' | placeholder_svg_tag: 'hero-product-img placeholder-svg' }}
                {%- endif -%}
              </div>
              <div class="product-reflection"></div>
            </div>

            <!-- Orbiting Elements -->
            <div class="orbit-container">
              <div class="orbit orbit-1">
                <div class="orbit-item">
                  <div class="icon-wrapper">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                      <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                      <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                      <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                  </div>
                </div>
              </div>
              <div class="orbit orbit-2">
                <div class="orbit-item">
                  <div class="icon-wrapper">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                      <path d="M21 16V8C20.9996 7.64927 20.9071 7.30481 20.7315 7.00116C20.556 6.69751 20.3037 6.44536 20 6.27L13 2.27C12.696 2.09446 12.3511 2.00205 12 2.00205C11.6489 2.00205 11.304 2.09446 11 2.27L4 6.27C3.69626 6.44536 3.44398 6.69751 3.26846 7.00116C3.09294 7.30481 3.00036 7.64927 3 8V16C3.00036 16.3507 3.09294 16.6952 3.26846 16.9988C3.44398 17.3025 3.69626 17.5546 4 17.73L11 21.73C11.304 21.9055 11.6489 21.9979 12 21.9979C12.3511 21.9979 12.696 21.9055 13 21.73L20 17.73C20.3037 17.5546 20.556 17.3025 20.7315 16.9988C20.9071 16.6952 20.9996 16.3507 21 16Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Floating Badges -->
          <div class="floating-badges">
            <div class="badge badge-1 animate-float" data-delay="0.5">
              <span class="badge-icon">🏆</span>
              <span class="badge-text">{{ section.settings.badge_1_text | default: 'Award Winning' }}</span>
              <div class="badge-shine"></div>
            </div>
            <div class="badge badge-2 animate-float" data-delay="0.7">
              <span class="badge-icon">⚡</span>
              <span class="badge-text">{{ section.settings.badge_2_text | default: 'Fast Printing' }}</span>
              <div class="badge-shine"></div>
            </div>
            <div class="badge badge-3 animate-float" data-delay="0.9">
              <span class="badge-icon">♾️</span>
              <span class="badge-text">{{ section.settings.badge_3_text | default: 'Infinite Z-axis' }}</span>
              <div class="badge-shine"></div>
            </div>
          </div>

          <!-- Spec Highlights -->
          <div class="spec-highlights animate-fade-in" data-delay="1.1">
            <div class="spec-item">
              <span class="spec-value" data-value="200×170×∞">250×250×∞</span>
              <span class="spec-label">Build Volume (mm)</span>
            </div>
            <div class="spec-item">
              <span class="spec-value" data-value="0.05">0.01mm</span>
              <span class="spec-label">Layer Precision</span>
            </div>
            <div class="spec-item">
              <span class="spec-value" data-value="24/7">24/7</span>
              <span class="spec-label">Continuous Operation</span>
            </div>
          </div>
        </div>

        <!-- CTA Group -->
        <div class="cta-group animate-fade-in" data-delay="0.5">
          <a href="{{ section.settings.primary_button_link }}" class="primary-button magnetic-button">
            <span class="button-text">{{ section.settings.primary_button_text | default: 'Shop Now' }}</span>
            <span class="button-bg"></span>
            <svg class="button-icon" width="20" height="20" viewBox="0 0 24 24" fill="none">
              <path d="M5 12H19M19 12L12 5M19 12L12 19" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </a>
          <a href="{{ section.settings.secondary_button_link }}" class="secondary-button">
            <span class="button-inner">
              {{ section.settings.secondary_button_text | default: 'View Specs' }}
              <span class="button-border"></span>
            </span>
          </a>
        </div>

        <!-- Scroll Indicator -->
        <div class="scroll-indicator animate-fade-in" data-delay="0.7">
          <div class="mouse-icon">
            <div class="mouse-wheel"></div>
          </div>
          <span class="scroll-text">{{ section.settings.scroll_text | default: 'Scroll to explore' }}</span>
        </div>
      </div>
    </div>
  </div>
</section>

{{ 'IR3-hero-section-1.js' | asset_url | script_tag }}

{% schema %}
{
  "name": "Hero Product Section",
  "tag": "section",
  "class": "hero-product-section",
  "settings": [
    {
      "type": "header",
      "content": "Content Settings"
    },
    {
      "type": "text",
      "id": "main_title",
      "label": "Main Title",
      "default": "Ideaformer IR3 V2"
    },
    {
      "type": "text",
      "id": "sub_title",
      "label": "Subtitle",
      "default": "Professional Conveyor Belt 3D Printer"
    },
    {
      "type": "textarea",
      "id": "tagline",
      "label": "Tagline",
      "default": "Endless Printing Possibilities with Automatic Belt System"
    },
    {
      "type": "image_picker",
      "id": "product_image",
      "label": "Product Image"
    },
    {
      "type": "header",
      "content": "Button Settings"
    },
    {
      "type": "text",
      "id": "primary_button_text",
      "label": "Primary Button Text",
      "default": "Shop Now"
    },
    {
      "type": "url",
      "id": "primary_button_link",
      "label": "Primary Button Link"
    },
    {
      "type": "text",
      "id": "secondary_button_text",
      "label": "Secondary Button Text",
      "default": "View Specs"
    },
    {
      "type": "url",
      "id": "secondary_button_link",
      "label": "Secondary Button Link"
    },
    {
      "type": "text",
      "id": "scroll_text",
      "label": "Scroll Indicator Text",
      "default": "Scroll to explore"
    },
    {
      "type": "header",
      "content": "Badge Settings"
    },
    {
      "type": "text",
      "id": "badge_1_text",
      "label": "Badge 1 Text",
      "default": "Award Winning"
    },
    {
      "type": "text",
      "id": "badge_2_text",
      "label": "Badge 2 Text",
      "default": "Fast Printing"
    },
    {
      "type": "text",
      "id": "badge_3_text",
      "label": "Badge 3 Text",
      "default": "Infinite Z-axis"
    },
    {
      "type": "header",
      "content": "Spacing Settings"
    },
    {
      "type": "number",
      "id": "margin_top",
      "label": "Top Margin (px)",
      "default": 0,
      "info": "输入精确的上边距像素值"
    },
    {
      "type": "number",
      "id": "margin_bottom",
      "label": "Bottom Margin (px)",
      "default": 0,
      "info": "输入精确的下边距像素值"
    }
  ],
  "presets": [
    {
      "name": "Hero Product Section"
    }
  ]
}
{% endschema %}
