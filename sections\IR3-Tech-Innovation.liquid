{{ 'IR3-tech-innovation-clean.css' | asset_url | stylesheet_tag }}

<section class="tech-innovation-section" id="tech-innovation">
  <!-- VS背景冲击效果容器 -->
  <div class="vs-background-effects" id="vsBackgroundEffects">
    <!-- 背景颜色冲击层 -->
    <div class="bg-color-impact"></div>


    <!-- 闪电效果 -->
    <div class="bg-element lightning lightning-1"></div>
    <div class="bg-element lightning lightning-2"></div>
    <div class="bg-element lightning lightning-3"></div>

    <!-- 爆炸粒子 -->
    <div class="bg-element explosion-particle particle-1"></div>
    <div class="bg-element explosion-particle particle-2"></div>
    <div class="bg-element explosion-particle particle-3"></div>
    <div class="bg-element explosion-particle particle-4"></div>
    <div class="bg-element explosion-particle particle-5"></div>
    <div class="bg-element explosion-particle particle-6"></div>
    <div class="bg-element explosion-particle particle-7"></div>
    <div class="bg-element explosion-particle particle-8"></div>


    <!-- 能量光束 -->
    <div class="bg-element energy-beam beam-1"></div>
    <div class="bg-element energy-beam beam-2"></div>
    <div class="bg-element energy-beam beam-3"></div>
    <div class="bg-element energy-beam beam-4"></div>
  </div>

  <div class="tech-innovation-container">

    <!-- 标题区域 -->
    <div class="section-header">
      <h2 class="section-title">
        Revolutionary Metal Belt Technology
      </h2>
      <p class="section-description">
        Experience the next generation of 3D printing with our breakthrough metal conveyor belt system. Unlike traditional cloth belts that cause high models to shake and wobble, our metal belt provides rock-solid stability for tall prints with 100% auto-leveling precision.
      </p>
    </div>

    <!-- 对比展示区域 -->
    <div class="comparison-container">
      
      <!-- 旧技术 - 左侧布带子 -->
      <div class="tech-side old-tech">
        <div class="tech-visual">
          <div class="belt-image-container interactive-media-container-left">
            <!-- 静态图片 -->
            <img src="https://cdn.shopify.com/s/files/1/0762/6113/0493/files/gd01.png?v=1752307805"
                 alt="Old Cloth Belt"
                 class="belt-image cloth-belt-image static-image-left">

            <!-- 交互视频 -->
            <video class="belt-video cloth-belt-video interactive-video-left"
                   muted
                   loop
                   playsinline
                   preload="metadata">
              <source src="https://cdn.shopify.com/videos/c/o/v/9eb1edf282934e38b50b5faa6cc7499b.mp4" type="video/mp4">
            </video>

            <!-- 交互提示元素 - 直接在容器内居中 -->
            <div class="interaction-hint">
              <div class="play-icon">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                  <path d="M8 5v14l11-7z" fill="currentColor"/>
                </svg>
              </div>
              <div class="hint-text">Hover to play video</div>
            </div>

            <div class="image-overlay">
              <div class="issue-badge">
                <span class="issue-icon">⚠️</span>
                <span class="issue-text">Wear & Tear</span>
              </div>
            </div>
          </div>
        </div>
        <div class="tech-info">
          <h3 class="tech-title">Old Cloth Belt</h3>
          <p class="tech-subtitle">Traditional Technology</p>
          <ul class="tech-specs">
            <li>Fabric-based material</li>
            <li>Causes high model shaking</li>
            <li>Leveling issues</li>
            <li>Regular replacement needed</li>
          </ul>
        </div>
      </div>

      <!-- VS 分隔符 -->
      <div class="vs-divider">
        <div class="vs-circle">
          <span class="vs-text">VS</span>
        </div>
      </div>

      <!-- 新技术 - 右侧金属带子 -->
      <div class="tech-side new-tech">
        <div class="tech-visual">
          <div class="belt-image-container interactive-media-container">
            <!-- 静态图片 -->
            <img src="https://cdn.shopify.com/s/files/1/0762/6113/0493/files/gd02.png?v=1752307804"
                 alt="New Metal Belt"
                 class="belt-image metal-belt-image static-image">

            <!-- 交互视频 -->
            <video class="belt-video metal-belt-video interactive-video"
                   muted
                   loop
                   playsinline
                   preload="metadata">
              <source src="https://cdn.shopify.com/videos/c/o/v/cb900f6a2ca640b796528b4cce8d6f79.mp4" type="video/mp4">
            </video>

            <!-- 交互提示元素 - 直接在容器内居中 -->
            <div class="interaction-hint">
              <div class="play-icon">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                  <path d="M8 5v14l11-7z" fill="currentColor"/>
                </svg>
              </div>
              <div class="hint-text">Hover to play video</div>
            </div>

            <div class="image-overlay">
              <div class="advantage-badge">
                <span class="advantage-icon">✨</span>
                <span class="advantage-text">Ultra Precision</span>
              </div>
            </div>
          </div>
        </div>
        <div class="tech-info">
          <h3 class="tech-title">New Upgrade Metal Belt</h3>
          <p class="tech-subtitle">Next-Gen Innovation</p>
          <ul class="tech-specs">
            <li>Better Adhesion</li>
            <li>Easy to Remove Model</li>
            <li>Metal belt with PEI coating</li>
            <li>Zero shaking for tall models</li>
          </ul>
        </div>
      </div>

    </div>

    <!-- 技术数据展示 -->
    <div class="tech-data">
      <div class="data-grid">
        <div class="data-item">
          <div class="data-value" data-value="400+">400+</div>
          <div class="data-label">mm/s Print Speed</div>
        </div>
        <div class="data-item">
          <div class="data-value" data-value="100%">100%</div>
          <div class="data-label">Auto Leveling</div>
        </div>
        <div class="data-item">
          <div class="data-value" data-value="PEI">PEI</div>
          <div class="data-label">Coated Surface</div>
        </div>
        <div class="data-item">
          <div class="data-value" data-value="0">0</div>
          <div class="data-label">Shaking Issues</div>
        </div>
      </div>
    </div>

  </div>
</section>

<!-- 优化的滚动动画 -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js"></script>
<script>
console.log('🚀 内联脚本开始执行');

// 统一动画控制器 - 确保真正的同步显示
let globalAnimationSync = {
  animationStartTime: null,
  animationDuration: 10000, // 10秒
  syncInterval: null,

  // 启动统一的动画控制器
  startUnifiedAnimation: function() {
    console.log('🚀 启动统一动画控制器');

    const allHints = document.querySelectorAll('.interaction-hint');
    console.log('📋 找到提示元素数量:', allHints.length);

    if (allHints.length === 0) return;

    // 停止现有的CSS动画，使用JavaScript控制
    allHints.forEach((hint, index) => {
      hint.style.animation = 'none';
      hint.style.opacity = '0';
      hint.style.visibility = 'hidden';
      console.log(`🎯 提示${index + 1}CSS动画已停用`);
    });

    // 记录动画开始时间
    this.animationStartTime = Date.now();

    // 启动统一的动画循环
    this.runUnifiedAnimationLoop();

    console.log('✅ 统一动画控制器已启动');
  },

  // 运行统一的动画循环
  runUnifiedAnimationLoop: function() {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
    }

    this.syncInterval = setInterval(() => {
      this.updateAllHintsState();
    }, 50); // 每50ms更新一次状态

    console.log('🔄 统一动画循环已启动');
  },

  // 更新所有提示的状态
  updateAllHintsState: function() {
    const allHints = document.querySelectorAll('.interaction-hint');
    if (allHints.length === 0) return;

    const currentTime = Date.now();
    const elapsed = (currentTime - this.animationStartTime) % this.animationDuration;
    const progress = elapsed / this.animationDuration; // 0-1

    // 根据进度计算应该显示的状态
    const shouldShow = this.calculateVisibilityState(progress);

    // 同时更新所有提示
    allHints.forEach((hint, index) => {
      // 跳过正在被鼠标悬停的容器
      const container = hint.closest('.interactive-media-container, .interactive-media-container-left');
      if (container && container.matches(':hover')) {
        return; // 鼠标悬停时不更新
      }

      hint.style.opacity = shouldShow.opacity;
      hint.style.visibility = shouldShow.visibility;
      hint.style.transform = shouldShow.transform;
    });
  },

  // 计算基于进度的可见性状态
  calculateVisibilityState: function(progress) {
    // 复制CSS动画的逻辑
    if (progress <= 0.3) {
      // 0-30%: 隐藏状态
      return {
        opacity: '0',
        visibility: 'hidden',
        transform: 'translate(-50%, -50%) scale(0.8)'
      };
    } else if (progress <= 0.4) {
      // 30-40%: 渐入过程
      const fadeProgress = (progress - 0.3) / 0.1;
      const opacity = fadeProgress;
      const scale = 0.8 + (0.2 * fadeProgress);
      return {
        opacity: opacity.toString(),
        visibility: 'visible',
        transform: `translate(-50%, -50%) scale(${scale})`
      };
    } else if (progress <= 0.6) {
      // 40-60%: 完全显示
      return {
        opacity: '1',
        visibility: 'visible',
        transform: 'translate(-50%, -50%) scale(1)'
      };
    } else if (progress <= 0.7) {
      // 60-70%: 渐出过程
      const fadeProgress = (progress - 0.6) / 0.1;
      const opacity = 1 - fadeProgress;
      const scale = 1 - (0.2 * fadeProgress);
      return {
        opacity: opacity.toString(),
        visibility: opacity > 0 ? 'visible' : 'hidden',
        transform: `translate(-50%, -50%) scale(${scale})`
      };
    } else {
      // 70-100%: 隐藏状态
      return {
        opacity: '0',
        visibility: 'hidden',
        transform: 'translate(-50%, -50%) scale(0.8)'
      };
    }
  },

  // 重启统一动画（用于鼠标离开后的同步）
  restartUnifiedAnimation: function() {
    console.log('🔄 重启统一动画');
    this.animationStartTime = Date.now();
    console.log('✅ 动画时间轴已重置');
  },

  // 停止统一动画
  stopUnifiedAnimation: function() {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
      console.log('⏹️ 统一动画已停止');
    }
  }
};

// 交互式视频控制功能
function initInteractiveVideo() {
  console.log('🚀 initInteractiveVideo 开始执行');

  // 右边的交互式视频
  const interactiveContainer = document.querySelector('.interactive-media-container');
  const video = document.querySelector('.interactive-video');
  const image = document.querySelector('.static-image');

  // 左边的交互式视频
  const interactiveContainerLeft = document.querySelector('.interactive-media-container-left');
  const videoLeft = document.querySelector('.interactive-video-left');
  const imageLeft = document.querySelector('.static-image-left');

  console.log('🔍 元素查找结果:', {
    rightContainer: !!interactiveContainer,
    rightVideo: !!video,
    rightImage: !!image,
    leftContainer: !!interactiveContainerLeft,
    leftVideo: !!videoLeft,
    leftImage: !!imageLeft
  });

  // 初始化右边的交互式视频
  if (interactiveContainer && video && image) {
    initSingleInteractiveVideo(interactiveContainer, video, image, '右边');
  } else {
    console.warn('❌ Right interactive video elements not found');
  }

  // 初始化左边的交互式视频
  if (interactiveContainerLeft && videoLeft && imageLeft) {
    initSingleInteractiveVideo(interactiveContainerLeft, videoLeft, imageLeft, '左边');
  } else {
    console.warn('❌ Left interactive video elements not found');
  }

  // 初始化完成后，启动统一动画控制器
  setTimeout(() => {
    console.log('🔄 初始化完成，启动统一动画控制器');
    globalAnimationSync.startUnifiedAnimation();
  }, 500); // 增加延迟确保DOM完全加载

  // 初始化VS按钮点击功能
  initVSButtonInteraction();
}

// VS按钮媒体切换控制器 - 设置为全局变量以便其他脚本访问
window.vsMediaController = {
  isPlaying: false,
  leftVideo: null,
  rightVideo: null,
  leftImage: null,
  rightImage: null,
  vsButton: null,
  animationTimeout: null,
  backgroundEffects: null,

  // 初始化媒体元素引用
  initMediaElements: function() {
    this.leftVideo = document.querySelector('.cloth-belt-video');
    this.rightVideo = document.querySelector('.metal-belt-video');
    this.leftImage = document.querySelector('.cloth-belt-image');
    this.rightImage = document.querySelector('.metal-belt-image');
    this.vsButton = document.querySelector('.vs-circle');
    this.backgroundEffects = document.getElementById('vsBackgroundEffects');

    console.log('🎬 媒体元素初始化:', {
      leftVideo: !!this.leftVideo,
      rightVideo: !!this.rightVideo,
      leftImage: !!this.leftImage,
      rightImage: !!this.rightImage,
      vsButton: !!this.vsButton,
      backgroundEffects: !!this.backgroundEffects
    });
  },

  // 获取视频时长
  getVideoDurations: async function() {
    const leftDuration = await this.getVideoDuration(this.leftVideo);
    const rightDuration = await this.getVideoDuration(this.rightVideo);

    console.log('⏱️ 视频时长:', {
      left: leftDuration,
      right: rightDuration,
      longer: Math.max(leftDuration, rightDuration)
    });

    return {
      left: leftDuration,
      right: rightDuration,
      longer: Math.max(leftDuration, rightDuration)
    };
  },

  // 获取单个视频时长
  getVideoDuration: function(video) {
    return new Promise((resolve) => {
      if (video.duration) {
        resolve(video.duration);
      } else {
        video.addEventListener('loadedmetadata', () => {
          resolve(video.duration);
        });
        // 确保视频开始加载
        video.load();
      }
    });
  },

  // VS按钮点击动画 - 简化连续点击处理
  animateVSButton: function() {
    console.log('🎯 VS按钮点击动画开始');

    if (!this.vsButton) return;

    // 清除之前的定时器
    if (this.animationTimeout) {
      clearTimeout(this.animationTimeout);
    }

    // 简单有效的重启方法：先移除，强制重排，再添加
    this.vsButton.classList.remove('vs-clicked');
    void this.vsButton.offsetWidth; // 强制重排，比offsetHeight更可靠
    this.vsButton.classList.add('vs-clicked');

    console.log('🎬 动画类已添加');

    // 设置定时器移除动画类
    this.animationTimeout = setTimeout(() => {
      if (this.vsButton && this.vsButton.classList.contains('vs-clicked')) {
        this.vsButton.classList.remove('vs-clicked');
        console.log('✅ VS按钮动画完成');
      }
      this.animationTimeout = null;
    }, 1200);
  },

  // 开始媒体切换序列
  startMediaSequence: async function() {
    // VS按钮动画总是执行，给用户反馈
    this.animateVSButton();

    if (this.isPlaying) {
      console.log('🎯 视频播放中，只执行VS按钮动画效果');
      return; // 播放中只执行动画，不影响视频
    }

    console.log('🚀 开始媒体切换序列');
    this.isPlaying = true;
    console.log(`🔒 VS模式已激活：isPlaying = ${this.isPlaying}`);

    // 1. 暂停统一动画控制器，隐藏交互提示
    this.pauseInteractionHints();

    // 2. 显示背景装饰效果
    this.showBackgroundEffects();

    // 3. 获取视频时长
    const durations = await this.getVideoDurations();

    // 4. 切换到视频
    await this.switchToVideos();

    // 5. 开始同步播放
    this.startSyncedPlayback(durations);
  },

  // 切换到视频显示
  switchToVideos: function() {
    return new Promise((resolve) => {
      console.log('🎬 切换到视频显示');

      // 淡出图片，淡入视频
      if (this.leftImage && this.leftVideo) {
        this.leftImage.style.opacity = '0';
        this.leftVideo.style.opacity = '1';
      }

      if (this.rightImage && this.rightVideo) {
        this.rightImage.style.opacity = '0';
        this.rightVideo.style.opacity = '1';
      }

      // 等待过渡完成
      setTimeout(resolve, 400);
    });
  },

  // 开始同步播放
  startSyncedPlayback: function(durations) {
    console.log('▶️ 开始同步播放');

    // 重置视频到开始位置
    this.leftVideo.currentTime = 0;
    this.rightVideo.currentTime = 0;

    // 设置较短视频循环播放
    if (durations.left < durations.right) {
      this.leftVideo.loop = true;
      this.rightVideo.loop = false;
    } else if (durations.right < durations.left) {
      this.rightVideo.loop = true;
      this.leftVideo.loop = false;
    } else {
      // 时长相同，都不循环
      this.leftVideo.loop = false;
      this.rightVideo.loop = false;
    }

    // 同时开始播放
    this.leftVideo.play();
    this.rightVideo.play();

    // 监听较长视频结束
    const longerVideo = durations.left >= durations.right ? this.leftVideo : this.rightVideo;
    const endHandler = () => {
      console.log('🎬 较长视频播放结束，触发序列结束');
      this.endMediaSequence();
    };

    longerVideo.addEventListener('ended', endHandler, { once: true });
    console.log(`📺 已绑定结束事件监听器到${durations.left >= durations.right ? '左边' : '右边'}视频`);
  },

  // 结束媒体序列
  endMediaSequence: function() {
    console.log('🏁 媒体序列结束');

    // 完全重置所有视频状态
    this.resetAllVideoStates();

    // 切换回图片
    this.switchToImages();

    // 隐藏背景装饰效果
    this.hideBackgroundEffects();

    // 恢复交互提示显示
    this.resumeInteractionHints();

    // 重置播放状态
    this.isPlaying = false;
    console.log(`🔓 VS模式已关闭：isPlaying = ${this.isPlaying}`);
  },

  // 完全重置所有视频状态
  resetAllVideoStates: function() {
    console.log('🔄 重置所有视频状态');

    if (this.leftVideo) {
      this.leftVideo.pause();
      this.leftVideo.currentTime = 0;
      this.leftVideo.loop = false;
      // 移除所有事件监听器
      this.leftVideo.removeEventListener('ended', this.endMediaSequence);
    }

    if (this.rightVideo) {
      this.rightVideo.pause();
      this.rightVideo.currentTime = 0;
      this.rightVideo.loop = false;
      // 移除所有事件监听器
      this.rightVideo.removeEventListener('ended', this.endMediaSequence);
    }

    console.log('✅ 所有视频状态已重置');
  },

  // 切换回图片显示
  switchToImages: function() {
    console.log('🖼️ 切换回图片显示');

    // 强制重置视觉状态
    if (this.leftImage && this.leftVideo) {
      this.leftVideo.style.opacity = '0';
      this.leftImage.style.opacity = '1';
      // 确保视频完全停止
      this.leftVideo.pause();
      this.leftVideo.currentTime = 0;
    }

    if (this.rightImage && this.rightVideo) {
      this.rightVideo.style.opacity = '0';
      this.rightImage.style.opacity = '1';
      // 确保视频完全停止
      this.rightVideo.pause();
      this.rightVideo.currentTime = 0;
    }

    console.log('✅ 图片显示状态已强制重置');
  },

  // 暂停交互提示显示
  pauseInteractionHints: function() {
    console.log('⏸️ 暂停交互提示显示');

    // 停止统一动画控制器
    globalAnimationSync.stopUnifiedAnimation();

    // 隐藏所有交互提示
    const allHints = document.querySelectorAll('.interaction-hint');
    allHints.forEach(hint => {
      hint.style.opacity = '0';
      hint.style.visibility = 'hidden';
      hint.style.transition = 'all 0.3s ease';
    });

    console.log('✅ 交互提示已隐藏');
  },

  // 恢复交互提示显示
  resumeInteractionHints: function() {
    console.log('▶️ 恢复交互提示显示');

    // 延迟一下再恢复，确保视频切换完成
    setTimeout(() => {
      // 重新启动统一动画控制器
      globalAnimationSync.startUnifiedAnimation();
      console.log('✅ 交互提示已恢复');
    }, 500);
  },

  // 显示背景装饰效果
  showBackgroundEffects: function() {
    console.log('🎨 显示背景装饰效果');

    if (this.backgroundEffects) {
      this.backgroundEffects.classList.add('active');
      console.log('✅ 背景装饰效果已激活');
    }
  },

  // 隐藏背景装饰效果
  hideBackgroundEffects: function() {
    console.log('🎨 隐藏背景装饰效果');

    if (this.backgroundEffects) {
      this.backgroundEffects.classList.remove('active');
      console.log('✅ 背景装饰效果已隐藏');
    }
  }
};

// 初始化VS按钮交互
function initVSButtonInteraction() {
  console.log('🎯 初始化VS按钮交互');

  // 初始化媒体元素
  window.vsMediaController.initMediaElements();

  // 添加点击事件监听
  if (window.vsMediaController.vsButton) {
    window.vsMediaController.vsButton.addEventListener('click', () => {
      console.log('🎯 VS按钮被点击');
      window.vsMediaController.startMediaSequence();
    });

    // 添加鼠标悬停效果
    window.vsMediaController.vsButton.style.cursor = 'pointer';

    console.log('✅ VS按钮交互已初始化');
  } else {
    console.warn('❌ 找不到VS按钮元素');
  }
}

// 单个交互式视频初始化函数
function initSingleInteractiveVideo(container, video, image, side) {
  console.log(`🎯 初始化${side}交互式视频`);

  // 预加载视频
  video.load();

  // 设置初始状态：图片显示，视频隐藏
  video.style.opacity = '0';
  image.style.opacity = '1';

  // 强制设置过渡属性，使用setProperty确保优先级
  video.style.setProperty('transition', 'opacity 0.4s cubic-bezier(0.4, 0, 0.2, 1)', 'important');
  image.style.setProperty('transition', 'opacity 0.4s cubic-bezier(0.4, 0, 0.2, 1)', 'important');

  console.log(`✅ ${side}初始状态和过渡属性设置完成`);

  // 定义事件处理函数
  const handleMouseEnter = () => {
    // 强化VS模式检查 - 添加详细调试
    console.log(`🔍 ${side}鼠标进入检查：vsMediaController存在=${!!window.vsMediaController}, isPlaying=${window.vsMediaController?.isPlaying}`);

    if (window.vsMediaController && window.vsMediaController.isPlaying) {
      console.log(`🚫 ${side}鼠标进入：VS模式播放中(${window.vsMediaController.isPlaying})，完全忽略悬停`);
      return;
    }

    console.log(`🎯 ${side}鼠标进入：开始淡化到视频`);

    // 直接设置样式，触发CSS过渡效果
    video.style.opacity = '1';
    image.style.opacity = '0';

    // 播放视频
    video.currentTime = 0;
    video.play().catch(error => {
      console.warn(`${side}视频播放失败:`, error);
    });
  };

  const handleMouseLeave = () => {
    // 强化VS模式检查 - 添加详细调试
    console.log(`🔍 ${side}鼠标离开检查：vsMediaController存在=${!!window.vsMediaController}, isPlaying=${window.vsMediaController?.isPlaying}`);

    if (window.vsMediaController && window.vsMediaController.isPlaying) {
      console.log(`🚫 ${side}鼠标离开：VS模式播放中(${window.vsMediaController.isPlaying})，完全忽略离开`);
      return;
    }

    console.log(`🎯 ${side}鼠标离开：淡化回图片`);
    console.log(`📍 当前容器:`, container);
    console.log(`📍 容器类名:`, container.className);

    // 直接设置样式，触发CSS过渡效果
    video.style.opacity = '0';
    image.style.opacity = '1';

    // 停止视频
    video.pause();
    video.currentTime = 0;

    // 重启统一动画，确保同步
    console.log(`🔄 ${side}鼠标离开，重启统一动画`);
    setTimeout(() => {
      globalAnimationSync.restartUnifiedAnimation();
      console.log(`✅ ${side}动画时间轴已重置`);
    }, 50); // 短暂延迟确保CSS过渡完成
  };

  // 添加鼠标事件监听器
  console.log(`🎯 为${side}添加事件监听器:`, container);
  container.addEventListener('mouseenter', handleMouseEnter);
  container.addEventListener('mouseleave', handleMouseLeave);
  console.log(`✅ ${side}事件监听器添加完成`);

  // 视频加载完成
  video.addEventListener('loadeddata', () => {
    console.log(`${side}交互式视频加载成功`);
    video.setAttribute('data-loaded', 'true');
  });

  console.log(`${side}交互式视频初始化完成`);


}

// 全局调试函数
window.debugInteractiveVideo = () => {
  console.log('🔧 手动调试交互式视频');

  // 右边的交互式视频
  const container = document.querySelector('.interactive-media-container');
  const video = document.querySelector('.interactive-video');
  const image = document.querySelector('.static-image');

  // 左边的交互式视频
  const containerLeft = document.querySelector('.interactive-media-container-left');
  const videoLeft = document.querySelector('.interactive-video-left');
  const imageLeft = document.querySelector('.static-image-left');

  console.log('右边元素状态:', {
    container: !!container,
    video: !!video,
    image: !!image,
    videoOpacity: video ? window.getComputedStyle(video).opacity : 'N/A',
    imageOpacity: image ? window.getComputedStyle(image).opacity : 'N/A'
  });

  console.log('左边元素状态:', {
    container: !!containerLeft,
    video: !!videoLeft,
    image: !!imageLeft,
    videoOpacity: videoLeft ? window.getComputedStyle(videoLeft).opacity : 'N/A',
    imageOpacity: imageLeft ? window.getComputedStyle(imageLeft).opacity : 'N/A'
  });

  // 测试右边
  if (container) {
    console.log('手动触发右边鼠标进入事件');
    container.dispatchEvent(new MouseEvent('mouseenter', { bubbles: true }));

    setTimeout(() => {
      console.log('手动触发右边鼠标离开事件');
      container.dispatchEvent(new MouseEvent('mouseleave', { bubbles: true }));
    }, 2000);
  }

  // 测试左边
  if (containerLeft) {
    setTimeout(() => {
      console.log('手动触发左边鼠标进入事件');
      containerLeft.dispatchEvent(new MouseEvent('mouseenter', { bubbles: true }));

      setTimeout(() => {
        console.log('手动触发左边鼠标离开事件');
        containerLeft.dispatchEvent(new MouseEvent('mouseleave', { bubbles: true }));
      }, 2000);
    }, 4000);
  }
};

// 等待DOM加载完成后初始化
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initInteractiveVideo);
} else {
  initInteractiveVideo();
}

console.log('🚀 内联脚本执行完成');
</script>

<script src="{{ 'tech-innovation-animations.js' | asset_url }}?v={{ 'now' | date: '%s' }}" defer></script>

{% schema %}
{
  "name": "IR3 Tech Innovation",
  "settings": [
    {
      "type": "text",
      "id": "section_title",
      "label": "Section Title",
      "default": "Revolutionary Metal Belt Technology"
    },
    {
      "type": "textarea",
      "id": "section_description",
      "label": "Section Description",
      "default": "Experience the next generation of 3D printing with our breakthrough metal conveyor belt system."
    }
  ],
  "presets": [
    {
      "name": "IR3 Tech Innovation"
    }
  ]
}
{% endschema %}
