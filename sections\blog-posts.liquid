{%- assign blog = blogs[section.settings.blog] -%}

{%- if section.settings.divider -%}<div class="section--divider">{%- endif -%}

<div class="page-width">

  {%- if section.settings.title != blank -%}
    <header class="section-header">
      <h2 class="section-header__title">
        {{ section.settings.title | escape }}
        {%- if section.settings.view_all -%}
          <a href="{{ blog.url }}"  class="section-header__link">{{ 'blogs.article.view_all' | t }}</a>
        {%- endif -%}
      </h2>
    </header>
  {%- endif -%}

  {%- unless blog.empty? or blog.articles.size == 0 -%}

    <div class="grid grid--uniform">
      {%- for article in blog.articles limit: section.settings.post_limit -%}
        <div class="grid__item medium-up--one-third" data-aos="row-of-3">
          {%- if article.image -%}
            <a href="{{ article.url }}" class="article__grid-image" aria-label="{{ article.title | escape }}">
              <div class="image-wrap" style="height: 0; padding-bottom: {{ 100 | divided_by: article.image.aspect_ratio }}%;">
                {%- render 'image-element',
                  img: article.image,
                  widths: '180, 360, 540, 720, 900, 1080',
                  alt: article.title,
                  sizeVariable: '33vw',
                -%}
              </div>
            </a>
          {%- endif -%}

          <div class="article__grid-meta">
            {%- if section.settings.blog_show_date or article.tags.size > 0 or articles.comments_count > 0 -%}
              <div class="article__date">
                {%- if section.settings.blog_show_tags and article.tags.size > 0 -%}
                  {%- for tag in article.tags -%}
                    {% if tag contains "_" %}{%- assign tag_starts_with = tag | slice: 0 -%}{% if tag_starts_with == "_" %}{% if tag_count %}{%- assign tag_count = tag_count | minus: 1 | at_least: 0 -%}{% endif %}{% continue %}{% endif %}{% endif %}
                    <a href="{{ blog.url }}/tagged/{{ tag | handle }}">{{ tag }}</a> &middot;
                  {%- endfor -%}
                {%- endif -%}
                {%- if section.settings.blog_show_comments and article.comments_count > 0 -%}
                  <a href="{{ article.url }}#comments">
                    {{ 'blogs.comments.with_count' | t: count: article.comments_count }}
                  </a> &middot;
                {%- endif -%}
                {%- if section.settings.blog_show_date -%}
                  {{ article.published_at | time_tag: format: 'month_day_year' }}
                {%- endif -%}
              </div>
            {%- endif -%}

            <a href="{{ article.url }}" class="article__title">{{ article.title }}</a>

            {%- if section.settings.blog_show_author -%}
              <div class="article__author">by {{ article.author }}</div>
            {%- endif -%}
          </div>
        </div>
      {%- endfor -%}
    </div>

  {%- else -%}

    <div class="grid grid--uniform">
      {%- for i in (1..3) -%}
        <div class="grid__item medium-up--one-third" data-aos>

          <a href="#" class="article__grid-image"><div class="image-wrap">
            {{ 'image' | placeholder_svg_tag: 'placeholder-svg' }}
          </div></a>

          <div class="article__grid-meta">

            {%- if section.settings.blog_show_date -%}
              <div class="article__date">Jul 17 {{ "now" | date: "%Y" }}</div>
            {%- endif -%}

            <a href="#" class="article__title">Example blog post</a>

            {%- if section.settings.blog_show_author -%}
              <div class="article__author">by John Doe</div>
            {%- endif -%}
          </div>
        </div>
      {%- endfor -%}
    </div>

  {%- endunless -%}

</div>

{%- if section.settings.divider -%}</div>{%- endif -%}

{% schema %}
{
  "name": "t:sections.blog-posts.name",
  "class": "index-section",
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "t:sections.blog-posts.settings.title.label",
      "default": "Blog posts"
    },
    {
      "id": "blog",
      "type": "blog",
      "label": "t:sections.blog-posts.settings.blog.label"
    },
    {
      "type": "range",
      "id": "post_limit",
      "label": "t:sections.blog-posts.settings.post_limit.label",
      "default": 3,
      "min": 3,
      "max": 12,
      "step": 3
    },
    {
      "type": "checkbox",
      "id": "blog_show_tags",
      "label": "t:sections.blog-posts.settings.blog_show_tags.label"
    },
    {
      "type": "checkbox",
      "id": "blog_show_date",
      "label": "t:sections.blog-posts.settings.blog_show_date.label",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "blog_show_comments",
      "label": "t:sections.blog-posts.settings.blog_show_comments.label"
    },
    {
      "type": "checkbox",
      "id": "blog_show_author",
      "label": "t:sections.blog-posts.settings.blog_show_author.label"
    },
    {
      "type": "checkbox",
      "id": "view_all",
      "label": "t:sections.blog-posts.settings.view_all.label",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "divider",
      "label": "t:sections.blog-posts.settings.divider.label",
      "default": false
    }
  ],
  "presets": [
    {
      "name": "t:sections.blog-posts.presets.blog_posts.name",
      "settings": {
        "blog": "News",
        "post_limit": 3
      }
    }
  ],
  "disabled_on": {
    "groups": ["footer", "header", "custom.popups"]
  }
}
{% endschema %}
