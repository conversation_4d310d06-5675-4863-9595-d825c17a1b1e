{%- liquid
  assign collection = collections[section.settings.collection]
  assign isEmpty = false

  if section.settings.collection == blank
    assign isEmpty = true
  endif
-%}

{%- if section.settings.divider -%}<div class="section--divider">{%- endif -%}

<div class="page-width">
  <div class="feature-row feature-row--small-none">

    <div class="feature-row__item feature-row__callout-image">
      <div class="callout-images{% if isEmpty %} blank-state{% endif %}" data-aos="collection-callout">
        {%- if isEmpty -%}
          {%- for i in (1..5) -%}
            {%- capture current -%}{% cycle 1, 2, 3, 4, 5, 6 %}{%- endcapture -%}
            {%- if forloop.index == 1 -%}
              <div class="callout-image-centered">
            {%- endif -%}
              <div class="callout-image callout-image--{{ forloop.index }}">
                {{ 'product-' | append: current | placeholder_svg_tag: 'placeholder-svg' }}
              </div>
            {%- if forloop.last -%}
              </div>
            {%- endif -%}
          {%- endfor -%}
        {%- else -%}
          {%- for product in collection.products limit: 5 -%}
            {%- liquid
              case forloop.index
                when 1
                  assign callout_image_size_small = '280x'
                  assign callout_image_size_medium = '360x'
                  assign callout_image_size_large = '560x'
                  assign sizeVariable = '280px'
                  assign fallback = '140px'
                when 2
                  assign callout_image_size_small = '180x'
                  assign callout_image_size_medium = '234x'
                  assign callout_image_size_large = '360x'
                  assign sizeVariable = '180px'
                  assign fallback = '90px'
                when 3
                  assign callout_image_size_small = '160x'
                  assign callout_image_size_medium = '208x'
                  assign callout_image_size_large = '320x'
                  assign sizeVariable = '160px'
                  assign fallback = '80px'
                when 4
                  assign callout_image_size_small = '200x'
                  assign callout_image_size_medium = '260x'
                  assign callout_image_size_large = '400x'
                  assign sizeVariable = '200px'
                  assign fallback = '100px'
                when 5
                  assign callout_image_size_small = '100x'
                  assign callout_image_size_medium = '130x'
                  assign callout_image_size_large = '200x'
                  assign sizeVariable = '100px'
                  assign fallback = '50px'
              endcase
            -%}

            {%- if forloop.index == 1 -%}
              <div class="callout-image-centered">
            {%- endif -%}

              {%- capture widths -%}
                {{ callout_image_size_small | remove: 'x' | append: 'w' }},
                {{ callout_image_size_medium | remove: 'x' | append: 'w' }},
                {{ callout_image_size_large | remove: 'x' | append: 'w' }}
              {%- endcapture -%}

              {%- assign classes = 'callout-image callout-image--' | append: forloop.index -%}

              {%- render 'image-element',
                img: product,
                alt: product.title,
                classes: classes,
                widths: widths,
                sizeVariable: sizeVariable,
                fallback: fallback,
              -%}

            {%- if forloop.last -%}
              </div>
            {%- endif -%}
          {%- endfor -%}
        {%- endif -%}
      </div>
    </div>

    <div class="feature-row__item feature-row__text feature-row__text--{{ section.settings.layout }} feature-row__callout-text larger-text">
      <div class="feature-row__content">
        {%- if section.settings.subtitle -%}
          <p class="subtitle">{{ section.settings.subtitle }}</p>
        {%- endif -%}
        {%- if section.settings.title != blank -%}
          <h2 class="h3">{{ section.settings.title }}</h2>
        {%- endif -%}
        <div class="rte">
          {%- if section.settings.text != blank -%}
            <p>{{ section.settings.text }}</p>
          {%- endif -%}
        </div>
        {%- if section.settings.cta_text1 != blank -%}
          <a href="{{ section.settings.cta_link1 }}" class="btn{% if section.settings.cta_text2 != blank %} btn--no-animate{% endif %}">
            {{ section.settings.cta_text1 }}
          </a>
        {%- endif -%}
        {%- if section.settings.cta_text2 != blank -%}
          <a href="{{ section.settings.cta_link2 }}" class="btn btn--no-animate">
            {{ section.settings.cta_text2 }}
          </a>
        {%- endif -%}
      </div>
    </div>

  </div>
</div>

{%- if section.settings.divider -%}</div>{%- endif -%}

{% schema %}
{
  "name": "t:sections.collection-callout.name",
  "class": "index-section",
  "settings": [
    {
      "type": "collection",
      "id": "collection",
      "label": "t:sections.collection-callout.settings.collection.label"
    },
    {
      "type": "text",
      "id": "subtitle",
      "label": "t:sections.collection-callout.settings.subtitle.label",
      "default": "Brand new"
    },
    {
      "type": "text",
      "id": "title",
      "label": "t:sections.collection-callout.settings.title.label",
      "default": "Collection callout"
    },
    {
      "type": "textarea",
      "id": "text",
      "label": "t:sections.collection-callout.settings.text.label",
      "default": "Use this section to easily call attention to one of your collections. We'll show photos of the first 5 products."
    },
    {
      "type": "text",
      "id": "cta_text1",
      "label": "t:sections.collection-callout.settings.cta_text1.label",
      "default": "Shop Jackets"
    },
    {
      "type": "url",
      "id": "cta_link1",
      "label": "t:sections.collection-callout.settings.cta_link1.label"
    },
    {
      "type": "text",
      "id": "cta_text2",
      "label": "t:sections.collection-callout.settings.cta_text2.label",
      "default": "Shop All Mens"
    },
    {
      "type": "url",
      "id": "cta_link2",
      "label": "t:sections.collection-callout.settings.cta_link2.label"
    },
    {
      "type": "select",
      "id": "layout",
      "label": "t:sections.collection-callout.settings.layout.label",
      "default": "right",
      "options": [
        {
          "value": "left",
          "label": "t:sections.collection-callout.settings.layout.options.left.label"
        },
        {
          "value": "right",
          "label": "t:sections.collection-callout.settings.layout.options.right.label"
        }
      ]
    },
    {
      "type": "checkbox",
      "id": "divider",
      "label": "t:sections.collection-callout.settings.divider.label",
      "default": false
    }
  ],
  "presets": [
    {
      "name": "t:sections.collection-callout.presets.collection_callout.name"
    }
  ],
  "disabled_on": {
    "groups": ["footer", "header", "custom.popups"]
  }
}
{% endschema %}
