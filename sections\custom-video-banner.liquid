{%- style -%}
  .custom-video-banner {
    position: relative;
    width: 100%;
    overflow: hidden;
    margin-bottom: {{ section.settings.bottom_spacing }}px;
  }

  .custom-video-banner--100vh {
    height: 100vh;
  }

  .custom-video-banner--750px {
    height: 750px;
  }

  .custom-video-banner--650px {
    height: 650px;
  }

  .custom-video-banner--550px {
    height: 550px;
  }

  .custom-video-banner--450px {
    height: 450px;
  }

  .custom-video-banner--16-9 {
    padding-bottom: 56.25%;
    height: 0;
  }

  @media only screen and (max-width: 768px) {
    .custom-video-banner--mobile--100vh {
      height: 100vh;
    }

    .custom-video-banner--mobile--500px {
      height: 500px;
    }

    .custom-video-banner--mobile--400px {
      height: 400px;
    }

    .custom-video-banner--mobile--300px {
      height: 300px;
    }

    .custom-video-banner--mobile--250px {
      height: 250px;
    }

    .custom-video-banner--mobile--auto {
      height: auto;
      min-height: 250px;
    }
  }

  .custom-video-banner__video-wrapper {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    overflow: hidden;
  }

  .custom-video-banner__video {
    position: absolute;
    top: 50%;
    left: 50%;
    min-width: 100%;
    min-height: 100%;
    width: auto;
    height: auto;
    transform: translate(-50%, -50%);
    object-fit: cover;
  }

  .custom-video-banner__overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: {{ section.settings.overlay_color }};
    opacity: {{ section.settings.overlay_opacity | divided_by: 100.0 }};
    z-index: 2;
  }

  .custom-video-banner__content {
    position: relative;
    z-index: 3;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
  }

  .custom-video-banner__content--vertical-top {
    align-items: flex-start;
    padding-top: 80px;
  }

  .custom-video-banner__content--vertical-center {
    align-items: center;
  }

  .custom-video-banner__content--vertical-bottom {
    align-items: flex-end;
    padding-bottom: 80px;
  }

  .custom-video-banner__content--horizontal-left {
    justify-content: flex-start;
    padding-left: {{ section.settings.text_horizontal_padding }}px;
  }

  .custom-video-banner__content--horizontal-center {
    justify-content: center;
  }

  .custom-video-banner__content--horizontal-right {
    justify-content: flex-end;
    padding-right: {{ section.settings.text_horizontal_padding }}px;
  }

  .custom-video-banner__text-container {
    max-width: {{ section.settings.text_max_width }}px;
    text-align: center;
    padding: 20px;
  }

  .custom-video-banner__content--horizontal-left .custom-video-banner__text-container {
    text-align: left;
  }

  .custom-video-banner__content--horizontal-right .custom-video-banner__text-container {
    text-align: right;
  }

  .custom-video-banner__title {
    margin: 0 0 20px;
    font-size: {{ section.settings.title_size }}px;
    font-weight: {{ section.settings.title_weight }};
    color: {{ section.settings.title_color }};
    text-transform: {{ section.settings.title_transform }};
    line-height: 1.2;
    font-family: {{ section.settings.title_font_family }};
    {% if section.settings.title_shadow %}
      text-shadow: 0 2px 10px rgba(0,0,0,0.5);
    {% endif %}
  }

  .custom-video-banner__subtitle {
    margin: 0 0 25px;
    font-size: {{ section.settings.subtitle_size }}px;
    font-weight: {{ section.settings.subtitle_weight }};
    color: {{ section.settings.subtitle_color }};
    line-height: 1.4;
    font-family: {{ section.settings.subtitle_font_family }};
  }

  .custom-video-banner__buttons {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 15px;
  }

  .custom-video-banner__content--horizontal-left .custom-video-banner__buttons {
    justify-content: flex-start;
  }

  .custom-video-banner__content--horizontal-right .custom-video-banner__buttons {
    justify-content: flex-end;
  }

  .custom-video-banner__button {
    display: inline-block;
    padding: {{ section.settings.button_padding }}px {{ section.settings.button_padding | times: 2 }}px;
    font-size: {{ section.settings.button_font_size }}px;
    font-weight: {{ section.settings.button_weight }};
    text-decoration: none;
    border-radius: {{ section.settings.button_radius }}px;
    transition: all 0.3s ease;
    cursor: pointer;
    min-width: 150px;
    text-align: center;
    font-family: {{ section.settings.button_font_family }};

    {% if section.settings.button_style == 'solid' %}
      background-color: {{ section.settings.button_bg_color }};
      color: {{ section.settings.button_text_color }};
      border: none;
    {% else %}
      background-color: transparent;
      color: {{ section.settings.button_bg_color }};
      border: 2px solid {{ section.settings.button_bg_color }};
    {% endif %}
  }

  .custom-video-banner__button:hover {
    {% if section.settings.button_style == 'solid' %}
      background-color: {{ section.settings.button_hover_bg_color }};
      color: {{ section.settings.button_hover_text_color }};
    {% else %}
      background-color: {{ section.settings.button_bg_color }};
      color: {{ section.settings.button_text_color }};
    {% endif %}
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(0,0,0,0.1);
  }

  .custom-video-banner__button--secondary {
    {% if section.settings.secondary_button_style == 'solid' %}
      background-color: {{ section.settings.secondary_button_bg_color }};
      color: {{ section.settings.secondary_button_text_color }};
      border: none;
    {% else %}
      background-color: transparent;
      color: {{ section.settings.secondary_button_bg_color }};
      border: 2px solid {{ section.settings.secondary_button_bg_color }};
    {% endif %}
  }

  .custom-video-banner__button--secondary:hover {
    {% if section.settings.secondary_button_style == 'solid' %}
      background-color: {{ section.settings.secondary_button_hover_bg_color }};
      color: {{ section.settings.secondary_button_hover_text_color }};
    {% else %}
      background-color: {{ section.settings.secondary_button_bg_color }};
      color: {{ section.settings.secondary_button_text_color }};
    {% endif %}
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(0,0,0,0.1);
  }

  @media only screen and (max-width: 768px) {
    .custom-video-banner__title {
      font-size: {{ section.settings.title_size | times: 0.5 }}px;
      margin-bottom: 15px;
    }

    .custom-video-banner__subtitle {
      font-size: {{ section.settings.subtitle_size | times: 0.5 }}px;
      margin-bottom: 20px;
    }

    .custom-video-banner__content--vertical-top {
      padding-top: 40px;
    }

    .custom-video-banner__content--vertical-bottom {
      padding-bottom: 40px;
    }

    .custom-video-banner__buttons {
      flex-direction: {% if section.settings.stack_buttons_on_mobile %}column{% else %}row{% endif %};
    }
  }

  /* 设备显示控制 */
  @media only screen and (max-width: 768px) {
    .custom-video-banner--hide-mobile {
      display: none !important;
    }
  }

  @media only screen and (min-width: 769px) and (max-width: 1024px) {
    .custom-video-banner--hide-tablet {
      display: none !important;
    }
  }

  @media only screen and (min-width: 1025px) {
    .custom-video-banner--hide-desktop {
      display: none !important;
    }
  }
{%- endstyle -%}

<div
  class="
    custom-video-banner
    custom-video-banner--{{ section.settings.section_height }}
    custom-video-banner--mobile--{{ section.settings.mobile_height }}
    {% if section.settings.hide_on_mobile %}custom-video-banner--hide-mobile{% endif %}
    {% if section.settings.hide_on_tablet %}custom-video-banner--hide-tablet{% endif %}
    {% if section.settings.hide_on_desktop %}custom-video-banner--hide-desktop{% endif %}
  "
  data-section-id="{{ section.id }}"
  data-section-type="custom-video-banner"
>
  <div class="custom-video-banner__video-wrapper">
    <video class="custom-video-banner__video" autoplay muted loop playsinline>
      <source src="{{ section.settings.video_url }}" type="video/mp4">
    </video>
  </div>

  {% if section.settings.overlay_opacity > 0 %}
    <div class="custom-video-banner__overlay"></div>
  {% endif %}

  {% assign vertical_alignment = section.settings.text_align | split: ' ' | first %}
  {% assign horizontal_alignment = section.settings.text_align | split: ' ' | last %}

  <div
    class="
      custom-video-banner__content
      custom-video-banner__content--{{ vertical_alignment }}
      custom-video-banner__content--{{ horizontal_alignment }}
    "
  >
    <div class="custom-video-banner__text-container">
      {% if section.settings.title != blank %}
        <h2 class="custom-video-banner__title">{{ section.settings.title }}</h2>
      {% endif %}

      {% if section.settings.subheading != blank %}
        <div class="custom-video-banner__subtitle">{{ section.settings.subheading }}</div>
      {% endif %}

      <div class="custom-video-banner__buttons">
        {% if section.settings.link_text != blank %}
          <a href="{{ section.settings.link | default: '#' }}" class="custom-video-banner__button">
            {{ section.settings.link_text }}
          </a>
        {% endif %}

        {% if section.settings.secondary_link_text != blank %}
          <a
            href="{{ section.settings.secondary_link | default: '#' }}"
            class="custom-video-banner__button custom-video-banner__button--secondary"
          >
            {{ section.settings.secondary_link_text }}
          </a>
        {% endif %}
      </div>
    </div>
  </div>
</div>

{% schema %}
{
  "name": "Custom Video Banner",
  "class": "index-section--hero",
  "settings": [
    {
      "type": "header",
      "content": "Video Settings"
    },
    {
      "type": "text",
      "id": "video_url",
      "label": "Video URL",
      "default": "https://cdn.shopify.com/videos/c/o/v/9910e33e66834553a74d1f72c12e3156.mp4",
      "info": "Enter the direct URL to the MP4 video file"
    },
    {
      "type": "header",
      "content": "Text Content"
    },
    {
      "type": "textarea",
      "id": "title",
      "label": "Heading",
      "default": "Your Brand\nComes To Life"
    },
    {
      "type": "font_picker",
      "id": "title_font_family",
      "label": "Heading font",
      "default": "helvetica_n4"
    },
    {
      "type": "range",
      "id": "title_size",
      "label": "Heading font size",
      "default": 80,
      "min": 40,
      "max": 120,
      "unit": "px"
    },
    {
      "type": "select",
      "id": "title_weight",
      "label": "Heading font weight",
      "default": "700",
      "options": [
        {
          "value": "400",
          "label": "Regular"
        },
        {
          "value": "500",
          "label": "Medium"
        },
        {
          "value": "600",
          "label": "Semibold"
        },
        {
          "value": "700",
          "label": "Bold"
        },
        {
          "value": "800",
          "label": "Extra Bold"
        }
      ]
    },
    {
      "type": "select",
      "id": "title_transform",
      "label": "Heading text transform",
      "default": "none",
      "options": [
        {
          "value": "none",
          "label": "None"
        },
        {
          "value": "uppercase",
          "label": "Uppercase"
        },
        {
          "value": "lowercase",
          "label": "Lowercase"
        },
        {
          "value": "capitalize",
          "label": "Capitalize"
        }
      ]
    },
    {
      "type": "color",
      "id": "title_color",
      "label": "Heading color",
      "default": "#ffffff"
    },
    {
      "type": "checkbox",
      "id": "title_shadow",
      "label": "Add text shadow to heading",
      "default": true
    },
    {
      "type": "text",
      "id": "subheading",
      "label": "Subheading",
      "default": "Stunning video banner with customizable options"
    },
    {
      "type": "font_picker",
      "id": "subtitle_font_family",
      "label": "Subheading font",
      "default": "helvetica_n4"
    },
    {
      "type": "range",
      "id": "subtitle_size",
      "label": "Subheading font size",
      "default": 24,
      "min": 16,
      "max": 48,
      "unit": "px"
    },
    {
      "type": "select",
      "id": "subtitle_weight",
      "label": "Subheading font weight",
      "default": "400",
      "options": [
        {
          "value": "400",
          "label": "Regular"
        },
        {
          "value": "500",
          "label": "Medium"
        },
        {
          "value": "600",
          "label": "Semibold"
        },
        {
          "value": "700",
          "label": "Bold"
        }
      ]
    },
    {
      "type": "color",
      "id": "subtitle_color",
      "label": "Subheading color",
      "default": "#ffffff"
    },
    {
      "type": "range",
      "id": "text_max_width",
      "label": "Text maximum width",
      "default": 650,
      "min": 400,
      "max": 1000,
      "step": 50,
      "unit": "px"
    },
    {
      "type": "header",
      "content": "Primary Button Settings"
    },
    {
      "type": "text",
      "id": "link_text",
      "label": "Button text",
      "default": "Explore Now"
    },
    {
      "type": "url",
      "id": "link",
      "label": "Button link",
      "default": "/"
    },
    {
      "type": "select",
      "id": "button_style",
      "label": "Button style",
      "default": "solid",
      "options": [
        {
          "value": "solid",
          "label": "Solid (filled)"
        },
        {
          "value": "outline",
          "label": "Outline (border only)"
        }
      ],
      "info": "Choose between a solid filled button or an outline button with transparent background"
    },
    {
      "type": "font_picker",
      "id": "button_font_family",
      "label": "Button font",
      "default": "helvetica_n4"
    },
    {
      "type": "range",
      "id": "button_font_size",
      "label": "Button font size",
      "default": 16,
      "min": 12,
      "max": 24,
      "unit": "px"
    },
    {
      "type": "color",
      "id": "button_bg_color",
      "label": "Button color",
      "default": "#4A90E2",
      "info": "For solid buttons, this is the background color. For outline buttons, this is the border and text color."
    },
    {
      "type": "color",
      "id": "button_text_color",
      "label": "Button text color",
      "default": "#FFFFFF",
      "info": "For solid buttons only. Outline buttons use the button color for text."
    },
    {
      "type": "color",
      "id": "button_hover_bg_color",
      "label": "Button hover color",
      "default": "#3A80D2",
      "info": "For solid buttons, this is the hover background color. For outline buttons, this is not used."
    },
    {
      "type": "color",
      "id": "button_hover_text_color",
      "label": "Button hover text color",
      "default": "#FFFFFF",
      "info": "For both solid and outline buttons on hover."
    },
    {
      "type": "header",
      "content": "Secondary Button Settings"
    },
    {
      "type": "text",
      "id": "secondary_link_text",
      "label": "Secondary button text",
      "default": "Learn More"
    },
    {
      "type": "url",
      "id": "secondary_link",
      "label": "Secondary button link",
      "default": "/"
    },
    {
      "type": "select",
      "id": "secondary_button_style",
      "label": "Secondary button style",
      "default": "outline",
      "options": [
        {
          "value": "solid",
          "label": "Solid (filled)"
        },
        {
          "value": "outline",
          "label": "Outline (border only)"
        }
      ],
      "info": "Choose between a solid filled button or an outline button with transparent background"
    },
    {
      "type": "color",
      "id": "secondary_button_bg_color",
      "label": "Secondary button color",
      "default": "#FFFFFF",
      "info": "For solid buttons, this is the background color. For outline buttons, this is the border and text color."
    },
    {
      "type": "color",
      "id": "secondary_button_text_color",
      "label": "Secondary button text color",
      "default": "#000000",
      "info": "For solid buttons only. Outline buttons use the button color for text."
    },
    {
      "type": "color",
      "id": "secondary_button_hover_bg_color",
      "label": "Secondary button hover color",
      "default": "#F5F5F5",
      "info": "For solid buttons, this is the hover background color. For outline buttons, this is not used."
    },
    {
      "type": "color",
      "id": "secondary_button_hover_text_color",
      "label": "Secondary button hover text color",
      "default": "#000000",
      "info": "For both solid and outline buttons on hover."
    },
    {
      "type": "checkbox",
      "id": "stack_buttons_on_mobile",
      "label": "Stack buttons on mobile",
      "default": false,
      "info": "Display buttons vertically on mobile devices"
    },
    {
      "type": "header",
      "content": "Button Style"
    },
    {
      "type": "range",
      "id": "button_radius",
      "label": "Button corner radius",
      "default": 5,
      "min": 0,
      "max": 30,
      "unit": "px"
    },
    {
      "type": "range",
      "id": "button_padding",
      "label": "Button padding",
      "default": 15,
      "min": 8,
      "max": 25,
      "unit": "px"
    },
    {
      "type": "select",
      "id": "button_weight",
      "label": "Button font weight",
      "default": "600",
      "options": [
        {
          "value": "400",
          "label": "Regular"
        },
        {
          "value": "500",
          "label": "Medium"
        },
        {
          "value": "600",
          "label": "Semibold"
        },
        {
          "value": "700",
          "label": "Bold"
        }
      ]
    },
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "select",
      "id": "text_align",
      "label": "Text alignment",
      "default": "vertical-center horizontal-center",
      "options": [
        {
          "value": "vertical-top horizontal-left",
          "label": "Top left"
        },
        {
          "value": "vertical-top horizontal-center",
          "label": "Top center"
        },
        {
          "value": "vertical-top horizontal-right",
          "label": "Top right"
        },
        {
          "value": "vertical-center horizontal-left",
          "label": "Center left"
        },
        {
          "value": "vertical-center horizontal-center",
          "label": "Center"
        },
        {
          "value": "vertical-center horizontal-right",
          "label": "Center right"
        },
        {
          "value": "vertical-bottom horizontal-left",
          "label": "Bottom left"
        },
        {
          "value": "vertical-bottom horizontal-center",
          "label": "Bottom center"
        },
        {
          "value": "vertical-bottom horizontal-right",
          "label": "Bottom right"
        }
      ]
    },
    {
      "type": "range",
      "id": "text_horizontal_padding",
      "label": "Horizontal padding",
      "default": 20,
      "min": 0,
      "max": 100,
      "step": 5,
      "unit": "px",
      "info": "Adds padding to the left or right of text (depending on alignment)"
    },
    {
      "type": "color",
      "id": "overlay_color",
      "label": "Overlay color",
      "default": "#000000"
    },
    {
      "type": "range",
      "id": "overlay_opacity",
      "label": "Overlay opacity",
      "info": "Darkens the video to improve text visibility",
      "default": 30,
      "min": 0,
      "max": 100,
      "step": 5,
      "unit": "%"
    },
    {
      "type": "select",
      "id": "section_height",
      "label": "Desktop height",
      "default": "100vh",
      "options": [
        {
          "label": "16:9 Ratio",
          "value": "16-9"
        },
        {
          "label": "450px",
          "value": "450px"
        },
        {
          "label": "550px",
          "value": "550px"
        },
        {
          "label": "650px",
          "value": "650px"
        },
        {
          "label": "750px",
          "value": "750px"
        },
        {
          "label": "Full screen height",
          "value": "100vh"
        }
      ]
    },
    {
      "type": "select",
      "id": "mobile_height",
      "label": "Mobile height",
      "default": "400px",
      "options": [
        {
          "label": "Auto",
          "value": "auto"
        },
        {
          "label": "250px",
          "value": "250px"
        },
        {
          "label": "300px",
          "value": "300px"
        },
        {
          "label": "400px",
          "value": "400px"
        },
        {
          "label": "500px",
          "value": "500px"
        },
        {
          "label": "Full screen height",
          "value": "100vh"
        }
      ]
    },
    {
      "type": "range",
      "id": "bottom_spacing",
      "label": "Bottom spacing",
      "default": 0,
      "min": 0,
      "max": 100,
      "step": 5,
      "unit": "px",
      "info": "Adds space between this section and the next one"
    },
    {
      "type": "header",
      "content": "Device Visibility"
    },
    {
      "type": "checkbox",
      "id": "hide_on_mobile",
      "label": "Hide on mobile devices",
      "default": false,
      "info": "Screen width less than 769px"
    },
    {
      "type": "checkbox",
      "id": "hide_on_tablet",
      "label": "Hide on tablet devices",
      "default": false,
      "info": "Screen width between 769px and 1024px"
    },
    {
      "type": "checkbox",
      "id": "hide_on_desktop",
      "label": "Hide on desktop devices",
      "default": false,
      "info": "Screen width greater than 1024px"
    }
  ],
  "presets": [
    {
      "name": "Custom Video Banner",
      "category": "Video"
    }
  ],
  "disabled_on": {
    "groups": ["footer", "header", "custom.popups"]
  }
}
{% endschema %}
