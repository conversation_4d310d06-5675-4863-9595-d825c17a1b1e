<header class="giftcard-header" role="banner">
  {%- liquid
    assign has_logo = section.settings.logo
  -%}

  {%- if has_logo -%}
    {%- style -%}
    .site-header__logo a {
      max-width: {{ section.settings.mobile_logo_width }}px;
    }
    @media only screen and (min-width: 769px) {
      .site-header__logo a {
        max-width: {{ section.settings.desktop_logo_width }}px;
      }
    }
    {%- endstyle -%}
  {%- endif -%}

  <h1 id="LogoContainer" class="site-header__logo{% unless has_logo %} text-center{% endunless %}" itemscope itemtype="http://schema.org/Organization">
    {%- if section.settings.logo -%}
      <a
        href="{{ routes.root_url }}"
        itemprop="url"
        class="site-header__logo-link">

        {% comment %} Desktop logo {% endcomment %}
        {%- assign width = section.settings.desktop_logo_width | times: 2 -%}
        {%- assign height = section.settings.desktop_logo_width | divided_by: section.settings.logo.aspect_ratio -%}
        {%- capture sizes -%}{{ section.settings.desktop_logo_width }}px{%- endcapture -%}
        {%- capture widths -%}{{ section.settings.desktop_logo_width }}, {{ section.settings.desktop_logo_width | times: 2 }}{%- endcapture -%}
        {%- capture style -%}max-height: {{ section.settings.desktop_logo_width | divided_by: section.settings.logo.aspect_ratio }}px;max-width: {{ section.settings.desktop_logo_width }}px;{%- endcapture -%}

        {% comment %} Mobile logo {% endcomment %}
        {%- assign mobile_width = section.settings.mobile_logo_width | times: 2 -%}
        {%- assign mobile_height = section.settings.mobile_logo_width | divided_by: section.settings.logo.aspect_ratio -%}
        {%- capture mobile_sizes -%}{{ section.settings.mobile_logo_width }}px{%- endcapture -%}
        {%- capture mobile_widths -%}{{ section.settings.mobile_logo_width }}, {{ section.settings.mobile_logo_width | times: 2 }}{%- endcapture -%}
        {%- capture mobile_style -%}max-height: {{ section.settings.mobile_logo_width | divided_by: section.settings.logo.aspect_ratio }}px;max-width: {{ section.settings.mobile_logo_width }}px;{%- endcapture -%}
        {%- render 'image-element',
          img: section.settings.logo,
          img_width: width,
          img_height: height,
          img_tag_width: section.settings.desktop_logo_width,
          sizes: sizes,
          widths: widths,
          style: style,
          classes: 'small--hide',
          loading: 'eager',
          alt: section.settings.logo.alt | default: shop.name,
          itemprop: 'logo',
        -%}
        {%- render 'image-element',
          img: section.settings.logo,
          img_width: mobile_width,
          img_height: mobile_height,
          img_tag_width: section.settings.mobile_logo_width,
          sizes: mobile_sizes,
          widths: mobile_widths,
          style: mobile_style,
          classes: 'medium-up--hide',
          loading: 'eager',
          alt: section.settings.logo.alt | default: shop.name,
        -%}
      </a>
    {%- else -%}
      <a href="{{ routes.root_url }}" itemprop="url">{{ shop.name }}</a>
    {%- endif -%}
  </h1>

  <div class="shop-url">{{ shop.url }}</div>
</header>

{% schema %}
{
  "name": "t:sections.giftcard-header.name",
  "settings": [
    {
      "type": "image_picker",
      "id": "logo",
      "label": "t:sections.giftcard-header.settings.logo.label"
    },
    {
      "type": "range",
      "id": "desktop_logo_width",
      "label": "t:sections.giftcard-header.settings.desktop_logo_width.label",
      "default": 200,
      "min": 100,
      "max": 400,
      "step": 10,
      "unit": "px"
    },
    {
      "type": "range",
      "id": "mobile_logo_width",
      "label": "t:sections.giftcard-header.settings.mobile_logo_width.label",
      "default": 140,
      "min": 60,
      "max": 200,
      "step": 10,
      "unit": "px",
      "info": "t:sections.giftcard-header.settings.mobile_logo_width.info"
    }
  ],
  "default": {
    "settings": {}
  }
}
{% endschema %}
