

<style {% if section.settings.section_preload == "false" %} class="gps-link gps-style" type="text/disabled-css" {% endif %}>.gps-572758889473246360.gps.gpsil [style*="--aspect:"]{aspect-ratio:var(--aspect)}.gps-572758889473246360.gps.gpsil [style*="--bg:"]{background:var(--bg)}.gps-572758889473246360.gps.gpsil [style*="--bga:"]{background-attachment:var(--bga)}.gps-572758889473246360.gps.gpsil [style*="--bgc:"]{background-color:var(--bgc)}.gps-572758889473246360.gps.gpsil [style*="--bgi:"]{background-image:var(--bgi)}.gps-572758889473246360.gps.gpsil [style*="--bgp:"]{background-position:var(--bgp)}.gps-572758889473246360.gps.gpsil [style*="--bgr:"]{background-repeat:var(--bgr)}.gps-572758889473246360.gps.gpsil [style*="--bgs:"]{background-size:var(--bgs)}.gps-572758889473246360.gps.gpsil [style*="--b:"]{border:var(--b)}.gps-572758889473246360.gps.gpsil [style*="--bc:"]{border-color:var(--bc)}.gps-572758889473246360.gps.gpsil [style*="--bs:"]{border-style:var(--bs)}.gps-572758889473246360.gps.gpsil [style*="--bw:"]{border-width:var(--bw)}.gps-572758889473246360.gps.gpsil [style*="--shadow:"]{box-shadow:var(--shadow)}.gps-572758889473246360.gps.gpsil [style*="--c:"]{color:var(--c)}.gps-572758889473246360.gps.gpsil [style*="--cg:"]{-moz-column-gap:var(--cg);column-gap:var(--cg)}.gps-572758889473246360.gps.gpsil [style*="--ff:"]{font-family:var(--ff)}.gps-572758889473246360.gps.gpsil [style*="--size:"]{font-size:var(--size)}.gps-572758889473246360.gps.gpsil [style*="--weight:"]{font-weight:var(--weight)}.gps-572758889473246360.gps.gpsil [style*="--fs:"]{font-style:var(--fs)}.gps-572758889473246360.gps.gpsil [style*="--gtc:"]{grid-template-columns:var(--gtc)}.gps-572758889473246360.gps.gpsil [style*="--h:"]{height:var(--h)}.gps-572758889473246360.gps.gpsil [style*="--jc:"]{justify-content:var(--jc)}.gps-572758889473246360.gps.gpsil [style*="--ls:"]{letter-spacing:var(--ls)}.gps-572758889473246360.gps.gpsil [style*="--lh:"]{line-height:var(--lh)}.gps-572758889473246360.gps.gpsil [style*="--m:"]{margin:var(--m)}.gps-572758889473246360.gps.gpsil [style*="--mb:"]{margin-bottom:var(--mb)}.gps-572758889473246360.gps.gpsil [style*="--mt:"]{margin-top:var(--mt)}.gps-572758889473246360.gps.gpsil [style*="--objf:"]{-o-object-fit:var(--objf);object-fit:var(--objf)}.gps-572758889473246360.gps.gpsil [style*="--op:"]{opacity:var(--op)}.gps-572758889473246360.gps.gpsil [style*="--o:"]{order:var(--o)}.gps-572758889473246360.gps.gpsil [style*="--pc:"]{place-content:var(--pc)}.gps-572758889473246360.gps.gpsil [style*="--p:"]{padding:var(--p)}.gps-572758889473246360.gps.gpsil [style*="--pb:"]{padding-bottom:var(--pb)}.gps-572758889473246360.gps.gpsil [style*="--pl:"]{padding-left:var(--pl)}.gps-572758889473246360.gps.gpsil [style*="--pr:"]{padding-right:var(--pr)}.gps-572758889473246360.gps.gpsil [style*="--pt:"]{padding-top:var(--pt)}.gps-572758889473246360.gps.gpsil [style*="--ta:"]{text-align:var(--ta)}.gps-572758889473246360.gps.gpsil [style*="--ts:"]{text-shadow:var(--ts)}.gps-572758889473246360.gps.gpsil [style*="--tt:"]{text-transform:var(--tt)}.gps-572758889473246360.gps.gpsil [style*="--t:"]{transform:var(--t)}.gps-572758889473246360.gps.gpsil [style*="--w:"]{width:var(--w)}.gps-572758889473246360.gps.gpsil [style*="--line-clamp:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp);display:-webkit-box;overflow:hidden}@media only screen and (max-width:1024px){.gps-572758889473246360.gps.gpsil [style*="--size-tablet:"]{font-size:var(--size-tablet)}.gps-572758889473246360.gps.gpsil [style*="--h-tablet:"]{height:var(--h-tablet)}.gps-572758889473246360.gps.gpsil [style*="--lh-tablet:"]{line-height:var(--lh-tablet)}.gps-572758889473246360.gps.gpsil [style*="--pb-tablet:"]{padding-bottom:var(--pb-tablet)}.gps-572758889473246360.gps.gpsil [style*="--pl-tablet:"]{padding-left:var(--pl-tablet)}.gps-572758889473246360.gps.gpsil [style*="--pr-tablet:"]{padding-right:var(--pr-tablet)}.gps-572758889473246360.gps.gpsil [style*="--pt-tablet:"]{padding-top:var(--pt-tablet)}.gps-572758889473246360.gps.gpsil [style*="--w-tablet:"]{width:var(--w-tablet)}.gps-572758889473246360.gps.gpsil [style*="--line-clamp-tablet:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-tablet);display:-webkit-box;overflow:hidden}}@media only screen and (max-width:767px){.gps-572758889473246360.gps.gpsil [style*="--cg-mobile:"]{-moz-column-gap:var(--cg-mobile);column-gap:var(--cg-mobile)}.gps-572758889473246360.gps.gpsil [style*="--size-mobile:"]{font-size:var(--size-mobile)}.gps-572758889473246360.gps.gpsil [style*="--gtc-mobile:"]{grid-template-columns:var(--gtc-mobile)}.gps-572758889473246360.gps.gpsil [style*="--h-mobile:"]{height:var(--h-mobile)}.gps-572758889473246360.gps.gpsil [style*="--lh-mobile:"]{line-height:var(--lh-mobile)}.gps-572758889473246360.gps.gpsil [style*="--mb-mobile:"]{margin-bottom:var(--mb-mobile)}.gps-572758889473246360.gps.gpsil [style*="--pl-mobile:"]{padding-left:var(--pl-mobile)}.gps-572758889473246360.gps.gpsil [style*="--pr-mobile:"]{padding-right:var(--pr-mobile)}.gps-572758889473246360.gps.gpsil [style*="--w-mobile:"]{width:var(--w-mobile)}.gps-572758889473246360.gps.gpsil [style*="--line-clamp-mobile:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-mobile);display:-webkit-box;overflow:hidden}}.gps-572758889473246360 .gp-g-paragraph-1{font-family:var(--g-p1-ff);font-size:var(--g-p1-size);font-style:var(--g-p1-fs);font-weight:var(--g-p1-weight);letter-spacing:var(--g-p1-ls);line-height:var(--g-p1-lh)}.gps-572758889473246360 .gp-relative{position:relative}.gps-572758889473246360 .gp-mx-auto{margin-left:auto;margin-right:auto}.gps-572758889473246360 .gp-mb-0{margin-bottom:0}.gps-572758889473246360 .gp-flex{display:flex}.gps-572758889473246360 .gp-grid{display:grid}.gps-572758889473246360 .gp-contents{display:contents}.gps-572758889473246360 .\!gp-hidden{display:none!important}.gps-572758889473246360 .gp-hidden{display:none}.gps-572758889473246360 .gp-h-auto{height:auto}.gps-572758889473246360 .gp-h-full{height:100%}.gps-572758889473246360 .gp-w-full{width:100%}.gps-572758889473246360 .gp-max-w-full{max-width:100%}.gps-572758889473246360 .gp-flex-none{flex:none}.gps-572758889473246360 .gp-grid-rows-\[1fr\]{grid-template-rows:1fr}.gps-572758889473246360 .gp-flex-col{flex-direction:column}.gps-572758889473246360 .gp-gap-y-0{row-gap:0}.gps-572758889473246360 .gp-text-g-text-1{color:var(--g-c-text-1)}.gps-572758889473246360 .gp-transition-colors{transition-duration:.15s;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-572758889473246360 .gp-duration-200{transition-duration:.2s}.gps-572758889473246360 .gp-ease-in-out{transition-timing-function:cubic-bezier(.4,0,.2,1)}@media (max-width:1024px){.gps-572758889473246360 .tablet\:\!gp-hidden{display:none!important}.gps-572758889473246360 .tablet\:gp-hidden{display:none}.gps-572758889473246360 .tablet\:gp-h-auto{height:auto}.gps-572758889473246360 .tablet\:gp-flex-none{flex:none}}@media (max-width:767px){.gps-572758889473246360 .mobile\:\!gp-hidden{display:none!important}.gps-572758889473246360 .mobile\:gp-hidden{display:none}.gps-572758889473246360 .mobile\:gp-h-auto{height:auto}.gps-572758889473246360 .mobile\:gp-flex-none{flex:none}}.gps-572758889473246360 .\[\&_\*\]\:gp-max-w-full *{max-width:100%}.gps-572758889473246360 .\[\&_p\]\:gp-inline p{display:inline}.gps-572758889473246360 .\[\&_p\]\:after\:gp-whitespace-pre-wrap p:after{content:var(--tw-content);white-space:pre-wrap}.gps-572758889473246360 .\[\&_p\]\:after\:gp-content-\[\'\\A\'\] p:after{--tw-content:"\A";content:var(--tw-content)}</style>

       
      
            <section
              class="gp-mx-auto gp-max-w-full {% if section.settings.section_preload == "false" %}gps-lazy {% endif %}"
              style="--w:100%;--w-tablet:100%;--w-mobile:100%;--pl:0px;--pl-tablet:0px;--pl-mobile:0px;--pr:0px;--pr-tablet:0px;--pr-mobile:0px"
            >
              
    <div
      id="gATKGC1wNW" data-id="gATKGC1wNW"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--mb:-50px;--pt:var(--g-s-4xl);--pl:15px;--pb:20px;--pr:15px;--pt-tablet:var(--g-s-4xl);--pl-tablet:15px;--pb-tablet:var(--g-s-4xl);--pr-tablet:15px;--cg:var(--g-s-2xl);--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:#FFFFFF;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gATKGC1wNW gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="g1UjJjCRnN gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      parentTag="Col" id="gvR6niMaGA" data-id="gvR6niMaGA"
        style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--mt:-60px;--mb:var(--g-s-l);--cg:32px;--pc:start;--gtc:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gvR6niMaGA gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gn4plaRDLy gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gn-5ZCFbGt">
    <div
      parentTag="Col"
        class="gn-5ZCFbGt "
        style="--tt:default;--ta:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-2xl);--mb-mobile:var(--g-s-2xl)"
      >
      <div class="gp-flex" style="--jc:center">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:center;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#575757;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggn-5ZCFbGt_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
       
      
    <div
      parentTag="Col" id="geAEarGlIA" data-id="geAEarGlIA"
        style="--bs:solid;--bw:1px 1px 1px 1px;--bc:#E0E0E0;--shadow:none;--d:none;--d-mobile:grid;--d-tablet:none;--op:100%;--mb:var(--g-s-l);--cg:auto;--cg-mobile:0px;--pc:start;--gtc:minmax(0, 4fr) minmax(0, 4fr) minmax(0, 4fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="geAEarGlIA gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gib3oXwsqs gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      parentTag="Col" id="gt8vq0ZdTO" data-id="gt8vq0ZdTO"
        style="--bs:solid;--bw:0px 0px 1px 0px;--bc:#E0E0E0;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pt:var(--g-s-xl);--pl:var(--g-s-xl);--pb:var(--g-s-xl);--pr:var(--g-s-xl);--cg:var(--g-s-2xl);--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gt8vq0ZdTO gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="g9vBa4M8el gp-relative gp-flex gp-flex-col"
    >
      <div
    
     data-id="gBC_1X-gu1"
    role="presentation"
    class="gp-group/image gBC_1X-gu1 gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-xl);--ta:center"
    >
      <div
        class="pointer-events-auto gp-h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="border-radius:inherit;--jc:center"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" data-srcSet="https://ucarecdn.com/b0e5a3fb-7f60-425b-af6f-60390e0f6431/-/format/auto/" srcset="https://ucarecdn.com/b0e5a3fb-7f60-425b-af6f-60390e0f6431/-/format/auto/" />
      <source media="(max-width: 1024px)" data-srcSet="https://ucarecdn.com/b0e5a3fb-7f60-425b-af6f-60390e0f6431/-/format/auto/" srcset="https://ucarecdn.com/b0e5a3fb-7f60-425b-af6f-60390e0f6431/-/format/auto/" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex"
        src="https://ucarecdn.com/b0e5a3fb-7f60-425b-af6f-60390e0f6431/-/format/auto/"
        data-src="https://ucarecdn.com/b0e5a3fb-7f60-425b-af6f-60390e0f6431/-/format/auto/"
        width="100%"
        alt="Alt Image"
        style="--aspect:auto;--objf:cover;--w:48px;--w-tablet:48px;--w-mobile:48px;--h:auto;--h-tablet:auto;--h-mobile:auto"
      />
    
  </picture>
      </div>
  </div>
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g92Wf0IUqV">
    <div
      parentTag="Col"
        class="g92Wf0IUqV "
        style="--ta:left;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-xl);--mb-mobile:14px"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--size:19px;--size-tablet:19px;--size-mobile:17px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--c:#242424;word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.gg92Wf0IUqV_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gMpSGbn00d">
    <div
      parentTag="Col"
        class="gMpSGbn00d "
        style="--tt:default;--ta:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%"
      >
      <div class="gp-flex" style="--jc:center">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text-g-text-1 gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:center;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#575757;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggMpSGbn00d_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="g98JzyZB_C gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      parentTag="Col" id="g8MzOEHvrz" data-id="g8MzOEHvrz"
        style="--bs:solid;--bw:0px 0px 0px 0px;--bc:#E0E0E0;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pt:var(--g-s-xl);--pl:var(--g-s-xl);--pb:var(--g-s-xl);--pr:var(--g-s-xl);--cg:var(--g-s-2xl);--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="g8MzOEHvrz gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gtRPgcf2ri gp-relative gp-flex gp-flex-col"
    >
      <div
    
     data-id="gaVKWd0Im1"
    role="presentation"
    class="gp-group/image gaVKWd0Im1 gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-xl);--ta:center"
    >
      <div
        class="pointer-events-auto gp-h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="border-radius:inherit;--jc:center"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" data-srcSet="{{ "gempages_572751041980793671-9efdb4ae-ae9c-4273-b60f-9e51f7f46673.png" | file_url }}" srcset="{{ "gempages_572751041980793671-9efdb4ae-ae9c-4273-b60f-9e51f7f46673.png" | file_url }}" />
      <source media="(max-width: 1024px)" data-srcSet="{{ "gempages_572751041980793671-9efdb4ae-ae9c-4273-b60f-9e51f7f46673.png" | file_url }}" srcset="{{ "gempages_572751041980793671-9efdb4ae-ae9c-4273-b60f-9e51f7f46673.png" | file_url }}" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex"
        src="{{ "gempages_572751041980793671-9efdb4ae-ae9c-4273-b60f-9e51f7f46673.png" | file_url }}"
        data-src="{{ "gempages_572751041980793671-9efdb4ae-ae9c-4273-b60f-9e51f7f46673.png" | file_url }}"
        width="100%"
        alt="Alt Image"
        style="--aspect:auto;--objf:cover;--w:47px;--w-tablet:47px;--w-mobile:47px;--h:auto;--h-tablet:auto;--h-mobile:auto"
      />
    
  </picture>
      </div>
  </div>
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g_MgH8ZY7H">
    <div
      parentTag="Col"
        class="g_MgH8ZY7H "
        style="--ta:left;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-xl);--mb-mobile:14px"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--size:19px;--size-tablet:19px;--size-mobile:17px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--c:#242424;word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.gg_MgH8ZY7H_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gQ6LCBD40o">
    <div
      parentTag="Col"
        class="gQ6LCBD40o "
        style="--tt:default;--ta:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%"
      >
      <div class="gp-flex" style="--jc:center">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text-g-text-1 gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:center;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#575757;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggQ6LCBD40o_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gWH0eSMPPa gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      parentTag="Col" id="gtbc_IUF4r" data-id="gtbc_IUF4r"
        style="--bs:solid;--bw:1px 0px 0px 0px;--bc:#E0E0E0;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pt:var(--g-s-xl);--pl:var(--g-s-xl);--pb:var(--g-s-xl);--pr:var(--g-s-xl);--cg:var(--g-s-2xl);--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gtbc_IUF4r gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gatn8Ace2H gp-relative gp-flex gp-flex-col"
    >
      <div
    
     data-id="gpSQks3szZ"
    role="presentation"
    class="gp-group/image gpSQks3szZ gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-xl);--ta:center"
    >
      <div
        class="pointer-events-auto gp-h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="border-radius:inherit;--jc:center"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" data-srcSet="https://ucarecdn.com/63d3ecb4-93bc-4201-8820-e6644eecd59b/-/format/auto/" srcset="https://ucarecdn.com/63d3ecb4-93bc-4201-8820-e6644eecd59b/-/format/auto/" />
      <source media="(max-width: 1024px)" data-srcSet="https://ucarecdn.com/63d3ecb4-93bc-4201-8820-e6644eecd59b/-/format/auto/" srcset="https://ucarecdn.com/63d3ecb4-93bc-4201-8820-e6644eecd59b/-/format/auto/" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex"
        src="https://ucarecdn.com/63d3ecb4-93bc-4201-8820-e6644eecd59b/-/format/auto/"
        data-src="https://ucarecdn.com/63d3ecb4-93bc-4201-8820-e6644eecd59b/-/format/auto/"
        width="100%"
        alt="Alt Image"
        style="--aspect:auto;--objf:cover;--w:48px;--w-tablet:48px;--w-mobile:48px;--h:auto;--h-tablet:auto;--h-mobile:auto"
      />
    
  </picture>
      </div>
  </div>
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g6aCMgZsx7">
    <div
      parentTag="Col"
        class="g6aCMgZsx7 "
        style="--ta:left;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-xl);--mb-mobile:14px"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--size:19px;--size-tablet:19px;--size-mobile:17px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--c:#242424;word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.gg6aCMgZsx7_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gDJNIhhcSR">
    <div
      parentTag="Col"
        class="gDJNIhhcSR "
        style="--tt:default;--ta:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%"
      >
      <div class="gp-flex" style="--jc:center">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text-g-text-1 gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:center;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#575757;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggDJNIhhcSR_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
    </div>
    </div>
   
    
    </div>
    </div>
  
            </section>
           
    


{% schema %}
  {
    
    "name": "Section 12",
    "tag": "section",
    "class": "gps-572758889473246360 gps gpsil",
    "settings": [{"type":"paragraph","content":"Section design by GemPages. [Click here to start design](https://admin.shopify.com/store/i0pixe-10/apps/gempages-cro/app/shopify/edit?pageType=GP_PRODUCT&editorId=572751048691811510&sectionId=572758889473246360)"},{"type":"select","label":"Section Preload","id":"section_preload","options":[{"label":"On","value":"true"},{"label":"Off","value":"false"},{"label":"Off Lazy Script","value":"off_lazy_script"}],"default":"false"},{"type":"text","label":"Checksum","id":"checksum"},{"type":"html","id":"ggn-5ZCFbGt_text","label":"ggn-5ZCFbGt_text","default":"<p>Experience our prestigious after-sales service.</p>"},{"type":"html","id":"gg92Wf0IUqV_text","label":"gg92Wf0IUqV_text","default":"<p>Free Shipping</p>"},{"type":"html","id":"ggMpSGbn00d_text","label":"ggMpSGbn00d_text","default":"<p>Free shipping on any order of $150&nbsp;</p><p>or more.</p>"},{"type":"html","id":"gg_MgH8ZY7H_text","label":"gg_MgH8ZY7H_text","default":"<p>Full Refund</p>"},{"type":"html","id":"ggQ6LCBD40o_text","label":"ggQ6LCBD40o_text","default":"<p>If your product aren’t perfect, return them for a full refund</p>"},{"type":"html","id":"gg6aCMgZsx7_text","label":"gg6aCMgZsx7_text","default":"<p>Secure Online Payment</p>"},{"type":"html","id":"ggDJNIhhcSR_text","label":"ggDJNIhhcSR_text","default":"<p>secure payment<br>worldwide</p>"}],
    "blocks": [{"type": "@app"}],
    "locales": {}
  }
{% endschema %}
