

<style {% if section.settings.section_preload == "false" %} class="gps-link gps-style" type="text/disabled-css" {% endif %}>.gps-572765319089619783.gps.gpsil [style*="--bg:"]{background:var(--bg)}.gps-572765319089619783.gps.gpsil [style*="--bga:"]{background-attachment:var(--bga)}.gps-572765319089619783.gps.gpsil [style*="--bgc:"]{background-color:var(--bgc)}.gps-572765319089619783.gps.gpsil [style*="--bgi:"]{background-image:var(--bgi)}.gps-572765319089619783.gps.gpsil [style*="--bgp:"]{background-position:var(--bgp)}.gps-572765319089619783.gps.gpsil [style*="--bgr:"]{background-repeat:var(--bgr)}.gps-572765319089619783.gps.gpsil [style*="--bgs:"]{background-size:var(--bgs)}.gps-572765319089619783.gps.gpsil [style*="--b:"]{border:var(--b)}.gps-572765319089619783.gps.gpsil [style*="--bc:"]{border-color:var(--bc)}.gps-572765319089619783.gps.gpsil [style*="--bs:"]{border-style:var(--bs)}.gps-572765319089619783.gps.gpsil [style*="--bw:"]{border-width:var(--bw)}.gps-572765319089619783.gps.gpsil [style*="--bottom:"]{bottom:var(--bottom)}.gps-572765319089619783.gps.gpsil [style*="--shadow:"]{box-shadow:var(--shadow)}.gps-572765319089619783.gps.gpsil [style*="--left:"]{left:var(--left)}.gps-572765319089619783.gps.gpsil [style*="--op:"]{opacity:var(--op)}.gps-572765319089619783.gps.gpsil [style*="--o:"]{order:var(--o)}.gps-572765319089619783.gps.gpsil [style*="--p:"]{padding:var(--p)}.gps-572765319089619783.gps.gpsil [style*="--pos:"]{position:var(--pos)}.gps-572765319089619783.gps.gpsil [style*="--top:"]{top:var(--top)}.gps-572765319089619783.gps.gpsil [style*="--t:"]{transform:var(--t)}.gps-572765319089619783.gps.gpsil [style*="--w:"]{width:var(--w)}@media only screen and (max-width:1024px){.gps-572765319089619783.gps.gpsil [style*="--bottom-tablet:"]{bottom:var(--bottom-tablet)}.gps-572765319089619783.gps.gpsil [style*="--left-tablet:"]{left:var(--left-tablet)}.gps-572765319089619783.gps.gpsil [style*="--pos-tablet:"]{position:var(--pos-tablet)}.gps-572765319089619783.gps.gpsil [style*="--top-tablet:"]{top:var(--top-tablet)}.gps-572765319089619783.gps.gpsil [style*="--t-tablet:"]{transform:var(--t-tablet)}.gps-572765319089619783.gps.gpsil [style*="--w-tablet:"]{width:var(--w-tablet)}}@media only screen and (max-width:767px){.gps-572765319089619783.gps.gpsil [style*="--bottom-mobile:"]{bottom:var(--bottom-mobile)}.gps-572765319089619783.gps.gpsil [style*="--left-mobile:"]{left:var(--left-mobile)}.gps-572765319089619783.gps.gpsil [style*="--pos-mobile:"]{position:var(--pos-mobile)}.gps-572765319089619783.gps.gpsil [style*="--top-mobile:"]{top:var(--top-mobile)}.gps-572765319089619783.gps.gpsil [style*="--t-mobile:"]{transform:var(--t-mobile)}.gps-572765319089619783.gps.gpsil [style*="--w-mobile:"]{width:var(--w-mobile)}}.gps-572765319089619783 .\!gp-pointer-events-none{pointer-events:none!important}.gps-572765319089619783 .gp-relative{position:relative}.gps-572765319089619783 .gp-sticky{position:sticky}.gps-572765319089619783 .gp-flex{display:flex}.gps-572765319089619783 .\!gp-hidden{display:none!important}.gps-572765319089619783 .gp-hidden{display:none}.gps-572765319089619783 .gp-flex-col{flex-direction:column}@media (max-width:1024px){.gps-572765319089619783 .tablet\:\!gp-hidden{display:none!important}.gps-572765319089619783 .tablet\:gp-hidden{display:none}}@media (max-width:767px){.gps-572765319089619783 .mobile\:\!gp-hidden{display:none!important}.gps-572765319089619783 .mobile\:gp-hidden{display:none}}.gps-572765319089619783 .\[\&_gp-button_a\]\:\!gp-pointer-events-auto gp-button a{pointer-events:auto!important}</style>

    
    <gp-sticky
    data-id="gQVZxAWehT"
      gp-data='{"uid":"gQVZxAWehT","setting":{"display":{"desktop":"always"}},"advanced":{"d":{"desktop":true,"tablet":true,"mobile":true}}}'
      id="gQVZxAWehT"
      data-id="gQVZxAWehT"
      class="gQVZxAWehT {% if section.settings.section_preload == "false" %}gps-lazy {% endif %}  !gp-pointer-events-none [&_gp-button_a]:!gp-pointer-events-auto"
      style="margin:0 auto;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--left:50%;--t:translateX(-50%);--left-tablet:50%;--t-tablet:translateX(-50%);--left-mobile:50%;--t-mobile:translateX(-50%);--top:auto;--bottom:0;--pos:fixed;--top-tablet:auto;--bottom-tablet:0;--pos-tablet:fixed;--top-mobile:auto;--bottom-mobile:0;--pos-mobile:fixed;--w:96%;--w-tablet:100%;--w-mobile:100%;z-index:100000"
    >
      <div 
         
        style="--bgc:#00000000;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
      >
        <div
      
      
      class="glb11ikLGB gp-relative gp-flex gp-flex-col"
    >
      
    </div>
      </div>
    </gp-sticky>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-sticky.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  


{% schema %}
  {
    
    "name": "Section 2",
    "tag": "section",
    "class": "gps-572765319089619783 gps gpsil",
    "settings": [{"type":"paragraph","content":"Section design by GemPages. [Click here to start design](https://admin.shopify.com/store/i0pixe-10/apps/gempages-cro/app/shopify/edit?pageType=GP_PRODUCT&editorId=572751048691811510&sectionId=572765319089619783)"},{"type":"select","label":"Section Preload","id":"section_preload","options":[{"label":"On","value":"true"},{"label":"Off","value":"false"},{"label":"Off Lazy Script","value":"off_lazy_script"}],"default":"false"},{"type":"text","label":"Checksum","id":"checksum"}],
    "blocks": [{"type": "@app"}],
    "locales": {}
  }
{% endschema %}
