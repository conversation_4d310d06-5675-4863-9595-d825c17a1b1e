

<style {% if section.settings.section_preload == "false" %} class="gps-link gps-style" type="text/disabled-css" {% endif %}>.gps-572875351168910208.gps.gpsil [style*="--bg:"]{background:var(--bg)}.gps-572875351168910208.gps.gpsil [style*="--bga:"]{background-attachment:var(--bga)}.gps-572875351168910208.gps.gpsil [style*="--bgc:"]{background-color:var(--bgc)}.gps-572875351168910208.gps.gpsil [style*="--bgi:"]{background-image:var(--bgi)}.gps-572875351168910208.gps.gpsil [style*="--bgp:"]{background-position:var(--bgp)}.gps-572875351168910208.gps.gpsil [style*="--bgr:"]{background-repeat:var(--bgr)}.gps-572875351168910208.gps.gpsil [style*="--bgs:"]{background-size:var(--bgs)}.gps-572875351168910208.gps.gpsil [style*="--b:"]{border:var(--b)}.gps-572875351168910208.gps.gpsil [style*="--bc:"]{border-color:var(--bc)}.gps-572875351168910208.gps.gpsil [style*="--bs:"]{border-style:var(--bs)}.gps-572875351168910208.gps.gpsil [style*="--bw:"]{border-width:var(--bw)}.gps-572875351168910208.gps.gpsil [style*="--shadow:"]{box-shadow:var(--shadow)}.gps-572875351168910208.gps.gpsil [style*="--c:"]{color:var(--c)}.gps-572875351168910208.gps.gpsil [style*="--cg:"]{-moz-column-gap:var(--cg);column-gap:var(--cg)}.gps-572875351168910208.gps.gpsil [style*="--gtc:"]{grid-template-columns:var(--gtc)}.gps-572875351168910208.gps.gpsil [style*="--jc:"]{justify-content:var(--jc)}.gps-572875351168910208.gps.gpsil [style*="--op:"]{opacity:var(--op)}.gps-572875351168910208.gps.gpsil [style*="--o:"]{order:var(--o)}.gps-572875351168910208.gps.gpsil [style*="--pc:"]{place-content:var(--pc)}.gps-572875351168910208.gps.gpsil [style*="--p:"]{padding:var(--p)}.gps-572875351168910208.gps.gpsil [style*="--pb:"]{padding-bottom:var(--pb)}.gps-572875351168910208.gps.gpsil [style*="--pl:"]{padding-left:var(--pl)}.gps-572875351168910208.gps.gpsil [style*="--pr:"]{padding-right:var(--pr)}.gps-572875351168910208.gps.gpsil [style*="--pt:"]{padding-top:var(--pt)}.gps-572875351168910208.gps.gpsil [style*="--w:"]{width:var(--w)}@media only screen and (max-width:1024px){.gps-572875351168910208.gps.gpsil [style*="--pb-tablet:"]{padding-bottom:var(--pb-tablet)}.gps-572875351168910208.gps.gpsil [style*="--pl-tablet:"]{padding-left:var(--pl-tablet)}.gps-572875351168910208.gps.gpsil [style*="--pr-tablet:"]{padding-right:var(--pr-tablet)}.gps-572875351168910208.gps.gpsil [style*="--pt-tablet:"]{padding-top:var(--pt-tablet)}.gps-572875351168910208.gps.gpsil [style*="--w-tablet:"]{width:var(--w-tablet)}}@media only screen and (max-width:767px){.gps-572875351168910208.gps.gpsil [style*="--pl-mobile:"]{padding-left:var(--pl-mobile)}.gps-572875351168910208.gps.gpsil [style*="--pr-mobile:"]{padding-right:var(--pr-mobile)}.gps-572875351168910208.gps.gpsil [style*="--w-mobile:"]{width:var(--w-mobile)}}.gps-572875351168910208 .gp-relative{position:relative}.gps-572875351168910208 .gp-mx-auto{margin-left:auto;margin-right:auto}.gps-572875351168910208 .gp-mb-0{margin-bottom:0}.gps-572875351168910208 .gp-flex{display:flex}.gps-572875351168910208 .gp-grid{display:grid}.gps-572875351168910208 .\!gp-hidden{display:none!important}.gps-572875351168910208 .gp-hidden{display:none}.gps-572875351168910208 .gp-max-w-full{max-width:100%}.gps-572875351168910208 .gp-grid-rows-\[1fr\]{grid-template-rows:1fr}.gps-572875351168910208 .gp-flex-col{flex-direction:column}.gps-572875351168910208 .gp-gap-y-0{row-gap:0}.gps-572875351168910208 .gp-transition-colors{transition-duration:.15s;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-572875351168910208 .gp-duration-200{transition-duration:.2s}.gps-572875351168910208 .gp-ease-in-out{transition-timing-function:cubic-bezier(.4,0,.2,1)}@media (max-width:1024px){.gps-572875351168910208 .tablet\:\!gp-hidden{display:none!important}.gps-572875351168910208 .tablet\:gp-hidden{display:none}}@media (max-width:767px){.gps-572875351168910208 .mobile\:\!gp-hidden{display:none!important}.gps-572875351168910208 .mobile\:gp-hidden{display:none}}.gps-572875351168910208 .\[\&_\*\]\:gp-max-w-full *{max-width:100%}</style>

       
      
            <section
              class="gp-mx-auto gp-max-w-full {% if section.settings.section_preload == "false" %}gps-lazy {% endif %}"
              style="--w:100%;--w-tablet:100%;--w-mobile:100%;--pl:0px;--pl-tablet:0px;--pl-mobile:0px;--pr:0px;--pr-tablet:0px;--pr-mobile:0px"
            >
              
    <div
      id="gQVJvNEyDM" data-id="gQVJvNEyDM"
        style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pt:var(--g-s-4xl);--pl:15px;--pb:var(--g-s-4xl);--pr:15px;--pt-tablet:var(--g-s-4xl);--pl-tablet:15px;--pb-tablet:var(--g-s-4xl);--pr-tablet:15px;--cg:32px;--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:#FFFFFF;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gQVJvNEyDM gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start;--d-mobile:none"
      class="gWYeUp356c gp-relative gp-flex gp-flex-col"
    >
      
    </div>
    </div>
  
            </section>
           
    


{% schema %}
  {
    
    "name": "Section 15",
    "tag": "section",
    "class": "gps-572875351168910208 gps gpsil",
    "settings": [{"type":"paragraph","content":"Section design by GemPages. [Click here to start design](https://admin.shopify.com/store/i0pixe-10/apps/gempages-cro/app/shopify/edit?pageType=GP_PRODUCT&editorId=572751048691811510&sectionId=572875351168910208)"},{"type":"select","label":"Section Preload","id":"section_preload","options":[{"label":"On","value":"true"},{"label":"Off","value":"false"},{"label":"Off Lazy Script","value":"off_lazy_script"}],"default":"false"},{"type":"text","label":"Checksum","id":"checksum"}],
    "blocks": [{"type": "@app"}],
    "locales": {}
  }
{% endschema %}
