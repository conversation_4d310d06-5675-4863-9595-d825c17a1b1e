

<style {% if section.settings.section_preload == "false" %} class="gps-link gps-style" type="text/disabled-css" {% endif %}>.gps-572876051869336391.gps.gpsil [style*="--ai:"]{align-items:var(--ai)}.gps-572876051869336391.gps.gpsil [style*="--aspect:"]{aspect-ratio:var(--aspect)}.gps-572876051869336391.gps.gpsil [style*="--bg:"]{background:var(--bg)}.gps-572876051869336391.gps.gpsil [style*="--hvr-bg:"]:hover{background:var(--hvr-bg)}.gps-572876051869336391.gps.gpsil [style*="--bga:"]{background-attachment:var(--bga)}.gps-572876051869336391.gps.gpsil [style*="--bgc:"]{background-color:var(--bgc)}.gps-572876051869336391.gps.gpsil [style*="--bgi:"]{background-image:var(--bgi)}.gps-572876051869336391.gps.gpsil [style*="--bgp:"]{background-position:var(--bgp)}.gps-572876051869336391.gps.gpsil [style*="--bgr:"]{background-repeat:var(--bgr)}.gps-572876051869336391.gps.gpsil [style*="--bgs:"]{background-size:var(--bgs)}.gps-572876051869336391.gps.gpsil [style*="--b:"]{border:var(--b)}.gps-572876051869336391.gps.gpsil [style*="--hvr-b:"]:hover{border:var(--hvr-b)}.gps-572876051869336391.gps.gpsil [style*="--bb:"]{border-bottom:var(--bb)}.gps-572876051869336391.gps.gpsil [style*="--hvr-bb:"]:hover{border-bottom:var(--hvr-bb)}.gps-572876051869336391.gps.gpsil [style*="--bc:"]{border-color:var(--bc)}.gps-572876051869336391.gps.gpsil [style*="--bblr:"]{border-bottom-left-radius:var(--bblr)}.gps-572876051869336391.gps.gpsil [style*="--hvr-bblr:"]:hover{border-bottom-left-radius:var(--hvr-bblr)}.gps-572876051869336391.gps.gpsil [style*="--bbrr:"]{border-bottom-right-radius:var(--bbrr)}.gps-572876051869336391.gps.gpsil [style*="--hvr-bbrr:"]:hover{border-bottom-right-radius:var(--hvr-bbrr)}.gps-572876051869336391.gps.gpsil [style*="--radius:"]{border-radius:var(--radius)}.gps-572876051869336391.gps.gpsil [style*="--bs:"]{border-style:var(--bs)}.gps-572876051869336391.gps.gpsil [style*="--bt:"]{border-top:var(--bt)}.gps-572876051869336391.gps.gpsil [style*="--hvr-bt:"]:hover{border-top:var(--hvr-bt)}.gps-572876051869336391.gps.gpsil [style*="--btlr:"]{border-top-left-radius:var(--btlr)}.gps-572876051869336391.gps.gpsil [style*="--hvr-btlr:"]:hover{border-top-left-radius:var(--hvr-btlr)}.gps-572876051869336391.gps.gpsil [style*="--btrr:"]{border-top-right-radius:var(--btrr)}.gps-572876051869336391.gps.gpsil [style*="--hvr-btrr:"]:hover{border-top-right-radius:var(--hvr-btrr)}.gps-572876051869336391.gps.gpsil [style*="--bw:"]{border-width:var(--bw)}.gps-572876051869336391.gps.gpsil [style*="--bottom:"]{bottom:var(--bottom)}.gps-572876051869336391.gps.gpsil [style*="--shadow:"]{box-shadow:var(--shadow)}.gps-572876051869336391.gps.gpsil [style*="--c:"]{color:var(--c)}.gps-572876051869336391.gps.gpsil [style*="--cg:"]{-moz-column-gap:var(--cg);column-gap:var(--cg)}.gps-572876051869336391.gps.gpsil [style*="--ff:"]{font-family:var(--ff)}.gps-572876051869336391.gps.gpsil [style*="--size:"]{font-size:var(--size)}.gps-572876051869336391.gps.gpsil [style*="--weight:"]{font-weight:var(--weight)}.gps-572876051869336391.gps.gpsil [style*="--fs:"]{font-style:var(--fs)}.gps-572876051869336391.gps.gpsil [style*="--gg:"]{grid-gap:var(--gg)}.gps-572876051869336391.gps.gpsil [style*="--gtc:"]{grid-template-columns:var(--gtc)}.gps-572876051869336391.gps.gpsil [style*="--h:"]{height:var(--h)}.gps-572876051869336391.gps.gpsil [style*="--jc:"]{justify-content:var(--jc)}.gps-572876051869336391.gps.gpsil [style*="--left:"]{left:var(--left)}.gps-572876051869336391.gps.gpsil [style*="--ls:"]{letter-spacing:var(--ls)}.gps-572876051869336391.gps.gpsil [style*="--lh:"]{line-height:var(--lh)}.gps-572876051869336391.gps.gpsil [style*="--tdt:"]{text-decoration-thickness:var(--tdt)}.gps-572876051869336391.gps.gpsil [style*="--tdc:"]{text-decoration-color:var(--tdc)}.gps-572876051869336391.gps.gpsil [style*="--m:"]{margin:var(--m)}.gps-572876051869336391.gps.gpsil [style*="--mb:"]{margin-bottom:var(--mb)}.gps-572876051869336391.gps.gpsil [style*="--mt:"]{margin-top:var(--mt)}.gps-572876051869336391.gps.gpsil [style*="--maxw:"]{max-width:var(--maxw)}.gps-572876051869336391.gps.gpsil [style*="--objf:"]{-o-object-fit:var(--objf);object-fit:var(--objf)}.gps-572876051869336391.gps.gpsil [style*="--op:"]{opacity:var(--op)}.gps-572876051869336391.gps.gpsil [style*="--o:"]{order:var(--o)}.gps-572876051869336391.gps.gpsil [style*="--pc:"]{place-content:var(--pc)}.gps-572876051869336391.gps.gpsil [style*="--p:"]{padding:var(--p)}.gps-572876051869336391.gps.gpsil [style*="--pb:"]{padding-bottom:var(--pb)}.gps-572876051869336391.gps.gpsil [style*="--pl:"]{padding-left:var(--pl)}.gps-572876051869336391.gps.gpsil [style*="--pr:"]{padding-right:var(--pr)}.gps-572876051869336391.gps.gpsil [style*="--pt:"]{padding-top:var(--pt)}.gps-572876051869336391.gps.gpsil [style*="--pos:"]{position:var(--pos)}.gps-572876051869336391.gps.gpsil [style*="--ta:"]{text-align:var(--ta)}.gps-572876051869336391.gps.gpsil [style*="--tt:"]{text-transform:var(--tt)}.gps-572876051869336391.gps.gpsil [style*="--top:"]{top:var(--top)}.gps-572876051869336391.gps.gpsil [style*="--t:"]{transform:var(--t)}.gps-572876051869336391.gps.gpsil [style*="--w:"]{width:var(--w)}.gps-572876051869336391.gps.gpsil [style*="--line-clamp:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp);display:-webkit-box;overflow:hidden}@media only screen and (max-width:1024px){.gps-572876051869336391.gps.gpsil [style*="--aspect-tablet:"]{aspect-ratio:var(--aspect-tablet)}.gps-572876051869336391.gps.gpsil [style*="--bottom-tablet:"]{bottom:var(--bottom-tablet)}.gps-572876051869336391.gps.gpsil [style*="--cg-tablet:"]{-moz-column-gap:var(--cg-tablet);column-gap:var(--cg-tablet)}.gps-572876051869336391.gps.gpsil [style*="--size-tablet:"]{font-size:var(--size-tablet)}.gps-572876051869336391.gps.gpsil [style*="--gtc-tablet:"]{grid-template-columns:var(--gtc-tablet)}.gps-572876051869336391.gps.gpsil [style*="--h-tablet:"]{height:var(--h-tablet)}.gps-572876051869336391.gps.gpsil [style*="--jc-tablet:"]{justify-content:var(--jc-tablet)}.gps-572876051869336391.gps.gpsil [style*="--left-tablet:"]{left:var(--left-tablet)}.gps-572876051869336391.gps.gpsil [style*="--lh-tablet:"]{line-height:var(--lh-tablet)}.gps-572876051869336391.gps.gpsil [style*="--maxw-tablet:"]{max-width:var(--maxw-tablet)}.gps-572876051869336391.gps.gpsil [style*="--o-tablet:"]{order:var(--o-tablet)}.gps-572876051869336391.gps.gpsil [style*="--pb-tablet:"]{padding-bottom:var(--pb-tablet)}.gps-572876051869336391.gps.gpsil [style*="--pl-tablet:"]{padding-left:var(--pl-tablet)}.gps-572876051869336391.gps.gpsil [style*="--pr-tablet:"]{padding-right:var(--pr-tablet)}.gps-572876051869336391.gps.gpsil [style*="--pt-tablet:"]{padding-top:var(--pt-tablet)}.gps-572876051869336391.gps.gpsil [style*="--pos-tablet:"]{position:var(--pos-tablet)}.gps-572876051869336391.gps.gpsil [style*="--top-tablet:"]{top:var(--top-tablet)}.gps-572876051869336391.gps.gpsil [style*="--t-tablet:"]{transform:var(--t-tablet)}.gps-572876051869336391.gps.gpsil [style*="--w-tablet:"]{width:var(--w-tablet)}.gps-572876051869336391.gps.gpsil [style*="--line-clamp-tablet:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-tablet);display:-webkit-box;overflow:hidden}}@media only screen and (max-width:767px){.gps-572876051869336391.gps.gpsil [style*="--aspect-mobile:"]{aspect-ratio:var(--aspect-mobile)}.gps-572876051869336391.gps.gpsil [style*="--bottom-mobile:"]{bottom:var(--bottom-mobile)}.gps-572876051869336391.gps.gpsil [style*="--size-mobile:"]{font-size:var(--size-mobile)}.gps-572876051869336391.gps.gpsil [style*="--gtc-mobile:"]{grid-template-columns:var(--gtc-mobile)}.gps-572876051869336391.gps.gpsil [style*="--h-mobile:"]{height:var(--h-mobile)}.gps-572876051869336391.gps.gpsil [style*="--jc-mobile:"]{justify-content:var(--jc-mobile)}.gps-572876051869336391.gps.gpsil [style*="--left-mobile:"]{left:var(--left-mobile)}.gps-572876051869336391.gps.gpsil [style*="--lh-mobile:"]{line-height:var(--lh-mobile)}.gps-572876051869336391.gps.gpsil [style*="--mb-mobile:"]{margin-bottom:var(--mb-mobile)}.gps-572876051869336391.gps.gpsil [style*="--mt-mobile:"]{margin-top:var(--mt-mobile)}.gps-572876051869336391.gps.gpsil [style*="--maxw-mobile:"]{max-width:var(--maxw-mobile)}.gps-572876051869336391.gps.gpsil [style*="--o-mobile:"]{order:var(--o-mobile)}.gps-572876051869336391.gps.gpsil [style*="--pc-mobile:"]{place-content:var(--pc-mobile)}.gps-572876051869336391.gps.gpsil [style*="--pb-mobile:"]{padding-bottom:var(--pb-mobile)}.gps-572876051869336391.gps.gpsil [style*="--pl-mobile:"]{padding-left:var(--pl-mobile)}.gps-572876051869336391.gps.gpsil [style*="--pr-mobile:"]{padding-right:var(--pr-mobile)}.gps-572876051869336391.gps.gpsil [style*="--pt-mobile:"]{padding-top:var(--pt-mobile)}.gps-572876051869336391.gps.gpsil [style*="--pos-mobile:"]{position:var(--pos-mobile)}.gps-572876051869336391.gps.gpsil [style*="--top-mobile:"]{top:var(--top-mobile)}.gps-572876051869336391.gps.gpsil [style*="--t-mobile:"]{transform:var(--t-mobile)}.gps-572876051869336391.gps.gpsil [style*="--w-mobile:"]{width:var(--w-mobile)}.gps-572876051869336391.gps.gpsil [style*="--line-clamp-mobile:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-mobile);display:-webkit-box;overflow:hidden}}.gps-572876051869336391 .-gp-translate-x-1\/2,.gps-572876051869336391 .-gp-translate-y-1\/2{--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1}.gps-572876051869336391 .gp-shadow-none{--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000}.gps-572876051869336391 .gp-g-s-small{padding:4px 16px}.gps-572876051869336391 .gp-invisible{visibility:hidden}.gps-572876051869336391 .gp-absolute{position:absolute}.gps-572876051869336391 .gp-relative{position:relative}.gps-572876051869336391 .gp-sticky{position:sticky}.gps-572876051869336391 .gp-inset-0{inset:0}.gps-572876051869336391 .gp-left-0{left:0}.gps-572876051869336391 .gp-left-1\/2{left:50%}.gps-572876051869336391 .gp-top-0{top:0}.gps-572876051869336391 .gp-top-1\/2{top:50%}.gps-572876051869336391 .gp-z-0{z-index:0}.gps-572876051869336391 .gp-z-1{z-index:1}.gps-572876051869336391 .gp-z-\[90\]{z-index:90}.gps-572876051869336391 .\!gp-m-0{margin:0!important}.gps-572876051869336391 .gp-mx-auto{margin-left:auto;margin-right:auto}.gps-572876051869336391 .gp-mb-0{margin-bottom:0}.gps-572876051869336391 .gp-block{display:block}.gps-572876051869336391 .gp-flex{display:flex}.gps-572876051869336391 .gp-inline-flex{display:inline-flex}.gps-572876051869336391 .gp-grid{display:grid}.gps-572876051869336391 .\!gp-hidden{display:none!important}.gps-572876051869336391 .gp-hidden{display:none}.gps-572876051869336391 .gp-aspect-\[56\/32\]{aspect-ratio:56/32}.gps-572876051869336391 .gp-aspect-square{aspect-ratio:1/1}.gps-572876051869336391 .gp-h-0{height:0}.gps-572876051869336391 .gp-h-5{height:20px}.gps-572876051869336391 .gp-h-auto{height:auto}.gps-572876051869336391 .gp-h-full{height:100%}.gps-572876051869336391 .\!gp-w-full{width:100%!important}.gps-572876051869336391 .gp-w-14{width:56px}.gps-572876051869336391 .gp-w-5{width:20px}.gps-572876051869336391 .gp-w-full{width:100%}.gps-572876051869336391 .gp-w-max{width:-moz-max-content;width:max-content}.gps-572876051869336391 .gp-min-w-\[45px\]{min-width:45px}.gps-572876051869336391 .gp-min-w-max{min-width:-moz-max-content;min-width:max-content}.gps-572876051869336391 .\!gp-max-w-full{max-width:100%!important}.gps-572876051869336391 .\!gp-max-w-max{max-width:-moz-max-content!important;max-width:max-content!important}.gps-572876051869336391 .gp-max-w-full{max-width:100%}.gps-572876051869336391 .gp-flex-1{flex:1 1 0%}.gps-572876051869336391 .gp-shrink-\[99999\]{flex-shrink:99999}.gps-572876051869336391 .-gp-translate-x-1\/2{--tw-translate-x:-50%}.gps-572876051869336391 .-gp-translate-x-1\/2,.gps-572876051869336391 .-gp-translate-y-1\/2{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.gps-572876051869336391 .-gp-translate-y-1\/2{--tw-translate-y:-50%}.gps-572876051869336391 .gp-cursor-default{cursor:default}.gps-572876051869336391 .gp-cursor-pointer{cursor:pointer}.gps-572876051869336391 .gp-appearance-none{-webkit-appearance:none;-moz-appearance:none;appearance:none}.gps-572876051869336391 .gp-grid-rows-\[1fr\]{grid-template-rows:1fr}.gps-572876051869336391 .gp-flex-col{flex-direction:column}.gps-572876051869336391 .gp-flex-wrap{flex-wrap:wrap}.gps-572876051869336391 .gp-items-start{align-items:flex-start}.gps-572876051869336391 .gp-items-end{align-items:flex-end}.gps-572876051869336391 .gp-items-center{align-items:center}.gps-572876051869336391 .gp-justify-center{justify-content:center}.gps-572876051869336391 .gp-gap-3{gap:12px}.gps-572876051869336391 .gp-gap-y-0{row-gap:0}.gps-572876051869336391 .gp-overflow-hidden{overflow:hidden}.gps-572876051869336391 .gp-break-words{overflow-wrap:break-word}.gps-572876051869336391 .\!gp-rounded-none{border-radius:0!important}.gps-572876051869336391 .gp-rounded{border-radius:4px}.gps-572876051869336391 .gp-rounded-none{border-radius:0}.gps-572876051869336391 .\!gp-border-x-0{border-left-width:0!important;border-right-width:0!important}.gps-572876051869336391 .gp-border-y{border-bottom-width:1px;border-top-width:1px}.gps-572876051869336391 .gp-border-g-line-1{border-color:var(--g-c-line-1)}.gps-572876051869336391 .gp-bg-black\/80{background-color:rgba(0,0,0,.8)}.gps-572876051869336391 .gp-bg-g-bg-2{background-color:var(--g-c-bg-2)}.gps-572876051869336391 .gp-bg-g-bg-3{background-color:var(--g-c-bg-3)}.gps-572876051869336391 .gp-bg-transparent{background-color:transparent}.gps-572876051869336391 .gp-object-cover{-o-object-fit:cover;object-fit:cover}.gps-572876051869336391 .gp-px-4{padding-left:16px;padding-right:16px}.gps-572876051869336391 .\!gp-pb-0{padding-bottom:0!important}.gps-572876051869336391 .gp-text-center{text-align:center}.gps-572876051869336391 .gp-text-g-text-2{color:var(--g-c-text-2)}.gps-572876051869336391 .gp-text-g-text-3{color:var(--g-c-text-3)}.gps-572876051869336391 .gp-text-white{--tw-text-opacity:1;color:rgb(255 255 255/var(--tw-text-opacity))}.gps-572876051869336391 .gp-line-through{text-decoration-line:line-through}.gps-572876051869336391 .gp-no-underline{text-decoration-line:none}.gps-572876051869336391 .gp-decoration-g-text-1{text-decoration-color:var(--g-c-text-1)}.gps-572876051869336391 .gp-opacity-25{opacity:.25}.gps-572876051869336391 .gp-opacity-30{opacity:.3}.gps-572876051869336391 .gp-opacity-75{opacity:.75}.gps-572876051869336391 .gp-shadow-none{--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)}.gps-572876051869336391 .gp-outline-none{outline:2px solid transparent;outline-offset:2px}.gps-572876051869336391 .gp-transition-all{transition-duration:.15s;transition-property:all;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-572876051869336391 .gp-transition-colors{transition-duration:.15s;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-572876051869336391 .gp-transition-opacity{transition-duration:.15s;transition-property:opacity;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-572876051869336391 .gp-duration-150{transition-duration:.15s}.gps-572876051869336391 .gp-duration-200{transition-duration:.2s}.gps-572876051869336391 .gp-duration-300{transition-duration:.3s}.gps-572876051869336391 .gp-ease-in-out{transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-572876051869336391 .disabled\:gp-btn-disabled:disabled{cursor:default}@media (hover:hover) and (pointer:fine){.gps-572876051869336391 .hover\:gp-bg-\[\#ef0800\]:hover{--tw-bg-opacity:1;background-color:rgb(239 8 0/var(--tw-bg-opacity))}.gps-572876051869336391 .hover\:gp-text-black:hover{--tw-text-opacity:1;color:rgb(0 0 0/var(--tw-text-opacity))}}.gps-572876051869336391 .disabled\:gp-pointer-events-none:disabled{pointer-events:none}.gps-572876051869336391 .disabled\:gp-cursor-not-allowed:disabled{cursor:not-allowed}.gps-572876051869336391 .disabled\:gp-opacity-30:disabled{opacity:.3}@media (hover:hover) and (pointer:fine){.gps-572876051869336391 .gp-group\/button:hover .group-hover\/button\:\!gp-text-inherit{color:inherit!important}}.gps-572876051869336391 .gp-group\/button:active .group-active\/button\:\!gp-text-inherit{color:inherit!important}.gps-572876051869336391 .data-\[disabled\=true\]\:gp-hidden[data-disabled=true],.gps-572876051869336391 .data-\[hidden\=true\]\:gp-hidden[data-hidden=true]{display:none}.gps-572876051869336391 .data-\[disabled\=true\]\:gp-opacity-60[data-disabled=true]{opacity:.6}.gps-572876051869336391 .gp-group\/button[data-state=loading] .group-data-\[state\=loading\]\/button\:gp-visible{visibility:visible}.gps-572876051869336391 .gp-group[data-state=loading] .group-data-\[state\=loading\]\:gp-invisible,.gps-572876051869336391 .gp-group\/button[data-state=loading] .group-data-\[state\=loading\]\/button\:gp-invisible{visibility:hidden}@keyframes gp-spin{to{transform:rotate(1turn)}}.gps-572876051869336391 .gp-group\/button[data-state=loading] .group-data-\[state\=loading\]\/button\:gp-animate-spin{animation:gp-spin 1s linear infinite}@media (max-width:1024px){.gps-572876051869336391 .tablet\:\!gp-hidden{display:none!important}.gps-572876051869336391 .tablet\:gp-hidden{display:none}}@media (max-width:767px){.gps-572876051869336391 .mobile\:\!gp-hidden{display:none!important}.gps-572876051869336391 .mobile\:gp-hidden{display:none}}.gps-572876051869336391 .\[\&_\*\]\:gp-max-w-full *{max-width:100%}.gps-572876051869336391 .\[\&_p\]\:gp-inline p{display:inline}.gps-572876051869336391 .\[\&_p\]\:gp-whitespace-pre-line p{white-space:pre-line}.gps-572876051869336391 .\[\&_p\]\:after\:gp-whitespace-pre-wrap p:after{content:var(--tw-content);white-space:pre-wrap}.gps-572876051869336391 .\[\&_p\]\:after\:gp-content-\[\'\\A\'\] p:after{--tw-content:"\A";content:var(--tw-content)}</style>

    
    <gp-sticky
    data-id="gdQnXuM4BG"
      gp-data='{"uid":"gdQnXuM4BG","setting":{"display":{"desktop":"after-first-cart-button"}},"advanced":{"d":{"desktop":true,"mobile":true,"tablet":true}}}'
      id="gdQnXuM4BG"
      data-id="gdQnXuM4BG"
      class="gdQnXuM4BG {% if section.settings.section_preload == "false" %}gps-lazy {% endif %}  "
      style="margin:0 auto;--bs:solid;--bw:0px;--bc:#121212;--shadow:0px 0px 10px 0px #0000001a;--d:none;--d-mobile:none;--d-tablet:none;--op:100%;--pt:var(--g-s-m);--pl:15px;--pb:var(--g-s-m);--pr:15px;--left:50%;--t:translateX(-50%);--left-tablet:50%;--t-tablet:translateX(-50%);--left-mobile:50%;--t-mobile:translateX(-50%);--top:auto;--bottom:0;--pos:fixed;--top-tablet:auto;--bottom-tablet:0;--pos-tablet:fixed;--top-mobile:auto;--bottom-mobile:0;--pos-mobile:fixed;--w:100%;--w-tablet:100%;--w-mobile:100%;z-index:100000"
    >
      <div 
         
        style="--bgc:#FFFFFF;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
      >
        <div
      
      
      class="g5Mmr6Tm3H gp-relative gp-flex gp-flex-col"
    >
      
    {%- assign gpBkProduct = product -%}
    
        {%- liquid
          if request.page_type == 'product'
            if 'static' == 'static'
              if 'latest' == 'latest'
                paginate collections.all.products by 100000
                  assign product = collections.all.products | sort: 'created_at' | reverse | first
                endpaginate
              else
                assign product = all_products['']
                assign productId = 'latest' | times: 1
                if product == empty or product == null
                  paginate collections.all.products by 100000
                    for item in collections.all.products
                      if item.id == productId
                        assign product = item
                      endif
                    endfor
                  endpaginate
                endif
              endif
            endif
          else
            if 'latest' == 'latest'
              paginate collections.all.products by 100000
                assign product = collections.all.products | sort: 'created_at'| reverse | first
              endpaginate
            else
              assign product = all_products['']
              assign productId = 'latest' | times: 1
              if product == empty or product == null
                paginate collections.all.products by 100000
                  for item in collections.all.products
                    if item.id == productId
                      assign product = item
                    endif
                  endfor
                endpaginate
              endif
            endif
          endif
        -%}
      

    {%-if product != empty and product != null -%}
      {%- assign initVariantId =  -%}
      {%- assign product_form_id = 'product-form-' | append: "gEsPdmZcWy" -%}
      {%- assign variant = product.selected_or_first_available_variant -%}
      {%- assign productSelectedVariant = product.selected_or_first_available_variant -%}
      {%-if productSelectedVariant == empty or productSelectedVariant == null -%}
        {%- assign productSelectedVariant = product.selected_or_first_available_variant -%}
      {%- endif -%}
      {%-if variant == empty or variant == null -%}
        {%- assign variant = product.selected_or_first_available_variant -%}
      {%- endif -%}
      <gp-product data-uid="gEsPdmZcWy" data-id="gEsPdmZcWy"   gp-context='{"productId": {{ product.id }}, "preSelectedOptionIds": [], "isSyncProduct": "true", "variantSelected": {{ variant | json | escape }}, "inventoryQuantity": {{ variant.inventory_quantity }}, "quantity": 1, "formId": "{{ product_form_id }}" }'
        gp-data='{"variantSelected": {{ variant | json | escape }}, "quantity": 1, "productUrl":{{ product.url | json | escape }}, "productHandle":{{ product.handle | json | escape }}, "collectionUrl": {{ collection.url | json | escape }}, "collectionHandle": {{ collection.handle | json | escape }}}'>
        <product-form class="product-form">
          {%- form 'product', product, id: product_form_id, class: 'form contents', data-type: 'add-to-cart-form', autocomplete: 'off'  -%}
            <input type="hidden" name="id" value="{{ variant.id }}" />
            <input type="hidden" name="quantity" value="{{ quantity }}" />
            <button type="submit" onclick="return false;" style="display:none;"></button>
            
       
      
    <div
      id="gEsPdmZcWy" data-id="gEsPdmZcWy-row"
        style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pt:0px;--pl:0px;--pb:0px;--pr:0px;--pt-mobile:0px;--pl-mobile:0px;--pb-mobile:0px;--pr-mobile:0px;--cg:40px;--cg-tablet:12px;--pc:start;--gtc:minmax(0, 5fr) minmax(0, 7fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:100%;--w-mobile:100%"
        class="gEsPdmZcWy gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:center"
      class="gMar5HPhUD gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      parentTag="Col" id="gM00Ly2eD3" data-id="gM00Ly2eD3"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--radius:var(--g-radius-small);--mt:auto;--mt-mobile:auto;--mb-mobile:var(--g-s-xl);--cg:16px;--pc:start;--gtc:minmax(0, 2fr) minmax(0, 10fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gM00Ly2eD3 gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--ai:normal;--jc:center;--o:0"
      class="g2k3elH2xC gp-relative gp-flex gp-flex-col"
    >
      
  {% assign featured_image = product.featured_image %}
  {%- if variant != null and variant.featured_image != null -%}
  {% assign featured_image = variant.featured_image %}
  {%- endif -%}
    <gp-product-images-v2
      gp-data='
    {
      "id":"gnOdeGiZka",
      "pageContext": {"pageType":"GP_PRODUCT","sectionName":"","isPreviewing":false,"isOptimizePlan":false,"isTranslateWithLocale":false,"isLazyLoadSection":true,"isLazyLoadImage":false,"hasCollectionHandle":true,"numberOfProductOfProductList":0,"pageBackground":"","fontType":"google","primaryLocale":"","enableLazyLoadImage":false},
      "setting":{"arrowIcon":"","borderActive":{"borderType":"none","borderWidth":"1px","color":"#000000","isCustom":"false","width":"1px 1px 1px 1px"},"clickOpenLightBox":{"desktop":false,"mobile":false,"tablet":false},"dragToScroll":true,"ftArrowIcon":"<svg width=\"100%\" height=\"100%\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n    <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M12 0C18.6274 0 24 5.37258 24 12C24 18.6274 18.6274 24 12 24C5.37258 24 0 18.6274 0 12C0 5.37258 5.37258 0 12 0ZM10.6828 8.13017C10.5266 7.95661 10.2734 7.95661 10.1172 8.13017C9.96095 8.30374 9.96095 8.58515 10.1172 8.75871L13.0343 12L10.1172 15.2413C9.96095 15.4149 9.96095 15.6963 10.1172 15.8698C10.2734 16.0434 10.5266 16.0434 10.6828 15.8698L13.8828 12.3143C14.0391 12.1407 14.0391 11.8593 13.8828 11.6857L10.6828 8.13017Z\" fill=\"currentColor\"/>\n    </svg>","ftArrowIconColor":"#000000","ftClickOpenLightBox":{"desktop":"none"},"ftDotActiveColor":{"desktop":"line-3"},"ftDotColor":{"desktop":"bg-1"},"ftDotGapToCarousel":{"desktop":16},"ftDotSize":{"desktop":12},"ftDotStyle":{"desktop":"none"},"ftNavigationPosition":{"desktop":"none","mobile":"none","tablet":"none"},"hoverEffect":"none","loop":{"desktop":true},"navigationPosition":{"desktop":"inside","mobile":"inside","tablet":"inside"},"otherImage":0,"pauseOnHover":true,"speed":1,"type":{"desktop":"images","mobile":"images","tablet":"images"},"typeDisplay":"all-images","zoom":1.5,"zoomType":"default"},
      "styles":{"align":{"desktop":"center","mobile":"center","tablet":"center"},"aspectHeight":{"desktop":0},"aspectWidth":{"desktop":0},"dotActiveColor":{"desktop":"brand"},"dotColor":{"desktop":"brand"},"ftAspectHeight":{"desktop":0},"ftAspectWidth":{"desktop":0},"ftLayout":{"desktop":"fill"},"ftShape":{"desktop":{"shape":"original","shapeLinked":true,"width":"56px"},"mobile":{"shape":"original","shapeLinked":true,"width":"56px"},"tablet":{"shape":"original","shapeLinked":true,"width":"56px"}},"height":{"desktop":"100px","mobile":"100px","tablet":"100px"},"itemSpacing":{"desktop":"10px"},"layout":{"desktop":"cover"},"position":{"desktop":"only-feature","mobile":"only-feature","tablet":"only-feature"},"ratioLayout":{"desktop":{},"mobile":{},"tablet":{}},"ratioLayoutRight":{"desktop":{},"mobile":{},"tablet":{}},"shape":{"desktop":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1","width":"100%"},"mobile":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1"},"tablet":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1"}},"shapeFor1Col":{"desktop":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1","width":"100%"},"mobile":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1"},"tablet":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1"}},"shapeFor2Col":{"desktop":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1","width":"50%"},"mobile":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1"},"tablet":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1"}},"shapeForBottom":{"desktop":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1","width":"20%"},"mobile":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1"},"tablet":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1"}},"shapeForFtOnly":{"desktop":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1"},"mobile":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1"},"tablet":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1"}},"spacing":{"desktop":"10px"},"verticalLayout":{"desktop":false}}, "productUrl":{{product.url | json | escape}}, "product":{{product | json | escape}}, "collectionUrl": {{ collection.url | json | escape }}, "collection": {{ collection | json | escape}}
    }
  '
      section-id="{{section.id}}"
      style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small)"
      data-id="gnOdeGiZka"
      class="gnOdeGiZka gp-overflow-hidden"
    >
      <div
        class="{%- if product.media.size > 1 -%} gp-grid gp-w-full !gp-m-0 gp-relative {%- else -%} gp-w-full !gp-m-0 gp-relative {%- endif -%}"
        {%- if product.media.size > 1 -%}
        style="--gtc:minmax(0, 12fr);--gtc-tablet:minmax(0, 12fr);--gtc-mobile:minmax(0, 12fr);--gg:10px"
        {%- else -%}
        style="--gtc:minmax(0, 12fr);--gg:10px"
        {%- endif -%}
      >
        
    {% capture featureImageOnlyOne %}
      
        {% assign featureMedia = variant.featured_media %}
        {% unless featureMedia %}
        {% assign featureMedia = product.featured_media %}
        {% endunless %}
        {% if product.media.size > 1 %}
          
          {% assign featureMedia = variant.featured_media %}
          {% unless featureMedia %}
          {% assign featureMedia = product.featured_media %}
          {% endunless %}
        
        {% endif %}
        {% assign largestRatio = 0 %}
        {% assign height = featureMedia.height | times: 1.0 %}
        {% assign width = featureMedia.width | times: 1.0 %}
        {% assign ratio = height | divided_by: width %}
        {% if ratio > largestRatio %}
          {% assign largestRatio = ratio %}
        {% endif %}
        {% assign productImageWidth = 0 %}
        {% case featureMedia.media_type %}
          {% when 'image' %}
            {% assign productImageWidth = featureMedia.width %}
          {% else %}
            {% assign productImageWidth = featureMedia.preview_image.width %}
        {% endcase %}
        {% if featureMedia == null %}
        {% assign productImageWidth = 1600 %}
        {% endif %}
        <div 
          class='gp-feature-image-carousel gp-feature-image-only' 
          style="--o:0;--o-tablet:0;--o-mobile:0;--d:flex;--d-mobile:flex;--d-tablet:flex;--jc:center;--jc-tablet:center;--jc-mobile:center"
        >
          <div 
            class="gp-relative"
            style="--w:56px;--w-tablet:56px;--w-mobile:56px"
          >
            
      
    
            <div 
              type="gp-feature-image-only"
              product-id="{{product.id}}"
              product-media="{{product.media.size}}"
              class="gp-image-item gp-ft-image-item gp-group gp-z-0 gp-flex !gp-max-w-full !gp-w-full gp-relative gp-items-start gp-justify-center gp-overflow-hidden gp-featured-image-wrapper"
              style="--h:auto;--h-tablet:auto;--h-mobile:auto;width:{{productImageWidth}}px"
            >
              <div 
                class="gp-w-full  {% if featureMedia == null or featureMedia.media_type == 'image' %} {{ 'gp-h-0' }} {% else %} {{ 'gp-h-full !gp-pb-0' }} {% endif %} gp-relative" 
                style="--pb: calc(({%if largestRatio == 0%} 100 / 100 {%else%} {{largestRatio}} {%endif%})*100%);--pb-tablet: calc(({%if largestRatio == 0%} 100 / 100 {%else%} {{largestRatio}} {%endif%})*100%);--pb-mobile: calc(({%if largestRatio == 0%} 100 / 100 {%else%} {{largestRatio}} {%endif%})*100%); {% if featureMedia.media_type == 'video' or featureMedia.media_type == 'external_video' %} {{ 'display: flex; align-items: center; justify-content: center' }} {% endif %}"
              >
                
    {% case featureMedia.media_type %}
      {% when 'image' %}
        
      
    {% assign src = featureMedia.src %}
    
    
  <img
      id="{%- if featureMedia != null -%}
              {{featureMedia.id}}
            {%- endif -%}"
      
      draggable="false"
      class="gp-w-full gp-h-full gp-absolute gp-top-0 gp-left-0 featured-image-only gp-cursor-pointer !gp-rounded-none gp-w-full gp-transition-opacity {{shouldHidden}}"
      data-src="{{src | image_url}}" data-srcset="{%- if src != null -%}
              {{src | img_url: '480x480'}} 480w, {{src | img_url: '768x768'}} 768w,{{src | img_url: '1024x1024'}} 1024w,{{src | img_url: '1440x1440'}} 1440w
            {%- else -%}
              {{ 'https://cdn.shopify.com/s/assets/no-image-2048-5e88c1b20e087fb7bbe9a3771824e743c244f437e4f8ba93bbf7b11b53f7824c_large.gif' }}
            {%- endif -%}" src="{{src | image_url}}" width="{{featureMedia.width}}" height="{{featureMedia.height}}" alt="{{featureMedia.alt}}" base-src="{{src | image_url}}"
      quality-type={}
      quality-percent={}
      data-sizes="auto"
      sizes="100vw"
      style="--aspect:{{featureMedia.width}}/{{featureMedia.height}};--aspect-tablet:{{featureMedia.width}}/{{featureMedia.height}};--aspect-mobile:{{featureMedia.width}}/{{featureMedia.height}};--objf:fill"
    />
  
      
      
    
      {% when 'external_video' %}
        {% assign mediaSourceVideo = featureMedia | external_video_url %}
        
    <iframe
      width="100%" height="100%"
      class="feature-video gp-w-full gp-h-full gp-relative"
      src="{{mediaSourceVideo}}"
      controls="true"
      allowfullscreen="true"
      allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture"
      frameborder="0"
      autoplay="false"
      alt="{{featureMedia.alt}}"
      style="--aspect:;--aspect-tablet:;--aspect-mobile:"
    >
    </iframe>
  
      {% when 'video' %}
        {% assign mediaSourceVideo = featureMedia.sources.last.url %}
        
  <gp-lite-html5-embed>
    
    <video
      id="gp-video-undefined"
      class=" gp-w-full lazy"
      style="width:100%;max-height:100%"
      controls
      
      
      
      title="{{featureMedia.alt}}"
      preload="none"
      data-src="{{mediaSourceVideo}}"
      src=""
      poster=""
      playsinline
    >
    
  </video>
    <div
      style="width:100%;max-height:100%"
      class="gp-absolute gp-top-0 gp-left-0  gp-w-full gp-thumbnail-video gp-hidden"
    >
      <img
        id="video-thumbnail"
        src=""
        class="gp-w-full gp-h-full gp-object-cover"
        alt="Video Thumbnail"
      ></img>
      <button
        type="button"
        class="gp-absolute gp-left-1/2 gp-top-1/2 gp-flex gp-aspect-[56/32] gp-w-14 -gp-translate-x-1/2 -gp-translate-y-1/2 gp-items-center gp-justify-center gp-rounded gp-bg-black/80 gp-transition-colors hover:gp-bg-[#ef0800]"
        aria-label="Play"
      >
        <svg class="gp-w-5 gp-text-white" viewBox="0 0 24 24">
          <path
            fill="currentColor"
            d="M5 5.274c0-1.707 1.826-2.792 3.325-1.977l12.362 6.726c1.566.853 1.566 3.101 0 3.953L8.325 20.702C6.826 21.518 5 20.432 5 18.726V5.274Z"
          />
        </svg>
      </button>
    </div>
    </gp-lite-html5-embed>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-lite-html5-embed.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
    <script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.3/dist/lazyload.min.js"></script>
    <script>
        if(lazyLoadInstance){lazyLoadInstance.update()}else{var lazyLoadInstance = new LazyLoad()}
    </script>
  
      {% when 'model' %}
        
    <model-viewer
      class="gp-w-full gp-h-full model-viewer gp-z-[90]"
      width="100%"
      
      
      
      
      src="{% if featureMedia.sources.first.url contains '.glb' %}{{ featureMedia.sources.first.url }}{% else %}{{featureMedia.sources.last.url}}{% endif %}"
      alt="{{featureMedia.preview_image.alt}}"
      camera-controls="true"
      poster="{{featureMedia.preview_image.src | product_img_url: '1024x1024'}}"
      data-js-focus-visible=""
      data-shopify-feature="1.12"
      ar-status="not-presenting"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1">
    </model-viewer>
  
      {% else %}
        
    
  <img
      
      
      draggable="false"
      class="gp-w-full gp-h-full gp-absolute gp-top-0 gp-left-0 featured-image-only gp-cursor-pointer !gp-rounded-none"
      data-src data-srcset="https://cdn.shopify.com/s/assets/no-image-2048-5e88c1b20e087fb7bbe9a3771824e743c244f437e4f8ba93bbf7b11b53f7824c_large.gif" src width="2237" height="1678" alt="No Image"
      quality-type={}
      quality-percent={}
      data-sizes="auto"
      sizes="100vw"
      style="--aspect:2237/1678;--aspect-tablet:2237/1678;--aspect-mobile:2237/1678;--objf:fill"
    />
  
    {% endcase %}
    
              </div>
            </div>
          </div>
        </div>
      
    {% endcapture %}
    {% if product.media.size > 1 %}
      
      {{ featureImageOnlyOne }}
    {% else %}
      {{ featureImageOnlyOne }}
    {% endif %}
  
         
      </div>
    </gp-product-images-v2>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-product-images-v2.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:center"
      class="gzwa_HZYts gp-relative gp-flex gp-flex-col"
    >
      <div gp-el-wrapper style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mb:var(--g-s-xs)" class="gSHch7589z ">
      
    {%- unless product -%}
      <p>{{ "gempages.ProductTitle.product_not_found" | t }}</p>
    {%- else -%}
      
        
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gSHch7589z">
    <div
      
        class="gSHch7589z "
        
      >
      <div  >
        <h1
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-product-title gp-text-g-text-2"
          style="--w:100%;--ta:left;--line-clamp-tablet:1;--line-clamp-mobile:1;--c:var(--g-c-text-2, text-2);--fs:normal;--ff:var(--g-font-heading, heading);--weight:400;--ls:normal;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;word-break:break-word;overflow:hidden"
        >{{ product.title }}</h1>
      </div>
    </div>
    </gp-text>
    
      
    {%- endunless -%}
  
      </div>
       
      
    <div
      parentTag="Col" id="gg7tQM-TJw" data-id="gg7tQM-TJw"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--radius:var(--g-radius-small);--cg:8px;--pc:start;--gtc:minmax(0, auto) minmax(0, auto) minmax(0, auto);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gg7tQM-TJw gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--ai:normal;--jc:center;--o:0"
      class="gWgvFM3GuT gp-relative gp-flex gp-flex-col"
    >
      
      <gp-product-price
        data-id="gPmxaAuqo2"
        class="gPmxaAuqo2 gp-product-price group-data-[state=loading]:gp-invisible data-[hidden=true]:gp-hidden"
        gp-data='{"priceType":"regular","uid":"gPmxaAuqo2","locale":"{{shop.locale}}","currency":"{{shop.currency}}","moneyFormat":"{{ shop.money_format | replace: '"', '\\"' | escape }}"}'
        
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small)"
        data-hidden="false"
      >
        
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="">
    <div
      id="p-price-gPmxaAuqo2"
        class=" "
        
      >
      <div  >
        <div
          type="regular"
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-price gp-money gp-decoration-g-text-1"
          style="--w:100%;--tdc:text-1;--tdt:1;--ta:left;--c:#242424;--fs:normal;--ff:var(--g-font-heading, heading);--weight:700;--ls:normal;--size:19px;--size-tablet:19px;--size-mobile:17px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;overflow:hidden"
        >
     {% if variant.price %} 
       {{ variant.price | money}}
     {% endif %}
   </div>
      </div>
    </div>
    </gp-text>
    
      </gp-product-price>
      <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-product-price.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:center"
      class="gm7tmKPYnD gp-relative gp-flex gp-flex-col"
    >
      
      <gp-product-price
        data-id="gnFh2ZA7yr"
        class="gnFh2ZA7yr gp-product-price group-data-[state=loading]:gp-invisible data-[hidden=true]:gp-hidden"
        gp-data='{"priceType":"compare","uid":"gnFh2ZA7yr","locale":"{{shop.locale}}","currency":"{{shop.currency}}","moneyFormat":"{{ shop.money_format | replace: '"', '\\"' | escape }}"}'
        
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small)"
        data-hidden="{% if variant.compare_at_price > variant.price and variant.compare_at_price >= 0 %}false{% else %}true{% endif %}"
      >
        
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="">
    <div
      id="p-price-gnFh2ZA7yr"
        class=" "
        
      >
      <div  >
        <div
          type="compare"
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-price gp-product-compare-price gp-line-through"
          style="--w:100%;--tdc:#B4B4B4;--tdt:1;--ta:left;--c:#B4B4B4;--fs:normal;--ff:var(--g-font-heading, heading);--weight:400;--ls:normal;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;word-break:break-word;overflow:hidden"
        >
      {% if variant.compare_at_price  %} 
        {{ variant.compare_at_price | money}}
      {% else %}
        
      {% endif %}
    </div>
      </div>
    </div>
    </gp-text>
    
      </gp-product-price>
      <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-product-price.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:center"
      class="g9ZVqVgqKp gp-relative gp-flex gp-flex-col"
    >
      <div gp-el-wrapper style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small)" class="gb9uLD_MnO ">
      
  {% liquid
    assign price = variant.price | times: 1.0
    assign salePrice = variant.compare_at_price | times: 1.0
    assign priceSave = salePrice | minus: price
    assign prefixVal = section.settings.ggb9uLD_MnO_customContent_prefix
    assign suffixVal = section.settings.ggb9uLD_MnO_customContent_suffix
    if salePrice == null or price == null
      assign pricePercentage = prefixVal | append: ' 0% ' | append: suffixVal
    else
         assign salePercent = priceSave | divided_by: salePrice | times: 100  | round
                assign pricePercentage = prefixVal | append: ' ' | append: salePercent | append: '% ' | append: suffixVal
              
    endif
  %}

  <gp-product-tag
  data-id="gb9uLD_MnO"
    data-disabled="{%- if priceSave > 0 -%} false {%- else -%} true {%- endif -%}"
    gp-data='{"setting":{"customContent":{"prefix":"-","suffix":"off","unit":"percentage"},"translate":"customContent"}, "id": "gb9uLD_MnO", "locale": "{{shop.locale}}", "currency": "{{shop.currency}}", "moneyFormat": "{{ shop.money_format | replace: '"', '\"' | escape }}"}'
    class="gp-block data-[disabled=true]:gp-hidden "
    style="--ta:left"
    price-save="{{priceSave}}"
    data-prefix="{{section.settings.ggb9uLD_MnO_customContent_prefix}}"
    data-suffix="{{section.settings.ggb9uLD_MnO_customContent_suffix}}"
  >
     <div class="gp-inline-flex gp-flex-wrap gp-items-end gp-gap-3" >
       <div
         class="gp-flex gp-items-center gp-w-full gp-h-full gp-bg-g-bg-2"
         style="--pl:16px;--pr:16px;--pt:16px;--pb:16px;--pl-tablet:16px;--pr-tablet:16px;--pt-tablet:16px;--pb-tablet:16px;--pl-mobile:16px;--pr-mobile:16px;--pt-mobile:16px;--pb-mobile:16px;--bs:none;--bw:0px;--bc:transparent;--c:#EA3335;--radius:var(--g-radius-small)"
       >
         
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="">
    <div
      id="p-tag-gb9uLD_MnO"
        class=" "
        
      >
      <div  >
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A']"
          style="--w:100%;--ta:left;--c:#EA3335;--fs:normal;--ff:var(--g-font-heading, heading);--weight:700;--ls:normal;--size:13px;--size-tablet:13px;--size-mobile:12px;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;word-break:break-word;overflow:hidden"
        >{{pricePercentage}}</div>
      </div>
    </div>
    </gp-text>
    
       </div>
     </div>

 </gp-product-tag>
 <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-product-tag.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
   
      </div>
    </div>
    </div>
   
    
    </div>
    </div>
   
    
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:center"
      class="gFQ19N4-gW gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      parentTag="Col" id="g0NBeFIiG8" data-id="g0NBeFIiG8"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--radius:var(--g-radius-small);--mt:0px;--cg:var(--g-s-3xl);--pc:start;--pc-mobile:center;--gtc:minmax(0, 5fr) minmax(0, 7fr);--gtc-tablet:minmax(0, 5fr) minmax(0, 7fr);--gtc-mobile:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="g0NBeFIiG8 gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--ai:normal;--jc:center;--jc-mobile:center;--o:0"
      class="gnzwqXy2jU gp-relative gp-flex gp-flex-col"
    >
      <div gp-el-wrapper style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:none;--d-tablet:block;--op:100%" class="gyRE4Mbq5E ">
      
    <gp-countdown-timer
      data-id="gyRE4Mbq5E"
     class="gp-flex "
     gp-data='{"behaviour":"evergreen","dateStandard":1************,"dayLabel":"Days","enableDay":true,"enableHour":true,"enableMinute":true,"enableSecond":true,"enableWeek":false,"hourLabel":"Hrs","minuteLabel":"Mins","redirectUrl":{"link":"#","target":"_blank"},"secondLabel":"Secs","timeZone":"UTC+7","timerDaily":{"format":"am","hours":24,"mins":30},"timerEverygreen":{"days":7,"endTime":1751606075384,"hours":3,"mins":0,"startTime":1684376018013},"translate":"dayLabel,hourLabel,minuteLabel,secondLabel,weekLabel","weekLabel":"Weeks","autoHideWhenTimeout":true,"uid":"gyRE4Mbq5E","builderData":{"advanced":{"border":{"desktop":{"normal":{"border":"none","borderType":"none","borderWidth":{},"color":"transparent","isCustom":false,"width":"0px"}}},"boxShadow":{"desktop":{"normal":{"angle":90,"blur":"4px","color":"rgba(18, 18, 18, 0.12)","distance":"2px","spread":"0px","type":"shadow-1"}}},"d":{"desktop":true,"mobile":false,"tablet":true},"hasBoxShadow":{"desktop":{"normal":false}},"op":{"desktop":"100%"},"rounded":{"desktop":{"normal":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"none"}}},"spacing-setting":{"desktop":{"margin":{},"padding":""},"mobile":{"margin":"","padding":""},"tablet":{"margin":"","padding":""}}},"label":"CountDown Timer","settings":{"behaviour":"evergreen","dateStandard":1************,"dayLabel":"Days","enableDay":true,"enableHour":true,"enableMinute":true,"enableSecond":true,"enableWeek":false,"hourLabel":"Hrs","minuteLabel":"Mins","redirectUrl":{"link":"#","target":"_blank"},"secondLabel":"Secs","timeZone":"UTC+7","timerDaily":{"format":"am","hours":24,"mins":30},"timerEverygreen":{"days":7,"endTime":1751606075384,"hours":3,"mins":0,"startTime":1684376018013},"translate":"dayLabel,hourLabel,minuteLabel,secondLabel,weekLabel","weekLabel":"Weeks","autoHideWhenTimeout":true},"styles":{"backgroundItemColor":"rgba(243, 243, 243, 0)","borderState":{"normal":{"border":"none","borderType":"none","color":"transparent","isCustom":false,"width":"0px"}},"colorLabel":"#B4B4B4","colorNumber":"text-2","horizontalGap":"0px","itemPadding":{"type":"small"},"labelTypo":{"attrs":{"color":"#B4B4B4"},"custom":{"fontSize":{"desktop":"13px","mobile":"12px","tablet":"13px"},"fontStyle":"normal","fontWeight":"400","letterSpacing":"normal","lineHeight":{"desktop":"150%","mobile":"150%","tablet":"150%"}},"type":"paragraph-1"},"labelTypography":{"custom":{"desktop":{"fontSize":"13px","fontStyle":"normal","fontWeight":"700","letterSpacing":"0px","lineHeight":"150%"},"mobile":{"fontSize":"16px","letterSpacing":"0px"},"tablet":{"fontSize":"16px","lineHeight":"150%"}}},"numTypo":{"attrs":{"color":"#242424"},"custom":{"fontSize":{"desktop":"23px","mobile":"20px","tablet":"23px"},"fontStyle":"normal","fontWeight":"700","letterSpacing":"normal","lineHeight":{"desktop":"130%","mobile":"130%","tablet":"130%"}},"type":"heading-2"},"numTypography":{"custom":{"desktop":{"fontSize":"28px","fontStyle":"normal","fontWeight":"600","letterSpacing":"0px","lineHeight":"130%"},"mobile":{"fontSize":"24px","fontWeight":"400","letterSpacing":"0px"},"tablet":{"fontSize":"19px","fontWeight":"600","letterSpacing":"0px","lineHeight":"130%"}}},"roundedState":{"normal":{"radiusType":"none"}},"textAlign":{"desktop":"left"},"verticalGap":"-5px"},"tag":"Countdown","uid":"gyRE4Mbq5E","childrens":[],"type":"component"}}'
     gp-href="#"
     style="--jc:left"
     >
      <div id="section-countdown" class="gp-flex gp-flex-wrap gp-w-max gp-relative" style="gap:0px;--jc:left">
        <a
            href="#"
            target="_blank"
            aria-label="Countdown link"
            class="gp-absolute gp-inset-0 gp-z-1"
          > </a>
          
          
    <div
        style="--bs:none;--bw:0px;--bc:transparent;--bg:rgba(243, 243, 243, 0)"
    class="gp-flex-1 gp-text-center gp-min-w-max !gp-max-w-max gp-g-s-small">
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gyRE4Mbq5E-text">
    <div
      id="day"
        class="gyRE4Mbq5E "
        style="--mb:-5px;--c:var(--g-c-text-2, text-2)"
      >
      <div  >
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-countdown-number"
          style="--w:100%;--fs:normal;--ff:var(--g-font-heading, heading);--weight:700;--ls:normal;--size:23px;--size-tablet:23px;--size-mobile:20px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--c:#242424;word-break:break-word;overflow:hidden"
        >00</div>
      </div>
    </div>
    </gp-text>
    
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gyRE4Mbq5E-text">
    <div
      
        class="gyRE4Mbq5E "
        style="--c:#B4B4B4"
      >
      <div  >
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A']"
          style="--w:100%;--fs:normal;--ff:var(--g-font-body, body);--weight:400;--ls:normal;--size:13px;--size-tablet:13px;--size-mobile:12px;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;--c:#B4B4B4;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggyRE4Mbq5E_dayLabel }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
    
          
    <div
        style="--bs:none;--bw:0px;--bc:transparent;--bg:rgba(243, 243, 243, 0)"
    class="gp-flex-1 gp-text-center gp-min-w-max !gp-max-w-max gp-g-s-small">
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gyRE4Mbq5E-text">
    <div
      id="hour"
        class="gyRE4Mbq5E "
        style="--mb:-5px;--c:var(--g-c-text-2, text-2)"
      >
      <div  >
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-countdown-number"
          style="--w:100%;--fs:normal;--ff:var(--g-font-heading, heading);--weight:700;--ls:normal;--size:23px;--size-tablet:23px;--size-mobile:20px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--c:#242424;word-break:break-word;overflow:hidden"
        >00</div>
      </div>
    </div>
    </gp-text>
    
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gyRE4Mbq5E-text">
    <div
      
        class="gyRE4Mbq5E "
        style="--c:#B4B4B4"
      >
      <div  >
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A']"
          style="--w:100%;--fs:normal;--ff:var(--g-font-body, body);--weight:400;--ls:normal;--size:13px;--size-tablet:13px;--size-mobile:12px;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;--c:#B4B4B4;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggyRE4Mbq5E_hourLabel }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
    
          
    <div
        style="--bs:none;--bw:0px;--bc:transparent;--bg:rgba(243, 243, 243, 0)"
    class="gp-flex-1 gp-text-center gp-min-w-max !gp-max-w-max gp-g-s-small">
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gyRE4Mbq5E-text">
    <div
      id="minute"
        class="gyRE4Mbq5E "
        style="--mb:-5px;--c:var(--g-c-text-2, text-2)"
      >
      <div  >
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-countdown-number"
          style="--w:100%;--fs:normal;--ff:var(--g-font-heading, heading);--weight:700;--ls:normal;--size:23px;--size-tablet:23px;--size-mobile:20px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--c:#242424;word-break:break-word;overflow:hidden"
        >00</div>
      </div>
    </div>
    </gp-text>
    
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gyRE4Mbq5E-text">
    <div
      
        class="gyRE4Mbq5E "
        style="--c:#B4B4B4"
      >
      <div  >
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A']"
          style="--w:100%;--fs:normal;--ff:var(--g-font-body, body);--weight:400;--ls:normal;--size:13px;--size-tablet:13px;--size-mobile:12px;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;--c:#B4B4B4;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggyRE4Mbq5E_minuteLabel }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
    
          
    <div
        style="--bs:none;--bw:0px;--bc:transparent;--bg:rgba(243, 243, 243, 0)"
    class="gp-flex-1 gp-text-center gp-min-w-max !gp-max-w-max gp-g-s-small">
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gyRE4Mbq5E-text">
    <div
      id="second"
        class="gyRE4Mbq5E "
        style="--mb:-5px;--c:var(--g-c-text-2, text-2)"
      >
      <div  >
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-countdown-number"
          style="--w:100%;--fs:normal;--ff:var(--g-font-heading, heading);--weight:700;--ls:normal;--size:23px;--size-tablet:23px;--size-mobile:20px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--c:#242424;word-break:break-word;overflow:hidden"
        >00</div>
      </div>
    </div>
    </gp-text>
    
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gyRE4Mbq5E-text">
    <div
      
        class="gyRE4Mbq5E "
        style="--c:#B4B4B4"
      >
      <div  >
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A']"
          style="--w:100%;--fs:normal;--ff:var(--g-font-body, body);--weight:400;--ls:normal;--size:13px;--size-tablet:13px;--size-mobile:12px;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;--c:#B4B4B4;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggyRE4Mbq5E_secondLabel }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
    
      </div>
     </gp-countdown-timer>
     <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-countdown.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
      </div>
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:center;--jc-mobile:center"
      class="gFVcq5wGSj gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      parentTag="Col" id="gn76H7_RHR" data-id="gn76H7_RHR"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--cg:var(--g-s-l);--pc:start;--gtc:minmax(0, 6fr) minmax(0, 6fr);--gtc-tablet:minmax(0, 6fr) minmax(0, 6fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gn76H7_RHR gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gNMq0nufhl gp-relative gp-flex gp-flex-col"
    >
      <div gp-el-wrapper style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small)" class="gFlBW8fUQx ">
      
    {%- liquid
      assign current_variant = product.selected_or_first_available_variant
      assign available = current_variant.available | default: false
    -%}
      <gp-product-quantity
        data-id="gFlBW8fUQx"
        data-disabled="{%- if available -%} false {%- else -%} true {%- endif -%}"
        data-price=""
        class="quantityClass gp-relative gp-inline-flex gp-w-full gp-bg-transparent gp-transition-all gp-duration-150 data-[disabled=true]:gp-opacity-60 "
        style="--h:50px;--jc:center"
      >

      
          <button
            title="Decrement"
            aria-label="decrement"
            {% if available == false %} disabled {% endif %}
            class="gp-border-g-line-1 gp-bg-g-bg-3 gp-minus gp-flex gp-aspect-square gp-h-full gp-cursor-pointer gp-items-center gp-justify-center gp-outline-none disabled:gp-cursor-not-allowed"
            style="--w:50px;--w-tablet:40px;--bg:var(--g-c-bg-3, bg-3);--c:#242424;--bs:solid;--bw:1px 1px 1px 1px;--bc:var(--g-c-line-1, line-1);--bblr:6px;--bbrr:auto;--btlr:6px;--btrr:auto;--hvr-bblr:6px;--hvr-btlr:6px"
          >
            <span class="gp-inline-flex gp-items-center gp-justify-center" style="--w:21px;--w-tablet:21px;--w-mobile:21px">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="gp-h-full gp-w-full"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 12H6" />
              </svg>
            </span>
          </button>
      
        <input
          type="text"
          name="product-quantity"
          {% if available == false %} disabled {% endif %}
          class="gp-border-g-line-1 gp-bg-g-bg-3 !gp-border-x-0 gp-px-4 gp-flex gp-shadow-none gp-appearance-none gp-items-center gp-border-y gp-text-center gp-outline-none gp-transition-all gp-duration-150 hover:gp-text-black disabled:gp-pointer-events-none gp-h-auto gp-rounded-none gp-shrink-[99999] gp-w-full gp-min-w-[45px]"
          style="--maxw:100%;--maxw-tablet:100%;--maxw-mobile:100%;--fs:normal;--ff:var(--g-font-body, body);--weight:400;--ls:normal;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;--c:#242424;--bg:var(--g-c-bg-3, bg-3);--bs:solid;--bw:1px 1px 1px 1px;--bc:var(--g-c-line-1, line-1)"
          aria-label="Quantity"
          inputmode="numeric"
          min="{{ current_variant.quantity_rule.min }}"
          step="{{ current_variant.quantity_rule.increment }}"
          value="1"
          autocomplete='off'
        />

        
          <button
            {% if available == false %} disabled {% endif %}
            aria-label="increment"
            title="Increment"
            class="gp-border-g-line-1 gp-bg-g-bg-3 gp-plus gp-flex gp-aspect-square gp-h-full gp-cursor-pointer gp-items-center gp-justify-center gp-outline-none gp-transition-all gp-duration-150 disabled:gp-pointer-events-none"
            style="--w:50px;--w-tablet:40px;--bg:var(--g-c-bg-3, bg-3);--c:#242424;--bs:solid;--bw:1px 1px 1px 1px;--bc:var(--g-c-line-1, line-1);--bblr:auto;--bbrr:6px;--btlr:auto;--btrr:6px;--hvr-bbrr:6px;--hvr-btrr:6px"
          >
            <span class="gp-inline-flex gp-items-center gp-justify-center" style="--w:21px;--w-tablet:21px;--w-mobile:21px">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="gp-h-full gp-w-full"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                />
              </svg>
            </span>
          </button>
        
      </gp-product-quantity>
      <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-product-quantity.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
      </div>
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gh_HqWbOx3 gp-relative gp-flex gp-flex-col"
    >
      
   {%- liquid
      assign inventory_quantity = variant.inventory_quantity | default: 0
    -%}
    <gp-product-button
      class="gp-product-button"
      gp-data-wrapper="true"
      gp-label-out-of-stock="{{section.settings.ggZnuDNHO5e_outOfStockLabel}}"
      gp-label-unavailable="{{section.settings.ggZnuDNHO5e_unavailableLabel}}"
      gp-data='{"setting":{"actionEffect":"open-cart-drawer","errorMessage":"Cannot add product to cart","successMessage":"Add product to cart successfully","enableSuccessMessage":true,"enableErrorMessage":true,"label":"Add to Cart","outOfStockLabel":"Out of stock","customURL":{}},"styles":{"errorTypo":{"attrs":{"color":"error"},"type":"paragraph-1"},"successTypo":{"attrs":{"color":"success"},"type":"paragraph-1"}},"disabled":"{{variant.available}}","variantID":"{{variant.id}}","totalVariant":"{{product.variants.size}}"}' 
       gp-href="{{ request.origin }}{{ routes.root_url | split: '/' | join: '/' }}/cart"
       data-variant-selection-required-message="{{}}"
    >
        
  <gp-button >
  <div
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--bblr:5px;--bbrr:5px;--btlr:5px;--btrr:5px;--ta:left"
    class="{% unless variant.available %} !gp-hidden {% endunless %}"
  >
    <style>
    .gZnuDNHO5e.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 0px;
  border-color: transparent;
  
      border-radius: var(--g-radius-medium);
    }

    .gZnuDNHO5e:hover::before {
      
      
    }

    .gZnuDNHO5e:hover .gp-button-icon {
      color: undefined;
    }

     .gZnuDNHO5e .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .gZnuDNHO5e:hover .gp-button-price {
      color: undefined;
    }

    .gZnuDNHO5e .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .gZnuDNHO5e .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .gZnuDNHO5e:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <button
      type="submit" data-id="gZnuDNHO5e" aria-label="Add to Cart"
      name="add" gp-data-hidden="{% unless variant.available %}true{% endunless %}"
      data-state="idle"
      class="gZnuDNHO5e gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-button-atc tcustomizer-submit-button"
      style="--hvr-bg:#424242;--bg:#242424;--radius:var(--g-radius-medium);--pl:24px;--pr:24px;--pt:12px;--pb:12px;--pl-tablet:24px;--pr-tablet:24px;--pt-tablet:12px;--pb-tablet:12px;--pl-mobile:24px;--pr-mobile:24px;--pt-mobile:12px;--pb-mobile:12px;--w:100%;--w-tablet:100%;--w-mobile:Auto;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--c:var(--g-c-text-3, text-3);--size:16px;--size-tablet:16px;--size-mobile:14px;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;--tt:capitalize"
    >
        <svg
          class="gp-invisible gp-absolute gp-h-5 gp-w-5 group-data-[state=loading]/button:gp-animate-spin group-data-[state=loading]/button:gp-visible"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            class="gp-opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            stroke-width="4"
          ></circle>
          <path
            class="gp-opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          ></path>
        </svg>
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--size:16px;--size-tablet:16px;--size-mobile:14px;--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.ggZnuDNHO5e_label }}
      </span>
      </div>
    
    </button>
  </div>
  </gp-button>

        
  <gp-button >
  <div
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--bblr:5px;--bbrr:5px;--btlr:5px;--btrr:5px;--ta:left"
    class="{% if variant.available %} !gp-hidden {% endif %}"
  >
    <style>
    .gZnuDNHO5e-sold-out.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 0px;
  border-color: transparent;
  
      border-radius: var(--g-radius-medium);
    }

    .gZnuDNHO5e-sold-out:hover::before {
      
      
    }

    .gZnuDNHO5e-sold-out:hover .gp-button-icon {
      color: undefined;
    }

     .gZnuDNHO5e-sold-out .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .gZnuDNHO5e-sold-out:hover .gp-button-price {
      color: undefined;
    }

    .gZnuDNHO5e-sold-out .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .gZnuDNHO5e-sold-out .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .gZnuDNHO5e-sold-out:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <button
      type="button" data-id="gZnuDNHO5e" aria-label="{{section.settings.ggZnuDNHO5e_outOfStockLabel}}"
      gp-data-hidden="{% if variant.available %}true{% endif %}"
      data-state="idle"
      class="gZnuDNHO5e-sold-out gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-button-sold-out btn-disabled gp-opacity-30 gp-cursor-default"
      style="--hvr-bg:#424242;--bg:#242424;--radius:var(--g-radius-medium);--pl:24px;--pr:24px;--pt:12px;--pb:12px;--pl-tablet:24px;--pr-tablet:24px;--pt-tablet:12px;--pb-tablet:12px;--pl-mobile:24px;--pr-mobile:24px;--pt-mobile:12px;--pb-mobile:12px;--w:100%;--w-tablet:100%;--w-mobile:Auto;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--c:var(--g-c-text-3, text-3);--size:16px;--size-tablet:16px;--size-mobile:14px;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;--tt:capitalize"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--size:16px;--size-tablet:16px;--size-mobile:14px;--c:var(--g-c-text-3, text-3)"
      >
        {{section.settings.ggZnuDNHO5e_outOfStockLabel}}
      </span>
      </div>
    
    </button>
  </div>
  </gp-button>

    </gp-product-button>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-product-button.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
    </div>
    </div>
   
    
    </div>
    </div>
   
    
    </div>
    </div>
   
    
          {%- endform -%}
        </product-form>
      </gp-product>
      {%- assign product = gpBkProduct -%}
    {% else %}
      <div class="gp-text-center">{{ "gempages.Product.product_not_found" | t }}</div>
    {%- endif -%}
     <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-product.v2.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
    </div>
      </div>
    </gp-sticky>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-sticky.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  


{% schema %}
  {
    
    "name": "Section 1",
    "tag": "section",
    "class": "gps-572876051869336391 gps gpsil",
    "settings": [{"type":"paragraph","content":"Section design by GemPages. [Click here to start design](https://admin.shopify.com/store/i0pixe-10/apps/gempages-cro/app/shopify/edit?pageType=GP_PRODUCT&editorId=572751048691811510&sectionId=572876051869336391)"},{"type":"select","label":"Section Preload","id":"section_preload","options":[{"label":"On","value":"true"},{"label":"Off","value":"false"},{"label":"Off Lazy Script","value":"off_lazy_script"}],"default":"false"},{"type":"text","label":"Checksum","id":"checksum"},{"type":"html","id":"ggb9uLD_MnO_customContent_prefix","label":"ggb9uLD_MnO_customContent_prefix","default":"-"},{"type":"html","id":"ggb9uLD_MnO_customContent_suffix","label":"ggb9uLD_MnO_customContent_suffix","default":"off"},{"type":"html","id":"ggyRE4Mbq5E_dayLabel","label":"ggyRE4Mbq5E_dayLabel","default":"Days"},{"type":"html","id":"ggyRE4Mbq5E_hourLabel","label":"ggyRE4Mbq5E_hourLabel","default":"Hrs"},{"type":"html","id":"ggyRE4Mbq5E_minuteLabel","label":"ggyRE4Mbq5E_minuteLabel","default":"Mins"},{"type":"html","id":"ggyRE4Mbq5E_secondLabel","label":"ggyRE4Mbq5E_secondLabel","default":"Secs"},{"type":"html","id":"ggyRE4Mbq5E_weekLabel","label":"ggyRE4Mbq5E_weekLabel","default":"Weeks"},{"type":"html","id":"ggZnuDNHO5e_label","label":"ggZnuDNHO5e_label","default":"Add to Cart"},{"type":"html","id":"ggZnuDNHO5e_outOfStockLabel","label":"ggZnuDNHO5e_outOfStockLabel","default":"Out of stock"},{"type":"html","id":"ggZnuDNHO5e_unavailableLabel","label":"ggZnuDNHO5e_unavailableLabel","default":"Unavailable"}],
    "blocks": [{"type": "@app"}],
    "locales": {}
  }
{% endschema %}
