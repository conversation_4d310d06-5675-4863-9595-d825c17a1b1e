

<style {% if section.settings.section_preload == "false" %} class="gps-link gps-style" type="text/disabled-css" {% endif %}>.gps-572876054906012544.gps.gpsil [style*="--aspect:"]{aspect-ratio:var(--aspect)}.gps-572876054906012544.gps.gpsil [style*="--bg:"]{background:var(--bg)}.gps-572876054906012544.gps.gpsil [style*="--bga:"]{background-attachment:var(--bga)}.gps-572876054906012544.gps.gpsil [style*="--bgc:"]{background-color:var(--bgc)}.gps-572876054906012544.gps.gpsil [style*="--bgi:"]{background-image:var(--bgi)}.gps-572876054906012544.gps.gpsil [style*="--bgp:"]{background-position:var(--bgp)}.gps-572876054906012544.gps.gpsil [style*="--bgr:"]{background-repeat:var(--bgr)}.gps-572876054906012544.gps.gpsil [style*="--bgs:"]{background-size:var(--bgs)}.gps-572876054906012544.gps.gpsil [style*="--b:"]{border:var(--b)}.gps-572876054906012544.gps.gpsil [style*="--bb:"]{border-bottom:var(--bb)}.gps-572876054906012544.gps.gpsil [style*="--bbw:"]{border-bottom-width:var(--bbw)}.gps-572876054906012544.gps.gpsil [style*="--bc:"]{border-color:var(--bc)}.gps-572876054906012544.gps.gpsil [style*="--bblr:"]{border-bottom-left-radius:var(--bblr)}.gps-572876054906012544.gps.gpsil [style*="--bbrr:"]{border-bottom-right-radius:var(--bbrr)}.gps-572876054906012544.gps.gpsil [style*="--bs:"]{border-style:var(--bs)}.gps-572876054906012544.gps.gpsil [style*="--bt:"]{border-top:var(--bt)}.gps-572876054906012544.gps.gpsil [style*="--btlr:"]{border-top-left-radius:var(--btlr)}.gps-572876054906012544.gps.gpsil [style*="--btrr:"]{border-top-right-radius:var(--btrr)}.gps-572876054906012544.gps.gpsil [style*="--bw:"]{border-width:var(--bw)}.gps-572876054906012544.gps.gpsil [style*="--shadow:"]{box-shadow:var(--shadow)}.gps-572876054906012544.gps.gpsil [style*="--c:"]{color:var(--c)}.gps-572876054906012544.gps.gpsil [style*="--cg:"]{-moz-column-gap:var(--cg);column-gap:var(--cg)}.gps-572876054906012544.gps.gpsil [style*="--ff:"]{font-family:var(--ff)}.gps-572876054906012544.gps.gpsil [style*="--size:"]{font-size:var(--size)}.gps-572876054906012544.gps.gpsil [style*="--weight:"]{font-weight:var(--weight)}.gps-572876054906012544.gps.gpsil [style*="--fs:"]{font-style:var(--fs)}.gps-572876054906012544.gps.gpsil [style*="--gtc:"]{grid-template-columns:var(--gtc)}.gps-572876054906012544.gps.gpsil [style*="--h:"]{height:var(--h)}.gps-572876054906012544.gps.gpsil [style*="--jc:"]{justify-content:var(--jc)}.gps-572876054906012544.gps.gpsil [style*="--left:"]{left:var(--left)}.gps-572876054906012544.gps.gpsil [style*="--ls:"]{letter-spacing:var(--ls)}.gps-572876054906012544.gps.gpsil [style*="--lh:"]{line-height:var(--lh)}.gps-572876054906012544.gps.gpsil [style*="--m:"]{margin:var(--m)}.gps-572876054906012544.gps.gpsil [style*="--mb:"]{margin-bottom:var(--mb)}.gps-572876054906012544.gps.gpsil [style*="--ml:"]{margin-left:var(--ml)}.gps-572876054906012544.gps.gpsil [style*="--mr:"]{margin-right:var(--mr)}.gps-572876054906012544.gps.gpsil [style*="--mt:"]{margin-top:var(--mt)}.gps-572876054906012544.gps.gpsil [style*="--minw:"]{min-width:var(--minw)}.gps-572876054906012544.gps.gpsil [style*="--objf:"]{-o-object-fit:var(--objf);object-fit:var(--objf)}.gps-572876054906012544.gps.gpsil [style*="--op:"]{opacity:var(--op)}.gps-572876054906012544.gps.gpsil [style*="--o:"]{order:var(--o)}.gps-572876054906012544.gps.gpsil [style*="--pc:"]{place-content:var(--pc)}.gps-572876054906012544.gps.gpsil [style*="--p:"]{padding:var(--p)}.gps-572876054906012544.gps.gpsil [style*="--pb:"]{padding-bottom:var(--pb)}.gps-572876054906012544.gps.gpsil [style*="--pl:"]{padding-left:var(--pl)}.gps-572876054906012544.gps.gpsil [style*="--pr:"]{padding-right:var(--pr)}.gps-572876054906012544.gps.gpsil [style*="--pt:"]{padding-top:var(--pt)}.gps-572876054906012544.gps.gpsil [style*="--pos:"]{position:var(--pos)}.gps-572876054906012544.gps.gpsil [style*="--ta:"]{text-align:var(--ta)}.gps-572876054906012544.gps.gpsil [style*="--tt:"]{text-transform:var(--tt)}.gps-572876054906012544.gps.gpsil [style*="--top:"]{top:var(--top)}.gps-572876054906012544.gps.gpsil [style*="--t:"]{transform:var(--t)}.gps-572876054906012544.gps.gpsil [style*="--w:"]{width:var(--w)}.gps-572876054906012544.gps.gpsil [style*="--z:"]{z-index:var(--z)}.gps-572876054906012544.gps.gpsil [style*="--line-clamp:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp);display:-webkit-box;overflow:hidden}@media only screen and (max-width:1024px){.gps-572876054906012544.gps.gpsil [style*="--bgi-tablet:"]{background-image:var(--bgi-tablet)}.gps-572876054906012544.gps.gpsil [style*="--bbw-tablet:"]{border-bottom-width:var(--bbw-tablet)}.gps-572876054906012544.gps.gpsil [style*="--size-tablet:"]{font-size:var(--size-tablet)}.gps-572876054906012544.gps.gpsil [style*="--h-tablet:"]{height:var(--h-tablet)}.gps-572876054906012544.gps.gpsil [style*="--lh-tablet:"]{line-height:var(--lh-tablet)}.gps-572876054906012544.gps.gpsil [style*="--mb-tablet:"]{margin-bottom:var(--mb-tablet)}.gps-572876054906012544.gps.gpsil [style*="--mt-tablet:"]{margin-top:var(--mt-tablet)}.gps-572876054906012544.gps.gpsil [style*="--pc-tablet:"]{place-content:var(--pc-tablet)}.gps-572876054906012544.gps.gpsil [style*="--pb-tablet:"]{padding-bottom:var(--pb-tablet)}.gps-572876054906012544.gps.gpsil [style*="--pl-tablet:"]{padding-left:var(--pl-tablet)}.gps-572876054906012544.gps.gpsil [style*="--pr-tablet:"]{padding-right:var(--pr-tablet)}.gps-572876054906012544.gps.gpsil [style*="--pt-tablet:"]{padding-top:var(--pt-tablet)}.gps-572876054906012544.gps.gpsil [style*="--pos-tablet:"]{position:var(--pos-tablet)}.gps-572876054906012544.gps.gpsil [style*="--top-tablet:"]{top:var(--top-tablet)}.gps-572876054906012544.gps.gpsil [style*="--w-tablet:"]{width:var(--w-tablet)}.gps-572876054906012544.gps.gpsil [style*="--line-clamp-tablet:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-tablet);display:-webkit-box;overflow:hidden}}@media only screen and (max-width:767px){.gps-572876054906012544.gps.gpsil [style*="--bgi-mobile:"]{background-image:var(--bgi-mobile)}.gps-572876054906012544.gps.gpsil [style*="--bbw-mobile:"]{border-bottom-width:var(--bbw-mobile)}.gps-572876054906012544.gps.gpsil [style*="--size-mobile:"]{font-size:var(--size-mobile)}.gps-572876054906012544.gps.gpsil [style*="--gtc-mobile:"]{grid-template-columns:var(--gtc-mobile)}.gps-572876054906012544.gps.gpsil [style*="--h-mobile:"]{height:var(--h-mobile)}.gps-572876054906012544.gps.gpsil [style*="--lh-mobile:"]{line-height:var(--lh-mobile)}.gps-572876054906012544.gps.gpsil [style*="--mb-mobile:"]{margin-bottom:var(--mb-mobile)}.gps-572876054906012544.gps.gpsil [style*="--mt-mobile:"]{margin-top:var(--mt-mobile)}.gps-572876054906012544.gps.gpsil [style*="--objf-mobile:"]{-o-object-fit:var(--objf-mobile);object-fit:var(--objf-mobile)}.gps-572876054906012544.gps.gpsil [style*="--pc-mobile:"]{place-content:var(--pc-mobile)}.gps-572876054906012544.gps.gpsil [style*="--pb-mobile:"]{padding-bottom:var(--pb-mobile)}.gps-572876054906012544.gps.gpsil [style*="--pl-mobile:"]{padding-left:var(--pl-mobile)}.gps-572876054906012544.gps.gpsil [style*="--pr-mobile:"]{padding-right:var(--pr-mobile)}.gps-572876054906012544.gps.gpsil [style*="--pt-mobile:"]{padding-top:var(--pt-mobile)}.gps-572876054906012544.gps.gpsil [style*="--pos-mobile:"]{position:var(--pos-mobile)}.gps-572876054906012544.gps.gpsil [style*="--top-mobile:"]{top:var(--top-mobile)}.gps-572876054906012544.gps.gpsil [style*="--w-mobile:"]{width:var(--w-mobile)}.gps-572876054906012544.gps.gpsil [style*="--line-clamp-mobile:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-mobile);display:-webkit-box;overflow:hidden}}.gps-572876054906012544 .group-hover\/hero\:gp-scale-\[var\(--scale\)\]{--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1}.gps-572876054906012544 .gp-g-paragraph-1{font-family:var(--g-p1-ff);font-size:var(--g-p1-size);font-style:var(--g-p1-fs);font-weight:var(--g-p1-weight);letter-spacing:var(--g-p1-ls);line-height:var(--g-p1-lh)}.gps-572876054906012544 .gp-absolute{position:absolute}.gps-572876054906012544 .gp-relative{position:relative}.gps-572876054906012544 .gp-inset-0{inset:0}.gps-572876054906012544 .gp-left-\[var\(--left\)\]{left:var(--left)}.gps-572876054906012544 .gp-top-\[var\(--top\)\]{top:var(--top)}.gps-572876054906012544 .gp-z-1{z-index:1}.gps-572876054906012544 .gp-mx-auto{margin-left:auto;margin-right:auto}.gps-572876054906012544 .gp-mb-0{margin-bottom:0}.gps-572876054906012544 .gp-block{display:block}.gps-572876054906012544 .gp-flex{display:flex}.gps-572876054906012544 .gp-inline-flex{display:inline-flex}.gps-572876054906012544 .gp-grid{display:grid}.gps-572876054906012544 .gp-contents{display:contents}.gps-572876054906012544 .\!gp-hidden{display:none!important}.gps-572876054906012544 .gp-hidden{display:none}.gps-572876054906012544 .gp-h-auto{height:auto}.gps-572876054906012544 .gp-h-full{height:100%}.gps-572876054906012544 .gp-w-full{width:100%}.gps-572876054906012544 .gp-max-w-full{max-width:100%}.gps-572876054906012544 .gp-flex-none{flex:none}.gps-572876054906012544 .gp-grid-rows-\[1fr\]{grid-template-rows:1fr}.gps-572876054906012544 .gp-flex-col{flex-direction:column}.gps-572876054906012544 .gp-items-center{align-items:center}.gps-572876054906012544 .gp-justify-start{justify-content:flex-start}.gps-572876054906012544 .\!gp-justify-center{justify-content:center!important}.gps-572876054906012544 .gp-justify-center{justify-content:center}.gps-572876054906012544 .gp-gap-y-0{row-gap:0}.gps-572876054906012544 .gp-overflow-hidden{overflow:hidden}.gps-572876054906012544 .gp-text-g-text-1{color:var(--g-c-text-1)}.gps-572876054906012544 .gp-text-g-text-2{color:var(--g-c-text-2)}.gps-572876054906012544 .gp-transition-all{transition-duration:.15s;transition-property:all;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-572876054906012544 .gp-transition-colors{transition-duration:.15s;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-572876054906012544 .gp-transition-transform{transition-duration:.15s;transition-property:transform;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-572876054906012544 .gp-duration-200{transition-duration:.2s}.gps-572876054906012544 .gp-duration-300{transition-duration:.3s}.gps-572876054906012544 .gp-duration-\[var\(--duration\)\]{transition-duration:var(--duration)}.gps-572876054906012544 .gp-ease-in-out{transition-timing-function:cubic-bezier(.4,0,.2,1)}@media (hover:hover) and (pointer:fine){.gps-572876054906012544 .gp-group\/hero:hover .group-hover\/hero\:gp-scale-\[var\(--scale\)\]{--tw-scale-x:var(--scale);--tw-scale-y:var(--scale);transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.gps-572876054906012544 .gp-group\/hero:hover .group-hover\/hero\:gp-bg-\[color\:var\(--hvr-bgc\2c _var\(--bgc\)\)\]{background-color:var(--hvr-bgc,var(--bgc))}.gps-572876054906012544 .gp-group\/hero:hover .group-hover\/hero\:gp-opacity-\[var\(--hvr-op\2c _var\(--op\)\)\]{opacity:var(--hvr-op,var(--op))}}@media (max-width:1024px){.gps-572876054906012544 .tablet\:gp-relative{position:relative}.gps-572876054906012544 .tablet\:gp-block{display:block}.gps-572876054906012544 .tablet\:\!gp-hidden{display:none!important}.gps-572876054906012544 .tablet\:gp-hidden{display:none}.gps-572876054906012544 .tablet\:gp-h-auto{height:auto}.gps-572876054906012544 .tablet\:gp-flex-none{flex:none}.gps-572876054906012544 .tablet\:\!gp-content-stretch{align-content:stretch!important}.gps-572876054906012544 .tablet\:\!gp-justify-center{justify-content:center!important}}@media (max-width:767px){.gps-572876054906012544 .mobile\:gp-relative{position:relative}.gps-572876054906012544 .mobile\:gp-block{display:block}.gps-572876054906012544 .mobile\:\!gp-hidden{display:none!important}.gps-572876054906012544 .mobile\:gp-hidden{display:none}.gps-572876054906012544 .mobile\:gp-h-auto{height:auto}.gps-572876054906012544 .mobile\:gp-flex-none{flex:none}.gps-572876054906012544 .mobile\:\!gp-content-stretch{align-content:stretch!important}.gps-572876054906012544 .mobile\:\!gp-justify-center{justify-content:center!important}}@media (max-width:1024px){.gps-572876054906012544 .tablet\:\[\&\>\*\]\:\!gp-justify-center>*{justify-content:center!important}}@media (max-width:767px){.gps-572876054906012544 .mobile\:\[\&\>\*\]\:\!gp-justify-center>*{justify-content:center!important}}.gps-572876054906012544 .\[\&_\*\]\:gp-max-w-full *{max-width:100%}.gps-572876054906012544 .\[\&_p\]\:gp-inline p{display:inline}.gps-572876054906012544 .\[\&_p\]\:after\:gp-whitespace-pre-wrap p:after{content:var(--tw-content);white-space:pre-wrap}.gps-572876054906012544 .\[\&_p\]\:after\:gp-content-\[\'\\A\'\] p:after{--tw-content:"\A";content:var(--tw-content)}</style>

       
      
            <section
              class="gp-mx-auto gp-max-w-full {% if section.settings.section_preload == "false" %}gps-lazy {% endif %}"
              style="--w:100%;--w-tablet:100%;--w-mobile:100%;--pl:0px;--pl-tablet:0px;--pl-mobile:0px;--pr:0px;--pr-tablet:0px;--pr-mobile:0px"
            >
              
    <div
      id="gCECZnEfJY" data-id="gCECZnEfJY"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--mt:-50px;--pt:20px;--pl:15px;--pb:var(--g-s-4xl);--pr:15px;--pt-tablet:var(--g-s-4xl);--pl-tablet:15px;--pb-tablet:var(--g-s-4xl);--pr-tablet:15px;--cg:var(--g-s-2xl);--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:#FFFFFF;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gCECZnEfJY gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gcnktU1ilQ gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      parentTag="Col" id="gXGy-QIYli" data-id="gXGy-QIYli"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:none;--d-tablet:grid;--op:100%;--pt:0px;--cg:var(--g-s-2xl);--pc:start;--gtc:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gXGy-QIYli gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="g0hR6Eb9L- gp-relative gp-flex gp-flex-col"
    >
      
    <div class="gp-flex gp-w-full" style="--h:fit-content;--h-tablet:fit-content;--h-mobile:fit-content;--d:flex;--d-mobile:flex;--d-tablet:flex;--mb:0px">
      <gp-hero-banner
        gp-data='{"target":"_blank","background":{"desktop":{"attachment":"scroll","color":"bg-2","image":{"height":1392,"src":"https://ucarecdn.com/7ce300f3-82fd-4b5a-a328-91cf388581b2/-/format/auto/","width":3510},"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"image"},"tablet":{"attachment":"scroll","color":"bg-2","image":{"height":1392,"src":"https://ucarecdn.com/7ce300f3-82fd-4b5a-a328-91cf388581b2/-/format/auto/","width":3510},"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"image"},"mobile":{"attachment":"scroll","color":"bg-2","image":{"height":1392,"src":"https://ucarecdn.com/7ce300f3-82fd-4b5a-a328-91cf388581b2/-/format/auto/","width":3510},"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"image"}},"uid":"gLxkL81L_Z","enableParallax":false,"speedParallax":0,"hoverEffect":true,"hoverEffectScale":"110%","layout":{"desktop":{"cols":[12],"display":"fill"},"mobile":{"cols":[12]}},"contentPosition1Col":{"desktop":"center"},"contentPosition2Col":{"desktop":"center"}}'
        gp-href=""
        
        class="gLxkL81L_Z gp-group/hero gp-flex gp-flex-col gp-transition-colors gp-duration-200 gp-ease-in-out gp-w-full"
        style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:flex;--d-mobile:flex;--d-tablet:flex;--op:100%;--h:fit-content;--h-tablet:fit-content;--h-mobile:fit-content;--w:100%;--w-tablet:100%;--w-mobile:100%"
        data-id="gLxkL81L_Z"
      >
        
      <div
        class="gp-relative gp-flex gp-flex-col gp-items-center gp-overflow-hidden gp_haslazybg !gp-justify-center tablet:!gp-justify-center mobile:!gp-justify-center"
        style="--h:fit-content;--h-tablet:fit-content;--h-mobile:fit-content;--bgc:var(--g-c-bg-2)"
      >
        
  <picture style="border-radius: inherit" class="gp-w-full">
  
      <source media="(max-width: 767px)" srcset="https://ucarecdn.com/7ce300f3-82fd-4b5a-a328-91cf388581b2/-/format/auto/" />
      <source media="(max-width: 1024px)" srcset="https://ucarecdn.com/7ce300f3-82fd-4b5a-a328-91cf388581b2/-/format/auto/" />
      <img
        title
        class="adaptive-hero-banner"
        src="https://ucarecdn.com/7ce300f3-82fd-4b5a-a328-91cf388581b2/-/format/auto/"
        alt=""
        fetchpriority="high"
        style="--d:none;--d-tablet:none;--d-mobile:none;--h:fit-content;--h-tablet:fit-content;--h-mobile:fit-content;--w:100%;--w-tablet:100%;--w-mobile:100%;--op:0;--z:-1"
      />
    
  </picture>
        <div class="gp-absolute gp-w-full gp-h-full">
          <div
            class="gp-overflow-hidden gp-relative gp-w-full gp-h-full"
            >
            <div
              aria-label="Background Image"
              role="banner"
              class='gp-hero-banner-bg gp-absolute gp-overflow-hidden gp-w-full gp-h-full top-0 left-0'
              style="clip-path:inset(0 0 0)"
            >
            <div class="hero-banner-bg-parallax gp-hero-banner-image-background gp-duration-[var(--duration)] group-hover/hero:gp-scale-[var(--scale)] gp-transition-transform"
              style="--bgi:url(https://ucarecdn.com/7ce300f3-82fd-4b5a-a328-91cf388581b2/-/format/auto/);--bgi-tablet:url(https://ucarecdn.com/7ce300f3-82fd-4b5a-a328-91cf388581b2/-/format/auto/);--bgi-mobile:url(https://ucarecdn.com/7ce300f3-82fd-4b5a-a328-91cf388581b2/-/format/auto/);--bgc:var(--g-c-bg-2);--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--duration:0.5s;--scale:110%;--pos:absolute;--pos-tablet:absolute;--pos-mobile:absolute;--h:100%;--h-tablet:100%;--h-mobile:100%;--w:100%;--w-tablet:100%;--w-mobile:100%;--top:;--top-tablet:;--top-mobile:"
            >
            
            
            </div></div>
          </div>
          <div
              aria-label="Overlay"
              role="banner"
              class="gp-absolute gp-inset-0 gp-left-[var(--left)] gp-top-[var(--top)] gp-transition-all gp-duration-300 group-hover/hero:gp-bg-[color:var(--hvr-bgc,_var(--bgc))] group-hover/hero:gp-opacity-[var(--hvr-op,_var(--op))]"
            style="--bgc:transparent;--top:0;--left:0;--op:0%">
            </div>
          </div>
          
       
      
    <div
      id data-id
        style="--cg:32px;--pc:center;--pc-tablet:center;--pc-mobile:center;--gtc:minmax(0, 12fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 1200px);--w-mobile:var(--g-ct-w, 1200px)"
        class="gp-hero-banner-row gp-z-1 gp-w-full tablet:gp-relative mobile:gp-relative tablet:[&>*>*]:!gp-justify-undefined mobile:[&>*>*]:!gp-justify-undefined tablet:!gp-content-stretch tablet:[&>*]:!gp-justify-center mobile:!gp-content-stretch mobile:[&>*]:!gp-justify-center gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="guG2oo35mr gp-relative gp-flex gp-flex-col"
    >
      
       <link rel="preload" href="https://cdn.shopify.com/s/files/1/0762/6113/0493/files/gempages_572751041980793671-dd9c3b96-42a2-4224-b096-f16e8949bcae.jpg" as="image" />
      
    <div
      parentTag="Col" id="gL5Az8yuNI" data-id="gL5Az8yuNI"
        style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--mb:0px;--pl:100px;--pos:static;--cg:32px;--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--h:auto;--bgc:transparent;--bgi:url({{ "gempages_572751041980793671-dd9c3b96-42a2-4224-b096-f16e8949bcae.jpg" | file_url }});--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gL5Az8yuNI gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gIO_TCatsG gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      parentTag="Col" id="gBrYjH4HnC" data-id="gBrYjH4HnC"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:none;--d-tablet:grid;--op:97%;--mt:100px;--ml:auto;--mb:100px;--mr:auto;--pt:var(--g-s-2xl);--pl:var(--g-s-2xl);--pb:var(--g-s-2xl);--pr:var(--g-s-2xl);--cg:var(--g-s-2xl);--pc:start;--gtc:minmax(0, 12fr);--w:80%;--w-tablet:40%;--w-mobile:100%;--bgc:var(--g-c-bg-3);--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gBrYjH4HnC gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gE2buTuh37 gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g6weOAhO47">
    <div
      parentTag="Col"
        class="g6weOAhO47 "
        style="--tt:default;--ta:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-xl)"
      >
      <div class="gp-flex" style="--jc:center">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:center;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#575757;--fs:italic;word-break:break-word;overflow:hidden"
        >{{ section.settings.gg6weOAhO47_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    <div
    
     data-id="gy-pAjb-bz"
    role="presentation"
    class="gp-group/image gy-pAjb-bz gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-s);--ta:center"
    >
      <div
        class="pointer-events-auto gp-h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="border-radius:inherit;--jc:center"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" data-srcSet="{{ "gempages_572751041980793671-c8c1f67b-501d-44b2-b9de-db365222f478.png" | file_url }}" srcset="{{ "gempages_572751041980793671-c8c1f67b-501d-44b2-b9de-db365222f478.png" | file_url }}" />
      <source media="(max-width: 1024px)" data-srcSet="{{ "gempages_572751041980793671-c8c1f67b-501d-44b2-b9de-db365222f478.png" | file_url }}" srcset="{{ "gempages_572751041980793671-c8c1f67b-501d-44b2-b9de-db365222f478.png" | file_url }}" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex"
        src="{{ "gempages_572751041980793671-c8c1f67b-501d-44b2-b9de-db365222f478.png" | file_url }}"
        data-src="{{ "gempages_572751041980793671-c8c1f67b-501d-44b2-b9de-db365222f478.png" | file_url }}"
        width="100%"
        alt="Alt Image"
        style="--aspect:auto;--objf:cover;--w:135px;--w-tablet:135px;--w-mobile:135px;--h:auto;--h-tablet:auto;--h-mobile:auto"
      />
    
  </picture>
      </div>
  </div>
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gYpdAO-NGq">
    <div
      parentTag="Col"
        class="gYpdAO-NGq "
        style="--tt:default;--ta:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%"
      >
      <div class="gp-flex" style="--jc:center">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text-g-text-1 gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:center;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#B4B4B4;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggYpdAO-NGq_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
    </div>
    </div>
   
    
    </div>
    </div>
   
    
      </div>
    
        
      </gp-hero-banner>
    </div>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-hero-banner.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>

  
    </div>
    </div>
   
    
       
      
    <div
      parentTag="Col" id="gR8wLStmHt" data-id="gR8wLStmHt"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:none;--d-mobile:grid;--d-tablet:none;--op:100%;--pt-mobile:0px;--pb-mobile:0px;--cg:var(--g-s-2xl);--pc:start;--gtc:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gR8wLStmHt gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="g6uRcjbyxJ gp-relative gp-flex gp-flex-col"
    >
      <div
    
     data-id="grsFCd1EMU"
    role="presentation"
    class="gp-group/image grsFCd1EMU gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-l);--mt-mobile:0px;--mb-mobile:0px;--mt-tablet:0px;--mb-tablet:0px;--ta:left"
    >
      <div
        class="pointer-events-auto gp-h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="border-radius:inherit;--jc:left"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" data-srcSet="{{ "gempages_572751041980793671-dcb6fa42-afc3-4b84-b012-a94c8bf5464f.jpg" | file_url }}" srcset="{{ "gempages_572751041980793671-dcb6fa42-afc3-4b84-b012-a94c8bf5464f.jpg" | file_url }}" />
      <source media="(max-width: 1024px)" data-srcSet="{{ "gempages_572751041980793671-b426ba52-921f-4ae9-b9f7-9a820ca15cf5.png" | file_url }}" srcset="{{ "gempages_572751041980793671-b426ba52-921f-4ae9-b9f7-9a820ca15cf5.png" | file_url }}" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex"
        src="{{ "gempages_572751041980793671-b426ba52-921f-4ae9-b9f7-9a820ca15cf5.png" | file_url }}"
        data-src="{{ "gempages_572751041980793671-b426ba52-921f-4ae9-b9f7-9a820ca15cf5.png" | file_url }}"
        width="100%"
        alt="Alt Image"
        style="--aspect:auto;--objf:cover;--objf-mobile:fill;--w:100%;--w-tablet:100%;--w-mobile:100%;--h:auto;--h-tablet:auto;--h-mobile:auto"
      />
    
  </picture>
      </div>
  </div>
    </div>
    </div>
   
    <div gp-el-wrapper style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:none;--d-tablet:block;--op:100%;--mb:var(--g-s-3xl)" class="gns98tGcva ">
      
    <div
    data-id="gns98tGcva"
      class="gp-flex gp-justify-start"
    >
      <div
        style="--w:100px;--w-tablet:100px;--w-mobile:100px;--bs:solid;--bbw:1pxpx;--bbw-tablet:undefinedpx;--bbw-mobile:undefinedpx;--t:gp-rotate(0deg);--bc:rgba(125, 125, 125, 0)"
        class="gp-block tablet:gp-block mobile:gp-block"
      ></div>
      <div
        class="gp-items-center gp-hidden tablet:gp-hidden mobile:gp-hidden"
        style="--w:100px;--w-tablet:100px;--w-mobile:100px;--bc:rgba(125, 125, 125, 0);--t:gp-rotate(0deg)"
      >
        
    <div
      class="gp-hidden"
      style="--w:100px;--w-tablet:100px;--w-mobile:100px;--bc:rgba(125, 125, 125, 0);--bs:solid;--bbw:1pxpx;--bbw-tablet:undefinedpx;--bbw-mobile:undefinedpx;--minw:100px"
    ></div>
  
        
            <span
              class="gp-inline-flex gp-items-center gp-justify-center gp-flex-none"
              style="--mr:var(--g-s-xs);--w:20px;--h:20px;--t:gp-rotate(0);--c:var(--g-c-text-1, text-1)"
            >
              <svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M428.8 137.6h-86.177a115.52 115.52 0 0 0 2.176-22.4c0-47.914-35.072-83.2-92-83.2-45.314 0-57.002 48.537-75.707 78.784-7.735 12.413-16.994 23.317-25.851 33.253l-.131.146-.129.148C135.662 161.807 127.764 168 120.8 168h-2.679c-5.747-4.952-13.536-8-22.12-8H32c-17.673 0-32 12.894-32 28.8v230.4C0 435.106 14.327 448 32 448h64c8.584 0 16.373-3.048 22.12-8h2.679c28.688 0 67.137 40 127.2 40h21.299c62.542 0 98.8-38.658 99.94-91.145 12.482-17.813 18.491-40.785 15.985-62.791A93.148 93.148 0 0 0 393.152 304H428.8c45.435 0 83.2-37.584 83.2-83.2 0-45.099-38.101-83.2-83.2-83.2zm0 118.4h-91.026c12.837 14.669 14.415 42.825-4.95 61.05 11.227 19.646 1.687 45.624-12.925 53.625 6.524 39.128-10.076 61.325-50.6 61.325H248c-45.491 0-77.21-35.913-120-39.676V215.571c25.239-2.964 42.966-21.222 59.075-39.596 11.275-12.65 21.725-25.3 30.799-39.875C232.355 112.712 244.006 80 252.8 80c23.375 0 44 8.8 44 35.2 0 35.2-26.4 53.075-26.4 70.4h158.4c18.425 0 35.2 16.5 35.2 35.2 0 18.975-16.225 35.2-35.2 35.2zM88 384c0 13.255-10.745 24-24 24s-24-10.745-24-24 10.745-24 24-24 24 10.745 24 24z" /></svg>
            </span>
          
        
            <span
              class="gp-inline-flex gp-items-center gp-justify-center gp-flex-none gp-g-paragraph-1"
              style="--tt:horizontal;--c:var(--g-c-text-1, text-1);--mr:var(--g-s-s)"
            >
              Title
            </span>
          
        
    <div
      class="gp-flex"
      style="--w:100px;--w-tablet:100px;--w-mobile:100px;--bc:rgba(125, 125, 125, 0);--bs:solid;--bbw:1pxpx;--bbw-tablet:undefinedpx;--bbw-mobile:undefinedpx;--minw:100px"
    ></div>
  
      </div>
    </div>
  
      </div>
       
      
    <div
      parentTag="Col" id="gaBJvwm2FA" data-id="gaBJvwm2FA"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:none;--d-mobile:grid;--d-tablet:none;--op:100%;--pt:var(--g-s-2xl);--pl:var(--g-s-2xl);--pb:var(--g-s-2xl);--pr:var(--g-s-2xl);--mb-mobile:var(--g-s-3xl);--cg:var(--g-s-2xl);--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:var(--g-c-bg-3);--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gaBJvwm2FA gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gExK9sEzr7 gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g97Enaylxk">
    <div
      parentTag="Col"
        class="g97Enaylxk "
        style="--tt:default;--ta:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-xl)"
      >
      <div class="gp-flex" style="--jc:center">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:center;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#575757;--fs:italic;word-break:break-word;overflow:hidden"
        >{{ section.settings.gg97Enaylxk_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    <div
    
     data-id="g6aD3G-IAU"
    role="presentation"
    class="gp-group/image g6aD3G-IAU gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-s);--ta:center"
    >
      <div
        class="pointer-events-auto gp-h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="border-radius:inherit;--jc:center"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" data-srcSet="https://ucarecdn.com/6fcf06e8-68f5-4b4c-a495-bd3be236cc92/-/format/auto/" srcset="https://ucarecdn.com/6fcf06e8-68f5-4b4c-a495-bd3be236cc92/-/format/auto/" />
      <source media="(max-width: 1024px)" data-srcSet="https://ucarecdn.com/6fcf06e8-68f5-4b4c-a495-bd3be236cc92/-/format/auto/" srcset="https://ucarecdn.com/6fcf06e8-68f5-4b4c-a495-bd3be236cc92/-/format/auto/" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex"
        src="https://ucarecdn.com/6fcf06e8-68f5-4b4c-a495-bd3be236cc92/-/format/auto/"
        data-src="https://ucarecdn.com/6fcf06e8-68f5-4b4c-a495-bd3be236cc92/-/format/auto/"
        width="100%"
        alt="Alt Image"
        style="--aspect:auto;--objf:cover;--w:135px;--w-tablet:135px;--w-mobile:135px;--h:auto;--h-tablet:auto;--h-mobile:auto"
      />
    
  </picture>
      </div>
  </div>
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gBT2TQRcH3">
    <div
      parentTag="Col"
        class="gBT2TQRcH3 "
        style="--tt:default;--ta:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%"
      >
      <div class="gp-flex" style="--jc:center">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text-g-text-1 gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:center;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#B4B4B4;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggBT2TQRcH3_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
       
      
    <div
      parentTag="Col" id="gBHhoju72R" data-id="gBHhoju72R"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--mb:var(--g-s-2xl);--pl:100px;--mb-mobile:var(--g-s-2xl);--pl-mobile:0px;--cg:var(--g-s-2xl);--pc:start;--gtc:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gBHhoju72R gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gVer41mq0l gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g_6JEFm9sx">
    <div
      parentTag="Col"
        class="g_6JEFm9sx "
        style="--tt:default;--ta:left;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text-g-text-2 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--c:#242424;--fs:normal;--ff:var(--g-font-heading, heading);--weight:700;--ls:normal;--size:33px;--size-tablet:33px;--size-mobile:29px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;overflow:hidden"
        >{{ section.settings.gg_6JEFm9sx_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
       
      
    <div
      parentTag="Col" id="gRjQPciK0n" data-id="gRjQPciK0n"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pl:100px;--pr:100px;--pl-mobile:0px;--pr-mobile:0px;--cg:62px;--pc:start;--gtc:minmax(0, 7fr) minmax(0, 5fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gRjQPciK0n gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="g6y2_6wkqm gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gQZWa_Ro0l">
    <div
      parentTag="Col"
        class="gQZWa_Ro0l "
        style="--tt:default;--ta:left;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb-mobile:var(--g-s-2xl)"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#575757;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggQZWa_Ro0l_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gJ24fLjwHm gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g3pjVrJtcx">
    <div
      parentTag="Col"
        class="g3pjVrJtcx "
        style="--tt:default;--ta:left;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-l)"
      >
      <div class="gp-flex" style="--jc:left">
        <h3
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text-g-text-2 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--c:#242424;--fs:normal;--ff:var(--g-font-heading, heading);--weight:700;--ls:normal;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;overflow:hidden"
        >{{ section.settings.gg3pjVrJtcx_text | replace: '$locationOrigin', locationOrigin }}</h3>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="goU5qsBIV9">
    <div
      parentTag="Col"
        class="goU5qsBIV9 "
        style="--tt:default;--ta:left;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-2xl)"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#575757;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggoU5qsBIV9_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gcx_CPsm7q">
    <div
      parentTag="Col"
        class="gcx_CPsm7q "
        style="--tt:default;--ta:left;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-l)"
      >
      <div class="gp-flex" style="--jc:left">
        <h3
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text-g-text-2 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--c:#242424;--fs:normal;--ff:var(--g-font-heading, heading);--weight:700;--ls:normal;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggcx_CPsm7q_text | replace: '$locationOrigin', locationOrigin }}</h3>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gBmajOMXHk">
    <div
      parentTag="Col"
        class="gBmajOMXHk "
        style="--tt:default;--ta:left;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#575757;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggBmajOMXHk_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
    </div>
    </div>
  
            </section>
           
    


{% schema %}
  {
    
    "name": "Section 13",
    "tag": "section",
    "class": "gps-572876054906012544 gps gpsil",
    "settings": [{"type":"paragraph","content":"Section design by GemPages. [Click here to start design](https://admin.shopify.com/store/i0pixe-10/apps/gempages-cro/app/shopify/edit?pageType=GP_PRODUCT&editorId=572751048691811510&sectionId=572876054906012544)"},{"type":"select","label":"Section Preload","id":"section_preload","options":[{"label":"On","value":"true"},{"label":"Off","value":"false"},{"label":"Off Lazy Script","value":"off_lazy_script"}],"default":"false"},{"type":"text","label":"Checksum","id":"checksum"},{"type":"html","id":"gg6weOAhO47_text","label":"gg6weOAhO47_text","default":"<p><span style=\"font-size:16px;\"><strong>Ideaformer</strong></span>&nbsp;facus on 3d printing accessorise since 2017. we sold our products to many country , we are on amazon , ebay ,aliexpress, alibaba, Temu and our website. think many 3d printer user should be used our products . &nbsp;</p><p>Last two years , we are start to bulid 3d printer, we are start over with ir3 and ir3 v1 , we learned from them well，and we got the experienced from customer . More than one year later, we updated&nbsp;to ir3 v2 . then take half an year to testing and adjusting , we made it easier and more stable to use right now.</p><p>For the belt 3d printer on the market The main issue is the leveling + braid belt , this may cause many issues, such as sticker not enough or stick too tight , high model shaking etc , then printing fail&nbsp;or print quality is bad . our IR3 V2 with auto leveing + metal belt , it totally solve this issue，if you need print something long stuff, ir3 v2 is a great choice .</p>"},{"type":"html","id":"ggYpdAO-NGq_text","label":"ggYpdAO-NGq_text","default":"<p><span style=\"color:#242424;font-size:16px;\"><strong>Rico Haa</strong></span> &nbsp;<span style=\"color:#B4B4B4;font-size:16px;\">/ CEO</span></p>"},{"type":"html","id":"gg97Enaylxk_text","label":"gg97Enaylxk_text","default":"<p>“We’re a fully distributed team of 85 people living and working in 15 countries around the world. And we’re working to build the best products to help our customers.”</p>"},{"type":"html","id":"ggBT2TQRcH3_text","label":"ggBT2TQRcH3_text","default":"<p><span style=\"color:#242424;font-size:16px;\"><strong>James Frank</strong></span> &nbsp;<span style=\"color:#B4B4B4;font-size:16px;\">/ CEO</span></p>"},{"type":"html","id":"gg_6JEFm9sx_text","label":"gg_6JEFm9sx_text","default":"<p>Our Mission</p>"},{"type":"html","id":"ggQZWa_Ro0l_text","label":"ggQZWa_Ro0l_text","default":"<p>At IdeaFormer, our mission is to redefine the standards of belt 3D printing. Since 2017, we’ve served global makers with precision 3D printing accessories. Building on this foundation, we ventured into printer manufacturing with a deep understanding of real user challenges.&nbsp;</p><p>&nbsp;</p><p>From IR3 to IR3 V2, we’ve continuously refined our design — integrating automatic leveling and a metal belt to solve the core issues of adhesion, print stability, and ease of use.</p><p>&nbsp;</p><p>&nbsp;We are committed to creating reliable, high-speed, and intelligent 3D printing solutions that empower creators to go further, print longer, and build better.</p>"},{"type":"html","id":"gg3pjVrJtcx_text","label":"gg3pjVrJtcx_text","default":"<p>Creative</p>"},{"type":"html","id":"ggoU5qsBIV9_text","label":"ggoU5qsBIV9_text","default":"<p>IdeaFormer empowers creators with precise, powerful tools to bring imagination to life.</p>"},{"type":"html","id":"ggcx_CPsm7q_text","label":"ggcx_CPsm7q_text","default":"<p>Sustainability</p>"},{"type":"html","id":"ggBmajOMXHk_text","label":"ggBmajOMXHk_text","default":"<p>We design eco-conscious printers with durable parts to reduce waste and support responsible making.</p>"}],
    "blocks": [{"type": "@app"}],
    "locales": {}
  }
{% endschema %}
