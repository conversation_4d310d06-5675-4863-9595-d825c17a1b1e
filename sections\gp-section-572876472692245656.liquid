

<style {% if section.settings.section_preload == "false" %} class="gps-link gps-style" type="text/disabled-css" {% endif %}>.gps-572876472692245656.gps.gpsil [style*="--aspect:"]{aspect-ratio:var(--aspect)}.gps-572876472692245656.gps.gpsil [style*="--bg:"]{background:var(--bg)}.gps-572876472692245656.gps.gpsil [style*="--hvr-bg:"]:hover{background:var(--hvr-bg)}.gps-572876472692245656.gps.gpsil [style*="--bga:"]{background-attachment:var(--bga)}.gps-572876472692245656.gps.gpsil [style*="--bgc:"]{background-color:var(--bgc)}.gps-572876472692245656.gps.gpsil [style*="--bgi:"]{background-image:var(--bgi)}.gps-572876472692245656.gps.gpsil [style*="--bgp:"]{background-position:var(--bgp)}.gps-572876472692245656.gps.gpsil [style*="--bgr:"]{background-repeat:var(--bgr)}.gps-572876472692245656.gps.gpsil [style*="--bgs:"]{background-size:var(--bgs)}.gps-572876472692245656.gps.gpsil [style*="--b:"]{border:var(--b)}.gps-572876472692245656.gps.gpsil [style*="--hvr-b:"]:hover{border:var(--hvr-b)}.gps-572876472692245656.gps.gpsil [style*="--bb:"]{border-bottom:var(--bb)}.gps-572876472692245656.gps.gpsil [style*="--bbw:"]{border-bottom-width:var(--bbw)}.gps-572876472692245656.gps.gpsil [style*="--bc:"]{border-color:var(--bc)}.gps-572876472692245656.gps.gpsil [style*="--bs:"]{border-style:var(--bs)}.gps-572876472692245656.gps.gpsil [style*="--bw:"]{border-width:var(--bw)}.gps-572876472692245656.gps.gpsil [style*="--shadow:"]{box-shadow:var(--shadow)}.gps-572876472692245656.gps.gpsil [style*="--c:"]{color:var(--c)}.gps-572876472692245656.gps.gpsil [style*="--cg:"]{-moz-column-gap:var(--cg);column-gap:var(--cg)}.gps-572876472692245656.gps.gpsil [style*="--ff:"]{font-family:var(--ff)}.gps-572876472692245656.gps.gpsil [style*="--size:"]{font-size:var(--size)}.gps-572876472692245656.gps.gpsil [style*="--weight:"]{font-weight:var(--weight)}.gps-572876472692245656.gps.gpsil [style*="--fs:"]{font-style:var(--fs)}.gps-572876472692245656.gps.gpsil [style*="--gtc:"]{grid-template-columns:var(--gtc)}.gps-572876472692245656.gps.gpsil [style*="--h:"]{height:var(--h)}.gps-572876472692245656.gps.gpsil [style*="--jc:"]{justify-content:var(--jc)}.gps-572876472692245656.gps.gpsil [style*="--ls:"]{letter-spacing:var(--ls)}.gps-572876472692245656.gps.gpsil [style*="--lh:"]{line-height:var(--lh)}.gps-572876472692245656.gps.gpsil [style*="--m:"]{margin:var(--m)}.gps-572876472692245656.gps.gpsil [style*="--mb:"]{margin-bottom:var(--mb)}.gps-572876472692245656.gps.gpsil [style*="--mr:"]{margin-right:var(--mr)}.gps-572876472692245656.gps.gpsil [style*="--minw:"]{min-width:var(--minw)}.gps-572876472692245656.gps.gpsil [style*="--objf:"]{-o-object-fit:var(--objf);object-fit:var(--objf)}.gps-572876472692245656.gps.gpsil [style*="--op:"]{opacity:var(--op)}.gps-572876472692245656.gps.gpsil [style*="--o:"]{order:var(--o)}.gps-572876472692245656.gps.gpsil [style*="--pc:"]{place-content:var(--pc)}.gps-572876472692245656.gps.gpsil [style*="--p:"]{padding:var(--p)}.gps-572876472692245656.gps.gpsil [style*="--pb:"]{padding-bottom:var(--pb)}.gps-572876472692245656.gps.gpsil [style*="--pl:"]{padding-left:var(--pl)}.gps-572876472692245656.gps.gpsil [style*="--pr:"]{padding-right:var(--pr)}.gps-572876472692245656.gps.gpsil [style*="--pt:"]{padding-top:var(--pt)}.gps-572876472692245656.gps.gpsil [style*="--ta:"]{text-align:var(--ta)}.gps-572876472692245656.gps.gpsil [style*="--tt:"]{text-transform:var(--tt)}.gps-572876472692245656.gps.gpsil [style*="--t:"]{transform:var(--t)}.gps-572876472692245656.gps.gpsil [style*="--w:"]{width:var(--w)}.gps-572876472692245656.gps.gpsil [style*="--line-clamp:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp);display:-webkit-box;overflow:hidden}@media only screen and (max-width:1024px){.gps-572876472692245656.gps.gpsil [style*="--bbw-tablet:"]{border-bottom-width:var(--bbw-tablet)}.gps-572876472692245656.gps.gpsil [style*="--size-tablet:"]{font-size:var(--size-tablet)}.gps-572876472692245656.gps.gpsil [style*="--h-tablet:"]{height:var(--h-tablet)}.gps-572876472692245656.gps.gpsil [style*="--lh-tablet:"]{line-height:var(--lh-tablet)}.gps-572876472692245656.gps.gpsil [style*="--pb-tablet:"]{padding-bottom:var(--pb-tablet)}.gps-572876472692245656.gps.gpsil [style*="--pl-tablet:"]{padding-left:var(--pl-tablet)}.gps-572876472692245656.gps.gpsil [style*="--pr-tablet:"]{padding-right:var(--pr-tablet)}.gps-572876472692245656.gps.gpsil [style*="--pt-tablet:"]{padding-top:var(--pt-tablet)}.gps-572876472692245656.gps.gpsil [style*="--w-tablet:"]{width:var(--w-tablet)}.gps-572876472692245656.gps.gpsil [style*="--line-clamp-tablet:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-tablet);display:-webkit-box;overflow:hidden}}@media only screen and (max-width:767px){.gps-572876472692245656.gps.gpsil [style*="--bbw-mobile:"]{border-bottom-width:var(--bbw-mobile)}.gps-572876472692245656.gps.gpsil [style*="--cg-mobile:"]{-moz-column-gap:var(--cg-mobile);column-gap:var(--cg-mobile)}.gps-572876472692245656.gps.gpsil [style*="--size-mobile:"]{font-size:var(--size-mobile)}.gps-572876472692245656.gps.gpsil [style*="--gtc-mobile:"]{grid-template-columns:var(--gtc-mobile)}.gps-572876472692245656.gps.gpsil [style*="--h-mobile:"]{height:var(--h-mobile)}.gps-572876472692245656.gps.gpsil [style*="--jc-mobile:"]{justify-content:var(--jc-mobile)}.gps-572876472692245656.gps.gpsil [style*="--ls-mobile:"]{letter-spacing:var(--ls-mobile)}.gps-572876472692245656.gps.gpsil [style*="--lh-mobile:"]{line-height:var(--lh-mobile)}.gps-572876472692245656.gps.gpsil [style*="--mb-mobile:"]{margin-bottom:var(--mb-mobile)}.gps-572876472692245656.gps.gpsil [style*="--pb-mobile:"]{padding-bottom:var(--pb-mobile)}.gps-572876472692245656.gps.gpsil [style*="--pl-mobile:"]{padding-left:var(--pl-mobile)}.gps-572876472692245656.gps.gpsil [style*="--pr-mobile:"]{padding-right:var(--pr-mobile)}.gps-572876472692245656.gps.gpsil [style*="--pt-mobile:"]{padding-top:var(--pt-mobile)}.gps-572876472692245656.gps.gpsil [style*="--ta-mobile:"]{text-align:var(--ta-mobile)}.gps-572876472692245656.gps.gpsil [style*="--w-mobile:"]{width:var(--w-mobile)}.gps-572876472692245656.gps.gpsil [style*="--line-clamp-mobile:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-mobile);display:-webkit-box;overflow:hidden}}.gps-572876472692245656 .focus\:\!gp-shadow-none,.gps-572876472692245656 .hover\:\!gp-shadow-none{--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000}.gps-572876472692245656 .gp-g-paragraph-1{font-family:var(--g-p1-ff);font-size:var(--g-p1-size);font-style:var(--g-p1-fs);font-weight:var(--g-p1-weight);letter-spacing:var(--g-p1-ls);line-height:var(--g-p1-lh)}.gps-572876472692245656 .gp-fixed{position:fixed}.gps-572876472692245656 .gp-relative{position:relative}.gps-572876472692245656 .gp-top-\[27px\]{top:27px}.gps-572876472692245656 .gp-z-1{z-index:1}.gps-572876472692245656 .gp-z-100{z-index:100}.gps-572876472692245656 .gp-mx-auto{margin-left:auto;margin-right:auto}.gps-572876472692245656 .gp-mb-0{margin-bottom:0}.gps-572876472692245656 .gp-block{display:block}.gps-572876472692245656 .gp-flex{display:flex}.gps-572876472692245656 .gp-inline-flex{display:inline-flex}.gps-572876472692245656 .gp-grid{display:grid}.gps-572876472692245656 .gp-contents{display:contents}.gps-572876472692245656 .\!gp-hidden{display:none!important}.gps-572876472692245656 .gp-hidden{display:none}.gps-572876472692245656 .\!gp-h-auto{height:auto!important}.gps-572876472692245656 .gp-h-auto{height:auto}.gps-572876472692245656 .gp-h-full{height:100%}.gps-572876472692245656 .gp-w-full{width:100%}.gps-572876472692245656 .gp-max-w-full{max-width:100%}.gps-572876472692245656 .gp-flex-none{flex:none}.gps-572876472692245656 .gp-shrink-0{flex-shrink:0}.gps-572876472692245656 .gp-grid-rows-\[1fr\]{grid-template-rows:1fr}.gps-572876472692245656 .gp-flex-col{flex-direction:column}.gps-572876472692245656 .gp-items-center{align-items:center}.gps-572876472692245656 .gp-justify-start{justify-content:flex-start}.gps-572876472692245656 .gp-justify-center{justify-content:center}.gps-572876472692245656 .gp-gap-y-0{row-gap:0}.gps-572876472692245656 .gp-overflow-hidden{overflow:hidden}.gps-572876472692245656 .gp-break-words{overflow-wrap:break-word}.gps-572876472692245656 .gp-rounded{border-radius:4px}.gps-572876472692245656 .gp-rounded-none{border-radius:0}.gps-572876472692245656 .\!gp-bg-none{background-image:none!important}.gps-572876472692245656 .gp-p-\[16px\]{padding:16px}.gps-572876472692245656 .gp-text-\[14px\]{font-size:14px}.gps-572876472692245656 .gp-leading-normal{line-height:1.5}.gps-572876472692245656 .gp-text-g-text-2{color:var(--g-c-text-2)}.gps-572876472692245656 .gp-text-g-text-3{color:var(--g-c-text-3)}.gps-572876472692245656 .gp-no-underline{text-decoration-line:none}.gps-572876472692245656 .\!gp-outline-none{outline:2px solid transparent!important;outline-offset:2px!important}.gps-572876472692245656 .gp-transition-colors{transition-duration:.15s;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-572876472692245656 .gp-duration-200{transition-duration:.2s}.gps-572876472692245656 .gp-duration-300{transition-duration:.3s}.gps-572876472692245656 .gp-ease-in-out{transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-572876472692245656 .disabled\:gp-btn-disabled:disabled{cursor:default}@media (hover:hover) and (pointer:fine){.gps-572876472692245656 .hover\:\!gp-shadow-none:hover{--tw-shadow:0 0 #0000!important;--tw-shadow-colored:0 0 #0000!important;box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)!important}}.gps-572876472692245656 .focus\:\!gp-shadow-none:focus{--tw-shadow:0 0 #0000!important;--tw-shadow-colored:0 0 #0000!important;box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)!important}.gps-572876472692245656 .disabled\:gp-pointer-events-none:disabled{pointer-events:none}.gps-572876472692245656 .disabled\:gp-opacity-30:disabled{opacity:.3}@media (hover:hover) and (pointer:fine){.gps-572876472692245656 .gp-group\/button:hover .group-hover\/button\:\!gp-text-inherit{color:inherit!important}}.gps-572876472692245656 .gp-group\/button:active .group-active\/button\:\!gp-text-inherit{color:inherit!important}.gps-572876472692245656 .gp-group\/button[data-state=loading] .group-data-\[state\=loading\]\/button\:gp-invisible{visibility:hidden}@media (max-width:1024px){.gps-572876472692245656 .tablet\:gp-block{display:block}.gps-572876472692245656 .tablet\:\!gp-hidden{display:none!important}.gps-572876472692245656 .tablet\:gp-hidden{display:none}.gps-572876472692245656 .tablet\:gp-h-auto{height:auto}.gps-572876472692245656 .tablet\:gp-flex-none{flex:none}}@media (max-width:767px){.gps-572876472692245656 .mobile\:gp-block{display:block}.gps-572876472692245656 .mobile\:\!gp-hidden{display:none!important}.gps-572876472692245656 .mobile\:gp-hidden{display:none}.gps-572876472692245656 .mobile\:gp-h-auto{height:auto}.gps-572876472692245656 .mobile\:gp-flex-none{flex:none}}.gps-572876472692245656 .\[\&\>svg\]\:\!gp-h-\[var\(--height-desktop\)\]>svg{height:var(--height-desktop)!important}.gps-572876472692245656 .\[\&\>svg\]\:\!gp-w-auto>svg{width:auto!important}@media (max-width:1024px){.gps-572876472692245656 .tablet\:\[\&\>svg\]\:\!gp-h-\[var\(--height-tablet\)\]>svg{height:var(--height-tablet)!important}}@media (max-width:767px){.gps-572876472692245656 .mobile\:\[\&\>svg\]\:\!gp-h-\[var\(--height-mobile\)\]>svg{height:var(--height-mobile)!important}}.gps-572876472692245656 .\[\&_\*\]\:gp-max-w-full *{max-width:100%}.gps-572876472692245656 .\[\&_p\]\:gp-inline p{display:inline}.gps-572876472692245656 .\[\&_p\]\:gp-whitespace-pre-line p{white-space:pre-line}.gps-572876472692245656 .\[\&_p\]\:after\:gp-whitespace-pre-wrap p:after{content:var(--tw-content);white-space:pre-wrap}.gps-572876472692245656 .\[\&_p\]\:after\:gp-content-\[\'\\A\'\] p:after{--tw-content:"\A";content:var(--tw-content)}</style>

       
      
            <section
              class="gp-mx-auto gp-max-w-full {% if section.settings.section_preload == "false" %}gps-lazy {% endif %}"
              style="--w:100%;--w-tablet:100%;--w-mobile:100%;--pl:0px;--pl-tablet:0px;--pl-mobile:0px;--pr:0px;--pr-tablet:0px;--pr-mobile:0px"
            >
              
    <div
      id="gYtRXR0WKG" data-id="gYtRXR0WKG"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pt:var(--g-s-4xl);--pb:var(--g-s-4xl);--pt-tablet:var(--g-s-4xl);--pb-tablet:var(--g-s-4xl);--cg:var(--g-s-2xl);--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:#FFFFFF;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gYtRXR0WKG gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gUv7yY7kjy gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      parentTag="Col" id="g6oGI2LUHk" data-id="g6oGI2LUHk"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--mb:var(--g-s-3xl);--pl-tablet:15px;--pr-tablet:15px;--cg:var(--g-s-2xl);--pc:start;--gtc:minmax(0, 3fr) minmax(0, 3fr) minmax(0, 6fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="g6oGI2LUHk gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="g89fCbDEpV gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gL4guCxE3v">
    <div
      parentTag="Col"
        class="gL4guCxE3v "
        style="--tt:default;--ta:left;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-2xl)"
      >
      <div class="gp-flex" style="--jc:left">
        <h3
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text-g-text-2 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--c:#242424;--fs:normal;--ff:var(--g-font-heading, heading);--weight:700;--ls:normal;--size:19px;--size-tablet:19px;--size-mobile:17px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggL4guCxE3v_text | replace: '$locationOrigin', locationOrigin }}</h3>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gyef5ZqiRM">
    <div
      parentTag="Col"
        class="gyef5ZqiRM "
        style="--tt:default;--ta:left;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-l)"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#575757;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggyef5ZqiRM_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gue3T1Gg1_">
    <div
      parentTag="Col"
        class="gue3T1Gg1_ "
        style="--tt:default;--ta:left;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-l)"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#575757;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggue3T1Gg1__text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gmsr_bm3rg">
    <div
      parentTag="Col"
        class="gmsr_bm3rg "
        style="--tt:default;--ta:left;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-l)"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#575757;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggmsr_bm3rg_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g1Tlz4x_IS">
    <div
      parentTag="Col"
        class="g1Tlz4x_IS "
        style="--tt:default;--ta:left;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-l);--mb-mobile:var(--g-s-3xl)"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#575757;word-break:break-word;overflow:hidden"
        >{{ section.settings.gg1Tlz4x_IS_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gfQRnXuII4 gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gh1KWxyxyt">
    <div
      parentTag="Col"
        class="gh1KWxyxyt "
        style="--tt:default;--ta:left;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-2xl)"
      >
      <div class="gp-flex" style="--jc:left">
        <h3
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text-g-text-2 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--c:#242424;--fs:normal;--ff:var(--g-font-heading, heading);--weight:700;--ls:normal;--size:19px;--size-tablet:19px;--size-mobile:17px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggh1KWxyxyt_text | replace: '$locationOrigin', locationOrigin }}</h3>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gE-Mp9MxnQ">
    <div
      parentTag="Col"
        class="gE-Mp9MxnQ "
        style="--tt:default;--ta:left;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-l)"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#575757;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggE-Mp9MxnQ_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gSpMJqnpz8">
    <div
      parentTag="Col"
        class="gSpMJqnpz8 "
        style="--tt:default;--ta:left;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-l)"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#575757;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggSpMJqnpz8_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="glgrobpOZZ">
    <div
      parentTag="Col"
        class="glgrobpOZZ "
        style="--tt:default;--ta:left;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-l)"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#575757;word-break:break-word;overflow:hidden"
        >{{ section.settings.gglgrobpOZZ_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gKynVJDEwA">
    <div
      parentTag="Col"
        class="gKynVJDEwA "
        style="--tt:default;--ta:left;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-l);--mb-mobile:var(--g-s-3xl)"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#575757;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggKynVJDEwA_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gKKStVFn4t gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g79y1dF4K5">
    <div
      parentTag="Col"
        class="g79y1dF4K5 "
        style="--tt:default;--ta:left;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-2xl)"
      >
      <div class="gp-flex" style="--jc:left">
        <h3
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text-g-text-2 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--c:#242424;--fs:normal;--ff:var(--g-font-heading, heading);--weight:700;--ls:normal;--size:19px;--size-tablet:19px;--size-mobile:17px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;overflow:hidden"
        >{{ section.settings.gg79y1dF4K5_text | replace: '$locationOrigin', locationOrigin }}</h3>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="ggJjorMUA0">
    <div
      parentTag="Col"
        class="ggJjorMUA0 "
        style="--tt:default;--ta:left;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-l)"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#575757;word-break:break-word;overflow:hidden"
        >{{ section.settings.gggJjorMUA0_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    <div gp-el-wrapper style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-2xl)" class="g3CFqX-vX5 ">
      
    <gp-form
      id="g3CFqX-vX5"
      data-id="g3CFqX-vX5"
      
      data-submit-action=''
      data-callback='{"link":"","target":"_self"}'
    >
      <a
        hidden
        id="gp-form-callback-g3CFqX-vX5"
        href="" target=""
      >
      </a>
      {% form 'customer', class: 'gp-form-g3CFqX-vX5 ', id: 'contact_form_g3CFqX-vX5' %}
        {% if form.errors %}
          <div
            id="gp-form-error-g3CFqX-vX5"
            class="gp-form-message gp-hidden gp-top-[27px] gp-z-100 gp-fixed gp-rounded gp-p-[16px] gp-text-[14px] gp-leading-normal"
            style="background-color:#FFE9E9;left:50%;transform:translateX(-50%);color:#EA3335"
          >
            {{ section.settings.gg3CFqX-vX5_errorMessage }}
          </div>
        {% endif %}
        <div popover id="my-popover-g3CFqX-vX5">
        <style>
            #my-popover-g3CFqX-vX5::backdrop {
              width: fit-content;
              height: fit-content;
            }
        </style>
        <div
          id="gp-form-success-g3CFqX-vX5"
          class="gp-form-message gp-hidden gp-top-[27px] gp-z-100 gp-fixed gp-rounded gp-p-[16px] gp-text-[14px] gp-leading-normal"
          style="background-color:#F2FFEC;left:50%;transform:translateX(-50%);color:#52C41A"
        >
          {{ section.settings.gg3CFqX-vX5_successMessage }}
        </div></div>
        
       
      
    <div
      parentTag="Newsletter" id="goKIsyy4Pl" data-id="goKIsyy4Pl"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--cg:0px;--cg-mobile:0px;--pc:start;--gtc:minmax(0, auto) minmax(0, auto);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="goKIsyy4Pl gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="g4-gXlXZ63 gp-relative gp-flex gp-flex-col"
    >
      
    <div
    data-id="gjT6N53PzN"
      class="gjT6N53PzN"
      style="--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:0px;--ta:left"
    >
      <input
        type="email"
        class="gp-form-item gp-g-paragraph-1 !gp-outline-none !gp-h-auto focus:!gp-shadow-none hover:!gp-shadow-none !gp-bg-none"
        style="--pl:32px;--pr:32px;--pt:11px;--pb:11px;--pl-mobile:32px;--pr-mobile:32px;--pt-mobile:12.5px;--pb-mobile:12.5px;--w:521px;--w-tablet:370px;--w-mobile:298px;--bs:solid;--bw:1px 1px 1px 1px;--bc:#F3F3F3;--shadow:none"
        placeholder="{{ section.settings.ggjT6N53PzN_placeholder }}"
        {% if true %}
        required
        {% endif %}
        name="contact[email]"
        value=""
        autocomplete="email"
      ></input>
    </div>
  
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="g4suhnC3lo gp-relative gp-flex gp-flex-col"
    >
      
    
  <gp-button >
  <div
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
    
  >
    <style>
    .g-plMxqItm.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: solid;
  border-width: 0px 0px 0px 0px;
  border-color: var(--g-c-brand, brand);
  
      
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
    }

    .g-plMxqItm:hover::before {
      border-style: solid;
  border-width: 0px 0px 0px 0px;
  border-color: var(--g-c-brand, brand);
  
      
    }

    .g-plMxqItm:hover .gp-button-icon {
      color: undefined;
    }

     .g-plMxqItm .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .g-plMxqItm:hover .gp-button-price {
      color: undefined;
    }

    .g-plMxqItm .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .g-plMxqItm .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .g-plMxqItm:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <button
      type="submit" data-id="g-plMxqItm" aria-label
      
      data-state="idle"
      class="g-plMxqItm gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3"
      style="--hvr-bg:#424242;--bg:#242424;--pl:13px;--pr:13px;--pt:13px;--pb:13px;--pl-tablet:13px;--pr-tablet:13px;--pt-tablet:13px;--pb-tablet:13px;--pl-mobile:13px;--pr-mobile:13px;--pt-mobile:13px;--pb-mobile:13px;--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--c:var(--g-c-text-3, text-3);--size:22px;--size-tablet:22px;--size-mobile:22px;--lh:150%;--fs:normal;--weight:400;--ls:0px;--lh-tablet:150%;--ls-mobile:0px"
    >
      
    <div
    class="gp-inline-flex">
    <span
        class="gp-inline-flex gp-button-icon gp-transition-colors gp-duration-300 gp-shrink-0 gp-items-center gp-justify-center group-data-[state=loading]/button:gp-invisible gp-z-1 [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
        style="--height-desktop:1em;--height-tablet:1em;--height-mobile:1em"
      ><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">
              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M32 256a16 16 0 0 1 16 -16h377.376l-100.704 -100.672a16 16 0 0 1 22.656 -22.656l128 128a16 16 0 0 1 0 22.656l-128 128a16 16 0 0 1 -22.656 -22.656L425.376 272H48A16 16 0 0 1 32 256z" /></svg></span>
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--size:22px;--size-tablet:22px;--size-mobile:22px;--c:var(--g-c-text-3, text-3)"
      >
        
      </span>
      </div>
    
    </button>
  </div>
  </gp-button>

  
    </div>
    </div>
   
    
      {% endform %}
    </gp-form>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-form.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
      </div>
       
      
    <div
      parentTag="Col" id="gsPJyeO83O" data-id="gsPJyeO83O"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--cg:var(--g-s-xl);--pc:start;--gtc:minmax(0, auto) minmax(0, auto) minmax(0, auto) minmax(0, auto);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gsPJyeO83O gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:center"
      class="gLQvRNVkcT gp-relative gp-flex gp-flex-col"
    >
      <div
    
     data-id="gumXyz7DJ-"
    role="presentation"
    class="gp-group/image gumXyz7DJ- gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:center"
    >
      <a
        class="pointer-events-auto gp-h-full gp-flex"
        href="https://www.facebook.com/groups/911760202804295" target="_self" title="Image Title"
        class="gp-h-full gp-w-full"
        style="border-radius:inherit;--jc:center"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" data-srcSet="{{ "gempages_572751041980793671-c52d14cd-3392-4b6f-83e1-8f11e8b17903.png" | file_url }}" srcset="{{ "gempages_572751041980793671-c52d14cd-3392-4b6f-83e1-8f11e8b17903.png" | file_url }}" />
      <source media="(max-width: 1024px)" data-srcSet="{{ "gempages_572751041980793671-c52d14cd-3392-4b6f-83e1-8f11e8b17903.png" | file_url }}" srcset="{{ "gempages_572751041980793671-c52d14cd-3392-4b6f-83e1-8f11e8b17903.png" | file_url }}" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex"
        src="{{ "gempages_572751041980793671-c52d14cd-3392-4b6f-83e1-8f11e8b17903.png" | file_url }}"
        data-src="{{ "gempages_572751041980793671-c52d14cd-3392-4b6f-83e1-8f11e8b17903.png" | file_url }}"
        width="100%"
        alt="Alt Image"
        style="--aspect:auto;--objf:cover;--w:50px;--w-tablet:50px;--w-mobile:44px;--h:auto;--h-tablet:auto;--h-mobile:auto"
      />
    
  </picture>
      </a>
  </div>
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:center"
      class="gbf0tq9EQn gp-relative gp-flex gp-flex-col"
    >
      <div
    
     data-id="gTDMMBb28j"
    role="presentation"
    class="gp-group/image gTDMMBb28j gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:center"
    >
      <a
        class="pointer-events-auto gp-h-full gp-flex"
        href="https://www.youtube.com/@zhuhaibeier" target="_self" title="Image Title"
        class="gp-h-full gp-w-full"
        style="border-radius:inherit;--jc:center"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" data-srcSet="{{ "gempages_572751041980793671-25e4be84-c40a-4aa6-9edc-8bcdd9ef944b.png" | file_url }}" srcset="{{ "gempages_572751041980793671-25e4be84-c40a-4aa6-9edc-8bcdd9ef944b.png" | file_url }}" />
      <source media="(max-width: 1024px)" data-srcSet="{{ "gempages_572751041980793671-25e4be84-c40a-4aa6-9edc-8bcdd9ef944b.png" | file_url }}" srcset="{{ "gempages_572751041980793671-25e4be84-c40a-4aa6-9edc-8bcdd9ef944b.png" | file_url }}" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex"
        src="{{ "gempages_572751041980793671-25e4be84-c40a-4aa6-9edc-8bcdd9ef944b.png" | file_url }}"
        data-src="{{ "gempages_572751041980793671-25e4be84-c40a-4aa6-9edc-8bcdd9ef944b.png" | file_url }}"
        width="100%"
        alt="Alt Image"
        style="--aspect:auto;--objf:cover;--w:50px;--w-tablet:50px;--w-mobile:44px;--h:auto;--h-tablet:auto;--h-mobile:auto"
      />
    
  </picture>
      </a>
  </div>
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:center;--d:none;--d-tablet:none;--d-mobile:none"
      class="gHY87MGegR gp-relative gp-flex gp-flex-col"
    >
      
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:center;--d:none;--d-tablet:none;--d-mobile:none"
      class="gMza2Z96_d gp-relative gp-flex gp-flex-col"
    >
      
    </div>
    </div>
   
    
    </div>
    </div>
   
    <div gp-el-wrapper style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-xl)" class="g8eWQi-vht ">
      
    <div
    data-id="g8eWQi-vht"
      class="gp-flex gp-justify-start"
    >
      <div
        style="--w:100%;--w-tablet:100%;--w-mobile:100%;--bs:solid;--bbw:1px;--bbw-tablet:1px;--bbw-mobile:1px;--t:gp-rotate(0deg);--bc:#EEEEEE"
        class="gp-block tablet:gp-block mobile:gp-block"
      ></div>
      <div
        class="gp-items-center gp-hidden tablet:gp-hidden mobile:gp-hidden"
        style="--w:100%;--w-tablet:100%;--w-mobile:100%;--bc:#EEEEEE;--t:gp-rotate(0deg)"
      >
        
    <div
      class="gp-hidden"
      style="--w:100%;--w-tablet:100%;--w-mobile:100%;--bc:#EEEEEE;--bs:solid;--bbw:1px;--bbw-tablet:1px;--bbw-mobile:1px;--minw:100px"
    ></div>
  
        
            <span
              class="gp-inline-flex gp-items-center gp-justify-center gp-flex-none"
              style="--mr:var(--g-s-xs);--w:20px;--h:20px;--t:gp-rotate(0);--c:var(--g-c-text-1, text-1)"
            >
              <svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M428.8 137.6h-86.177a115.52 115.52 0 0 0 2.176-22.4c0-47.914-35.072-83.2-92-83.2-45.314 0-57.002 48.537-75.707 78.784-7.735 12.413-16.994 23.317-25.851 33.253l-.131.146-.129.148C135.662 161.807 127.764 168 120.8 168h-2.679c-5.747-4.952-13.536-8-22.12-8H32c-17.673 0-32 12.894-32 28.8v230.4C0 435.106 14.327 448 32 448h64c8.584 0 16.373-3.048 22.12-8h2.679c28.688 0 67.137 40 127.2 40h21.299c62.542 0 98.8-38.658 99.94-91.145 12.482-17.813 18.491-40.785 15.985-62.791A93.148 93.148 0 0 0 393.152 304H428.8c45.435 0 83.2-37.584 83.2-83.2 0-45.099-38.101-83.2-83.2-83.2zm0 118.4h-91.026c12.837 14.669 14.415 42.825-4.95 61.05 11.227 19.646 1.687 45.624-12.925 53.625 6.524 39.128-10.076 61.325-50.6 61.325H248c-45.491 0-77.21-35.913-120-39.676V215.571c25.239-2.964 42.966-21.222 59.075-39.596 11.275-12.65 21.725-25.3 30.799-39.875C232.355 112.712 244.006 80 252.8 80c23.375 0 44 8.8 44 35.2 0 35.2-26.4 53.075-26.4 70.4h158.4c18.425 0 35.2 16.5 35.2 35.2 0 18.975-16.225 35.2-35.2 35.2zM88 384c0 13.255-10.745 24-24 24s-24-10.745-24-24 10.745-24 24-24 24 10.745 24 24z" /></svg>
            </span>
          
        
            <span
              class="gp-inline-flex gp-items-center gp-justify-center gp-flex-none gp-g-paragraph-1"
              style="--tt:horizontal;--c:var(--g-c-text-1, text-1);--mr:var(--g-s-s)"
            >
              Title
            </span>
          
        
    <div
      class="gp-flex"
      style="--w:100%;--w-tablet:100%;--w-mobile:100%;--bc:#EEEEEE;--bs:solid;--bbw:1px;--bbw-tablet:1px;--bbw-mobile:1px;--minw:100px"
    ></div>
  
      </div>
    </div>
  
      </div>
       
      
    <div
      parentTag="Col" id="gd3P3K5UZx" data-id="gd3P3K5UZx"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:none;--d-mobile:grid;--d-tablet:none;--op:100%;--mb:var(--g-s-l);--mb-mobile:auto;--pl-mobile:15px;--pr-mobile:15px;--cg:var(--g-s-2xl);--pc:start;--gtc:minmax(0, 6fr) minmax(0, 6fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gd3P3K5UZx gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:center"
      class="gGDzx9cIWN gp-relative gp-flex gp-flex-col"
    >
      <div
    
     data-id="gDQ-fbuyVi"
    role="presentation"
    class="gp-group/image gDQ-fbuyVi gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb-mobile:var(--g-s-xl);--ta:right;--ta-mobile:left"
    >
      <div
        class="pointer-events-auto gp-h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="border-radius:inherit;--jc:right;--jc-mobile:left"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" data-srcSet="https://ucarecdn.com/9849e951-051f-46b1-beeb-413f525472d6/-/format/auto/" srcset="https://ucarecdn.com/9849e951-051f-46b1-beeb-413f525472d6/-/format/auto/" />
      <source media="(max-width: 1024px)" data-srcSet="https://ucarecdn.com/9849e951-051f-46b1-beeb-413f525472d6/-/format/auto/" srcset="https://ucarecdn.com/9849e951-051f-46b1-beeb-413f525472d6/-/format/auto/" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex"
        src="https://ucarecdn.com/9849e951-051f-46b1-beeb-413f525472d6/-/format/auto/"
        data-src="https://ucarecdn.com/9849e951-051f-46b1-beeb-413f525472d6/-/format/auto/"
        width="100%"
        alt="Alt Image"
        style="--aspect:auto;--objf:cover;--w:400px;--w-tablet:400px;--w-mobile:345px;--h:auto;--h-tablet:auto;--h-mobile:auto"
      />
    
  </picture>
      </div>
  </div>
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:center"
      class="g33rtt82HD gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gejxm3EZuN">
    <div
      parentTag="Col"
        class="gejxm3EZuN "
        style="--tt:default;--ta:left;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#575757;--size:13px;--lh:150%;--fs:normal;--weight:400;--ls:0px;--size-tablet:13px;--lh-tablet:150%;--size-mobile:12px;--ls-mobile:0px;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggejxm3EZuN_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
       
      
    <div
      parentTag="Col" id="gHrhCdgagV" data-id="gHrhCdgagV"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:none;--d-tablet:grid;--op:100%;--mb:0px;--pl-tablet:15px;--pr-tablet:15px;--cg:var(--g-s-2xl);--pc:start;--gtc:minmax(0, 6fr) minmax(0, 6fr);--w:var(--g-ct-w, 1200px);--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gHrhCdgagV gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:center"
      class="gVGPToqMKh gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gikU_m39t-">
    <div
      parentTag="Col"
        class="gikU_m39t- "
        style="--tt:default;--ta:left;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#575757;--fs:normal;--ff:var(--g-font-body, body);--weight:400;--ls:normal;--size:13px;--size-tablet:14px;--size-mobile:12px;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggikU_m39t-_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:center"
      class="gSLdF_yRuw gp-relative gp-flex gp-flex-col"
    >
      <div
    
     data-id="gDSx3Qu6Za"
    role="presentation"
    class="gp-group/image gDSx3Qu6Za gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:right"
    >
      <div
        class="pointer-events-auto gp-h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="border-radius:inherit;--jc:right"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" data-srcSet="{{ "gempages_572751041980793671-a9a2fa0e-4aae-4566-8184-10036830a2b4.png" | file_url }}" srcset="{{ "gempages_572751041980793671-a9a2fa0e-4aae-4566-8184-10036830a2b4.png" | file_url }}" />
      <source media="(max-width: 1024px)" data-srcSet="{{ "gempages_572751041980793671-a9a2fa0e-4aae-4566-8184-10036830a2b4.png" | file_url }}" srcset="{{ "gempages_572751041980793671-a9a2fa0e-4aae-4566-8184-10036830a2b4.png" | file_url }}" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex"
        src="{{ "gempages_572751041980793671-a9a2fa0e-4aae-4566-8184-10036830a2b4.png" | file_url }}"
        data-src="{{ "gempages_572751041980793671-a9a2fa0e-4aae-4566-8184-10036830a2b4.png" | file_url }}"
        width="100%"
        alt="Alt Image"
        style="--aspect:auto;--objf:cover;--w:400px;--w-tablet:400px;--w-mobile:400px;--h:auto;--h-tablet:auto;--h-mobile:auto"
      />
    
  </picture>
      </div>
  </div>
    </div>
    </div>
   
    
    </div>
    </div>
  
            </section>
           
    


{% schema %}
  {
    
    "name": "Section 16",
    "tag": "section",
    "class": "gps-572876472692245656 gps gpsil",
    "settings": [{"type":"paragraph","content":"Section design by GemPages. [Click here to start design](https://admin.shopify.com/store/i0pixe-10/apps/gempages-cro/app/shopify/edit?pageType=GP_PRODUCT&editorId=572751048691811510&sectionId=572876472692245656)"},{"type":"select","label":"Section Preload","id":"section_preload","options":[{"label":"On","value":"true"},{"label":"Off","value":"false"},{"label":"Off Lazy Script","value":"off_lazy_script"}],"default":"false"},{"type":"text","label":"Checksum","id":"checksum"},{"type":"html","id":"ggL4guCxE3v_text","label":"ggL4guCxE3v_text","default":"<p>About Us&nbsp;</p>"},{"type":"html","id":"ggyef5ZqiRM_text","label":"ggyef5ZqiRM_text","default":"<p><a href=\"#\">About Us</a></p>"},{"type":"html","id":"ggue3T1Gg1__text","label":"ggue3T1Gg1__text","default":"<p><a href=\"#\">FAQ</a></p>"},{"type":"html","id":"ggmsr_bm3rg_text","label":"ggmsr_bm3rg_text","default":"<p><a href=\"#\">Terms &amp; Conditions</a></p>"},{"type":"html","id":"gg1Tlz4x_IS_text","label":"gg1Tlz4x_IS_text","default":"<p><a href=\"#\">Privacy Policy</a></p>"},{"type":"html","id":"ggh1KWxyxyt_text","label":"ggh1KWxyxyt_text","default":"<p>Categories</p>"},{"type":"html","id":"ggE-Mp9MxnQ_text","label":"ggE-Mp9MxnQ_text","default":"<p>Home<a href=\"#\">#</a></p>"},{"type":"html","id":"ggSpMJqnpz8_text","label":"ggSpMJqnpz8_text","default":"<p><u>3D Printing</u><a href=\"#\"><u>#</u></a></p>"},{"type":"html","id":"gglgrobpOZZ_text","label":"gglgrobpOZZ_text","default":"<p><u>Accessories</u><a href=\"#\"><u>#</u></a></p>"},{"type":"html","id":"ggKynVJDEwA_text","label":"ggKynVJDEwA_text","default":"<p><u>Support</u><a href=\"#\"><u>#</u></a></p>"},{"type":"html","id":"gg79y1dF4K5_text","label":"gg79y1dF4K5_text","default":"<p>Our Newsletter</p>"},{"type":"html","id":"gggJjorMUA0_text","label":"gggJjorMUA0_text","default":"<p>Register to get the latest IdeaFormer offers and exclusive information</p>"},{"type":"html","id":"gg3CFqX-vX5_successMessage","label":"gg3CFqX-vX5_successMessage","default":"Thanks for contacting us. We'll get back to you as soon as possible."},{"type":"html","id":"gg3CFqX-vX5_errorMessage","label":"gg3CFqX-vX5_errorMessage","default":"Can’t send email. Please try again later."},{"type":"html","id":"ggjT6N53PzN_placeholder","label":"ggjT6N53PzN_placeholder","default":"Enter your email"},{"type":"html","id":"ggejxm3EZuN_text","label":"ggejxm3EZuN_text","default":"<p>Copyright © 2022 GemThemes. All Rights Reserved.</p>"},{"type":"html","id":"ggikU_m39t-_text","label":"ggikU_m39t-_text","default":"<p>@ 2025 IdeaFormer</p>"}],
    "blocks": [{"type": "@app"}],
    "locales": {}
  }
{% endschema %}
