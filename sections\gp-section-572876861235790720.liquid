

<style {% if section.settings.section_preload == "false" %} class="gps-link gps-style" type="text/disabled-css" {% endif %}>.gps-572876861235790720.gps.gpsil [style*="--aspect:"]{aspect-ratio:var(--aspect)}.gps-572876861235790720.gps.gpsil [style*="--bg:"]{background:var(--bg)}.gps-572876861235790720.gps.gpsil [style*="--bga:"]{background-attachment:var(--bga)}.gps-572876861235790720.gps.gpsil [style*="--bgc:"]{background-color:var(--bgc)}.gps-572876861235790720.gps.gpsil [style*="--bgi:"]{background-image:var(--bgi)}.gps-572876861235790720.gps.gpsil [style*="--bgp:"]{background-position:var(--bgp)}.gps-572876861235790720.gps.gpsil [style*="--bgr:"]{background-repeat:var(--bgr)}.gps-572876861235790720.gps.gpsil [style*="--bgs:"]{background-size:var(--bgs)}.gps-572876861235790720.gps.gpsil [style*="--b:"]{border:var(--b)}.gps-572876861235790720.gps.gpsil [style*="--bb:"]{border-bottom:var(--bb)}.gps-572876861235790720.gps.gpsil [style*="--bc:"]{border-color:var(--bc)}.gps-572876861235790720.gps.gpsil [style*="--bblr:"]{border-bottom-left-radius:var(--bblr)}.gps-572876861235790720.gps.gpsil [style*="--bbrr:"]{border-bottom-right-radius:var(--bbrr)}.gps-572876861235790720.gps.gpsil [style*="--bs:"]{border-style:var(--bs)}.gps-572876861235790720.gps.gpsil [style*="--bt:"]{border-top:var(--bt)}.gps-572876861235790720.gps.gpsil [style*="--btlr:"]{border-top-left-radius:var(--btlr)}.gps-572876861235790720.gps.gpsil [style*="--btrr:"]{border-top-right-radius:var(--btrr)}.gps-572876861235790720.gps.gpsil [style*="--bw:"]{border-width:var(--bw)}.gps-572876861235790720.gps.gpsil [style*="--bottom:"]{bottom:var(--bottom)}.gps-572876861235790720.gps.gpsil [style*="--shadow:"]{box-shadow:var(--shadow)}.gps-572876861235790720.gps.gpsil [style*="--c:"]{color:var(--c)}.gps-572876861235790720.gps.gpsil [style*="--cg:"]{-moz-column-gap:var(--cg);column-gap:var(--cg)}.gps-572876861235790720.gps.gpsil [style*="--gtc:"]{grid-template-columns:var(--gtc)}.gps-572876861235790720.gps.gpsil [style*="--h:"]{height:var(--h)}.gps-572876861235790720.gps.gpsil [style*="--jc:"]{justify-content:var(--jc)}.gps-572876861235790720.gps.gpsil [style*="--left:"]{left:var(--left)}.gps-572876861235790720.gps.gpsil [style*="--m:"]{margin:var(--m)}.gps-572876861235790720.gps.gpsil [style*="--mb:"]{margin-bottom:var(--mb)}.gps-572876861235790720.gps.gpsil [style*="--ml:"]{margin-left:var(--ml)}.gps-572876861235790720.gps.gpsil [style*="--maxw:"]{max-width:var(--maxw)}.gps-572876861235790720.gps.gpsil [style*="--minw:"]{min-width:var(--minw)}.gps-572876861235790720.gps.gpsil [style*="--op:"]{opacity:var(--op)}.gps-572876861235790720.gps.gpsil [style*="--o:"]{order:var(--o)}.gps-572876861235790720.gps.gpsil [style*="--pc:"]{place-content:var(--pc)}.gps-572876861235790720.gps.gpsil [style*="--p:"]{padding:var(--p)}.gps-572876861235790720.gps.gpsil [style*="--pb:"]{padding-bottom:var(--pb)}.gps-572876861235790720.gps.gpsil [style*="--pl:"]{padding-left:var(--pl)}.gps-572876861235790720.gps.gpsil [style*="--pr:"]{padding-right:var(--pr)}.gps-572876861235790720.gps.gpsil [style*="--pt:"]{padding-top:var(--pt)}.gps-572876861235790720.gps.gpsil [style*="--right:"]{right:var(--right)}.gps-572876861235790720.gps.gpsil [style*="--top:"]{top:var(--top)}.gps-572876861235790720.gps.gpsil [style*="--t:"]{transform:var(--t)}.gps-572876861235790720.gps.gpsil [style*="--w:"]{width:var(--w)}@media only screen and (max-width:1024px){.gps-572876861235790720.gps.gpsil [style*="--bottom-tablet:"]{bottom:var(--bottom-tablet)}.gps-572876861235790720.gps.gpsil [style*="--cg-tablet:"]{-moz-column-gap:var(--cg-tablet);column-gap:var(--cg-tablet)}.gps-572876861235790720.gps.gpsil [style*="--h-tablet:"]{height:var(--h-tablet)}.gps-572876861235790720.gps.gpsil [style*="--left-tablet:"]{left:var(--left-tablet)}.gps-572876861235790720.gps.gpsil [style*="--ml-tablet:"]{margin-left:var(--ml-tablet)}.gps-572876861235790720.gps.gpsil [style*="--maxw-tablet:"]{max-width:var(--maxw-tablet)}.gps-572876861235790720.gps.gpsil [style*="--minw-tablet:"]{min-width:var(--minw-tablet)}.gps-572876861235790720.gps.gpsil [style*="--pl-tablet:"]{padding-left:var(--pl-tablet)}.gps-572876861235790720.gps.gpsil [style*="--pr-tablet:"]{padding-right:var(--pr-tablet)}.gps-572876861235790720.gps.gpsil [style*="--right-tablet:"]{right:var(--right-tablet)}.gps-572876861235790720.gps.gpsil [style*="--top-tablet:"]{top:var(--top-tablet)}.gps-572876861235790720.gps.gpsil [style*="--w-tablet:"]{width:var(--w-tablet)}}@media only screen and (max-width:767px){.gps-572876861235790720.gps.gpsil [style*="--bottom-mobile:"]{bottom:var(--bottom-mobile)}.gps-572876861235790720.gps.gpsil [style*="--cg-mobile:"]{-moz-column-gap:var(--cg-mobile);column-gap:var(--cg-mobile)}.gps-572876861235790720.gps.gpsil [style*="--h-mobile:"]{height:var(--h-mobile)}.gps-572876861235790720.gps.gpsil [style*="--left-mobile:"]{left:var(--left-mobile)}.gps-572876861235790720.gps.gpsil [style*="--ml-mobile:"]{margin-left:var(--ml-mobile)}.gps-572876861235790720.gps.gpsil [style*="--maxw-mobile:"]{max-width:var(--maxw-mobile)}.gps-572876861235790720.gps.gpsil [style*="--minw-mobile:"]{min-width:var(--minw-mobile)}.gps-572876861235790720.gps.gpsil [style*="--pb-mobile:"]{padding-bottom:var(--pb-mobile)}.gps-572876861235790720.gps.gpsil [style*="--pl-mobile:"]{padding-left:var(--pl-mobile)}.gps-572876861235790720.gps.gpsil [style*="--pr-mobile:"]{padding-right:var(--pr-mobile)}.gps-572876861235790720.gps.gpsil [style*="--pt-mobile:"]{padding-top:var(--pt-mobile)}.gps-572876861235790720.gps.gpsil [style*="--right-mobile:"]{right:var(--right-mobile)}.gps-572876861235790720.gps.gpsil [style*="--top-mobile:"]{top:var(--top-mobile)}.gps-572876861235790720.gps.gpsil [style*="--w-mobile:"]{width:var(--w-mobile)}}.gps-572876861235790720 .-gp-translate-x-1\/2,.gps-572876861235790720 .-gp-translate-y-1\/2,.gps-572876861235790720 .gp-rotate-0,.gps-572876861235790720 .gp-rotate-180,.gps-572876861235790720 .mobile\:gp-rotate-0,.gps-572876861235790720 .mobile\:gp-rotate-180,.gps-572876861235790720 .tablet\:gp-rotate-0,.gps-572876861235790720 .tablet\:gp-rotate-180{--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1}.gps-572876861235790720 .gp-shadow-md{--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000}.gps-572876861235790720 .gp-visible{visibility:visible}.gps-572876861235790720 .gp-invisible{visibility:hidden}.gps-572876861235790720 .gp-static{position:static}.gps-572876861235790720 .gp-absolute{position:absolute}.gps-572876861235790720 .\!gp-relative{position:relative!important}.gps-572876861235790720 .gp-relative{position:relative}.gps-572876861235790720 .gp-left-0{left:0}.gps-572876861235790720 .gp-left-1\/2{left:50%}.gps-572876861235790720 .gp-right-0{right:0}.gps-572876861235790720 .gp-top-1\/2{top:50%}.gps-572876861235790720 .gp-z-1{z-index:1}.gps-572876861235790720 .gp-mx-auto{margin-left:auto;margin-right:auto}.gps-572876861235790720 .gp-my-0{margin-bottom:0;margin-top:0}.gps-572876861235790720 .gp-mb-0{margin-bottom:0}.gps-572876861235790720 .gp-block{display:block}.gps-572876861235790720 .\!gp-flex{display:flex!important}.gps-572876861235790720 .gp-flex{display:flex}.gps-572876861235790720 .gp-grid{display:grid}.gps-572876861235790720 .\!gp-hidden{display:none!important}.gps-572876861235790720 .gp-hidden{display:none}.gps-572876861235790720 .gp-aspect-square{aspect-ratio:1/1}.gps-572876861235790720 .gp-h-full{height:100%}.gps-572876861235790720 .\!gp-min-h-full{min-height:100%!important}.gps-572876861235790720 .gp-w-\[12px\]{width:12px}.gps-572876861235790720 .gp-w-\[68px\]{width:68px}.gps-572876861235790720 .gp-w-full{width:100%}.gps-572876861235790720 .\!gp-max-w-none{max-width:none!important}.gps-572876861235790720 .gp-max-w-full{max-width:100%}.gps-572876861235790720 .gp-shrink-0{flex-shrink:0}.gps-572876861235790720 .-gp-translate-x-1\/2{--tw-translate-x:-50%}.gps-572876861235790720 .-gp-translate-x-1\/2,.gps-572876861235790720 .-gp-translate-y-1\/2{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.gps-572876861235790720 .-gp-translate-y-1\/2{--tw-translate-y:-50%}.gps-572876861235790720 .gp-rotate-0{--tw-rotate:0deg}.gps-572876861235790720 .gp-rotate-0,.gps-572876861235790720 .gp-rotate-180{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.gps-572876861235790720 .gp-rotate-180{--tw-rotate:180deg}.gps-572876861235790720 .gp-cursor-pointer{cursor:pointer}.gps-572876861235790720 .gp-select-none{-webkit-user-select:none;-moz-user-select:none;user-select:none}.gps-572876861235790720 .gp-grid-rows-\[1fr\]{grid-template-rows:1fr}.gps-572876861235790720 .\!gp-flex-row{flex-direction:row!important}.gps-572876861235790720 .gp-flex-row{flex-direction:row}.gps-572876861235790720 .gp-flex-col{flex-direction:column}.gps-572876861235790720 .\!gp-flex-nowrap{flex-wrap:nowrap!important}.gps-572876861235790720 .gp-items-center{align-items:center}.gps-572876861235790720 .gp-justify-center{justify-content:center}.gps-572876861235790720 .gp-justify-between{justify-content:space-between}.gps-572876861235790720 .gp-gap-2{gap:8px}.gps-572876861235790720 .gp-gap-y-0{row-gap:0}.gps-572876861235790720 .gp-overflow-hidden{overflow:hidden}.gps-572876861235790720 .gp-rounded-full{border-radius:9999px}.gps-572876861235790720 .gp-bg-black{--tw-bg-opacity:1;background-color:rgb(0 0 0/var(--tw-bg-opacity))}.gps-572876861235790720 .gp-bg-cover{background-size:cover}.gps-572876861235790720 .gp-bg-center{background-position:50%}.gps-572876861235790720 .gp-text-center{text-align:center}.gps-572876861235790720 .gp-text-gray-500{--tw-text-opacity:1;color:rgb(107 114 128/var(--tw-text-opacity))}.gps-572876861235790720 .gp-opacity-100{opacity:1}.gps-572876861235790720 .gp-shadow-md{--tw-shadow:0 4px 6px -1px rgba(0,0,0,.1),0 2px 4px -2px rgba(0,0,0,.1);--tw-shadow-colored:0 4px 6px -1px var(--tw-shadow-color),0 2px 4px -2px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)}.gps-572876861235790720 .gp-transition-colors{transition-duration:.15s;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-572876861235790720 .gp-duration-200{transition-duration:.2s}.gps-572876861235790720 .gp-ease-in-out{transition-timing-function:cubic-bezier(.4,0,.2,1)}@media (max-width:1024px){.gps-572876861235790720 .tablet\:gp-static{position:static}.gps-572876861235790720 .tablet\:\!gp-relative{position:relative!important}.gps-572876861235790720 .tablet\:gp-left-0{left:0}.gps-572876861235790720 .tablet\:gp-right-0{right:0}.gps-572876861235790720 .tablet\:gp-block{display:block}.gps-572876861235790720 .tablet\:\!gp-flex{display:flex!important}.gps-572876861235790720 .tablet\:\!gp-hidden{display:none!important}.gps-572876861235790720 .tablet\:gp-hidden{display:none}.gps-572876861235790720 .tablet\:\!gp-min-h-full{min-height:100%!important}.gps-572876861235790720 .tablet\:gp-rotate-0{--tw-rotate:0deg}.gps-572876861235790720 .tablet\:gp-rotate-0,.gps-572876861235790720 .tablet\:gp-rotate-180{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.gps-572876861235790720 .tablet\:gp-rotate-180{--tw-rotate:180deg}.gps-572876861235790720 .tablet\:\!gp-flex-row{flex-direction:row!important}.gps-572876861235790720 .tablet\:gp-flex-row{flex-direction:row}.gps-572876861235790720 .tablet\:\!gp-flex-nowrap{flex-wrap:nowrap!important}.gps-572876861235790720 .tablet\:gp-px-0{padding-left:0;padding-right:0}}@media (max-width:767px){.gps-572876861235790720 .mobile\:gp-static{position:static}.gps-572876861235790720 .mobile\:gp-left-0{left:0}.gps-572876861235790720 .mobile\:gp-right-0{right:0}.gps-572876861235790720 .mobile\:gp-block{display:block}.gps-572876861235790720 .mobile\:\!gp-hidden{display:none!important}.gps-572876861235790720 .mobile\:gp-hidden{display:none}.gps-572876861235790720 .mobile\:\!gp-min-h-full{min-height:100%!important}.gps-572876861235790720 .mobile\:gp-rotate-0{--tw-rotate:0deg}.gps-572876861235790720 .mobile\:gp-rotate-0,.gps-572876861235790720 .mobile\:gp-rotate-180{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.gps-572876861235790720 .mobile\:gp-rotate-180{--tw-rotate:180deg}.gps-572876861235790720 .mobile\:\!gp-flex-row{flex-direction:row!important}.gps-572876861235790720 .mobile\:gp-flex-row{flex-direction:row}.gps-572876861235790720 .mobile\:\!gp-flex-nowrap{flex-wrap:nowrap!important}.gps-572876861235790720 .mobile\:gp-px-0{padding-left:0;padding-right:0}}.gps-572876861235790720 .\[\&\>svg\]\:gp-h-full>svg{height:100%}.gps-572876861235790720 .\[\&\>svg\]\:gp-w-full>svg{width:100%}.gps-572876861235790720 .\[\&_\*\]\:gp-max-w-full *{max-width:100%}.gps-572876861235790720 .\[\&_\>_article\]\:gp-aspect-\[var\(--aspect\)\]>article{aspect-ratio:var(--aspect)}@media (max-width:1024px){.gps-572876861235790720 .tablet\:\[\&_\>_article\]\:gp-aspect-\[var\(--aspect-tablet\2c _var\(--aspect\)\)\]>article{aspect-ratio:var(--aspect-tablet,var(--aspect))}}@media (max-width:767px){.gps-572876861235790720 .mobile\:\[\&_\>_article\]\:gp-aspect-\[var\(--aspect-mobile\2c _var\(--aspect-tablet\2c _var\(--aspect\)\)\)\]>article{aspect-ratio:var(--aspect-mobile,var(--aspect-tablet,var(--aspect)))}}</style>

       
      
            <section
              class="gp-mx-auto gp-max-w-full {% if section.settings.section_preload == "false" %}gps-lazy {% endif %}"
              style="--w:100%;--w-tablet:100%;--w-mobile:100%;--pl:0px;--pl-tablet:0px;--pl-mobile:0px;--pr:0px;--pr-tablet:0px;--pr-mobile:0px"
            >
              
    <div
      id="gSR-_3UBl_" data-id="gSR-_3UBl_"
        style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pt:0px;--pl:60px;--pb:100px;--pr:60px;--pt-mobile:70px;--pl-mobile:15px;--pb-mobile:56px;--pr-mobile:0px;--cg:32px;--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:#FFFFFF;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gSR-_3UBl_ gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="g7-wPizb3C gp-relative gp-flex gp-flex-col"
    >
      
    <gp-carousel data-id="gOje5emP94"  id="gp-root-carousel-gOje5emP94-{{section.id}}" class="  gp-group/carousel gp-flex" gp-data='{"id":"gOje5emP94-{{section.id}}","setting":{"animationMode":"ease-in","arrowBackgroundColor":"transparent","arrowBorder":{"desktop":{"border":"none","borderType":"none","borderWidth":"1px","isCustom":false,"width":"1px 1px 1px 1px"}},"arrowButtonSize":{"desktop":{"height":"32px","padding":{"linked":true},"shapeLinked":true,"shapeValue":"1/1","width":"32px"}},"arrowCustom":"<svg height=\"20\" width=\"20\" xmlns=\"http://www.w3.org/2000/svg\"  viewBox=\"0 0 256 256\" fill=\"currentColor\" data-id=\"508817723121664360\">\n              <path fill=\"currentColor\" strokeLinecap=\"round\" strokeLinejoin=\"round\" fill=\"currentColor\" d=\"M128,24A104,104,0,1,0,232,128,104.11,104.11,0,0,0,128,24Zm29.66,109.66-40,40a8,8,0,0,1-11.32-11.32L140.69,128,106.34,93.66a8,8,0,0,1,11.32-11.32l40,40A8,8,0,0,1,157.66,133.66Z\" /></svg>","arrowCustomColor":"#ECECEC","arrowGapToEachSide":"16","arrowIconSize":{"desktop":60},"autoplay":false,"autoplayTimeout":2,"background":{"desktop":{"attachment":"scroll","color":"transparent","image":{"height":0,"src":"","width":0},"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"color"}},"childItem":["Slide 1","Slide 2","Slide 3","Slide 4","Slide 5"],"controlOverContent":{"desktop":true},"dot":{"desktop":true,"mobile":true,"tablet":true},"dotActiveColor":{"desktop":"line-3"},"dotColor":{"desktop":"bg-1"},"dotGapToCarousel":{"desktop":16},"dotSize":{"desktop":12},"dotStyle":{"desktop":"none"},"enableDrag":{"desktop":true},"itemNumber":{"desktop":5,"mobile":2,"tablet":4},"label":true,"loop":{"desktop":true},"navigationStyle":{"desktop":"outside","mobile":"none"},"pauseOnHover":true,"roundedArrow":{"desktop":{"radiusType":"small"}},"rtl":false,"runPreview":false,"showWhenHover":false,"sneakPeak":{"desktop":true},"sneakPeakOffsetCenter":{"desktop":50},"sneakPeakOffsetForward":{"desktop":10},"sneakPeakType":{"desktop":"forward"},"vertical":{"desktop":false}},"styles":{"align":{"desktop":"center"},"borderContent":{"border":"none","borderType":"none","borderWidth":"1px","color":"#121212","isCustom":true,"position":"all","width":"1px 1px 1px 1px"},"carouselShadow":{"angle":90,"blur":"12px","color":"#121212","distance":"4px","spread":"0px","type":"shadow-1"},"fullWidth":{"desktop":false,"mobile":false,"tablet":false},"hasActiveShadow":false,"playSpeed":500,"roundedContent":{"radiusType":"none"},"sizeSetting":{"desktop":{"gap":"","height":"auto","shapeLinked":false,"width":"default"}},"spacing":{"desktop":"12","mobile":"24"}},"isHiddenArrowWhenDisabled":true}' style="--jc:center">
      <div class="gp-hidden gp-aspect-square gp-w-[12px] gp-rounded-full gp-shadow-md"></div>
      <div
        
        class="gp-relative gp-my-0 tablet:gp-px-0 gp-max-w-full mobile:gp-px-0 gp-carousel-wrapper mobile:gp-block tablet:gp-block gp-block gOje5emP94"
        style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-l);--w:var(--g-ct-w);--w-tablet:var(--g-ct-w);--w-mobile:var(--g-ct-w);--h:auto;--h-tablet:auto;--h-mobile:auto"
      >
        <div
          class="gp-h-full gp-relative gp-mx-auto gp-flex gp-items-center gp-justify-between mobile:!gp-flex-row tablet:!gp-flex-row !gp-flex-row"
          style="--h:100%;--h-tablet:100%;--h-mobile:100%;gap:16px"
        >
          
    <button
      type="button"
      aria-label='Carousel Back Arrow'
      class="gp-z-1 gp-carousel-action-back gem-slider-previous gOje5emP94-{{section.id}} gp-carousel-arrow-gOje5emP94 gp-items-center gp-justify-center gp-overflow-hidden gp-shrink-0  gp-cursor-pointer gp-text-gray-500 !gp-flex !gp-relative  tablet:!gp-flex tablet:!gp-relative  mobile:!gp-hidden"
      style="--left:initial;--top:initial;--right:initial;--bottom:;--left-tablet:initial;--top-tablet:initial;--right-tablet:initial;--bottom-tablet:;--left-mobile:initial;--top-mobile:initial;--right-mobile:initial;--bottom-mobile:;--d:flex;--d-tablet:flex;--d-mobile:none;background-color:transparent;--w:32px;--h:32px"
      onClick={onClick}
      
    >
      <div 
  class="gp-flex gp-items-center gp-justify-center [&>svg]:gp-h-full [&>svg]:gp-w-full gp-rotate-180 tablet:gp-rotate-180 mobile:gp-rotate-180"
  style="--c:#ECECEC;--w:60px;--w-tablet:60px;--w-mobile:60px;--h:60px;--h-tablet:60px;--h-mobile:60px"
  >
    <svg height="20" width="20" xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 256 256" fill="currentColor" data-id="508817723121664360">
              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M128,24A104,104,0,1,0,232,128,104.11,104.11,0,0,0,128,24Zm29.66,109.66-40,40a8,8,0,0,1-11.32-11.32L140.69,128,106.34,93.66a8,8,0,0,1,11.32-11.32l40,40A8,8,0,0,1,157.66,133.66Z" /></svg>
    </div>
      <style>
    .gp-carousel-arrow-gOje5emP94 {
      border-radius: var(--g-radius-small);
    }
    .gp-carousel-arrow-gOje5emP94::before {
      content: '';
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      z-index: 10;
      border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
      border-radius: var(--g-radius-small);
    }
    @media only screen and (max-width: 1024px) and (min-width: 768px) {
      .gp-carousel-arrow-gOje5emP94 {
        border-radius: var(--g-radius-small);
      }
      .gp-carousel-arrow-gOje5emP94::before {
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        border-radius: var(--g-radius-small);
      }
    }
    @media only screen and (max-width: 768px) {
      .gp-carousel-arrow-gOje5emP94 {
        border-radius: var(--g-radius-small);
      }
      .gp-carousel-arrow-gOje5emP94::before {
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        border-radius: var(--g-radius-small);
      }
    }
  </style>
    </button>
  
          <div id="gp-carousel-gOje5emP94-{{section.id}}" class="gem-slider gp-h-full gp-overflow-hidden gp-select-none mobile:!gp-flex-nowrap tablet:!gp-flex-nowrap !gp-flex-nowrap mobile:!gp-min-h-full tablet:!gp-min-h-full !gp-min-h-full"
          style="--cg-mobile:24px;--cg-tablet:12px;--cg:12px">
          
    <div
      media-type="{{media.media_type}}"
      media-poster="{{media.preview_image.src}}"
      media-source-url="{{media.sources.first.url}}"
      media-last-source-url="{{mediaSourceUrl}}"
      external-id="{{media.external_id}}"
      grid-index="{{forloop.index}}"
      
      label="Carousel Item" tag="CarouselItem" type="component"
      style="--bgc:transparent;--minw:calc(100% / 4.9 - 9.551020408163266px);--minw-tablet:calc(100% / 3.9 - 8.923076923076923px);--minw-mobile:calc(100% / 1.9 - 11.368421052631579px);--maxw:calc(100% / 4.9 - 9.551020408163266px);--maxw-tablet:calc(100% / 3.9 - 8.923076923076923px);--maxw-mobile:calc(100% / 1.9 - 11.368421052631579px);--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ml:0px;--ml-tablet:0px;--ml-mobile:0px"
      class="gem-slider-item gp-w-full gem-slider-item- gp-child-item-gOje5emP94 goil51vphk"
      data-index="0"
    >
      <div
        class="gp-w-full gp-h-full",
        style="--shadow:none"
      >
      
    <div
      
      data-id="gKNlqioMwW"
      class="!gp-max-w-none gp-bg-black gp-overflow-hidden [&_>_article]:gp-aspect-[var(--aspect)] tablet:[&_>_article]:gp-aspect-[var(--aspect-tablet,_var(--aspect))] mobile:[&_>_article]:gp-aspect-[var(--aspect-mobile,_var(--aspect-tablet,_var(--aspect)))] gp-relative gKNlqioMwW"
      style="--bs:solid;--bw:1px 1px 1px 1px;--bc:#242424;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--bblr:10px;--bbrr:10px;--btlr:10px;--btrr:10px;--aspect:auto"
    >
      
    <gp-lite-youtube-embed gp-data='{"lazy":true,"style":{"--aspect":"auto"},"videoTitle":"Video","iframeSrc":"https://www.youtube-nocookie.com/embed/sE8HkaP0fEU?autoplay=1&state=1&mute=1&&controls=1"}'>
      
      
      <article
        aria-hidden
        class="gp-relative gp-bg-cover gp-bg-center gp-lite-youtube-embed-article gp_lazybg"
        data-title="Video"
        style="--bgi:url('{{ 'gempages_572751041980793671-f0940b64-02ca-463a-bf46-0542a17c5397.jpg' | file_url }}');--aspect:auto"
      >
        
  <img
      id="gp-video-gKNlqioMwW"
      
      draggable="false"
      class="gp-invisible gp-w-full gp-h-full gp_lazyforbg"
      data-src="{{ 'gempages_572751041980793671-f0940b64-02ca-463a-bf46-0542a17c5397.jpg' | file_url }}" data-srcset="{{ 'gempages_572751041980793671-f0940b64-02ca-463a-bf46-0542a17c5397.jpg' | file_url }}" src="{{ 'gempages_572751041980793671-f0940b64-02ca-463a-bf46-0542a17c5397.jpg' | file_url }}" width="2237" height="1678" alt="Poster"
      quality-type={}
      quality-percent={}
      data-sizes="auto"
      sizes="100vw"
      
    />
  
        <button
          type="button"
          class="gp-absolute gp-top-1/2 gp-left-1/2 gp-w-[68px] -gp-translate-x-1/2 -gp-translate-y-1/2 gp-lite-youtube-embed-button gp-visible gp-opacity-100"
          aria-label="Play"
        >
          <svg height="100%" version="1.1" viewBox="0 0 68 48" width="100%">
            <path
              d="M66.52,7.74c-0.78-2.93-2.49-5.41-5.42-6.19C55.79,.13,34,0,34,0S12.21,.13,6.9,1.55 C3.97,2.33,2.27,4.81,1.48,7.74C0.06,13.05,0,24,0,24s0.06,10.95,1.48,16.26c0.78,2.93,2.49,5.41,5.42,6.19 C12.21,47.87,34,48,34,48s21.79-0.13,27.1-1.55c2.93-0.78,4.64-3.26,5.42-6.19C67.94,34.95,68,24,68,24S67.94,13.05,66.52,7.74z"
              fill="#f00"
            ></path>
            <path d="M 45,24 27,14 27,34" fill="#fff"></path>
          </svg>
        </button>
        
      </article>
    </gp-lite-youtube-embed>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-lite-youtube-embed.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
    </div>
  
      <style id="custom-css-gKNlqioMwW">
        .gKNlqioMwW[data-component-label="Video"] {

}
      </style>
    
      </div>
    </div>
  
    <div
      media-type="{{media.media_type}}"
      media-poster="{{media.preview_image.src}}"
      media-source-url="{{media.sources.first.url}}"
      media-last-source-url="{{mediaSourceUrl}}"
      external-id="{{media.external_id}}"
      grid-index="{{forloop.index}}"
      
      label="Carousel Item" tag="CarouselItem" type="component"
      style="--bgc:transparent;--minw:calc(100% / 4.9 - 9.551020408163266px);--minw-tablet:calc(100% / 3.9 - 8.923076923076923px);--minw-mobile:calc(100% / 1.9 - 11.368421052631579px);--maxw:calc(100% / 4.9 - 9.551020408163266px);--maxw-tablet:calc(100% / 3.9 - 8.923076923076923px);--maxw-mobile:calc(100% / 1.9 - 11.368421052631579px);--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ml:0px;--ml-tablet:0px;--ml-mobile:0px"
      class="gem-slider-item gp-w-full gem-slider-item- gp-child-item-gOje5emP94 gAFkWaqct4"
      data-index="1"
    >
      <div
        class="gp-w-full gp-h-full",
        style="--shadow:none"
      >
      
    <div
      
      data-id="gtJ4s8eami"
      class="!gp-max-w-none gp-bg-black gp-overflow-hidden [&_>_article]:gp-aspect-[var(--aspect)] tablet:[&_>_article]:gp-aspect-[var(--aspect-tablet,_var(--aspect))] mobile:[&_>_article]:gp-aspect-[var(--aspect-mobile,_var(--aspect-tablet,_var(--aspect)))] gp-relative gtJ4s8eami"
      style="--bs:solid;--bw:1px 1px 1px 1px;--bc:#242424;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--bblr:10px;--bbrr:10px;--btlr:10px;--btrr:10px;--aspect:auto"
    >
      
    <gp-lite-youtube-embed gp-data='{"lazy":true,"style":{"--aspect":"auto"},"videoTitle":"Video","iframeSrc":"https://www.youtube-nocookie.com/embed/cyzh48XRS4M?autoplay=1&state=1&mute=1&&controls=1"}'>
      
      
      <article
        aria-hidden
        class="gp-relative gp-bg-cover gp-bg-center gp-lite-youtube-embed-article gp_lazybg"
        data-title="Video"
        style="--bgi:url('{{ 'gempages_572751041980793671-a79845d6-ea84-4564-956a-d5e0ec829d24.jpg' | file_url }}');--aspect:auto"
      >
        
  <img
      id="gp-video-gtJ4s8eami"
      
      draggable="false"
      class="gp-invisible gp-w-full gp-h-full gp_lazyforbg"
      data-src="{{ 'gempages_572751041980793671-a79845d6-ea84-4564-956a-d5e0ec829d24.jpg' | file_url }}" data-srcset="{{ 'gempages_572751041980793671-a79845d6-ea84-4564-956a-d5e0ec829d24.jpg' | file_url }}" src="{{ 'gempages_572751041980793671-a79845d6-ea84-4564-956a-d5e0ec829d24.jpg' | file_url }}" width="2237" height="1678" alt="Poster"
      quality-type={}
      quality-percent={}
      data-sizes="auto"
      sizes="100vw"
      
    />
  
        <button
          type="button"
          class="gp-absolute gp-top-1/2 gp-left-1/2 gp-w-[68px] -gp-translate-x-1/2 -gp-translate-y-1/2 gp-lite-youtube-embed-button gp-visible gp-opacity-100"
          aria-label="Play"
        >
          <svg height="100%" version="1.1" viewBox="0 0 68 48" width="100%">
            <path
              d="M66.52,7.74c-0.78-2.93-2.49-5.41-5.42-6.19C55.79,.13,34,0,34,0S12.21,.13,6.9,1.55 C3.97,2.33,2.27,4.81,1.48,7.74C0.06,13.05,0,24,0,24s0.06,10.95,1.48,16.26c0.78,2.93,2.49,5.41,5.42,6.19 C12.21,47.87,34,48,34,48s21.79-0.13,27.1-1.55c2.93-0.78,4.64-3.26,5.42-6.19C67.94,34.95,68,24,68,24S67.94,13.05,66.52,7.74z"
              fill="#f00"
            ></path>
            <path d="M 45,24 27,14 27,34" fill="#fff"></path>
          </svg>
        </button>
        
      </article>
    </gp-lite-youtube-embed>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-lite-youtube-embed.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
    </div>
  
      <style id="custom-css-gtJ4s8eami">
        .gtJ4s8eami[data-component-label="Video"] {

}
      </style>
    
      </div>
    </div>
  
    <div
      media-type="{{media.media_type}}"
      media-poster="{{media.preview_image.src}}"
      media-source-url="{{media.sources.first.url}}"
      media-last-source-url="{{mediaSourceUrl}}"
      external-id="{{media.external_id}}"
      grid-index="{{forloop.index}}"
      
      label="Carousel Item" tag="CarouselItem" type="component"
      style="--bgc:transparent;--minw:calc(100% / 4.9 - 9.551020408163266px);--minw-tablet:calc(100% / 3.9 - 8.923076923076923px);--minw-mobile:calc(100% / 1.9 - 11.368421052631579px);--maxw:calc(100% / 4.9 - 9.551020408163266px);--maxw-tablet:calc(100% / 3.9 - 8.923076923076923px);--maxw-mobile:calc(100% / 1.9 - 11.368421052631579px);--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ml:0px;--ml-tablet:0px;--ml-mobile:0px"
      class="gem-slider-item gp-w-full gem-slider-item- gp-child-item-gOje5emP94 g493rnk2rh"
      data-index="2"
    >
      <div
        class="gp-w-full gp-h-full",
        style="--shadow:none"
      >
      
    <div
      
      data-id="gw3mOuqST1"
      class="!gp-max-w-none gp-bg-black gp-overflow-hidden [&_>_article]:gp-aspect-[var(--aspect)] tablet:[&_>_article]:gp-aspect-[var(--aspect-tablet,_var(--aspect))] mobile:[&_>_article]:gp-aspect-[var(--aspect-mobile,_var(--aspect-tablet,_var(--aspect)))] gp-relative gw3mOuqST1"
      style="--bs:solid;--bw:1px 1px 1px 1px;--bc:#242424;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--bblr:10px;--bbrr:10px;--btlr:10px;--btrr:10px;--aspect:auto"
    >
      
    <gp-lite-youtube-embed gp-data='{"lazy":true,"style":{"--aspect":"auto"},"videoTitle":"Video","iframeSrc":"https://www.youtube-nocookie.com/embed/cyzh48XRS4M?autoplay=1&state=1&mute=1&&controls=1"}'>
      
      
      <article
        aria-hidden
        class="gp-relative gp-bg-cover gp-bg-center gp-lite-youtube-embed-article gp_lazybg"
        data-title="Video"
        style="--bgi:url('{{ 'gempages_572751041980793671-b722a3dc-8683-4d63-9fdb-ea1d1ea433e5.jpg' | file_url }}');--aspect:auto"
      >
        
  <img
      id="gp-video-gw3mOuqST1"
      
      draggable="false"
      class="gp-invisible gp-w-full gp-h-full gp_lazyforbg"
      data-src="{{ 'gempages_572751041980793671-b722a3dc-8683-4d63-9fdb-ea1d1ea433e5.jpg' | file_url }}" data-srcset="{{ 'gempages_572751041980793671-b722a3dc-8683-4d63-9fdb-ea1d1ea433e5.jpg' | file_url }}" src="{{ 'gempages_572751041980793671-b722a3dc-8683-4d63-9fdb-ea1d1ea433e5.jpg' | file_url }}" width="2237" height="1678" alt="Poster"
      quality-type={}
      quality-percent={}
      data-sizes="auto"
      sizes="100vw"
      
    />
  
        <button
          type="button"
          class="gp-absolute gp-top-1/2 gp-left-1/2 gp-w-[68px] -gp-translate-x-1/2 -gp-translate-y-1/2 gp-lite-youtube-embed-button gp-visible gp-opacity-100"
          aria-label="Play"
        >
          <svg height="100%" version="1.1" viewBox="0 0 68 48" width="100%">
            <path
              d="M66.52,7.74c-0.78-2.93-2.49-5.41-5.42-6.19C55.79,.13,34,0,34,0S12.21,.13,6.9,1.55 C3.97,2.33,2.27,4.81,1.48,7.74C0.06,13.05,0,24,0,24s0.06,10.95,1.48,16.26c0.78,2.93,2.49,5.41,5.42,6.19 C12.21,47.87,34,48,34,48s21.79-0.13,27.1-1.55c2.93-0.78,4.64-3.26,5.42-6.19C67.94,34.95,68,24,68,24S67.94,13.05,66.52,7.74z"
              fill="#f00"
            ></path>
            <path d="M 45,24 27,14 27,34" fill="#fff"></path>
          </svg>
        </button>
        
      </article>
    </gp-lite-youtube-embed>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-lite-youtube-embed.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
    </div>
  
      <style id="custom-css-gw3mOuqST1">
        .gw3mOuqST1[data-component-label="Video"] {

}
      </style>
    
      </div>
    </div>
  
    <div
      media-type="{{media.media_type}}"
      media-poster="{{media.preview_image.src}}"
      media-source-url="{{media.sources.first.url}}"
      media-last-source-url="{{mediaSourceUrl}}"
      external-id="{{media.external_id}}"
      grid-index="{{forloop.index}}"
      
      label="Carousel Item" tag="CarouselItem" type="component"
      style="--bgc:transparent;--minw:calc(100% / 4.9 - 9.551020408163266px);--minw-tablet:calc(100% / 3.9 - 8.923076923076923px);--minw-mobile:calc(100% / 1.9 - 11.368421052631579px);--maxw:calc(100% / 4.9 - 9.551020408163266px);--maxw-tablet:calc(100% / 3.9 - 8.923076923076923px);--maxw-mobile:calc(100% / 1.9 - 11.368421052631579px);--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ml:0px;--ml-tablet:0px;--ml-mobile:0px"
      class="gem-slider-item gp-w-full gem-slider-item- gp-child-item-gOje5emP94 gjXA6ydm-v"
      data-index="3"
    >
      <div
        class="gp-w-full gp-h-full",
        style="--shadow:none"
      >
      
    <div
      
      data-id="gCl6PPkn-a"
      class="!gp-max-w-none gp-bg-black gp-overflow-hidden [&_>_article]:gp-aspect-[var(--aspect)] tablet:[&_>_article]:gp-aspect-[var(--aspect-tablet,_var(--aspect))] mobile:[&_>_article]:gp-aspect-[var(--aspect-mobile,_var(--aspect-tablet,_var(--aspect)))] gp-relative gCl6PPkn-a"
      style="--bs:solid;--bw:1px 1px 1px 1px;--bc:#242424;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--bblr:10px;--bbrr:10px;--btlr:10px;--btrr:10px;--aspect:auto"
    >
      
    <gp-lite-youtube-embed gp-data='{"lazy":true,"style":{"--aspect":"auto"},"videoTitle":"Video","iframeSrc":"https://www.youtube-nocookie.com/embed/E_v_Yvi9jKc?autoplay=1&state=1&mute=1&&controls=1"}'>
      
          <link rel="preconnect" href="https://www.youtube-nocookie.com" />
          <link rel="preconnect" href="https://www.google.com" />
        
      
      <article
        aria-hidden
        class="gp-relative gp-bg-cover gp-bg-center gp-lite-youtube-embed-article "
        data-title="Video"
        style="--bgi:url('{{ 'gempages_572751041980793671-30f58ab5-39ee-49e1-bd49-4e21d37545f1.jpg' | file_url }}');--aspect:auto"
      >
        
  <img
      id="gp-video-gCl6PPkn-a"
      
      draggable="false"
      class="gp-invisible gp-w-full gp-h-full gp_lazyforbg"
      data-src="{{ 'gempages_572751041980793671-30f58ab5-39ee-49e1-bd49-4e21d37545f1.jpg' | file_url }}" data-srcset="{{ 'gempages_572751041980793671-30f58ab5-39ee-49e1-bd49-4e21d37545f1.jpg' | file_url }}" src="{{ 'gempages_572751041980793671-30f58ab5-39ee-49e1-bd49-4e21d37545f1.jpg' | file_url }}" width="2237" height="1678" alt="Poster"
      quality-type={}
      quality-percent={}
      data-sizes="auto"
      sizes="100vw"
      
    />
  
        <button
          type="button"
          class="gp-absolute gp-top-1/2 gp-left-1/2 gp-w-[68px] -gp-translate-x-1/2 -gp-translate-y-1/2 gp-lite-youtube-embed-button gp-visible gp-opacity-100"
          aria-label="Play"
        >
          <svg height="100%" version="1.1" viewBox="0 0 68 48" width="100%">
            <path
              d="M66.52,7.74c-0.78-2.93-2.49-5.41-5.42-6.19C55.79,.13,34,0,34,0S12.21,.13,6.9,1.55 C3.97,2.33,2.27,4.81,1.48,7.74C0.06,13.05,0,24,0,24s0.06,10.95,1.48,16.26c0.78,2.93,2.49,5.41,5.42,6.19 C12.21,47.87,34,48,34,48s21.79-0.13,27.1-1.55c2.93-0.78,4.64-3.26,5.42-6.19C67.94,34.95,68,24,68,24S67.94,13.05,66.52,7.74z"
              fill="#f00"
            ></path>
            <path d="M 45,24 27,14 27,34" fill="#fff"></path>
          </svg>
        </button>
        
      </article>
    </gp-lite-youtube-embed>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-lite-youtube-embed.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
    </div>
  
      <style id="custom-css-gCl6PPkn-a">
        .gCl6PPkn-a[data-component-label="Video"] {

}
      </style>
    
      </div>
    </div>
  
    <div
      media-type="{{media.media_type}}"
      media-poster="{{media.preview_image.src}}"
      media-source-url="{{media.sources.first.url}}"
      media-last-source-url="{{mediaSourceUrl}}"
      external-id="{{media.external_id}}"
      grid-index="{{forloop.index}}"
      
      label="Carousel Item" tag="CarouselItem" type="component"
      style="--bgc:transparent;--minw:calc(100% / 4.9 - 9.551020408163266px);--minw-tablet:calc(100% / 3.9 - 8.923076923076923px);--minw-mobile:calc(100% / 1.9 - 11.368421052631579px);--maxw:calc(100% / 4.9 - 9.551020408163266px);--maxw-tablet:calc(100% / 3.9 - 8.923076923076923px);--maxw-mobile:calc(100% / 1.9 - 11.368421052631579px);--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--ml:0px;--ml-tablet:0px;--ml-mobile:0px"
      class="gem-slider-item gp-w-full gem-slider-item- gp-child-item-gOje5emP94 gTePvPD07z"
      data-index="4"
    >
      <div
        class="gp-w-full gp-h-full",
        style="--shadow:none"
      >
      
    <div
      
      data-id="gBq5I68RrQ"
      class="!gp-max-w-none gp-bg-black gp-overflow-hidden [&_>_article]:gp-aspect-[var(--aspect)] tablet:[&_>_article]:gp-aspect-[var(--aspect-tablet,_var(--aspect))] mobile:[&_>_article]:gp-aspect-[var(--aspect-mobile,_var(--aspect-tablet,_var(--aspect)))] gp-relative gBq5I68RrQ"
      style="--bs:solid;--bw:1px 1px 1px 1px;--bc:#242424;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--bblr:10px;--bbrr:10px;--btlr:10px;--btrr:10px;--aspect:auto"
    >
      
    <gp-lite-youtube-embed gp-data='{"lazy":true,"style":{"--aspect":"auto"},"videoTitle":"Video","iframeSrc":"https://www.youtube-nocookie.com/embed/mWfSDP36Vnc?autoplay=1&state=1&mute=1&&controls=1"}'>
      
      
      <article
        aria-hidden
        class="gp-relative gp-bg-cover gp-bg-center gp-lite-youtube-embed-article gp_lazybg"
        data-title="Video"
        style="--bgi:url('{{ 'gempages_572751041980793671-73035fe3-7b16-4bda-bc73-97e394bfffb5.jpg' | file_url }}');--aspect:auto"
      >
        
  <img
      id="gp-video-gBq5I68RrQ"
      
      draggable="false"
      class="gp-invisible gp-w-full gp-h-full gp_lazyforbg"
      data-src="{{ 'gempages_572751041980793671-73035fe3-7b16-4bda-bc73-97e394bfffb5.jpg' | file_url }}" data-srcset="{{ 'gempages_572751041980793671-73035fe3-7b16-4bda-bc73-97e394bfffb5.jpg' | file_url }}" src="{{ 'gempages_572751041980793671-73035fe3-7b16-4bda-bc73-97e394bfffb5.jpg' | file_url }}" width="2237" height="1678" alt="Poster"
      quality-type={}
      quality-percent={}
      data-sizes="auto"
      sizes="100vw"
      
    />
  
        <button
          type="button"
          class="gp-absolute gp-top-1/2 gp-left-1/2 gp-w-[68px] -gp-translate-x-1/2 -gp-translate-y-1/2 gp-lite-youtube-embed-button gp-visible gp-opacity-100"
          aria-label="Play"
        >
          <svg height="100%" version="1.1" viewBox="0 0 68 48" width="100%">
            <path
              d="M66.52,7.74c-0.78-2.93-2.49-5.41-5.42-6.19C55.79,.13,34,0,34,0S12.21,.13,6.9,1.55 C3.97,2.33,2.27,4.81,1.48,7.74C0.06,13.05,0,24,0,24s0.06,10.95,1.48,16.26c0.78,2.93,2.49,5.41,5.42,6.19 C12.21,47.87,34,48,34,48s21.79-0.13,27.1-1.55c2.93-0.78,4.64-3.26,5.42-6.19C67.94,34.95,68,24,68,24S67.94,13.05,66.52,7.74z"
              fill="#f00"
            ></path>
            <path d="M 45,24 27,14 27,34" fill="#fff"></path>
          </svg>
        </button>
        
      </article>
    </gp-lite-youtube-embed>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-lite-youtube-embed.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
    </div>
  
      <style id="custom-css-gBq5I68RrQ">
        .gBq5I68RrQ[data-component-label="Video"] {

}
      </style>
    
      </div>
    </div>
  
          </div>
          
    <button
      type="button"
      aria-label='Carousel Next Arrow'
      class="gp-z-1 gp-carousel-action-next gem-slider-next gOje5emP94-{{section.id}} gp-carousel-arrow-gOje5emP94 gp-items-center gp-justify-center gp-overflow-hidden gp-shrink-0  gp-cursor-pointer gp-text-gray-500 !gp-flex !gp-relative  tablet:!gp-flex tablet:!gp-relative  mobile:!gp-hidden"
      style="--left:initial;--top:;--right:initial;--bottom:initial;--left-tablet:initial;--top-tablet:;--right-tablet:initial;--bottom-tablet:initial;--left-mobile:initial;--top-mobile:;--right-mobile:initial;--bottom-mobile:initial;--d:flex;--d-tablet:flex;--d-mobile:none;background-color:transparent;--w:32px;--h:32px"
      onClick={onClick}
      
    >
      <div 
  class="gp-flex gp-items-center gp-justify-center [&>svg]:gp-h-full [&>svg]:gp-w-full gp-rotate-0 tablet:gp-rotate-0 mobile:gp-rotate-0"
  style="--c:#ECECEC;--w:60px;--w-tablet:60px;--w-mobile:60px;--h:60px;--h-tablet:60px;--h-mobile:60px"
  >
    <svg height="20" width="20" xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 256 256" fill="currentColor" data-id="508817723121664360">
              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M128,24A104,104,0,1,0,232,128,104.11,104.11,0,0,0,128,24Zm29.66,109.66-40,40a8,8,0,0,1-11.32-11.32L140.69,128,106.34,93.66a8,8,0,0,1,11.32-11.32l40,40A8,8,0,0,1,157.66,133.66Z" /></svg>
    </div>
      <style>
    .gp-carousel-arrow-gOje5emP94 {
      border-radius: var(--g-radius-small);
    }
    .gp-carousel-arrow-gOje5emP94::before {
      content: '';
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      z-index: 10;
      border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
      border-radius: var(--g-radius-small);
    }
    @media only screen and (max-width: 1024px) and (min-width: 768px) {
      .gp-carousel-arrow-gOje5emP94 {
        border-radius: var(--g-radius-small);
      }
      .gp-carousel-arrow-gOje5emP94::before {
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        border-radius: var(--g-radius-small);
      }
    }
    @media only screen and (max-width: 768px) {
      .gp-carousel-arrow-gOje5emP94 {
        border-radius: var(--g-radius-small);
      }
      .gp-carousel-arrow-gOje5emP94::before {
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        border-radius: var(--g-radius-small);
      }
    }
  </style>
    </button>
  
        </div>
        <div
    class="gp-items-center gp-justify-center gp-gap-2 gp-text-center gp-carousel-dot-container gp-carousel-dot-container-gOje5emP94-{{section.id}} gp-static gp-flex-row gp-left-0 gp-right-0 tablet:gp-static tablet:gp-flex-row tablet:gp-left-0 tablet:gp-right-0 mobile:gp-static mobile:gp-flex-row mobile:gp-left-0 mobile:gp-right-0"
    style="--bottom:16px;--bottom-tablet:16px;--bottom-mobile:16px;--d:none;--d-tablet:none;--d-mobile:none"
 ></div>
      </div>
    </gp-carousel>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-carousel-v2.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>

  
    </div>
    </div>
  
            </section>
           
    


{% schema %}
  {
    
    "name": "Section 8",
    "tag": "section",
    "class": "gps-572876861235790720 gps gpsil",
    "settings": [{"type":"paragraph","content":"Section design by GemPages. [Click here to start design](https://admin.shopify.com/store/i0pixe-10/apps/gempages-cro/app/shopify/edit?pageType=GP_PRODUCT&editorId=572751048691811510&sectionId=572876861235790720)"},{"type":"select","label":"Section Preload","id":"section_preload","options":[{"label":"On","value":"true"},{"label":"Off","value":"false"},{"label":"Off Lazy Script","value":"off_lazy_script"}],"default":"false"},{"type":"text","label":"Checksum","id":"checksum"}],
    "blocks": [{"type": "@app"}],
    "locales": {}
  }
{% endschema %}
