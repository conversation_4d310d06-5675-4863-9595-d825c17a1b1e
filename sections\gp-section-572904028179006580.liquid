

<style {% if section.settings.section_preload == "false" %} class="gps-link gps-style" type="text/disabled-css" {% endif %}>.gps-572904028179006580.gps.gpsil [style*="--ai:"]{align-items:var(--ai)}.gps-572904028179006580.gps.gpsil [style*="--aspect:"]{aspect-ratio:var(--aspect)}.gps-572904028179006580.gps.gpsil [style*="--bg:"]{background:var(--bg)}.gps-572904028179006580.gps.gpsil [style*="--hvr-bg:"]:hover{background:var(--hvr-bg)}.gps-572904028179006580.gps.gpsil [style*="--bga:"]{background-attachment:var(--bga)}.gps-572904028179006580.gps.gpsil [style*="--bgc:"]{background-color:var(--bgc)}.gps-572904028179006580.gps.gpsil [style*="--bgi:"]{background-image:var(--bgi)}.gps-572904028179006580.gps.gpsil [style*="--bgp:"]{background-position:var(--bgp)}.gps-572904028179006580.gps.gpsil [style*="--bgr:"]{background-repeat:var(--bgr)}.gps-572904028179006580.gps.gpsil [style*="--bgs:"]{background-size:var(--bgs)}.gps-572904028179006580.gps.gpsil [style*="--b:"]{border:var(--b)}.gps-572904028179006580.gps.gpsil [style*="--hvr-b:"]:hover{border:var(--hvr-b)}.gps-572904028179006580.gps.gpsil [style*="--bb:"]{border-bottom:var(--bb)}.gps-572904028179006580.gps.gpsil [style*="--hvr-bb:"]:hover{border-bottom:var(--hvr-bb)}.gps-572904028179006580.gps.gpsil [style*="--bc:"]{border-color:var(--bc)}.gps-572904028179006580.gps.gpsil [style*="--hvr-bc:"]:hover{border-color:var(--hvr-bc)}.gps-572904028179006580.gps.gpsil [style*="--bblr:"]{border-bottom-left-radius:var(--bblr)}.gps-572904028179006580.gps.gpsil [style*="--hvr-bblr:"]:hover{border-bottom-left-radius:var(--hvr-bblr)}.gps-572904028179006580.gps.gpsil [style*="--bbrr:"]{border-bottom-right-radius:var(--bbrr)}.gps-572904028179006580.gps.gpsil [style*="--hvr-bbrr:"]:hover{border-bottom-right-radius:var(--hvr-bbrr)}.gps-572904028179006580.gps.gpsil [style*="--radius:"]{border-radius:var(--radius)}.gps-572904028179006580.gps.gpsil [style*="--bs:"]{border-style:var(--bs)}.gps-572904028179006580.gps.gpsil [style*="--hvr-bs:"]:hover{border-style:var(--hvr-bs)}.gps-572904028179006580.gps.gpsil [style*="--bt:"]{border-top:var(--bt)}.gps-572904028179006580.gps.gpsil [style*="--hvr-bt:"]:hover{border-top:var(--hvr-bt)}.gps-572904028179006580.gps.gpsil [style*="--btlr:"]{border-top-left-radius:var(--btlr)}.gps-572904028179006580.gps.gpsil [style*="--hvr-btlr:"]:hover{border-top-left-radius:var(--hvr-btlr)}.gps-572904028179006580.gps.gpsil [style*="--btrr:"]{border-top-right-radius:var(--btrr)}.gps-572904028179006580.gps.gpsil [style*="--hvr-btrr:"]:hover{border-top-right-radius:var(--hvr-btrr)}.gps-572904028179006580.gps.gpsil [style*="--bw:"]{border-width:var(--bw)}.gps-572904028179006580.gps.gpsil [style*="--hvr-bw:"]:hover{border-width:var(--hvr-bw)}.gps-572904028179006580.gps.gpsil [style*="--bottom:"]{bottom:var(--bottom)}.gps-572904028179006580.gps.gpsil [style*="--shadow:"]{box-shadow:var(--shadow)}.gps-572904028179006580.gps.gpsil [style*="--c:"]{color:var(--c)}.gps-572904028179006580.gps.gpsil [style*="--hvr-c:"]:hover{color:var(--hvr-c)}.gps-572904028179006580.gps.gpsil [style*="--cg:"]{-moz-column-gap:var(--cg);column-gap:var(--cg)}.gps-572904028179006580.gps.gpsil [style*="--ff:"]{font-family:var(--ff)}.gps-572904028179006580.gps.gpsil [style*="--size:"]{font-size:var(--size)}.gps-572904028179006580.gps.gpsil [style*="--weight:"]{font-weight:var(--weight)}.gps-572904028179006580.gps.gpsil [style*="--fs:"]{font-style:var(--fs)}.gps-572904028179006580.gps.gpsil [style*="--gg:"]{grid-gap:var(--gg)}.gps-572904028179006580.gps.gpsil [style*="--gtc:"]{grid-template-columns:var(--gtc)}.gps-572904028179006580.gps.gpsil [style*="--h:"]{height:var(--h)}.gps-572904028179006580.gps.gpsil [style*="--jc:"]{justify-content:var(--jc)}.gps-572904028179006580.gps.gpsil [style*="--left:"]{left:var(--left)}.gps-572904028179006580.gps.gpsil [style*="--ls:"]{letter-spacing:var(--ls)}.gps-572904028179006580.gps.gpsil [style*="--lh:"]{line-height:var(--lh)}.gps-572904028179006580.gps.gpsil [style*="--tdt:"]{text-decoration-thickness:var(--tdt)}.gps-572904028179006580.gps.gpsil [style*="--tdc:"]{text-decoration-color:var(--tdc)}.gps-572904028179006580.gps.gpsil [style*="--m:"]{margin:var(--m)}.gps-572904028179006580.gps.gpsil [style*="--mb:"]{margin-bottom:var(--mb)}.gps-572904028179006580.gps.gpsil [style*="--ml:"]{margin-left:var(--ml)}.gps-572904028179006580.gps.gpsil [style*="--maxw:"]{max-width:var(--maxw)}.gps-572904028179006580.gps.gpsil [style*="--minw:"]{min-width:var(--minw)}.gps-572904028179006580.gps.gpsil [style*="--objf:"]{-o-object-fit:var(--objf);object-fit:var(--objf)}.gps-572904028179006580.gps.gpsil [style*="--op:"]{opacity:var(--op)}.gps-572904028179006580.gps.gpsil [style*="--o:"]{order:var(--o)}.gps-572904028179006580.gps.gpsil [style*="--pc:"]{place-content:var(--pc)}.gps-572904028179006580.gps.gpsil [style*="--p:"]{padding:var(--p)}.gps-572904028179006580.gps.gpsil [style*="--pb:"]{padding-bottom:var(--pb)}.gps-572904028179006580.gps.gpsil [style*="--pl:"]{padding-left:var(--pl)}.gps-572904028179006580.gps.gpsil [style*="--pr:"]{padding-right:var(--pr)}.gps-572904028179006580.gps.gpsil [style*="--pt:"]{padding-top:var(--pt)}.gps-572904028179006580.gps.gpsil [style*="--pos:"]{position:var(--pos)}.gps-572904028179006580.gps.gpsil [style*="--right:"]{right:var(--right)}.gps-572904028179006580.gps.gpsil [style*="--rg:"]{row-gap:var(--rg)}.gps-572904028179006580.gps.gpsil [style*="--ta:"]{text-align:var(--ta)}.gps-572904028179006580.gps.gpsil [style*="--tt:"]{text-transform:var(--tt)}.gps-572904028179006580.gps.gpsil [style*="--top:"]{top:var(--top)}.gps-572904028179006580.gps.gpsil [style*="--t:"]{transform:var(--t)}.gps-572904028179006580.gps.gpsil [style*="--w:"]{width:var(--w)}.gps-572904028179006580.gps.gpsil [style*="--line-clamp:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp);display:-webkit-box;overflow:hidden}@media only screen and (max-width:1024px){.gps-572904028179006580.gps.gpsil [style*="--aspect-tablet:"]{aspect-ratio:var(--aspect-tablet)}.gps-572904028179006580.gps.gpsil [style*="--bottom-tablet:"]{bottom:var(--bottom-tablet)}.gps-572904028179006580.gps.gpsil [style*="--cg-tablet:"]{-moz-column-gap:var(--cg-tablet);column-gap:var(--cg-tablet)}.gps-572904028179006580.gps.gpsil [style*="--size-tablet:"]{font-size:var(--size-tablet)}.gps-572904028179006580.gps.gpsil [style*="--gtc-tablet:"]{grid-template-columns:var(--gtc-tablet)}.gps-572904028179006580.gps.gpsil [style*="--h-tablet:"]{height:var(--h-tablet)}.gps-572904028179006580.gps.gpsil [style*="--left-tablet:"]{left:var(--left-tablet)}.gps-572904028179006580.gps.gpsil [style*="--lh-tablet:"]{line-height:var(--lh-tablet)}.gps-572904028179006580.gps.gpsil [style*="--mb-tablet:"]{margin-bottom:var(--mb-tablet)}.gps-572904028179006580.gps.gpsil [style*="--ml-tablet:"]{margin-left:var(--ml-tablet)}.gps-572904028179006580.gps.gpsil [style*="--maxw-tablet:"]{max-width:var(--maxw-tablet)}.gps-572904028179006580.gps.gpsil [style*="--minw-tablet:"]{min-width:var(--minw-tablet)}.gps-572904028179006580.gps.gpsil [style*="--o-tablet:"]{order:var(--o-tablet)}.gps-572904028179006580.gps.gpsil [style*="--pb-tablet:"]{padding-bottom:var(--pb-tablet)}.gps-572904028179006580.gps.gpsil [style*="--pos-tablet:"]{position:var(--pos-tablet)}.gps-572904028179006580.gps.gpsil [style*="--right-tablet:"]{right:var(--right-tablet)}.gps-572904028179006580.gps.gpsil [style*="--top-tablet:"]{top:var(--top-tablet)}.gps-572904028179006580.gps.gpsil [style*="--t-tablet:"]{transform:var(--t-tablet)}.gps-572904028179006580.gps.gpsil [style*="--w-tablet:"]{width:var(--w-tablet)}.gps-572904028179006580.gps.gpsil [style*="--line-clamp-tablet:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-tablet);display:-webkit-box;overflow:hidden}}@media only screen and (max-width:767px){.gps-572904028179006580.gps.gpsil [style*="--aspect-mobile:"]{aspect-ratio:var(--aspect-mobile)}.gps-572904028179006580.gps.gpsil [style*="--bottom-mobile:"]{bottom:var(--bottom-mobile)}.gps-572904028179006580.gps.gpsil [style*="--cg-mobile:"]{-moz-column-gap:var(--cg-mobile);column-gap:var(--cg-mobile)}.gps-572904028179006580.gps.gpsil [style*="--size-mobile:"]{font-size:var(--size-mobile)}.gps-572904028179006580.gps.gpsil [style*="--gg-mobile:"]{grid-gap:var(--gg-mobile)}.gps-572904028179006580.gps.gpsil [style*="--gtc-mobile:"]{grid-template-columns:var(--gtc-mobile)}.gps-572904028179006580.gps.gpsil [style*="--h-mobile:"]{height:var(--h-mobile)}.gps-572904028179006580.gps.gpsil [style*="--left-mobile:"]{left:var(--left-mobile)}.gps-572904028179006580.gps.gpsil [style*="--lh-mobile:"]{line-height:var(--lh-mobile)}.gps-572904028179006580.gps.gpsil [style*="--mb-mobile:"]{margin-bottom:var(--mb-mobile)}.gps-572904028179006580.gps.gpsil [style*="--ml-mobile:"]{margin-left:var(--ml-mobile)}.gps-572904028179006580.gps.gpsil [style*="--maxw-mobile:"]{max-width:var(--maxw-mobile)}.gps-572904028179006580.gps.gpsil [style*="--minw-mobile:"]{min-width:var(--minw-mobile)}.gps-572904028179006580.gps.gpsil [style*="--o-mobile:"]{order:var(--o-mobile)}.gps-572904028179006580.gps.gpsil [style*="--pb-mobile:"]{padding-bottom:var(--pb-mobile)}.gps-572904028179006580.gps.gpsil [style*="--pl-mobile:"]{padding-left:var(--pl-mobile)}.gps-572904028179006580.gps.gpsil [style*="--pr-mobile:"]{padding-right:var(--pr-mobile)}.gps-572904028179006580.gps.gpsil [style*="--pt-mobile:"]{padding-top:var(--pt-mobile)}.gps-572904028179006580.gps.gpsil [style*="--pos-mobile:"]{position:var(--pos-mobile)}.gps-572904028179006580.gps.gpsil [style*="--right-mobile:"]{right:var(--right-mobile)}.gps-572904028179006580.gps.gpsil [style*="--rg-mobile:"]{row-gap:var(--rg-mobile)}.gps-572904028179006580.gps.gpsil [style*="--top-mobile:"]{top:var(--top-mobile)}.gps-572904028179006580.gps.gpsil [style*="--t-mobile:"]{transform:var(--t-mobile)}.gps-572904028179006580.gps.gpsil [style*="--w-mobile:"]{width:var(--w-mobile)}.gps-572904028179006580.gps.gpsil [style*="--line-clamp-mobile:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-mobile);display:-webkit-box;overflow:hidden}}.gps-572904028179006580 .-gp-translate-x-1\/2,.gps-572904028179006580 .-gp-translate-y-1\/2,.gps-572904028179006580 .before\:-gp-rotate-45:before,.gps-572904028179006580 .gp-rotate-0,.gps-572904028179006580 .gp-rotate-180,.gps-572904028179006580 .gp-translate-x-\[-50\%\],.gps-572904028179006580 .gp-translate-y-0,.gps-572904028179006580 .mobile\:gp-rotate-0,.gps-572904028179006580 .mobile\:gp-rotate-180,.gps-572904028179006580 .tablet\:gp-rotate-0,.gps-572904028179006580 .tablet\:gp-rotate-180{--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1}.gps-572904028179006580 .gp-shadow-md,.gps-572904028179006580 .gp-shadow-none{--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000}.gps-572904028179006580 .gp-sr-only{clip:rect(0,0,0,0);border-width:0;height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;white-space:nowrap;width:1px}.gps-572904028179006580 .gp-pointer-events-none{pointer-events:none}.gps-572904028179006580 .gp-invisible{visibility:hidden}.gps-572904028179006580 .gp-static{position:static}.gps-572904028179006580 .\!gp-absolute{position:absolute!important}.gps-572904028179006580 .gp-absolute{position:absolute}.gps-572904028179006580 .gp-relative{position:relative}.gps-572904028179006580 .gp-sticky{position:sticky}.gps-572904028179006580 .gp-inset-0{inset:0}.gps-572904028179006580 .gp-bottom-0{bottom:0}.gps-572904028179006580 .gp-bottom-\[-4px\]{bottom:-4px}.gps-572904028179006580 .gp-bottom-\[calc\(100\%\+20px\)\]{bottom:calc(100% + 20px)}.gps-572904028179006580 .gp-left-0{left:0}.gps-572904028179006580 .gp-left-1\/2,.gps-572904028179006580 .gp-left-\[50\%\]{left:50%}.gps-572904028179006580 .gp-right-0{right:0}.gps-572904028179006580 .gp-top-0{top:0}.gps-572904028179006580 .gp-top-1\/2{top:50%}.gps-572904028179006580 .gp-z-0{z-index:0}.gps-572904028179006580 .gp-z-1{z-index:1}.gps-572904028179006580 .gp-z-10{z-index:10}.gps-572904028179006580 .gp-z-2{z-index:2}.gps-572904028179006580 .gp-z-\[90\]{z-index:90}.gps-572904028179006580 .\!gp-m-0{margin:0!important}.gps-572904028179006580 .gp-mx-auto{margin-left:auto;margin-right:auto}.gps-572904028179006580 .gp-my-0{margin-bottom:0;margin-top:0}.gps-572904028179006580 .\!gp-ml-0{margin-left:0!important}.gps-572904028179006580 .gp-mb-0{margin-bottom:0}.gps-572904028179006580 .gp-mb-\[-10px\]{margin-bottom:-10px}.gps-572904028179006580 .gp-block{display:block}.gps-572904028179006580 .\!gp-flex{display:flex!important}.gps-572904028179006580 .gp-flex{display:flex}.gps-572904028179006580 .gp-inline-flex{display:inline-flex}.gps-572904028179006580 .gp-grid{display:grid}.gps-572904028179006580 .\!gp-hidden{display:none!important}.gps-572904028179006580 .gp-hidden{display:none}.gps-572904028179006580 .gp-aspect-\[56\/32\]{aspect-ratio:56/32}.gps-572904028179006580 .gp-aspect-square{aspect-ratio:1/1}.gps-572904028179006580 .\!gp-h-fit{height:-moz-fit-content!important;height:fit-content!important}.gps-572904028179006580 .gp-h-0{height:0}.gps-572904028179006580 .gp-h-5{height:20px}.gps-572904028179006580 .gp-h-6{height:24px}.gps-572904028179006580 .gp-h-auto{height:auto}.gps-572904028179006580 .gp-h-full{height:100%}.gps-572904028179006580 .gp-max-h-full{max-height:100%}.gps-572904028179006580 .\!gp-min-h-full{min-height:100%!important}.gps-572904028179006580 .\!gp-w-full{width:100%!important}.gps-572904028179006580 .gp-w-14{width:56px}.gps-572904028179006580 .gp-w-5{width:20px}.gps-572904028179006580 .gp-w-6{width:24px}.gps-572904028179006580 .gp-w-\[12px\]{width:12px}.gps-572904028179006580 .gp-w-auto{width:auto}.gps-572904028179006580 .gp-w-full{width:100%}.gps-572904028179006580 .gp-w-max{width:-moz-max-content;width:max-content}.gps-572904028179006580 .\!gp-min-w-full{min-width:100%!important}.gps-572904028179006580 .gp-min-w-\[45px\]{min-width:45px}.gps-572904028179006580 .gp-min-w-fit{min-width:-moz-fit-content;min-width:fit-content}.gps-572904028179006580 .\!gp-max-w-\[150px\]{max-width:150px!important}.gps-572904028179006580 .\!gp-max-w-full{max-width:100%!important}.gps-572904028179006580 .gp-max-w-full{max-width:100%}.gps-572904028179006580 .gp-flex-1{flex:1 1 0%}.gps-572904028179006580 .gp-shrink-0{flex-shrink:0}.gps-572904028179006580 .gp-shrink-\[99999\]{flex-shrink:99999}.gps-572904028179006580 .-gp-translate-x-1\/2{--tw-translate-x:-50%}.gps-572904028179006580 .-gp-translate-x-1\/2,.gps-572904028179006580 .-gp-translate-y-1\/2{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.gps-572904028179006580 .-gp-translate-y-1\/2{--tw-translate-y:-50%}.gps-572904028179006580 .gp-translate-x-\[-50\%\]{--tw-translate-x:-50%}.gps-572904028179006580 .gp-translate-x-\[-50\%\],.gps-572904028179006580 .gp-translate-y-0{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.gps-572904028179006580 .gp-translate-y-0{--tw-translate-y:0px}.gps-572904028179006580 .gp-rotate-0{--tw-rotate:0deg}.gps-572904028179006580 .gp-rotate-0,.gps-572904028179006580 .gp-rotate-180{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.gps-572904028179006580 .gp-rotate-180{--tw-rotate:180deg}.gps-572904028179006580 .\!gp-cursor-not-allowed{cursor:not-allowed!important}.gps-572904028179006580 .gp-cursor-default{cursor:default}.gps-572904028179006580 .gp-cursor-pointer{cursor:pointer}.gps-572904028179006580 .gp-select-none{-webkit-user-select:none;-moz-user-select:none;user-select:none}.gps-572904028179006580 .gp-appearance-none{-webkit-appearance:none;-moz-appearance:none;appearance:none}.gps-572904028179006580 .gp-grid-rows-\[1fr\]{grid-template-rows:1fr}.gps-572904028179006580 .\!gp-flex-row{flex-direction:row!important}.gps-572904028179006580 .gp-flex-row{flex-direction:row}.gps-572904028179006580 .gp-flex-col{flex-direction:column}.gps-572904028179006580 .gp-flex-wrap{flex-wrap:wrap}.gps-572904028179006580 .\!gp-flex-nowrap{flex-wrap:nowrap!important}.gps-572904028179006580 .gp-items-start{align-items:flex-start}.gps-572904028179006580 .gp-items-center{align-items:center}.gps-572904028179006580 .gp-justify-start{justify-content:flex-start}.gps-572904028179006580 .gp-justify-center{justify-content:center}.gps-572904028179006580 .gp-justify-between{justify-content:space-between}.gps-572904028179006580 .gp-gap-2{gap:8px}.gps-572904028179006580 .gp-gap-\[6px\]{gap:6px}.gps-572904028179006580 .gp-gap-y-0{row-gap:0}.gps-572904028179006580 .gp-overflow-hidden{overflow:hidden}.gps-572904028179006580 .gp-truncate{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.gps-572904028179006580 .gp-text-ellipsis{text-overflow:ellipsis}.gps-572904028179006580 .gp-break-words{overflow-wrap:break-word}.gps-572904028179006580 .\!gp-rounded-none{border-radius:0!important}.gps-572904028179006580 .gp-rounded{border-radius:4px}.gps-572904028179006580 .gp-rounded-\[8px\]{border-radius:8px}.gps-572904028179006580 .gp-rounded-full{border-radius:9999px}.gps-572904028179006580 .gp-rounded-none{border-radius:0}.gps-572904028179006580 .\!gp-border-x-0{border-left-width:0!important;border-right-width:0!important}.gps-572904028179006580 .gp-border-y{border-bottom-width:1px;border-top-width:1px}.gps-572904028179006580 .gp-bg-\[\#333333\]{--tw-bg-opacity:1;background-color:rgb(51 51 51/var(--tw-bg-opacity))}.gps-572904028179006580 .gp-bg-black\/50{background-color:rgba(0,0,0,.5)}.gps-572904028179006580 .gp-bg-black\/80{background-color:rgba(0,0,0,.8)}.gps-572904028179006580 .gp-bg-g-bg-3{background-color:var(--g-c-bg-3)}.gps-572904028179006580 .gp-bg-transparent{background-color:transparent}.gps-572904028179006580 .gp-bg-auto{background-size:auto}.gps-572904028179006580 .gp-object-cover{-o-object-fit:cover;object-fit:cover}.gps-572904028179006580 .gp-p-\[4px\]{padding:4px}.gps-572904028179006580 .gp-px-0{padding-left:0;padding-right:0}.gps-572904028179006580 .gp-px-1{padding-left:4px;padding-right:4px}.gps-572904028179006580 .gp-px-4{padding-left:16px;padding-right:16px}.gps-572904028179006580 .gp-px-\[8px\]{padding-left:8px;padding-right:8px}.gps-572904028179006580 .gp-py-0{padding-bottom:0;padding-top:0}.gps-572904028179006580 .gp-py-\[4px\]{padding-bottom:4px;padding-top:4px}.gps-572904028179006580 .\!gp-pb-0{padding-bottom:0!important}.gps-572904028179006580 .gp-pb-1{padding-bottom:4px}.gps-572904028179006580 .gp-pl-4{padding-left:16px}.gps-572904028179006580 .gp-pr-1{padding-right:4px}.gps-572904028179006580 .gp-pr-6{padding-right:24px}.gps-572904028179006580 .gp-text-center{text-align:center}.gps-572904028179006580 .gp-text-\[12px\]{font-size:12px}.gps-572904028179006580 .gp-text-\[\#F9F9F9\]{--tw-text-opacity:1;color:rgb(249 249 249/var(--tw-text-opacity))}.gps-572904028179006580 .gp-text-g-text-2{color:var(--g-c-text-2)}.gps-572904028179006580 .gp-text-g-text-3{color:var(--g-c-text-3)}.gps-572904028179006580 .gp-text-gray-500{--tw-text-opacity:1;color:rgb(107 114 128/var(--tw-text-opacity))}.gps-572904028179006580 .gp-text-white{--tw-text-opacity:1;color:rgb(255 255 255/var(--tw-text-opacity))}.gps-572904028179006580 .gp-line-through{text-decoration-line:line-through}.gps-572904028179006580 .gp-no-underline{text-decoration-line:none}.gps-572904028179006580 .gp-decoration-g-text-1{text-decoration-color:var(--g-c-text-1)}.gps-572904028179006580 .gp-opacity-0{opacity:0}.gps-572904028179006580 .gp-opacity-20{opacity:.2}.gps-572904028179006580 .gp-opacity-25{opacity:.25}.gps-572904028179006580 .gp-opacity-30{opacity:.3}.gps-572904028179006580 .gp-opacity-75{opacity:.75}.gps-572904028179006580 .gp-shadow-md{--tw-shadow:0 4px 6px -1px rgba(0,0,0,.1),0 2px 4px -2px rgba(0,0,0,.1);--tw-shadow-colored:0 4px 6px -1px var(--tw-shadow-color),0 2px 4px -2px var(--tw-shadow-color)}.gps-572904028179006580 .gp-shadow-md,.gps-572904028179006580 .gp-shadow-none{box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)}.gps-572904028179006580 .gp-shadow-none{--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000}.gps-572904028179006580 .gp-outline-none{outline:2px solid transparent;outline-offset:2px}.gps-572904028179006580 .gp-outline-1{outline-width:1px}.gps-572904028179006580 .-gp-outline-offset-1{outline-offset:-1px}.gps-572904028179006580 .gp-transition-all{transition-duration:.15s;transition-property:all;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-572904028179006580 .gp-transition-colors{transition-duration:.15s;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-572904028179006580 .gp-transition-opacity{transition-duration:.15s;transition-property:opacity;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-572904028179006580 .gp-duration-100{transition-duration:.1s}.gps-572904028179006580 .gp-duration-150{transition-duration:.15s}.gps-572904028179006580 .gp-duration-200{transition-duration:.2s}.gps-572904028179006580 .gp-duration-300{transition-duration:.3s}.gps-572904028179006580 .gp-ease-in-out{transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-572904028179006580 .disabled\:gp-btn-disabled:disabled{cursor:default}.gps-572904028179006580 .before\:gp-absolute:before{content:var(--tw-content);position:absolute}.gps-572904028179006580 .before\:gp-top-\[50\%\]:before{content:var(--tw-content);top:50%}.gps-572904028179006580 .before\:gp-z-1:before{content:var(--tw-content);z-index:1}.gps-572904028179006580 .before\:gp-hidden:before{content:var(--tw-content);display:none}.gps-572904028179006580 .before\:gp-w-full:before{content:var(--tw-content);width:100%}.gps-572904028179006580 .before\:-gp-rotate-45:before{--tw-rotate:-45deg;content:var(--tw-content);transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.gps-572904028179006580 .before\:gp-border-t:before{border-top-width:1px;content:var(--tw-content)}.gps-572904028179006580 .before\:gp-content-\[\'\'\]:before{--tw-content:"";content:var(--tw-content)}.gps-572904028179006580 .after\:gp-absolute:after{content:var(--tw-content);position:absolute}.gps-572904028179006580 .after\:gp-bottom-\[-10px\]:after{bottom:-10px;content:var(--tw-content)}.gps-572904028179006580 .after\:gp-left-0:after{content:var(--tw-content);left:0}.gps-572904028179006580 .after\:gp-w-full:after{content:var(--tw-content);width:100%}.gps-572904028179006580 .after\:gp-p-\[7px\]:after{content:var(--tw-content);padding:7px}.gps-572904028179006580 .after\:gp-content-\[\'\'\]:after{--tw-content:"";content:var(--tw-content)}@media (hover:hover) and (pointer:fine){.gps-572904028179006580 .hover\:gp-bg-\[\#ef0800\]:hover{--tw-bg-opacity:1;background-color:rgb(239 8 0/var(--tw-bg-opacity))}.gps-572904028179006580 .hover\:gp-bg-g-bg-3:hover{background-color:var(--g-c-bg-3)}.gps-572904028179006580 .hover\:gp-text-black:hover{--tw-text-opacity:1;color:rgb(0 0 0/var(--tw-text-opacity))}}.gps-572904028179006580 .active\:gp-bg-g-bg-3:active{background-color:var(--g-c-bg-3)}.gps-572904028179006580 .disabled\:gp-pointer-events-none:disabled{pointer-events:none}.gps-572904028179006580 .disabled\:gp-cursor-not-allowed:disabled{cursor:not-allowed}.gps-572904028179006580 .disabled\:gp-opacity-30:disabled{opacity:.3}@media (hover:hover) and (pointer:fine){.gps-572904028179006580 .gp-group:hover .group-hover\:gp-visible{visibility:visible}.gps-572904028179006580 .gp-group\/button:hover .group-hover\/button\:\!gp-text-inherit{color:inherit!important}.gps-572904028179006580 .gp-group:hover .group-hover\:gp-opacity-100{opacity:1}}.gps-572904028179006580 .gp-group\/button:active .group-active\/button\:\!gp-text-inherit{color:inherit!important}.gps-572904028179006580 .data-\[disabled\=\'disabled\'\]\:gp-pointer-events-none[data-disabled=disabled]{pointer-events:none}.gps-572904028179006580 .data-\[hidden\=\'false\'\]\:gp-flex[data-hidden=false]{display:flex}.gps-572904028179006580 .data-\[hidden\=true\]\:gp-hidden[data-hidden=true],.gps-572904028179006580 .data-\[only-image\=true\]\:gp-hidden[data-only-image=true]{display:none}.gps-572904028179006580 .data-\[disabled\=\'disabled\'\]\:\!gp-cursor-not-allowed[data-disabled=disabled]{cursor:not-allowed!important}.gps-572904028179006580 .data-\[disabled\=true\]\:gp-opacity-60[data-disabled=true]{opacity:.6}.gps-572904028179006580 .data-\[outline\=active\]\:gp-outline[data-outline=active]{outline-style:solid}.gps-572904028179006580 .data-\[outline\=deactive\]\:after\:\!gp-border-transparent[data-outline=deactive]:after{border-color:transparent!important;content:var(--tw-content)}.gps-572904028179006580 .gp-group\/button[data-state=loading] .group-data-\[state\=loading\]\/button\:gp-visible{visibility:visible}.gps-572904028179006580 .gp-group[data-state=loading] .group-data-\[state\=loading\]\:gp-invisible,.gps-572904028179006580 .gp-group\/button[data-state=loading] .group-data-\[state\=loading\]\/button\:gp-invisible{visibility:hidden}@keyframes gp-spin{to{transform:rotate(1turn)}}.gps-572904028179006580 .gp-group\/button[data-state=loading] .group-data-\[state\=loading\]\/button\:gp-animate-spin{animation:gp-spin 1s linear infinite}.gps-572904028179006580 .scrollbar-thumb\:gp-rounded-2xl::-webkit-scrollbar-thumb,.gps-572904028179006580 .scrollbar-track\:gp-rounded-2xl::-webkit-scrollbar-track{border-radius:16px}.gps-572904028179006580 .scrollbar-thumb\:gp-bg-gray-400::-webkit-scrollbar-thumb{--tw-bg-opacity:1;background-color:rgb(156 163 175/var(--tw-bg-opacity))}@media (max-width:1024px){.gps-572904028179006580 .tablet\:gp-static{position:static}.gps-572904028179006580 .tablet\:\!gp-absolute{position:absolute!important}.gps-572904028179006580 .tablet\:gp-left-0{left:0}.gps-572904028179006580 .tablet\:gp-right-0{right:0}.gps-572904028179006580 .tablet\:gp-z-2{z-index:2}.gps-572904028179006580 .tablet\:gp-block{display:block}.gps-572904028179006580 .tablet\:\!gp-flex{display:flex!important}.gps-572904028179006580 .tablet\:\!gp-hidden{display:none!important}.gps-572904028179006580 .tablet\:gp-hidden{display:none}.gps-572904028179006580 .tablet\:\!gp-min-h-full{min-height:100%!important}.gps-572904028179006580 .tablet\:gp-rotate-0{--tw-rotate:0deg}.gps-572904028179006580 .tablet\:gp-rotate-0,.gps-572904028179006580 .tablet\:gp-rotate-180{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.gps-572904028179006580 .tablet\:gp-rotate-180{--tw-rotate:180deg}.gps-572904028179006580 .tablet\:\!gp-flex-row{flex-direction:row!important}.gps-572904028179006580 .tablet\:gp-flex-row{flex-direction:row}.gps-572904028179006580 .tablet\:\!gp-flex-nowrap{flex-wrap:nowrap!important}.gps-572904028179006580 .tablet\:gp-px-0{padding-left:0;padding-right:0}.gps-572904028179006580 .tablet\:gp-py-0{padding-bottom:0;padding-top:0}}@media (max-width:767px){.gps-572904028179006580 .mobile\:gp-static{position:static}.gps-572904028179006580 .mobile\:\!gp-absolute{position:absolute!important}.gps-572904028179006580 .mobile\:gp-left-0{left:0}.gps-572904028179006580 .mobile\:gp-right-0{right:0}.gps-572904028179006580 .mobile\:gp-z-2{z-index:2}.gps-572904028179006580 .mobile\:gp-block{display:block}.gps-572904028179006580 .mobile\:\!gp-flex{display:flex!important}.gps-572904028179006580 .mobile\:\!gp-hidden{display:none!important}.gps-572904028179006580 .mobile\:gp-hidden{display:none}.gps-572904028179006580 .mobile\:\!gp-min-h-full{min-height:100%!important}.gps-572904028179006580 .mobile\:\!gp-max-w-\[0px\]{max-width:0!important}.gps-572904028179006580 .mobile\:gp-rotate-0{--tw-rotate:0deg}.gps-572904028179006580 .mobile\:gp-rotate-0,.gps-572904028179006580 .mobile\:gp-rotate-180{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.gps-572904028179006580 .mobile\:gp-rotate-180{--tw-rotate:180deg}.gps-572904028179006580 .mobile\:\!gp-flex-row{flex-direction:row!important}.gps-572904028179006580 .mobile\:gp-flex-row{flex-direction:row}.gps-572904028179006580 .mobile\:\!gp-flex-nowrap{flex-wrap:nowrap!important}.gps-572904028179006580 .mobile\:gp-overflow-hidden{overflow:hidden}.gps-572904028179006580 .mobile\:gp-px-0,.gps-572904028179006580 .mobile\:gp-px-\[0px\]{padding-left:0;padding-right:0}.gps-572904028179006580 .mobile\:gp-py-0{padding-bottom:0;padding-top:0}.gps-572904028179006580 .mobile\:after\:gp-p-\[0px\]:after{content:var(--tw-content);padding:0}}.gps-572904028179006580 .\[\&\>svg\]\:gp-h-full>svg{height:100%}.gps-572904028179006580 .\[\&\>svg\]\:gp-w-full>svg{width:100%}.gps-572904028179006580 .\[\&_\*\]\:gp-max-w-full *{max-width:100%}.gps-572904028179006580 .\[\&_p\]\:gp-inline p{display:inline}.gps-572904028179006580 .\[\&_p\]\:gp-whitespace-pre-line p{white-space:pre-line}.gps-572904028179006580 .\[\&_p\]\:after\:gp-whitespace-pre-wrap p:after{content:var(--tw-content);white-space:pre-wrap}.gps-572904028179006580 .\[\&_p\]\:after\:gp-content-\[\'\\A\'\] p:after{--tw-content:"\A";content:var(--tw-content)}</style>

    
    <gp-sticky
    data-id="gLvmE29viM"
      gp-data='{"uid":"gLvmE29viM","setting":{"display":{"desktop":"always"}},"advanced":{"d":{"desktop":true,"mobile":true,"tablet":true}}}'
      id="gLvmE29viM"
      data-id="gLvmE29viM"
      class="gLvmE29viM {% if section.settings.section_preload == "false" %}gps-lazy {% endif %}  "
      style="margin:0 auto;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:0px 0px 10px 0px #0000001a;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--left:50%;--t:translateX(-50%);--left-tablet:50%;--t-tablet:translateX(-50%);--left-mobile:50%;--t-mobile:translateX(-50%);--top:auto;--bottom:0;--pos:fixed;--top-tablet:auto;--bottom-tablet:0;--pos-tablet:fixed;--top-mobile:auto;--bottom-mobile:0;--pos-mobile:fixed;--w:100%;--w-tablet:100%;--w-mobile:100%;z-index:100000"
    >
      <div 
         
        style="--bgc:#FFFFFF;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
      >
        <div
      
      
      class="g2Vp_7VvJU gp-relative gp-flex gp-flex-col"
    >
      
    {%- assign gpBkProduct = product -%}
    
        {%- liquid
          if request.page_type == 'product'
            if 'static' == 'static'
              if 'latest' == 'latest'
                paginate collections.all.products by 100000
                  assign product = collections.all.products | sort: 'created_at' | reverse | first
                endpaginate
              else
                assign product = all_products['']
                assign productId = 'latest' | times: 1
                if product == empty or product == null
                  paginate collections.all.products by 100000
                    for item in collections.all.products
                      if item.id == productId
                        assign product = item
                      endif
                    endfor
                  endpaginate
                endif
              endif
            endif
          else
            if 'latest' == 'latest'
              paginate collections.all.products by 100000
                assign product = collections.all.products | sort: 'created_at'| reverse | first
              endpaginate
            else
              assign product = all_products['']
              assign productId = 'latest' | times: 1
              if product == empty or product == null
                paginate collections.all.products by 100000
                  for item in collections.all.products
                    if item.id == productId
                      assign product = item
                    endif
                  endfor
                endpaginate
              endif
            endif
          endif
        -%}
      

    {%-if product != empty and product != null -%}
      {%- assign initVariantId =  -%}
      {%- assign product_form_id = 'product-form-' | append: "g9PiVMv_bi" -%}
      {%- assign variant = product.selected_or_first_available_variant -%}
      {%- assign productSelectedVariant = product.selected_or_first_available_variant -%}
      {%-if productSelectedVariant == empty or productSelectedVariant == null -%}
        {%- assign productSelectedVariant = product.selected_or_first_available_variant -%}
      {%- endif -%}
      {%-if variant == empty or variant == null -%}
        {%- assign variant = product.selected_or_first_available_variant -%}
      {%- endif -%}
      <gp-product data-uid="g9PiVMv_bi" data-id="g9PiVMv_bi"   gp-context='{"productId": {{ product.id }}, "preSelectedOptionIds": [], "isSyncProduct": "true", "variantSelected": {{ variant | json | escape }}, "inventoryQuantity": {{ variant.inventory_quantity }}, "quantity": 1, "formId": "{{ product_form_id }}" }'
        gp-data='{"variantSelected": {{ variant | json | escape }}, "quantity": 1, "productUrl":{{ product.url | json | escape }}, "productHandle":{{ product.handle | json | escape }}, "collectionUrl": {{ collection.url | json | escape }}, "collectionHandle": {{ collection.handle | json | escape }}}'>
        <product-form class="product-form">
          {%- form 'product', product, id: product_form_id, class: 'form contents', data-type: 'add-to-cart-form', autocomplete: 'off'  -%}
            <input type="hidden" name="id" value="{{ variant.id }}" />
            <input type="hidden" name="quantity" value="{{ quantity }}" />
            <button type="submit" onclick="return false;" style="display:none;"></button>
            
       
      
    <div
      id="g9PiVMv_bi" data-id="g9PiVMv_bi-row"
        style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pt:21px;--pl:15px;--pb:21px;--pr:15px;--mb-mobile:0px;--pt-mobile:21px;--pl-mobile:15px;--pb-mobile:21px;--pr-mobile:15px;--cg:12px;--cg-tablet:12px;--pc:start;--gtc:minmax(0, 5fr) minmax(0, 7fr);--gtc-tablet:minmax(0, 12fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:100%;--w-mobile:100%"
        class="g9PiVMv_bi gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:center"
      class="g4KBegJY6b gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      parentTag="Col" id="gsq9K3A-Mj" data-id="gsq9K3A-Mj"
        style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--mb:0px;--mb-mobile:14px;--mb-tablet:16px;--cg:24px;--cg-tablet:24px;--cg-mobile:14px;--pc:start;--gtc:minmax(0, auto) minmax(0, auto);--gtc-mobile:minmax(0, auto) minmax(0, auto);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gsq9K3A-Mj gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:center"
      class="gglLMcyi7_ gp-relative gp-flex gp-flex-col"
    >
      
  {% assign featured_image = product.featured_image %}
  {%- if variant != null and variant.featured_image != null -%}
  {% assign featured_image = variant.featured_image %}
  {%- endif -%}
    <gp-product-images-v2
      gp-data='
    {
      "id":"gA_QhaOpLX",
      "pageContext": {"pageType":"GP_PRODUCT","sectionName":"","isPreviewing":false,"isOptimizePlan":false,"isTranslateWithLocale":false,"isLazyLoadSection":true,"isLazyLoadImage":false,"hasCollectionHandle":true,"numberOfProductOfProductList":0,"pageBackground":"","fontType":"google","primaryLocale":"","enableLazyLoadImage":false},
      "setting":{"arrowIcon":"<svg width=\"100%\" height=\"100%\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n    <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M12 0C18.6274 0 24 5.37258 24 12C24 18.6274 18.6274 24 12 24C5.37258 24 0 18.6274 0 12C0 5.37258 5.37258 0 12 0ZM10.6828 8.13017C10.5266 7.95661 10.2734 7.95661 10.1172 8.13017C9.96095 8.30374 9.96095 8.58515 10.1172 8.75871L13.0343 12L10.1172 15.2413C9.96095 15.4149 9.96095 15.6963 10.1172 15.8698C10.2734 16.0434 10.5266 16.0434 10.6828 15.8698L13.8828 12.3143C14.0391 12.1407 14.0391 11.8593 13.8828 11.6857L10.6828 8.13017Z\" fill=\"currentColor\"/>\n    </svg>","arrowIconColor":"#000000","arrowNavBorder":{"border":"none","borderType":"none","borderWidth":"1px","isCustom":false,"width":"1px 1px 1px 1px"},"arrowNavRadius":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"none"},"borderActive":{"border":"solid","borderType":"none","borderWidth":"1px","color":"#000000","isCustom":"false","width":"1px 1px 1px 1px"},"clickOpenLightBox":{"desktop":false},"dragToScroll":true,"ftArrowIcon":"<svg width=\"100%\" height=\"100%\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n    <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M12 0C18.6274 0 24 5.37258 24 12C24 18.6274 18.6274 24 12 24C5.37258 24 0 18.6274 0 12C0 5.37258 5.37258 0 12 0ZM10.6828 8.13017C10.5266 7.95661 10.2734 7.95661 10.1172 8.13017C9.96095 8.30374 9.96095 8.58515 10.1172 8.75871L13.0343 12L10.1172 15.2413C9.96095 15.4149 9.96095 15.6963 10.1172 15.8698C10.2734 16.0434 10.5266 16.0434 10.6828 15.8698L13.8828 12.3143C14.0391 12.1407 14.0391 11.8593 13.8828 11.6857L10.6828 8.13017Z\" fill=\"currentColor\"/>\n    </svg>","ftArrowIconColor":"#000000","ftArrowNavBorder":{"border":"none","borderType":"none","borderWidth":"1px","isCustom":false,"width":"1px 1px 1px 1px"},"ftArrowNavRadius":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"none"},"ftClickOpenLightBox":{"desktop":"none"},"ftDotActiveColor":{"desktop":"line-3"},"ftDotColor":{"desktop":"bg-1"},"ftDotGapToCarousel":{"desktop":16},"ftDotSize":{"desktop":12},"ftDotStyle":{"desktop":"none"},"ftDragToScroll":false,"ftLoop":{"desktop":false},"ftNavigationPosition":{"desktop":"none"},"ftPauseOnHover":true,"ftSpeed":1,"galleryHoverEffect":"none","galleryZoom":150,"galleryZoomType":"default","hoverEffect":"none","loop":{"desktop":true},"navigationPosition":{"desktop":"inside"},"otherImage":0,"pauseOnHover":true,"preDisplay":"1st-available-variant","preload":false,"qualityPercent":{"desktop":100},"qualityType":{"desktop":"finest"},"speed":1,"type":{"desktop":"slider"},"typeDisplay":"all-images","zoom":150,"zoomType":"default"},
      "styles":{"align":{"desktop":"flex-start"},"corner":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"none"},"ftCorner":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"none"},"ftLayout":{"desktop":"cover"},"ftShape":{"desktop":{"gap":"","height":"70px","shape":"square","shapeLinked":true,"shapeValue":"1/1","width":"70px"},"mobile":{"customShapeValue":"","gap":"","height":"56px","shape":"square","shapeLinked":true,"shapeValue":"1/1","width":"56px"}},"itemSpacing":{"desktop":"var(--g-s-s)","mobile":"var(--g-s-s)"},"layout":{"desktop":"cover"},"position":{"desktop":"only-feature","mobile":"bottom-center"},"ratioLayout":{"desktop":[2,10]},"ratioLayoutRight":{"desktop":[10,2]},"shape":{"desktop":{"shape":"square","shapeLinked":true,"shapeValue":"1/1","width":"100%"}},"shapeFor1Col":{"desktop":{"shape":"square","shapeLinked":true,"shapeValue":"1/1","width":"100%"}},"shapeFor2Col":{"desktop":{"shape":"square","shapeLinked":true,"shapeValue":"1/1","width":"50%"}},"shapeForBottom":{"desktop":{"shape":"square","shapeLinked":true,"shapeValue":"1/1","width":"20%"}},"shapeForFtOnly":{"desktop":{"shape":"square","shapeLinked":true,"shapeValue":"1/1","width":"100%"}},"spacing":{"desktop":"var(--g-s-s)","mobile":"var(--g-s-s)"}}, "productUrl":{{product.url | json | escape}}, "product":{{product | json | escape}}, "collectionUrl": {{ collection.url | json | escape }}, "collection": {{ collection | json | escape}}
    }
  '
      section-id="{{section.id}}"
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:0px"
      data-id="gA_QhaOpLX"
      class="gA_QhaOpLX gp-overflow-hidden"
    >
      <div
        class="{%- if product.media.size > 1 -%} gp-grid gp-w-full !gp-m-0 gp-relative {%- else -%} gp-w-full !gp-m-0 gp-relative {%- endif -%}"
        {%- if product.media.size > 1 -%}
        style="--gtc:minmax(0, 12fr);--gtc-tablet:minmax(0, 12fr);--gtc-mobile:minmax(0, 12fr);--gg:var(--g-s-s);--gg-mobile:var(--g-s-s)"
        {%- else -%}
        style="--gtc:minmax(0, 12fr);--gg:var(--g-s-s);--gg-mobile:var(--g-s-s)"
        {%- endif -%}
      >
        
    {% capture featureImageOnlyOne %}
      
        {% assign featureMedia = variant.featured_media %}
        {% unless featureMedia %}
        {% assign featureMedia = product.featured_media %}
        {% endunless %}
        {% if product.media.size > 1 %}
          
          {% assign featureMedia = variant.featured_media %}
          {% unless featureMedia %}
          {% assign featureMedia = product.featured_media %}
          {% endunless %}
        
        {% endif %}
        {% assign largestRatio = 0 %}
        {% assign height = featureMedia.height | times: 1.0 %}
        {% assign width = featureMedia.width | times: 1.0 %}
        {% assign ratio = height | divided_by: width %}
        {% if ratio > largestRatio %}
          {% assign largestRatio = ratio %}
        {% endif %}
        {% assign productImageWidth = 0 %}
        {% case featureMedia.media_type %}
          {% when 'image' %}
            {% assign productImageWidth = featureMedia.width %}
          {% else %}
            {% assign productImageWidth = featureMedia.preview_image.width %}
        {% endcase %}
        {% if featureMedia == null %}
        {% assign productImageWidth = 1600 %}
        {% endif %}
        <div 
          class='gp-feature-image-carousel gp-feature-image-only' 
          style="--o:0;--o-tablet:0;--o-mobile:0;--d:flex;--d-mobile:flex;--d-tablet:flex;--jc:flex-start"
        >
          <div 
            class="gp-relative"
            style="--w:70px;--w-tablet:70px;--w-mobile:56px"
          >
            
      
    
            <div 
              type="gp-feature-image-only"
              product-id="{{product.id}}"
              product-media="{{product.media.size}}"
              class="gp-image-item gp-ft-image-item gp-group gp-z-0 gp-flex !gp-max-w-full !gp-w-full gp-relative gp-items-start gp-justify-center gp-overflow-hidden gp-featured-image-wrapper"
              style="--h:auto;--h-tablet:auto;--h-mobile:auto;width:{{productImageWidth}}px"
            >
              <div 
                class="gp-w-full  {% if featureMedia == null or featureMedia.media_type == 'image' %} {{ 'gp-h-0' }} {% else %} {{ 'gp-h-full !gp-pb-0' }} {% endif %} gp-relative" 
                style="--pb: 100%;--pb-tablet: 100%;--pb-mobile: 100%; {% if featureMedia.media_type == 'video' or featureMedia.media_type == 'external_video' %} {{ 'display: flex; align-items: center; justify-content: center' }} {% endif %}"
              >
                
    {% case featureMedia.media_type %}
      {% when 'image' %}
        
      
    {% assign src = featureMedia.src %}
    
    
  <img
      id="{%- if featureMedia != null -%}
              {{featureMedia.id}}
            {%- endif -%}"
      
      draggable="false"
      class="gp-w-full gp-h-full gp-absolute gp-top-0 gp-left-0 featured-image-only gp-cursor-pointer !gp-rounded-none gp-w-full gp-transition-opacity {{shouldHidden}}"
      data-src="{{src | image_url}}" data-srcset="{%- if src != null -%}
              {{src | img_url: '480x480'}} 480w, {{src | img_url: '768x768'}} 768w,{{src | img_url: '1024x1024'}} 1024w,{{src | img_url: '1440x1440'}} 1440w
            {%- else -%}
              {{ 'https://cdn.shopify.com/s/assets/no-image-2048-5e88c1b20e087fb7bbe9a3771824e743c244f437e4f8ba93bbf7b11b53f7824c_large.gif' }}
            {%- endif -%}" src="{{src | image_url}}" width="{{featureMedia.width}}" height="{{featureMedia.height}}" alt="{{featureMedia.alt}}" base-src="{{src | image_url}}"
      quality-type={"desktop":"finest"}
      quality-percent={"desktop":100}
      data-sizes="auto"
      sizes="100vw"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1;--objf:cover"
    />
  
      
      
    
      {% when 'external_video' %}
        {% assign mediaSourceVideo = featureMedia | external_video_url %}
        
    <iframe
      width="100%" height="100%"
      class="feature-video gp-w-full gp-h-full gp-relative"
      src="{{mediaSourceVideo}}"
      controls="true"
      allowfullscreen="true"
      allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture"
      frameborder="0"
      autoplay="false"
      alt="{{featureMedia.alt}}"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1"
    >
    </iframe>
  
      {% when 'video' %}
        {% assign mediaSourceVideo = featureMedia.sources.last.url %}
        
  <gp-lite-html5-embed>
    
    <video
      id="gp-video-undefined"
      class=" gp-w-full lazy"
      style="width:100%;max-height:100%"
      controls
      
      
      
      title="{{featureMedia.alt}}"
      preload="none"
      data-src="{{mediaSourceVideo}}"
      src=""
      poster=""
      playsinline
    >
    
  </video>
    <div
      style="width:100%;max-height:100%"
      class="gp-absolute gp-top-0 gp-left-0  gp-w-full gp-thumbnail-video gp-hidden"
    >
      <img
        id="video-thumbnail"
        src=""
        class="gp-w-full gp-h-full gp-object-cover"
        alt="Video Thumbnail"
      ></img>
      <button
        type="button"
        class="gp-absolute gp-left-1/2 gp-top-1/2 gp-flex gp-aspect-[56/32] gp-w-14 -gp-translate-x-1/2 -gp-translate-y-1/2 gp-items-center gp-justify-center gp-rounded gp-bg-black/80 gp-transition-colors hover:gp-bg-[#ef0800]"
        aria-label="Play"
      >
        <svg class="gp-w-5 gp-text-white" viewBox="0 0 24 24">
          <path
            fill="currentColor"
            d="M5 5.274c0-1.707 1.826-2.792 3.325-1.977l12.362 6.726c1.566.853 1.566 3.101 0 3.953L8.325 20.702C6.826 21.518 5 20.432 5 18.726V5.274Z"
          />
        </svg>
      </button>
    </div>
    </gp-lite-html5-embed>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-lite-html5-embed.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
    <script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.3/dist/lazyload.min.js"></script>
    <script>
        if(lazyLoadInstance){lazyLoadInstance.update()}else{var lazyLoadInstance = new LazyLoad()}
    </script>
  
      {% when 'model' %}
        
    <model-viewer
      class="gp-w-full gp-h-full model-viewer gp-z-[90]"
      width="100%"
      
      
      
      
      src="{% if featureMedia.sources.first.url contains '.glb' %}{{ featureMedia.sources.first.url }}{% else %}{{featureMedia.sources.last.url}}{% endif %}"
      alt="{{featureMedia.preview_image.alt}}"
      camera-controls="true"
      poster="{{featureMedia.preview_image.src | product_img_url: '1024x1024'}}"
      data-js-focus-visible=""
      data-shopify-feature="1.12"
      ar-status="not-presenting"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1">
    </model-viewer>
  
      {% else %}
        
    
  <img
      
      
      draggable="false"
      class="gp-w-full gp-h-full gp-absolute gp-top-0 gp-left-0 featured-image-only gp-cursor-pointer !gp-rounded-none"
      data-src data-srcset="https://cdn.shopify.com/s/assets/no-image-2048-5e88c1b20e087fb7bbe9a3771824e743c244f437e4f8ba93bbf7b11b53f7824c_large.gif" src width="2237" height="1678" alt="No Image"
      quality-type={"desktop":"finest"}
      quality-percent={"desktop":100}
      data-sizes="auto"
      sizes="100vw"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1;--objf:cover"
    />
  
    {% endcase %}
    
              </div>
            </div>
          </div>
        </div>
      
    {% endcapture %}
    {% if product.media.size > 1 %}
      
      
    <gp-carousel data-id="gp-carousel-gA_QhaOpLX" type="gp-feature-image-carousel" product-id="{{product.id}}" product-media="{{product.media.size}}" id="gp-root-carousel-ft-gp-carousel-gA_QhaOpLX-{{section.id}}-{{product.id}}-{{section.id}}" class="
          gp-px-0 tablet:gp-px-0 mobile:gp-px-0
          gp-flex-1 gp-w-full gp-feature-image-carousel
          gp-group/carousel gp-flex" gp-data='{"id":"ft-gp-carousel-gA_QhaOpLX-{{section.id}}-{{product.id}}-{{section.id}}","setting":{"loop":{"desktop":false},"slidesToShow":{"desktop":1},"dotStyle":{"desktop":"none","tablet":"none","mobile":"none"},"dotSize":{"desktop":12},"dotGapToCarousel":{"desktop":16},"dotColor":{"desktop":"bg-1"},"dotActiveColor":{"desktop":"line-3"},"controlOverContent":{"desktop":false,"tablet":false,"mobile":false},"enableDrag":{"desktop":false,"tablet":false,"mobile":false},"arrowCustom":"<svg width=\"100%\" height=\"100%\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n    <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M12 0C18.6274 0 24 5.37258 24 12C24 18.6274 18.6274 24 12 24C5.37258 24 0 18.6274 0 12C0 5.37258 5.37258 0 12 0ZM10.6828 8.13017C10.5266 7.95661 10.2734 7.95661 10.1172 8.13017C9.96095 8.30374 9.96095 8.58515 10.1172 8.75871L13.0343 12L10.1172 15.2413C9.96095 15.4149 9.96095 15.6963 10.1172 15.8698C10.2734 16.0434 10.5266 16.0434 10.6828 15.8698L13.8828 12.3143C14.0391 12.1407 14.0391 11.8593 13.8828 11.6857L10.6828 8.13017Z\" fill=\"currentColor\"/>\n    </svg>","arrowCustomColor":"#000000","arrowBorder":{"desktop":{"border":"none","borderType":"none","borderWidth":"1px","isCustom":false,"width":"1px 1px 1px 1px"}},"roundedArrow":{"desktop":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"none"}},"sneakPeakType":{"desktop":"center"},"arrowGapToEachSide":"16","navigationStyle":{"desktop":"none"},"arrowButtonSize":{"desktop":{"width":"24px","height":"24px"}}},"styles":{"sizeSetting":{"desktop":{"width":"70px","height":"auto"},"tablet":{"width":"70px","height":"auto"},"mobile":{"width":"56px","height":"auto"}},"align":{"desktop":"flex-start","tablet":"flex-start","mobile":"flex-start"}},"isHiddenArrowWhenDisabled":true}' style="--jc:flex-start;--o:0;--o-tablet:0;--o-mobile:0;--d:flex;--d-mobile:flex;--d-tablet:flex">
      <div class="gp-hidden gp-aspect-square gp-w-[12px] gp-rounded-full gp-shadow-md"></div>
      <div
        
        class="gp-relative gp-my-0 tablet:gp-px-0 gp-max-w-full mobile:gp-px-0 gp-carousel-wrapper mobile:gp-block tablet:gp-block gp-block gp-carousel-gA_QhaOpLX gp-featured-image-wrapper"
        style="--w:70px;--w-tablet:70px;--w-mobile:56px;--h:auto;--h-tablet:auto;--h-mobile:auto"
      >
        <div
          class="gp-h-full gp-relative gp-mx-auto gp-flex gp-items-center gp-justify-between mobile:!gp-flex-row tablet:!gp-flex-row !gp-flex-row"
          style="--h:100%;--h-tablet:100%;--h-mobile:100%;gap:16px"
        >
          
    <button
      type="button"
      aria-label='Carousel Back Arrow'
      class="gp-z-1 ft-gp-carousel-gA_QhaOpLX-{{section.id}}-{{product.id}} gp-carousel-action-back gem-slider-previous ft-gp-carousel-gA_QhaOpLX-{{section.id}}-{{product.id}}-{{section.id}} gp-carousel-arrow-gp-carousel-gA_QhaOpLX gp-items-center gp-justify-center gp-overflow-hidden gp-shrink-0  gp-cursor-pointer gp-text-gray-500 !gp-hidden tablet:!gp-hidden mobile:!gp-hidden"
      style="--left:initial;--top:initial;--right:initial;--bottom:;--left-tablet:initial;--top-tablet:initial;--right-tablet:initial;--bottom-tablet:;--left-mobile:initial;--top-mobile:initial;--right-mobile:initial;--bottom-mobile:;--d:none;--d-tablet:none;--d-mobile:none;background-color:;--w:24px;--h:24px"
      onClick={onClick}
      
    >
      <div 
  class="gp-flex gp-items-center gp-justify-center [&>svg]:gp-h-full [&>svg]:gp-w-full gp-rotate-180 tablet:gp-rotate-180 mobile:gp-rotate-180"
  style="--c:#000000;--w:undefinedpx;--w-tablet:undefinedpx;--w-mobile:undefinedpx;--h:undefinedpx;--h-tablet:undefinedpx;--h-mobile:undefinedpx"
  >
    <svg width="100%" height="100%" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fill-rule="evenodd" clip-rule="evenodd" d="M12 0C18.6274 0 24 5.37258 24 12C24 18.6274 18.6274 24 12 24C5.37258 24 0 18.6274 0 12C0 5.37258 5.37258 0 12 0ZM10.6828 8.13017C10.5266 7.95661 10.2734 7.95661 10.1172 8.13017C9.96095 8.30374 9.96095 8.58515 10.1172 8.75871L13.0343 12L10.1172 15.2413C9.96095 15.4149 9.96095 15.6963 10.1172 15.8698C10.2734 16.0434 10.5266 16.0434 10.6828 15.8698L13.8828 12.3143C14.0391 12.1407 14.0391 11.8593 13.8828 11.6857L10.6828 8.13017Z" fill="currentColor"/>
    </svg>
    </div>
      <style>
    .ft-gp-carousel-gA_QhaOpLX-{{section.id}}-{{product.id}}.gp-carousel-arrow-gp-carousel-gA_QhaOpLX {
      
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
    }
    .ft-gp-carousel-gA_QhaOpLX-{{section.id}}-{{product.id}}.gp-carousel-arrow-gp-carousel-gA_QhaOpLX::before {
      content: '';
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      z-index: 10;
      border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
      
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
    }
    @media only screen and (max-width: 1024px) and (min-width: 768px) {
      .ft-gp-carousel-gA_QhaOpLX-{{section.id}}-{{product.id}}.gp-carousel-arrow-gp-carousel-gA_QhaOpLX {
        
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
      }
      .ft-gp-carousel-gA_QhaOpLX-{{section.id}}-{{product.id}}.gp-carousel-arrow-gp-carousel-gA_QhaOpLX::before {
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
      }
    }
    @media only screen and (max-width: 768px) {
      .ft-gp-carousel-gA_QhaOpLX-{{section.id}}-{{product.id}}.gp-carousel-arrow-gp-carousel-gA_QhaOpLX {
        
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
      }
      .ft-gp-carousel-gA_QhaOpLX-{{section.id}}-{{product.id}}.gp-carousel-arrow-gp-carousel-gA_QhaOpLX::before {
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
      }
    }
  </style>
    </button>
  
          <div id="gp-carousel-ft-gp-carousel-gA_QhaOpLX-{{section.id}}-{{product.id}}-{{section.id}}" class="gem-slider gp-h-full gp-overflow-hidden gp-select-none mobile:!gp-flex-nowrap tablet:!gp-flex-nowrap !gp-flex-nowrap mobile:!gp-min-h-full tablet:!gp-min-h-full !gp-min-h-full"
          style="--cg-mobile:undefinedpx;--cg-tablet:undefinedpx;--cg:undefinedpx">
          
          {%- if product.media.size > 0 -%}
            
      
    
            {% assign largestRatio = 0 %}
            {% for featureMedia in product.media %}
              {% assign height = featureMedia.height | times: 1.0 %}
              {% assign width = featureMedia.width | times: 1.0 %}
              {% assign ratio = height | divided_by: width %}
              {% if ratio > largestRatio %}
                {% assign largestRatio = ratio %}
              {% endif %}
            {% endfor %}
            {%- for featureMedia in product.media -%}
              {%- if featureMedia.media_type == 'image' -%}
                {%- for image in product.images -%}
                  {% if image.src == featureMedia.src %}
                    {% assign imageID = image.id %}
                    {% break %}
                  {% endif%}
                {% endfor %}
              {%- else -%}
                {% assign imageID = '' %}
              {%- endif -%}
              
    {% assign productImageWidth = 0 %}
    {% case featureMedia.media_type %}
      {% when 'image' %}
        {% assign productImageWidth = featureMedia.width %}
      {% else %}
        {% assign productImageWidth = featureMedia.preview_image.width %}
    {% endcase %}
    
    <div
      media-type="{{media.media_type}}"
      media-poster="{{media.preview_image.src}}"
      media-source-url="{{media.sources.first.url}}"
      media-last-source-url="{{mediaSourceUrl}}"
      external-id="{{media.external_id}}"
      grid-index="{{forloop.index}}"
      
      id="{{section.id}}-{{imageID}}"
      style="width:{{productImageWidth}}px;--minw:calc(100% / 4 - 33px);--minw-tablet:calc(100% / 4 - 33px);--minw-mobile:calc(100% / 4 - 33px);--maxw:calc(100% / 4 - 33px);--maxw-tablet:calc(100% / 4 - 33px);--maxw-mobile:calc(100% / 4 - 33px);outline-color:var(--g-c-brand, brand);--h:auto;--h-tablet:auto;--h-mobile:auto;--ml:0px;--ml-tablet:0px;--ml-mobile:0px"
      class="gem-slider-item gp-w-full gem-slider-item-gp-carousel-gA_QhaOpLX-{{section.id}}-{{product.id}} gp-child-item-undefined gp-group gp-z-0 gp-flex !gp-min-w-full !gp-max-w-full gp-w-full gp-relative gp-items-start gp-justify-center gp-overflow-hidden gp-outline-1 -gp-outline-offset-1 gp-image-item gp-ft-image-item data-[outline=active]:gp-outline undefined"
      data-index=""
    >
      <div
        class="gp-w-full gp-h-full",
        
      >
      
      <div 
        class="gp-w-full  {% if featureMedia == null or featureMedia.media_type == 'image'  %} {{ 'gp-h-0' }} {% else %} {{ 'gp-h-full !gp-pb-0' }} {% endif %} gp-relative" 
        style="--pb: 100%;--pb-tablet: 100%;--pb-mobile: 100%; {% if featureMedia.media_type == 'video' or featureMedia.media_type == 'external_video' %} {{ 'display: flex; align-items: center; justify-content: center' }} {% endif %}"
      >
        
    {% case featureMedia.media_type %}
      {% when 'image' %}
        
      
    {% assign src = featureMedia.src %}
    
    
  <img
      id="{%- if featureMedia != null -%}
              {{featureMedia.id}}
            {%- endif -%}"
      
      draggable="false"
      class="gp-w-full gp-h-full gp-absolute gp-top-0 gp-left-0 featured-image-only gp-cursor-pointer !gp-rounded-none gp-w-full gp-transition-opacity {{shouldHidden}}"
      data-src="{{src | image_url}}" data-srcset="{%- if src != null -%}
              {{src | img_url: '480x480'}} 480w, {{src | img_url: '768x768'}} 768w,{{src | img_url: '1024x1024'}} 1024w,{{src | img_url: '1440x1440'}} 1440w
            {%- else -%}
              {{ 'https://cdn.shopify.com/s/assets/no-image-2048-5e88c1b20e087fb7bbe9a3771824e743c244f437e4f8ba93bbf7b11b53f7824c_large.gif' }}
            {%- endif -%}" src="{{src | image_url}}" width="{{featureMedia.width}}" height="{{featureMedia.height}}" alt="{{featureMedia.alt}}" base-src="{{src | image_url}}"
      quality-type={"desktop":"finest"}
      quality-percent={"desktop":100}
      data-sizes="auto"
      sizes="100vw"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1;--objf:cover"
    />
  
      
      
    
      {% when 'external_video' %}
        {% assign mediaSourceVideo = featureMedia | external_video_url %}
        
    <iframe
      width="100%" height="100%"
      class="feature-video gp-w-full gp-h-full gp-relative"
      src="{{mediaSourceVideo}}"
      controls="true"
      allowfullscreen="true"
      allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture"
      frameborder="0"
      autoplay="false"
      alt="{{featureMedia.alt}}"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1"
    >
    </iframe>
  
      {% when 'video' %}
        {% assign mediaSourceVideo = featureMedia.sources.last.url %}
        
  <gp-lite-html5-embed>
    
    <video
      id="gp-video-undefined"
      class=" gp-w-full lazy"
      style="width:100%;max-height:100%"
      controls
      
      
      
      title="{{featureMedia.alt}}"
      preload="none"
      data-src="{{mediaSourceVideo}}"
      src=""
      poster=""
      playsinline
    >
    
  </video>
    <div
      style="width:100%;max-height:100%"
      class="gp-absolute gp-top-0 gp-left-0  gp-w-full gp-thumbnail-video gp-hidden"
    >
      <img
        id="video-thumbnail"
        src=""
        class="gp-w-full gp-h-full gp-object-cover"
        alt="Video Thumbnail"
      ></img>
      <button
        type="button"
        class="gp-absolute gp-left-1/2 gp-top-1/2 gp-flex gp-aspect-[56/32] gp-w-14 -gp-translate-x-1/2 -gp-translate-y-1/2 gp-items-center gp-justify-center gp-rounded gp-bg-black/80 gp-transition-colors hover:gp-bg-[#ef0800]"
        aria-label="Play"
      >
        <svg class="gp-w-5 gp-text-white" viewBox="0 0 24 24">
          <path
            fill="currentColor"
            d="M5 5.274c0-1.707 1.826-2.792 3.325-1.977l12.362 6.726c1.566.853 1.566 3.101 0 3.953L8.325 20.702C6.826 21.518 5 20.432 5 18.726V5.274Z"
          />
        </svg>
      </button>
    </div>
    </gp-lite-html5-embed>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-lite-html5-embed.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
    <script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.3/dist/lazyload.min.js"></script>
    <script>
        if(lazyLoadInstance){lazyLoadInstance.update()}else{var lazyLoadInstance = new LazyLoad()}
    </script>
  
      {% when 'model' %}
        
    <model-viewer
      class="gp-w-full gp-h-full model-viewer gp-z-[90]"
      width="100%"
      
      
      
      
      src="{% if featureMedia.sources.first.url contains '.glb' %}{{ featureMedia.sources.first.url }}{% else %}{{featureMedia.sources.last.url}}{% endif %}"
      alt="{{featureMedia.preview_image.alt}}"
      camera-controls="true"
      poster="{{featureMedia.preview_image.src | product_img_url: '1024x1024'}}"
      data-js-focus-visible=""
      data-shopify-feature="1.12"
      ar-status="not-presenting"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1">
    </model-viewer>
  
      {% else %}
        
    
  <img
      
      
      draggable="false"
      class="gp-w-full gp-h-full gp-absolute gp-top-0 gp-left-0 featured-image-only gp-cursor-pointer !gp-rounded-none"
      data-src data-srcset="https://cdn.shopify.com/s/assets/no-image-2048-5e88c1b20e087fb7bbe9a3771824e743c244f437e4f8ba93bbf7b11b53f7824c_large.gif" src width="2237" height="1678" alt="No Image"
      quality-type={"desktop":"finest"}
      quality-percent={"desktop":100}
      data-sizes="auto"
      sizes="100vw"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1;--objf:cover"
    />
  
    {% endcase %}
    
      </div>
      
      </div>
    </div>
  
            {% endfor %}
          {%- else -%}
            
  <img
      id="noImageError"
      
      draggable="false"
      class="gp-w-full featured-image-only !gp-rounded-none"
      data-src="{{ 'https://cdn.shopify.com/s/assets/no-image-2048-5e88c1b20e087fb7bbe9a3771824e743c244f437e4f8ba93bbf7b11b53f7824c_large.gif' }}" data-srcset="{{ 'https://cdn.shopify.com/s/assets/no-image-2048-5e88c1b20e087fb7bbe9a3771824e743c244f437e4f8ba93bbf7b11b53f7824c_large.gif' }}" src="{{ 'https://cdn.shopify.com/s/assets/no-image-2048-5e88c1b20e087fb7bbe9a3771824e743c244f437e4f8ba93bbf7b11b53f7824c_large.gif' }}" width="480" height="480" alt="no image"
      quality-type={"desktop":"finest"}
      quality-percent={"desktop":100}
      data-sizes="auto"
      sizes="100vw"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1;--objf:cover;height:100%"
    />
  
          {%- endif -%}
        
          </div>
          
    <button
      type="button"
      aria-label='Carousel Next Arrow'
      class="gp-z-1 ft-gp-carousel-gA_QhaOpLX-{{section.id}}-{{product.id}} gp-carousel-action-next gem-slider-next ft-gp-carousel-gA_QhaOpLX-{{section.id}}-{{product.id}}-{{section.id}} gp-carousel-arrow-gp-carousel-gA_QhaOpLX gp-items-center gp-justify-center gp-overflow-hidden gp-shrink-0  gp-cursor-pointer gp-text-gray-500 !gp-hidden tablet:!gp-hidden mobile:!gp-hidden"
      style="--left:initial;--top:;--right:initial;--bottom:initial;--left-tablet:initial;--top-tablet:;--right-tablet:initial;--bottom-tablet:initial;--left-mobile:initial;--top-mobile:;--right-mobile:initial;--bottom-mobile:initial;--d:none;--d-tablet:none;--d-mobile:none;background-color:;--w:24px;--h:24px"
      onClick={onClick}
      
    >
      <div 
  class="gp-flex gp-items-center gp-justify-center [&>svg]:gp-h-full [&>svg]:gp-w-full gp-rotate-0 tablet:gp-rotate-0 mobile:gp-rotate-0"
  style="--c:#000000;--w:undefinedpx;--w-tablet:undefinedpx;--w-mobile:undefinedpx;--h:undefinedpx;--h-tablet:undefinedpx;--h-mobile:undefinedpx"
  >
    <svg width="100%" height="100%" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fill-rule="evenodd" clip-rule="evenodd" d="M12 0C18.6274 0 24 5.37258 24 12C24 18.6274 18.6274 24 12 24C5.37258 24 0 18.6274 0 12C0 5.37258 5.37258 0 12 0ZM10.6828 8.13017C10.5266 7.95661 10.2734 7.95661 10.1172 8.13017C9.96095 8.30374 9.96095 8.58515 10.1172 8.75871L13.0343 12L10.1172 15.2413C9.96095 15.4149 9.96095 15.6963 10.1172 15.8698C10.2734 16.0434 10.5266 16.0434 10.6828 15.8698L13.8828 12.3143C14.0391 12.1407 14.0391 11.8593 13.8828 11.6857L10.6828 8.13017Z" fill="currentColor"/>
    </svg>
    </div>
      <style>
    .ft-gp-carousel-gA_QhaOpLX-{{section.id}}-{{product.id}}.gp-carousel-arrow-gp-carousel-gA_QhaOpLX {
      
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
    }
    .ft-gp-carousel-gA_QhaOpLX-{{section.id}}-{{product.id}}.gp-carousel-arrow-gp-carousel-gA_QhaOpLX::before {
      content: '';
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      z-index: 10;
      border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
      
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
    }
    @media only screen and (max-width: 1024px) and (min-width: 768px) {
      .ft-gp-carousel-gA_QhaOpLX-{{section.id}}-{{product.id}}.gp-carousel-arrow-gp-carousel-gA_QhaOpLX {
        
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
      }
      .ft-gp-carousel-gA_QhaOpLX-{{section.id}}-{{product.id}}.gp-carousel-arrow-gp-carousel-gA_QhaOpLX::before {
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
      }
    }
    @media only screen and (max-width: 768px) {
      .ft-gp-carousel-gA_QhaOpLX-{{section.id}}-{{product.id}}.gp-carousel-arrow-gp-carousel-gA_QhaOpLX {
        
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
      }
      .ft-gp-carousel-gA_QhaOpLX-{{section.id}}-{{product.id}}.gp-carousel-arrow-gp-carousel-gA_QhaOpLX::before {
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
      }
    }
  </style>
    </button>
  
        </div>
        <div
    class="gp-items-center gp-justify-center gp-gap-2 gp-text-center gp-carousel-dot-container gp-carousel-dot-container-ft-gp-carousel-gA_QhaOpLX-{{section.id}}-{{product.id}}-{{section.id}} gp-static gp-flex-row gp-left-0 gp-right-0 tablet:gp-static tablet:gp-flex-row tablet:gp-left-0 tablet:gp-right-0 mobile:gp-static mobile:gp-flex-row mobile:gp-left-0 mobile:gp-right-0"
    style="--bottom:16px;--bottom-tablet:16px;--bottom-mobile:16px;--d:none;--d-tablet:none;--d-mobile:none"
 ></div>
      </div>
    </gp-carousel>
    {% if product.media.size > 1 %} <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-carousel-v2.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script> {% endif %}

  
    
      
    {% else %}
      {{ featureImageOnlyOne }}
    {% endif %}
  
        {%- if product.media.size > 1 -%}
            <div
              class="gallery-wrapper gp-product-images-gallery gp-flex gp-overflow-hidden gp-max-w-full gp-max-h-full data-[only-image=true]:gp-hidden"
              style="--o:1;--o-tablet:1;--o-mobile:1;--jc:flex-start;--pos:static;--pos-tablet:static;--pos-mobile:static;--w:100%;--w-tablet:100%;--w-mobile:100%;--bottom:auto;--bottom-tablet:auto;--bottom-mobile:auto;--top:auto;--top-tablet:auto;--top-mobile:auto;--left:auto;--left-tablet:auto;--left-mobile:auto;--right:auto;--right-tablet:auto;--right-mobile:auto"
              data-only-image="{%- if product.media.size > 1 -%}false{%- else -%}true{%- endif -%}"
            >
              <style>
    .gem-slider-item-gA_QhaOpLX-{{product.id}}.gp-gallery-image-item::after, .gem-slider-item-gp-gallery-gA_QhaOpLX-{{product.id}}.gp-gallery-image-item::after {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      z-index: 1000;
      top: 0;
      left: 0;
      border-style: solid;
  border-width: 1px 1px 1px 1px;
  border-color: #000000;
  
      
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
    }
    .gem-slider-item-gA_QhaOpLX-{{product.id}}.gp-gallery-image-item[data-outline=active]:after, .gem-slider-item-gp-gallery-gA_QhaOpLX-{{product.id}}.gp-gallery-image-item[data-outline=active]:after {
      pointer-events: none;
    }
  </style>
              
    
    <gp-carousel data-id="gp-gallery-gA_QhaOpLX" type="grid-carousel" product-media="{{product.media.size}}" id="gp-root-carousel-gp-gallery-gA_QhaOpLX-{{product.id}}-{{section.id}}" class="gp-flex-1 gp-w-full carousel-gallery gp-px-0 tablet:gp-px-0 mobile:gp-px-0  gp-group/carousel gp-flex" gp-data='{"id":"gp-gallery-gA_QhaOpLX-{{product.id}}-{{section.id}}","setting":{"loop":{"desktop":true},"itemNumber":{"desktop":"auto","tablet":"auto","mobile":5},"dot":{"desktop":false,"tablet":false,"mobile":false},"dotStyle":{"desktop":"none","tablet":"none","mobile":"none"},"controlOverContent":{"desktop":true,"tablet":true,"mobile":true},"enableDrag":{"desktop":true,"tablet":true,"mobile":true},"vertical":{"desktop":false,"mobile":false,"tablet":false},"arrowCustom":"<svg width=\"100%\" height=\"100%\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n    <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M12 0C18.6274 0 24 5.37258 24 12C24 18.6274 18.6274 24 12 24C5.37258 24 0 18.6274 0 12C0 5.37258 5.37258 0 12 0ZM10.6828 8.13017C10.5266 7.95661 10.2734 7.95661 10.1172 8.13017C9.96095 8.30374 9.96095 8.58515 10.1172 8.75871L13.0343 12L10.1172 15.2413C9.96095 15.4149 9.96095 15.6963 10.1172 15.8698C10.2734 16.0434 10.5266 16.0434 10.6828 15.8698L13.8828 12.3143C14.0391 12.1407 14.0391 11.8593 13.8828 11.6857L10.6828 8.13017Z\" fill=\"currentColor\"/>\n    </svg>","arrowCustomColor":"#000000","arrowBorder":{"desktop":{"border":"none","borderType":"none","borderWidth":"1px","isCustom":false,"width":"1px 1px 1px 1px"}},"roundedArrow":{"desktop":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"none"}},"sneakPeakType":{"desktop":"center"},"arrowGapToEachSide":"16","navigationStyle":{"desktop":"inside"},"arrowButtonSize":{"desktop":{"width":"24px","height":"24px"}}},"styles":{"sizeSetting":{"desktop":{"width":"100%","height":"auto"},"tablet":{"width":"100%","height":"auto"},"mobile":{"width":"100%","height":"auto"}},"spacing":{"desktop":5,"tablet":5,"mobile":5}},"isHiddenArrowWhenDisabled":true}' style="--d:none;--d-mobile:flex;--d-tablet:none">
      <div class="gp-hidden gp-aspect-square gp-w-[12px] gp-rounded-full gp-shadow-md"></div>
      <div
        
        class="gp-relative gp-my-0 tablet:gp-px-0 gp-max-w-full mobile:gp-px-0 gp-carousel-wrapper mobile:gp-block tablet:gp-block gp-block gp-gallery-gA_QhaOpLX  gp-py-0 tablet:gp-py-0 mobile:gp-py-0"
        style="--w:100%;--w-tablet:100%;--w-mobile:100%;--h:auto;--h-tablet:auto;--h-mobile:auto"
      >
        <div
          class="gp-h-full gp-relative gp-mx-auto gp-flex gp-items-center gp-justify-between mobile:!gp-flex-row tablet:!gp-flex-row !gp-flex-row"
          style="--h:100%;--h-tablet:100%;--h-mobile:100%;gap:16px"
        >
          
    <button
      type="button"
      aria-label='Carousel Back Arrow'
      class="gp-z-1 gp-gallery-gA_QhaOpLX-{{product.id}} gp-carousel-action-back gem-slider-previous gp-gallery-gA_QhaOpLX-{{product.id}}-{{section.id}} gp-carousel-arrow-gp-gallery-gA_QhaOpLX gp-items-center gp-justify-center gp-overflow-hidden gp-shrink-0  gp-cursor-pointer gp-text-gray-500 !gp-flex !gp-absolute gp-z-2 tablet:!gp-flex tablet:!gp-absolute tablet:gp-z-2 mobile:!gp-flex mobile:!gp-absolute mobile:gp-z-2"
      style="--left:16px;--top:initial;--right:initial;--bottom:;--left-tablet:16px;--top-tablet:initial;--right-tablet:initial;--bottom-tablet:;--left-mobile:16px;--top-mobile:initial;--right-mobile:initial;--bottom-mobile:;--d:flex;--d-tablet:flex;--d-mobile:flex;background-color:;--w:24px;--h:24px"
      onClick={onClick}
      
    >
      <div 
  class="gp-flex gp-items-center gp-justify-center [&>svg]:gp-h-full [&>svg]:gp-w-full gp-rotate-180 tablet:gp-rotate-180 mobile:gp-rotate-180"
  style="--c:#000000;--w:undefinedpx;--w-tablet:undefinedpx;--w-mobile:undefinedpx;--h:undefinedpx;--h-tablet:undefinedpx;--h-mobile:undefinedpx"
  >
    <svg width="100%" height="100%" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fill-rule="evenodd" clip-rule="evenodd" d="M12 0C18.6274 0 24 5.37258 24 12C24 18.6274 18.6274 24 12 24C5.37258 24 0 18.6274 0 12C0 5.37258 5.37258 0 12 0ZM10.6828 8.13017C10.5266 7.95661 10.2734 7.95661 10.1172 8.13017C9.96095 8.30374 9.96095 8.58515 10.1172 8.75871L13.0343 12L10.1172 15.2413C9.96095 15.4149 9.96095 15.6963 10.1172 15.8698C10.2734 16.0434 10.5266 16.0434 10.6828 15.8698L13.8828 12.3143C14.0391 12.1407 14.0391 11.8593 13.8828 11.6857L10.6828 8.13017Z" fill="currentColor"/>
    </svg>
    </div>
      <style>
    .gp-gallery-gA_QhaOpLX-{{product.id}}.gp-carousel-arrow-gp-gallery-gA_QhaOpLX {
      
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
    }
    .gp-gallery-gA_QhaOpLX-{{product.id}}.gp-carousel-arrow-gp-gallery-gA_QhaOpLX::before {
      content: '';
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      z-index: 10;
      border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
      
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
    }
    @media only screen and (max-width: 1024px) and (min-width: 768px) {
      .gp-gallery-gA_QhaOpLX-{{product.id}}.gp-carousel-arrow-gp-gallery-gA_QhaOpLX {
        
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
      }
      .gp-gallery-gA_QhaOpLX-{{product.id}}.gp-carousel-arrow-gp-gallery-gA_QhaOpLX::before {
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
      }
    }
    @media only screen and (max-width: 768px) {
      .gp-gallery-gA_QhaOpLX-{{product.id}}.gp-carousel-arrow-gp-gallery-gA_QhaOpLX {
        
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
      }
      .gp-gallery-gA_QhaOpLX-{{product.id}}.gp-carousel-arrow-gp-gallery-gA_QhaOpLX::before {
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
      }
    }
  </style>
    </button>
  
          <div id="gp-carousel-gp-gallery-gA_QhaOpLX-{{product.id}}-{{section.id}}" class="gem-slider gp-h-full gp-overflow-hidden gp-select-none mobile:!gp-flex-nowrap tablet:!gp-flex-nowrap !gp-flex-nowrap mobile:!gp-min-h-full tablet:!gp-min-h-full !gp-min-h-full"
          style="--cg-mobile:5px;--cg-tablet:5px;--cg:5px">
          
    {%- if product.media.size > 1 -%}
      {%- for media in product.media -%}
      {% if media.media_type == 'image' %}
        {%- for image in product.images -%}
          {% if image.src == media.src %}
            {% assign imageID = image.id %}
            {% break %}
          {% endif%}
        {% endfor %}
      {% else %}
        {% assign imageID = '' %}
      {% endif %}
        {%- if media.id == product.featured_media.id -%}
          
    {% if media.media_type == 'video' %}
      {% assign mediaSourceUrl = media.sources.last.url %}
    {% endif %}
    {% if media.media_type == 'external_video' %}
      {% assign mediaSourceUrl = media | external_video_url %}
    {% endif %}
      {% if media.media_type == 'image' %}
      {% assign mediaSourceUrl = media.src %}
    {% endif %}
    
    <div
      media-type="{{media.media_type}}"
      media-poster="{{media.preview_image.src}}"
      media-source-url="{{media.sources.first.url}}"
      media-last-source-url="{{mediaSourceUrl}}"
      external-id="{{media.external_id}}"
      grid-index="{{forloop.index}}"
      data-outline="active"
      id="{{imageID}}"
      style="--minw:100%;--minw-tablet:100%;--minw-mobile:calc(20% - 4px);--maxw:calc(100% / 4 - 33px);--maxw-tablet:calc(100% / 4 - 33px);--maxw-mobile:calc(100% / 4 - 33px);maxWidth:100%;--w:100%;--w-tablet:100%;--w-mobile:100%;--h:auto;--h-tablet:auto;--h-mobile:auto;--ml:0px;--ml-tablet:0px;--ml-mobile:0px"
      class="gem-slider-item gp-w-full gem-slider-item-gp-gallery-gA_QhaOpLX-{{product.id}} gp-child-item-undefined gp-group gp-flex gp-w-full gp-relative gp-items-center gp-justify-center gp-overflow-hidden gp-image-item gp-gallery-image-item gp-cursor-pointer active data-[outline=deactive]:after:!gp-border-transparent"
      data-index=""
    >
      <div
        class="gp-w-full gp-h-full",
        
      >
      
      <div class="gp-w-full gp-relative"
        style="--pb: calc((1/1)*100%);--pb-tablet: calc((1/1)*100%);--pb-mobile: calc((1/1)*100%);;"
      >
      
  <img
      
      
      draggable="false"
      class="!gp-rounded-none gp-w-full gp-h-full gp-absolute gp-top-0 gp-left-0 gp-cursor-pointer"
      data-src="{{media.preview_image | image_url}}" data-srcset="{{media.preview_image | img_url: '480x480'}} 480w, {{media.preview_image | img_url: '768x768'}} 768w,{{media.preview_image | img_url: '1024x1024'}} 1024w,{{media.preview_image | img_url: '1440x1440'}} 1440w" src="{{media.preview_image | image_url}}" width="{{media.width}}" height="{{media.height}}" alt="{{media.alt}}" base-src="{{media.preview_image | image_url}}"
      quality-type={"desktop":"finest"}
      quality-percent={"desktop":100}
      data-sizes="auto"
      sizes="100vw"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1;--objf:cover;width:100%;height:100%;cursor:pointer"
    />
  

    {% if media.media_type == 'video' or media.media_type == 'external_video' %}
    <div class="gp-absolute gp-pb-1 gp-pr-1 gp-right-0 gp-bottom-0" >
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect opacity="0.9" width="24" height="24" rx="3" fill="#212121"/>
        <path d="M17.6869 12.2646L17.6868 12.2646L6.78379 18.4464C6.78378 18.4464 6.78376 18.4464 6.78374 18.4464C6.52931 18.5903 6.1665 18.4179 6.1665 18.0416V5.95844C6.1665 5.58218 6.52917 5.40981 6.7836 5.55354C6.78366 5.55357 6.78373 5.55361 6.78379 5.55365L17.6868 11.7354L17.6869 11.7354C17.8819 11.846 17.8819 12.154 17.6869 12.2646Z" stroke="#F9F9F9" stroke-miterlimit="10"/>
      </svg>
    </div>
    {% endif %}

    {% if media.media_type == 'model' %}
    <div class="gp-absolute gp-pb-1 gp-pr-1 gp-right-0 gp-bottom-0">
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect opacity="0.9" width="24" height="24" rx="3" fill="#212121"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M11.7441 4.57034C11.9017 4.47655 12.098 4.47655 12.2555 4.57034L18.5889 8.33957C18.7404 8.42971 18.8332 8.59296 18.8332 8.76923V15.2308C18.8332 15.407 18.7404 15.5703 18.5889 15.6604L12.2555 19.4297C12.098 19.5234 11.9017 19.5234 11.7441 19.4297L5.41079 15.6604C5.25932 15.5703 5.1665 15.407 5.1665 15.2308V8.76923C5.1665 8.59296 5.25932 8.42971 5.41079 8.33957L11.7441 4.57034ZM6.1665 9.64865V14.9465L11.4998 18.1206V12.8227L6.1665 9.64865ZM12.4998 12.8227V18.1206L17.8332 14.9465V9.64865L12.4998 12.8227ZM17.3555 8.76923L11.9998 11.9566L6.64417 8.76923L11.9998 5.58185L17.3555 8.76923Z" fill="#F9F9F9"/>
      </svg>
    </div>
    {% endif %}

    <div class="gp-absolute gp-inset-0 gp-flex gp-cursor-pointer gp-items-center gp-justify-center gp-bg-black/50 gp-opacity-0 gp-transition-opacity gp-duration-100 group-hover:gp-opacity-100"
    style="--d: none;--d-mobile: none;--d-tablet: none;"
    >
      <svg
        height="100%"
        width="100%"
        xmlns="http://www.w3.org/2000/svg"
        class="gp-h-6 gp-w-6"
        viewBox="0 0 512 512"
        color="#fff"
      >
        <path
          fill="currentColor"
          stroke-linecap="round"
          stroke-linejoin="round"
          d="M62.2467 345.253C43.7072 326.714 29.1474 305.116 18.9714 281.057C8.42839 256.13 3.08301 229.671 3.08301 202.418C3.08301 175.165 8.43012 148.707 18.974 123.78C29.15 99.7213 43.7098 78.123 62.2485 59.5834C80.788 41.0439 102.386 26.4841 126.445 16.3081C151.372 5.76422 177.831 0.417969 205.084 0.417969C232.337 0.417969 258.794 5.76421 283.722 16.3064C307.78 26.4823 329.379 41.0422 347.918 59.5817C366.458 78.1212 381.017 99.7196 391.194 123.778C401.737 148.706 407.083 175.163 407.083 202.417C407.083 229.671 401.737 256.129 391.194 281.056C388.406 287.648 385.277 294.048 381.839 300.257L493.397 411.815C514.091 432.511 514.091 466.187 493.395 486.883L484.272 496.006C474.245 506.032 460.915 511.553 446.738 511.553C432.559 511.553 419.228 506.032 409.202 496.006L296.022 382.824C291.996 384.854 287.898 386.762 283.721 388.528C258.794 399.073 232.336 404.419 205.082 404.419C177.828 404.419 151.371 399.071 126.443 388.528C102.385 378.352 80.7863 363.793 62.2467 345.253ZM301.699 336.166C313.928 327.317 324.896 316.835 334.282 305.034C342.149 295.142 348.9 284.325 354.355 272.775C364.433 251.432 370.076 227.586 370.076 202.419C370.076 111.296 296.206 37.4253 205.083 37.4253C113.96 37.4253 40.0895 111.294 40.0895 202.418C40.0895 293.541 113.96 367.411 205.084 367.411C227.413 367.411 248.701 362.967 268.126 354.928C280.091 349.976 291.347 343.658 301.699 336.166ZM467.229 460.716C473.507 454.439 473.507 444.26 467.229 437.982L360.595 331.348C356.601 336.153 352.378 340.794 347.919 345.253C341.671 351.502 335.068 357.286 328.147 362.615L435.371 469.839C438.511 472.977 442.624 474.547 446.739 474.547C450.853 474.547 454.967 472.978 458.106 469.839L467.229 460.716ZM223.582 183.91H281.071C291.292 183.91 299.574 192.194 299.575 202.414C299.575 206.778 298.062 210.786 295.533 213.951C292.143 218.195 286.926 220.916 281.072 220.916H228.303H223.583V225.63V278.406C223.583 287.081 217.613 294.358 209.559 296.361C208.124 296.717 206.625 296.909 205.08 296.909C194.861 296.909 186.577 288.625 186.577 278.406V220.917H129.087C118.868 220.917 110.584 212.633 110.584 202.414C110.584 192.195 118.868 183.911 129.087 183.911H186.576V126.421C186.576 116.202 194.86 107.918 205.079 107.918C215.298 107.918 223.582 116.202 223.582 126.421V183.91Z"
        />
      </svg>
    </div>
    </div>
    
      </div>
    </div>
  
        {% else %}
          
    {% if media.media_type == 'video' %}
      {% assign mediaSourceUrl = media.sources.last.url %}
    {% endif %}
    {% if media.media_type == 'external_video' %}
      {% assign mediaSourceUrl = media | external_video_url %}
    {% endif %}
      {% if media.media_type == 'image' %}
      {% assign mediaSourceUrl = media.src %}
    {% endif %}
    
    <div
      media-type="{{media.media_type}}"
      media-poster="{{media.preview_image.src}}"
      media-source-url="{{media.sources.first.url}}"
      media-last-source-url="{{mediaSourceUrl}}"
      external-id="{{media.external_id}}"
      grid-index="{{forloop.index}}"
      data-outline="deactive"
      id="{{imageID}}"
      style="--minw:100%;--minw-tablet:100%;--minw-mobile:calc(20% - 4px);--maxw:calc(100% / 4 - 33px);--maxw-tablet:calc(100% / 4 - 33px);--maxw-mobile:calc(100% / 4 - 33px);maxWidth:100%;--w:100%;--w-tablet:100%;--w-mobile:100%;--h:auto;--h-tablet:auto;--h-mobile:auto;--ml:0px;--ml-tablet:0px;--ml-mobile:0px"
      class="gem-slider-item gp-w-full gem-slider-item-gp-gallery-gA_QhaOpLX-{{product.id}} gp-child-item-undefined gp-group gp-flex gp-w-full gp-relative gp-items-center gp-justify-center gp-overflow-hidden gp-image-item gp-gallery-image-item gp-cursor-pointer undefined data-[outline=deactive]:after:!gp-border-transparent"
      data-index=""
    >
      <div
        class="gp-w-full gp-h-full",
        
      >
      
      <div class="gp-w-full gp-relative"
        style="--pb: calc((1/1)*100%);--pb-tablet: calc((1/1)*100%);--pb-mobile: calc((1/1)*100%);;"
      >
      
  <img
      
      
      draggable="false"
      class="!gp-rounded-none gp-w-full gp-h-full gp-absolute gp-top-0 gp-left-0 gp-cursor-pointer"
      data-src="{{media.preview_image | image_url}}" data-srcset="{{media.preview_image | img_url: '480x480'}} 480w, {{media.preview_image | img_url: '768x768'}} 768w,{{media.preview_image | img_url: '1024x1024'}} 1024w,{{media.preview_image | img_url: '1440x1440'}} 1440w" src="{{media.preview_image | image_url}}" width="{{media.width}}" height="{{media.height}}" alt="{{media.alt}}" base-src="{{media.preview_image | image_url}}"
      quality-type={"desktop":"finest"}
      quality-percent={"desktop":100}
      data-sizes="auto"
      sizes="100vw"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1;--objf:cover;width:100%;height:100%;cursor:pointer"
    />
  

    {% if media.media_type == 'video' or media.media_type == 'external_video' %}
    <div class="gp-absolute gp-pb-1 gp-pr-1 gp-right-0 gp-bottom-0" >
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect opacity="0.9" width="24" height="24" rx="3" fill="#212121"/>
        <path d="M17.6869 12.2646L17.6868 12.2646L6.78379 18.4464C6.78378 18.4464 6.78376 18.4464 6.78374 18.4464C6.52931 18.5903 6.1665 18.4179 6.1665 18.0416V5.95844C6.1665 5.58218 6.52917 5.40981 6.7836 5.55354C6.78366 5.55357 6.78373 5.55361 6.78379 5.55365L17.6868 11.7354L17.6869 11.7354C17.8819 11.846 17.8819 12.154 17.6869 12.2646Z" stroke="#F9F9F9" stroke-miterlimit="10"/>
      </svg>
    </div>
    {% endif %}

    {% if media.media_type == 'model' %}
    <div class="gp-absolute gp-pb-1 gp-pr-1 gp-right-0 gp-bottom-0">
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect opacity="0.9" width="24" height="24" rx="3" fill="#212121"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M11.7441 4.57034C11.9017 4.47655 12.098 4.47655 12.2555 4.57034L18.5889 8.33957C18.7404 8.42971 18.8332 8.59296 18.8332 8.76923V15.2308C18.8332 15.407 18.7404 15.5703 18.5889 15.6604L12.2555 19.4297C12.098 19.5234 11.9017 19.5234 11.7441 19.4297L5.41079 15.6604C5.25932 15.5703 5.1665 15.407 5.1665 15.2308V8.76923C5.1665 8.59296 5.25932 8.42971 5.41079 8.33957L11.7441 4.57034ZM6.1665 9.64865V14.9465L11.4998 18.1206V12.8227L6.1665 9.64865ZM12.4998 12.8227V18.1206L17.8332 14.9465V9.64865L12.4998 12.8227ZM17.3555 8.76923L11.9998 11.9566L6.64417 8.76923L11.9998 5.58185L17.3555 8.76923Z" fill="#F9F9F9"/>
      </svg>
    </div>
    {% endif %}

    <div class="gp-absolute gp-inset-0 gp-flex gp-cursor-pointer gp-items-center gp-justify-center gp-bg-black/50 gp-opacity-0 gp-transition-opacity gp-duration-100 group-hover:gp-opacity-100"
    style="--d: none;--d-mobile: none;--d-tablet: none;"
    >
      <svg
        height="100%"
        width="100%"
        xmlns="http://www.w3.org/2000/svg"
        class="gp-h-6 gp-w-6"
        viewBox="0 0 512 512"
        color="#fff"
      >
        <path
          fill="currentColor"
          stroke-linecap="round"
          stroke-linejoin="round"
          d="M62.2467 345.253C43.7072 326.714 29.1474 305.116 18.9714 281.057C8.42839 256.13 3.08301 229.671 3.08301 202.418C3.08301 175.165 8.43012 148.707 18.974 123.78C29.15 99.7213 43.7098 78.123 62.2485 59.5834C80.788 41.0439 102.386 26.4841 126.445 16.3081C151.372 5.76422 177.831 0.417969 205.084 0.417969C232.337 0.417969 258.794 5.76421 283.722 16.3064C307.78 26.4823 329.379 41.0422 347.918 59.5817C366.458 78.1212 381.017 99.7196 391.194 123.778C401.737 148.706 407.083 175.163 407.083 202.417C407.083 229.671 401.737 256.129 391.194 281.056C388.406 287.648 385.277 294.048 381.839 300.257L493.397 411.815C514.091 432.511 514.091 466.187 493.395 486.883L484.272 496.006C474.245 506.032 460.915 511.553 446.738 511.553C432.559 511.553 419.228 506.032 409.202 496.006L296.022 382.824C291.996 384.854 287.898 386.762 283.721 388.528C258.794 399.073 232.336 404.419 205.082 404.419C177.828 404.419 151.371 399.071 126.443 388.528C102.385 378.352 80.7863 363.793 62.2467 345.253ZM301.699 336.166C313.928 327.317 324.896 316.835 334.282 305.034C342.149 295.142 348.9 284.325 354.355 272.775C364.433 251.432 370.076 227.586 370.076 202.419C370.076 111.296 296.206 37.4253 205.083 37.4253C113.96 37.4253 40.0895 111.294 40.0895 202.418C40.0895 293.541 113.96 367.411 205.084 367.411C227.413 367.411 248.701 362.967 268.126 354.928C280.091 349.976 291.347 343.658 301.699 336.166ZM467.229 460.716C473.507 454.439 473.507 444.26 467.229 437.982L360.595 331.348C356.601 336.153 352.378 340.794 347.919 345.253C341.671 351.502 335.068 357.286 328.147 362.615L435.371 469.839C438.511 472.977 442.624 474.547 446.739 474.547C450.853 474.547 454.967 472.978 458.106 469.839L467.229 460.716ZM223.582 183.91H281.071C291.292 183.91 299.574 192.194 299.575 202.414C299.575 206.778 298.062 210.786 295.533 213.951C292.143 218.195 286.926 220.916 281.072 220.916H228.303H223.583V225.63V278.406C223.583 287.081 217.613 294.358 209.559 296.361C208.124 296.717 206.625 296.909 205.08 296.909C194.861 296.909 186.577 288.625 186.577 278.406V220.917H129.087C118.868 220.917 110.584 212.633 110.584 202.414C110.584 192.195 118.868 183.911 129.087 183.911H186.576V126.421C186.576 116.202 194.86 107.918 205.079 107.918C215.298 107.918 223.582 116.202 223.582 126.421V183.91Z"
        />
      </svg>
    </div>
    </div>
    
      </div>
    </div>
  
        {%- endif -%}
      {% endfor %}
    {%- endif -%}
    
          </div>
          
    <button
      type="button"
      aria-label='Carousel Next Arrow'
      class="gp-z-1 gp-gallery-gA_QhaOpLX-{{product.id}} gp-carousel-action-next gem-slider-next gp-gallery-gA_QhaOpLX-{{product.id}}-{{section.id}} gp-carousel-arrow-gp-gallery-gA_QhaOpLX gp-items-center gp-justify-center gp-overflow-hidden gp-shrink-0  gp-cursor-pointer gp-text-gray-500 !gp-flex !gp-absolute gp-z-2 tablet:!gp-flex tablet:!gp-absolute tablet:gp-z-2 mobile:!gp-flex mobile:!gp-absolute mobile:gp-z-2"
      style="--left:initial;--top:;--right:16px;--bottom:initial;--left-tablet:initial;--top-tablet:;--right-tablet:16px;--bottom-tablet:initial;--left-mobile:initial;--top-mobile:;--right-mobile:16px;--bottom-mobile:initial;--d:flex;--d-tablet:flex;--d-mobile:flex;background-color:;--w:24px;--h:24px"
      onClick={onClick}
      
    >
      <div 
  class="gp-flex gp-items-center gp-justify-center [&>svg]:gp-h-full [&>svg]:gp-w-full gp-rotate-0 tablet:gp-rotate-0 mobile:gp-rotate-0"
  style="--c:#000000;--w:undefinedpx;--w-tablet:undefinedpx;--w-mobile:undefinedpx;--h:undefinedpx;--h-tablet:undefinedpx;--h-mobile:undefinedpx"
  >
    <svg width="100%" height="100%" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fill-rule="evenodd" clip-rule="evenodd" d="M12 0C18.6274 0 24 5.37258 24 12C24 18.6274 18.6274 24 12 24C5.37258 24 0 18.6274 0 12C0 5.37258 5.37258 0 12 0ZM10.6828 8.13017C10.5266 7.95661 10.2734 7.95661 10.1172 8.13017C9.96095 8.30374 9.96095 8.58515 10.1172 8.75871L13.0343 12L10.1172 15.2413C9.96095 15.4149 9.96095 15.6963 10.1172 15.8698C10.2734 16.0434 10.5266 16.0434 10.6828 15.8698L13.8828 12.3143C14.0391 12.1407 14.0391 11.8593 13.8828 11.6857L10.6828 8.13017Z" fill="currentColor"/>
    </svg>
    </div>
      <style>
    .gp-gallery-gA_QhaOpLX-{{product.id}}.gp-carousel-arrow-gp-gallery-gA_QhaOpLX {
      
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
    }
    .gp-gallery-gA_QhaOpLX-{{product.id}}.gp-carousel-arrow-gp-gallery-gA_QhaOpLX::before {
      content: '';
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      z-index: 10;
      border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
      
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
    }
    @media only screen and (max-width: 1024px) and (min-width: 768px) {
      .gp-gallery-gA_QhaOpLX-{{product.id}}.gp-carousel-arrow-gp-gallery-gA_QhaOpLX {
        
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
      }
      .gp-gallery-gA_QhaOpLX-{{product.id}}.gp-carousel-arrow-gp-gallery-gA_QhaOpLX::before {
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
      }
    }
    @media only screen and (max-width: 768px) {
      .gp-gallery-gA_QhaOpLX-{{product.id}}.gp-carousel-arrow-gp-gallery-gA_QhaOpLX {
        
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
      }
      .gp-gallery-gA_QhaOpLX-{{product.id}}.gp-carousel-arrow-gp-gallery-gA_QhaOpLX::before {
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
      }
    }
  </style>
    </button>
  
        </div>
        <div
    class="gp-items-center gp-justify-center gp-gap-2 gp-text-center gp-carousel-dot-container gp-carousel-dot-container-gp-gallery-gA_QhaOpLX-{{product.id}}-{{section.id}} gp-static gp-flex-row gp-left-0 gp-right-0 tablet:gp-static tablet:gp-flex-row tablet:gp-left-0 tablet:gp-right-0 mobile:gp-static mobile:gp-flex-row mobile:gp-left-0 mobile:gp-right-0"
    style="--bottom:0px;--bottom-tablet:0px;--bottom-mobile:0px;--d:none;--d-tablet:none;--d-mobile:none"
 ></div>
      </div>
    </gp-carousel>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-carousel-v2.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>

  
  
            </div>
            
  <div type="grid-gallery"
    class="grid-gallery gp-flex !gp-h-fit gp-w-full gp-flex-wrap scrollbar-track:gp-rounded-2xl scrollbar-thumb:gp-rounded-2xl scrollbar-thumb:gp-bg-gray-400"
    style="--d:none;--d-mobile:none;--d-tablet:none;--cg:var(--g-s-s);--cg-mobile:var(--g-s-s);--rg:var(--g-s-s);--rg-mobile:var(--g-s-s);--jc:flex-start;--pos:static;--pos-tablet:static;--pos-mobile:static;--w:100%;--w-tablet:100%;--w-mobile:100%;--bottom:auto;--bottom-tablet:auto;--bottom-mobile:auto;--top:auto;--top-tablet:auto;--top-mobile:auto;--left:auto;--left-tablet:auto;--left-mobile:auto;--right:auto;--right-tablet:auto;--right-mobile:auto"
  >
    {%- for media in product.media -%}
      {% if media.media_type == 'image' %}
        {%- for image in product.images -%}
          {% if image.src == media.src %}
            {% assign imageID = image.id %}
            {% break %}
          {% endif%}
        {% endfor %}
      {% else %}
        {% assign imageID = '' %}
      {% endif %}
      {%- if media.id == product.featured_media.id -%}
        
    {% if media.media_type == 'video' %}
      {% assign mediaSourceUrl = media.sources.last.url %}
    {% endif %}
    {% if media.media_type == 'external_video' %}
      {% assign mediaSourceUrl = media | external_video_url %}
      {% assign mediaSource = media | json %}
    {% endif %}
    {% if media.media_type == 'image' %}
      {% assign mediaSourceUrl = media.src %}
    {% endif %}
    <div
      aria-hidden
      id="{{imageID}}"
      media-type="{{media.media_type}}"
      media-poster="{{media.preview_image.src}}"
      media-source-url="{{media.sources.first.url}}"
      media-last-source-url="{{mediaSourceUrl}}"
      external-id="{{media.external_id}}"
      grid-index="{{forloop.index}}"
      data-outline="active"
      class="active gem-slider-item-gA_QhaOpLX-{{product.id}} gp-group gp-relative gp-overflow-hidden gp-cursor-pointer gp-image-item gp-gallery-image-item data-[outline=deactive]:after:!gp-border-transparent"
      style="max-width: 100%;--w: calc(100% - var(--g-s-s) * 0 / 1);--w-tablet: calc(100% - var(--g-s-s) * 0 / 1);--w-mobile: calc(20% - var(--g-s-s) * 4 / 5);--h: auto;--h-tablet: auto;--h-mobile: auto;"
      >
      <div class="gp-h-full" style="{% if media == null or media.media_type == 'image' %} display: block !important; {% endif %} --d: block;--d-tablet: block;--d-mobile: block;">
        {% if media != null %}
          
      {% if media.media_type == 'video' or media.media_type == 'external_video' %}
        <div class="gp-absolute gp-pb-1 gp-pr-1 gp-right-0 gp-bottom-0" >
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect opacity="0.9" width="24" height="24" rx="3" fill="#212121"/>
            <path d="M17.6869 12.2646L17.6868 12.2646L6.78379 18.4464C6.78378 18.4464 6.78376 18.4464 6.78374 18.4464C6.52931 18.5903 6.1665 18.4179 6.1665 18.0416V5.95844C6.1665 5.58218 6.52917 5.40981 6.7836 5.55354C6.78366 5.55357 6.78373 5.55361 6.78379 5.55365L17.6868 11.7354L17.6869 11.7354C17.8819 11.846 17.8819 12.154 17.6869 12.2646Z" stroke="#F9F9F9" stroke-miterlimit="10"/>
          </svg>
        </div>
      {% endif %}

      {% if media.media_type == 'model' %}
      <div class="gp-absolute gp-pb-1 gp-pr-1 gp-right-0 gp-bottom-0">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <rect opacity="0.9" width="24" height="24" rx="3" fill="#212121"/>
          <path fill-rule="evenodd" clip-rule="evenodd" d="M11.7441 4.57034C11.9017 4.47655 12.098 4.47655 12.2555 4.57034L18.5889 8.33957C18.7404 8.42971 18.8332 8.59296 18.8332 8.76923V15.2308C18.8332 15.407 18.7404 15.5703 18.5889 15.6604L12.2555 19.4297C12.098 19.5234 11.9017 19.5234 11.7441 19.4297L5.41079 15.6604C5.25932 15.5703 5.1665 15.407 5.1665 15.2308V8.76923C5.1665 8.59296 5.25932 8.42971 5.41079 8.33957L11.7441 4.57034ZM6.1665 9.64865V14.9465L11.4998 18.1206V12.8227L6.1665 9.64865ZM12.4998 12.8227V18.1206L17.8332 14.9465V9.64865L12.4998 12.8227ZM17.3555 8.76923L11.9998 11.9566L6.64417 8.76923L11.9998 5.58185L17.3555 8.76923Z" fill="#F9F9F9"/>
        </svg>
      </div>
      {% endif %}

      
      
  <img
      
      
      draggable="false"
      class="!gp-rounded-none"
      data-src="{{media.preview_image | image_url}}" data-srcset="{{media.preview_image | img_url: '480x480'}} 480w, {{media.preview_image | img_url: '768x768'}} 768w,{{media.preview_image | img_url: '1024x1024'}} 1024w,{{media.preview_image | img_url: '1440x1440'}} 1440w" src="{{media.preview_image | image_url}}" width="{{media.width}}" height="{{media.height}}" alt="{{media.alt}}" base-src="{{media.preview_image | image_url}}"
      quality-type={"desktop":"finest"}
      quality-percent={"desktop":100}
      data-sizes="auto"
      sizes="100vw"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1;--objf:cover"
    />
  
      
      
    

      <div class="gp-absolute gp-inset-0 gp-flex gp-cursor-pointer gp-items-center gp-justify-center gp-bg-black/50 gp-opacity-0 gp-transition-opacity gp-duration-100 group-hover:gp-opacity-100"
        style="--d: none;--d-mobile: none;--d-tablet: none;" >
        <svg
          height="100%"
          width="100%"
          xmlns="http://www.w3.org/2000/svg"
          class="gp-h-6 gp-w-6"
          viewBox="0 0 512 512"
          color="#fff"
        >
          <path
            fill="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            d="M62.2467 345.253C43.7072 326.714 29.1474 305.116 18.9714 281.057C8.42839 256.13 3.08301 229.671 3.08301 202.418C3.08301 175.165 8.43012 148.707 18.974 123.78C29.15 99.7213 43.7098 78.123 62.2485 59.5834C80.788 41.0439 102.386 26.4841 126.445 16.3081C151.372 5.76422 177.831 0.417969 205.084 0.417969C232.337 0.417969 258.794 5.76421 283.722 16.3064C307.78 26.4823 329.379 41.0422 347.918 59.5817C366.458 78.1212 381.017 99.7196 391.194 123.778C401.737 148.706 407.083 175.163 407.083 202.417C407.083 229.671 401.737 256.129 391.194 281.056C388.406 287.648 385.277 294.048 381.839 300.257L493.397 411.815C514.091 432.511 514.091 466.187 493.395 486.883L484.272 496.006C474.245 506.032 460.915 511.553 446.738 511.553C432.559 511.553 419.228 506.032 409.202 496.006L296.022 382.824C291.996 384.854 287.898 386.762 283.721 388.528C258.794 399.073 232.336 404.419 205.082 404.419C177.828 404.419 151.371 399.071 126.443 388.528C102.385 378.352 80.7863 363.793 62.2467 345.253ZM301.699 336.166C313.928 327.317 324.896 316.835 334.282 305.034C342.149 295.142 348.9 284.325 354.355 272.775C364.433 251.432 370.076 227.586 370.076 202.419C370.076 111.296 296.206 37.4253 205.083 37.4253C113.96 37.4253 40.0895 111.294 40.0895 202.418C40.0895 293.541 113.96 367.411 205.084 367.411C227.413 367.411 248.701 362.967 268.126 354.928C280.091 349.976 291.347 343.658 301.699 336.166ZM467.229 460.716C473.507 454.439 473.507 444.26 467.229 437.982L360.595 331.348C356.601 336.153 352.378 340.794 347.919 345.253C341.671 351.502 335.068 357.286 328.147 362.615L435.371 469.839C438.511 472.977 442.624 474.547 446.739 474.547C450.853 474.547 454.967 472.978 458.106 469.839L467.229 460.716ZM223.582 183.91H281.071C291.292 183.91 299.574 192.194 299.575 202.414C299.575 206.778 298.062 210.786 295.533 213.951C292.143 218.195 286.926 220.916 281.072 220.916H228.303H223.583V225.63V278.406C223.583 287.081 217.613 294.358 209.559 296.361C208.124 296.717 206.625 296.909 205.08 296.909C194.861 296.909 186.577 288.625 186.577 278.406V220.917H129.087C118.868 220.917 110.584 212.633 110.584 202.414C110.584 192.195 118.868 183.911 129.087 183.911H186.576V126.421C186.576 116.202 194.86 107.918 205.079 107.918C215.298 107.918 223.582 116.202 223.582 126.421V183.91Z"
          />
        </svg>
      </div>
    
        {% else media == null %}
          
    
  <img
      
      
      draggable="false"
      class="!gp-rounded-none"
      data-src data-srcset="https://cdn.shopify.com/s/assets/no-image-2048-5e88c1b20e087fb7bbe9a3771824e743c244f437e4f8ba93bbf7b11b53f7824c_large.gif" src width="2237" height="1678" alt="No Image"
      quality-type={"desktop":"finest"}
      quality-percent={"desktop":100}
      data-sizes="auto"
      sizes="100vw"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1;--objf:cover"
    />
  
        {% endif %}
      </div>

      {% assign isMedia= false %}
      {% if media.media_type == 'external_video' or media.media_type == 'video' or media.media_type == 'model' %}
        {% assign isMedia = true %}
      {% endif %}
      <div class="{% if isMedia %}
      gp-flex gp-justify-center gp-items-center
      {% endif %}"
      style="
        {% if isMedia %}
          --aspect: 1/1;--aspect-tablet: 1/1;--aspect-mobile: 1/1;
        {% endif %}
        --d: none;--d-tablet: none;--d-mobile: none;"
      >
        {% case media.media_type %}
          {% when 'external_video' %}
            {% assign mediaSourceVideo = media | external_video_url %}
            
    <iframe
      width="100%" height="100%"
      class="feature-video gp-w-full gp-h-full gp-relative"
      src="{{mediaSourceVideo}}"
      controls="true"
      allowfullscreen="true"
      allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture"
      frameborder="0"
      autoplay="false"
      alt="{{media.alt}}"
      
    >
    </iframe>
  
          {% when 'video' %}
            {% assign mediaSourceVideo = media.sources.last.url %}
            
  <gp-lite-html5-embed>
    
    <video
      id="gp-video-undefined"
      class=" gp-w-full lazy"
      style="width:100%;max-height:100%"
      controls
      
      
      
      title="{{media.alt}}"
      preload="none"
      data-src="{{mediaSourceVideo}}"
      src=""
      poster=""
      playsinline
    >
    
  </video>
    <div
      style="width:100%;max-height:100%"
      class="gp-absolute gp-top-0 gp-left-0  gp-w-full gp-thumbnail-video gp-hidden"
    >
      <img
        id="video-thumbnail"
        src=""
        class="gp-w-full gp-h-full gp-object-cover"
        alt="Video Thumbnail"
      ></img>
      <button
        type="button"
        class="gp-absolute gp-left-1/2 gp-top-1/2 gp-flex gp-aspect-[56/32] gp-w-14 -gp-translate-x-1/2 -gp-translate-y-1/2 gp-items-center gp-justify-center gp-rounded gp-bg-black/80 gp-transition-colors hover:gp-bg-[#ef0800]"
        aria-label="Play"
      >
        <svg class="gp-w-5 gp-text-white" viewBox="0 0 24 24">
          <path
            fill="currentColor"
            d="M5 5.274c0-1.707 1.826-2.792 3.325-1.977l12.362 6.726c1.566.853 1.566 3.101 0 3.953L8.325 20.702C6.826 21.518 5 20.432 5 18.726V5.274Z"
          />
        </svg>
      </button>
    </div>
    </gp-lite-html5-embed>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-lite-html5-embed.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
    <script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.3/dist/lazyload.min.js"></script>
    <script>
        if(lazyLoadInstance){lazyLoadInstance.update()}else{var lazyLoadInstance = new LazyLoad()}
    </script>
  
          {% when 'model' %}
            
    <model-viewer
      class="gp-w-full gp-h-full model-viewer gp-z-[90]"
      width="100%" height="100%"
      
      
      
      
      src="{% if media.sources.first.url contains '.glb' %} {{ media.sources.first.url }}{% else %}{{media.sources.last.url}}{% endif %}"
      alt="{{media.preview_image.alt}}"
      camera-controls="true"
      poster="{{media.preview_image.src | product_img_url: '1024x1024'}}"
      data-js-focus-visible=""
      data-shopify-feature="1.12"
      ar-status="not-presenting"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1">
    </model-viewer>
  
        {% endcase %}
      </div>
    </div>
      {% else %}
        
    {% if media.media_type == 'video' %}
      {% assign mediaSourceUrl = media.sources.last.url %}
    {% endif %}
    {% if media.media_type == 'external_video' %}
      {% assign mediaSourceUrl = media | external_video_url %}
      {% assign mediaSource = media | json %}
    {% endif %}
    {% if media.media_type == 'image' %}
      {% assign mediaSourceUrl = media.src %}
    {% endif %}
    <div
      aria-hidden
      id="{{imageID}}"
      media-type="{{media.media_type}}"
      media-poster="{{media.preview_image.src}}"
      media-source-url="{{media.sources.first.url}}"
      media-last-source-url="{{mediaSourceUrl}}"
      external-id="{{media.external_id}}"
      grid-index="{{forloop.index}}"
      data-outline="deactive"
      class="gem-slider-item-gA_QhaOpLX-{{product.id}} gp-group gp-relative gp-overflow-hidden gp-cursor-pointer gp-image-item gp-gallery-image-item data-[outline=deactive]:after:!gp-border-transparent"
      style="max-width: 100%;--w: calc(100% - var(--g-s-s) * 0 / 1);--w-tablet: calc(100% - var(--g-s-s) * 0 / 1);--w-mobile: calc(20% - var(--g-s-s) * 4 / 5);--h: auto;--h-tablet: auto;--h-mobile: auto;"
      >
      <div class="gp-h-full" style="{% if media == null or media.media_type == 'image' %} display: block !important; {% endif %} --d: block;--d-tablet: block;--d-mobile: block;">
        {% if media != null %}
          
      {% if media.media_type == 'video' or media.media_type == 'external_video' %}
        <div class="gp-absolute gp-pb-1 gp-pr-1 gp-right-0 gp-bottom-0" >
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect opacity="0.9" width="24" height="24" rx="3" fill="#212121"/>
            <path d="M17.6869 12.2646L17.6868 12.2646L6.78379 18.4464C6.78378 18.4464 6.78376 18.4464 6.78374 18.4464C6.52931 18.5903 6.1665 18.4179 6.1665 18.0416V5.95844C6.1665 5.58218 6.52917 5.40981 6.7836 5.55354C6.78366 5.55357 6.78373 5.55361 6.78379 5.55365L17.6868 11.7354L17.6869 11.7354C17.8819 11.846 17.8819 12.154 17.6869 12.2646Z" stroke="#F9F9F9" stroke-miterlimit="10"/>
          </svg>
        </div>
      {% endif %}

      {% if media.media_type == 'model' %}
      <div class="gp-absolute gp-pb-1 gp-pr-1 gp-right-0 gp-bottom-0">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <rect opacity="0.9" width="24" height="24" rx="3" fill="#212121"/>
          <path fill-rule="evenodd" clip-rule="evenodd" d="M11.7441 4.57034C11.9017 4.47655 12.098 4.47655 12.2555 4.57034L18.5889 8.33957C18.7404 8.42971 18.8332 8.59296 18.8332 8.76923V15.2308C18.8332 15.407 18.7404 15.5703 18.5889 15.6604L12.2555 19.4297C12.098 19.5234 11.9017 19.5234 11.7441 19.4297L5.41079 15.6604C5.25932 15.5703 5.1665 15.407 5.1665 15.2308V8.76923C5.1665 8.59296 5.25932 8.42971 5.41079 8.33957L11.7441 4.57034ZM6.1665 9.64865V14.9465L11.4998 18.1206V12.8227L6.1665 9.64865ZM12.4998 12.8227V18.1206L17.8332 14.9465V9.64865L12.4998 12.8227ZM17.3555 8.76923L11.9998 11.9566L6.64417 8.76923L11.9998 5.58185L17.3555 8.76923Z" fill="#F9F9F9"/>
        </svg>
      </div>
      {% endif %}

      
      
  <img
      
      
      draggable="false"
      class="!gp-rounded-none"
      data-src="{{media.preview_image | image_url}}" data-srcset="{{media.preview_image | img_url: '480x480'}} 480w, {{media.preview_image | img_url: '768x768'}} 768w,{{media.preview_image | img_url: '1024x1024'}} 1024w,{{media.preview_image | img_url: '1440x1440'}} 1440w" src="{{media.preview_image | image_url}}" width="{{media.width}}" height="{{media.height}}" alt="{{media.alt}}" base-src="{{media.preview_image | image_url}}"
      quality-type={"desktop":"finest"}
      quality-percent={"desktop":100}
      data-sizes="auto"
      sizes="100vw"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1;--objf:cover"
    />
  
      
      
    

      <div class="gp-absolute gp-inset-0 gp-flex gp-cursor-pointer gp-items-center gp-justify-center gp-bg-black/50 gp-opacity-0 gp-transition-opacity gp-duration-100 group-hover:gp-opacity-100"
        style="--d: none;--d-mobile: none;--d-tablet: none;" >
        <svg
          height="100%"
          width="100%"
          xmlns="http://www.w3.org/2000/svg"
          class="gp-h-6 gp-w-6"
          viewBox="0 0 512 512"
          color="#fff"
        >
          <path
            fill="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            d="M62.2467 345.253C43.7072 326.714 29.1474 305.116 18.9714 281.057C8.42839 256.13 3.08301 229.671 3.08301 202.418C3.08301 175.165 8.43012 148.707 18.974 123.78C29.15 99.7213 43.7098 78.123 62.2485 59.5834C80.788 41.0439 102.386 26.4841 126.445 16.3081C151.372 5.76422 177.831 0.417969 205.084 0.417969C232.337 0.417969 258.794 5.76421 283.722 16.3064C307.78 26.4823 329.379 41.0422 347.918 59.5817C366.458 78.1212 381.017 99.7196 391.194 123.778C401.737 148.706 407.083 175.163 407.083 202.417C407.083 229.671 401.737 256.129 391.194 281.056C388.406 287.648 385.277 294.048 381.839 300.257L493.397 411.815C514.091 432.511 514.091 466.187 493.395 486.883L484.272 496.006C474.245 506.032 460.915 511.553 446.738 511.553C432.559 511.553 419.228 506.032 409.202 496.006L296.022 382.824C291.996 384.854 287.898 386.762 283.721 388.528C258.794 399.073 232.336 404.419 205.082 404.419C177.828 404.419 151.371 399.071 126.443 388.528C102.385 378.352 80.7863 363.793 62.2467 345.253ZM301.699 336.166C313.928 327.317 324.896 316.835 334.282 305.034C342.149 295.142 348.9 284.325 354.355 272.775C364.433 251.432 370.076 227.586 370.076 202.419C370.076 111.296 296.206 37.4253 205.083 37.4253C113.96 37.4253 40.0895 111.294 40.0895 202.418C40.0895 293.541 113.96 367.411 205.084 367.411C227.413 367.411 248.701 362.967 268.126 354.928C280.091 349.976 291.347 343.658 301.699 336.166ZM467.229 460.716C473.507 454.439 473.507 444.26 467.229 437.982L360.595 331.348C356.601 336.153 352.378 340.794 347.919 345.253C341.671 351.502 335.068 357.286 328.147 362.615L435.371 469.839C438.511 472.977 442.624 474.547 446.739 474.547C450.853 474.547 454.967 472.978 458.106 469.839L467.229 460.716ZM223.582 183.91H281.071C291.292 183.91 299.574 192.194 299.575 202.414C299.575 206.778 298.062 210.786 295.533 213.951C292.143 218.195 286.926 220.916 281.072 220.916H228.303H223.583V225.63V278.406C223.583 287.081 217.613 294.358 209.559 296.361C208.124 296.717 206.625 296.909 205.08 296.909C194.861 296.909 186.577 288.625 186.577 278.406V220.917H129.087C118.868 220.917 110.584 212.633 110.584 202.414C110.584 192.195 118.868 183.911 129.087 183.911H186.576V126.421C186.576 116.202 194.86 107.918 205.079 107.918C215.298 107.918 223.582 116.202 223.582 126.421V183.91Z"
          />
        </svg>
      </div>
    
        {% else media == null %}
          
    
  <img
      
      
      draggable="false"
      class="!gp-rounded-none"
      data-src data-srcset="https://cdn.shopify.com/s/assets/no-image-2048-5e88c1b20e087fb7bbe9a3771824e743c244f437e4f8ba93bbf7b11b53f7824c_large.gif" src width="2237" height="1678" alt="No Image"
      quality-type={"desktop":"finest"}
      quality-percent={"desktop":100}
      data-sizes="auto"
      sizes="100vw"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1;--objf:cover"
    />
  
        {% endif %}
      </div>

      {% assign isMedia= false %}
      {% if media.media_type == 'external_video' or media.media_type == 'video' or media.media_type == 'model' %}
        {% assign isMedia = true %}
      {% endif %}
      <div class="{% if isMedia %}
      gp-flex gp-justify-center gp-items-center
      {% endif %}"
      style="
        {% if isMedia %}
          --aspect: 1/1;--aspect-tablet: 1/1;--aspect-mobile: 1/1;
        {% endif %}
        --d: none;--d-tablet: none;--d-mobile: none;"
      >
        {% case media.media_type %}
          {% when 'external_video' %}
            {% assign mediaSourceVideo = media | external_video_url %}
            
    <iframe
      width="100%" height="100%"
      class="feature-video gp-w-full gp-h-full gp-relative"
      src="{{mediaSourceVideo}}"
      controls="true"
      allowfullscreen="true"
      allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture"
      frameborder="0"
      autoplay="false"
      alt="{{media.alt}}"
      
    >
    </iframe>
  
          {% when 'video' %}
            {% assign mediaSourceVideo = media.sources.last.url %}
            
  <gp-lite-html5-embed>
    
    <video
      id="gp-video-undefined"
      class=" gp-w-full lazy"
      style="width:100%;max-height:100%"
      controls
      
      
      
      title="{{media.alt}}"
      preload="none"
      data-src="{{mediaSourceVideo}}"
      src=""
      poster=""
      playsinline
    >
    
  </video>
    <div
      style="width:100%;max-height:100%"
      class="gp-absolute gp-top-0 gp-left-0  gp-w-full gp-thumbnail-video gp-hidden"
    >
      <img
        id="video-thumbnail"
        src=""
        class="gp-w-full gp-h-full gp-object-cover"
        alt="Video Thumbnail"
      ></img>
      <button
        type="button"
        class="gp-absolute gp-left-1/2 gp-top-1/2 gp-flex gp-aspect-[56/32] gp-w-14 -gp-translate-x-1/2 -gp-translate-y-1/2 gp-items-center gp-justify-center gp-rounded gp-bg-black/80 gp-transition-colors hover:gp-bg-[#ef0800]"
        aria-label="Play"
      >
        <svg class="gp-w-5 gp-text-white" viewBox="0 0 24 24">
          <path
            fill="currentColor"
            d="M5 5.274c0-1.707 1.826-2.792 3.325-1.977l12.362 6.726c1.566.853 1.566 3.101 0 3.953L8.325 20.702C6.826 21.518 5 20.432 5 18.726V5.274Z"
          />
        </svg>
      </button>
    </div>
    </gp-lite-html5-embed>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-lite-html5-embed.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
    <script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.3/dist/lazyload.min.js"></script>
    <script>
        if(lazyLoadInstance){lazyLoadInstance.update()}else{var lazyLoadInstance = new LazyLoad()}
    </script>
  
          {% when 'model' %}
            
    <model-viewer
      class="gp-w-full gp-h-full model-viewer gp-z-[90]"
      width="100%" height="100%"
      
      
      
      
      src="{% if media.sources.first.url contains '.glb' %} {{ media.sources.first.url }}{% else %}{{media.sources.last.url}}{% endif %}"
      alt="{{media.preview_image.alt}}"
      camera-controls="true"
      poster="{{media.preview_image.src | product_img_url: '1024x1024'}}"
      data-js-focus-visible=""
      data-shopify-feature="1.12"
      ar-status="not-presenting"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1">
    </model-viewer>
  
        {% endcase %}
      </div>
    </div>
      {%- endif -%}
    {% endfor %}
    {%- if product.media.size > 0 -%}
      
      
    
    {%- endif -%}
    {%- if product.media.size < 1 -%}
      
    {% if media.media_type == 'video' %}
      {% assign mediaSourceUrl = media.sources.last.url %}
    {% endif %}
    {% if media.media_type == 'external_video' %}
      {% assign mediaSourceUrl = media | external_video_url %}
      {% assign mediaSource = media | json %}
    {% endif %}
    {% if media.media_type == 'image' %}
      {% assign mediaSourceUrl = media.src %}
    {% endif %}
    <div
      aria-hidden
      id="{{imageID}}"
      media-type="{{media.media_type}}"
      media-poster="{{media.preview_image.src}}"
      media-source-url="{{media.sources.first.url}}"
      media-last-source-url="{{mediaSourceUrl}}"
      external-id="{{media.external_id}}"
      grid-index="{{forloop.index}}"
      data-outline="deactive"
      class="gem-slider-item-gA_QhaOpLX-{{product.id}} gp-group gp-relative gp-overflow-hidden gp-cursor-pointer gp-image-item gp-gallery-image-item data-[outline=deactive]:after:!gp-border-transparent"
      style="max-width: 100%;--w: calc(100% - var(--g-s-s) * 0 / 1);--w-tablet: calc(100% - var(--g-s-s) * 0 / 1);--w-mobile: calc(20% - var(--g-s-s) * 4 / 5);--h: auto;--h-tablet: auto;--h-mobile: auto;"
      >
      <div class="gp-h-full" style="{% if media == null or media.media_type == 'image' %} display: block !important; {% endif %} --d: block;--d-tablet: block;--d-mobile: block;">
        {% if media != null %}
          
      {% if media.media_type == 'video' or media.media_type == 'external_video' %}
        <div class="gp-absolute gp-pb-1 gp-pr-1 gp-right-0 gp-bottom-0" >
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect opacity="0.9" width="24" height="24" rx="3" fill="#212121"/>
            <path d="M17.6869 12.2646L17.6868 12.2646L6.78379 18.4464C6.78378 18.4464 6.78376 18.4464 6.78374 18.4464C6.52931 18.5903 6.1665 18.4179 6.1665 18.0416V5.95844C6.1665 5.58218 6.52917 5.40981 6.7836 5.55354C6.78366 5.55357 6.78373 5.55361 6.78379 5.55365L17.6868 11.7354L17.6869 11.7354C17.8819 11.846 17.8819 12.154 17.6869 12.2646Z" stroke="#F9F9F9" stroke-miterlimit="10"/>
          </svg>
        </div>
      {% endif %}

      {% if media.media_type == 'model' %}
      <div class="gp-absolute gp-pb-1 gp-pr-1 gp-right-0 gp-bottom-0">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <rect opacity="0.9" width="24" height="24" rx="3" fill="#212121"/>
          <path fill-rule="evenodd" clip-rule="evenodd" d="M11.7441 4.57034C11.9017 4.47655 12.098 4.47655 12.2555 4.57034L18.5889 8.33957C18.7404 8.42971 18.8332 8.59296 18.8332 8.76923V15.2308C18.8332 15.407 18.7404 15.5703 18.5889 15.6604L12.2555 19.4297C12.098 19.5234 11.9017 19.5234 11.7441 19.4297L5.41079 15.6604C5.25932 15.5703 5.1665 15.407 5.1665 15.2308V8.76923C5.1665 8.59296 5.25932 8.42971 5.41079 8.33957L11.7441 4.57034ZM6.1665 9.64865V14.9465L11.4998 18.1206V12.8227L6.1665 9.64865ZM12.4998 12.8227V18.1206L17.8332 14.9465V9.64865L12.4998 12.8227ZM17.3555 8.76923L11.9998 11.9566L6.64417 8.76923L11.9998 5.58185L17.3555 8.76923Z" fill="#F9F9F9"/>
        </svg>
      </div>
      {% endif %}

      
      
  <img
      
      
      draggable="false"
      class="!gp-rounded-none"
      data-src="{{media.preview_image | image_url}}" data-srcset="{{media.preview_image | img_url: '480x480'}} 480w, {{media.preview_image | img_url: '768x768'}} 768w,{{media.preview_image | img_url: '1024x1024'}} 1024w,{{media.preview_image | img_url: '1440x1440'}} 1440w" src="{{media.preview_image | image_url}}" width="{{media.width}}" height="{{media.height}}" alt="{{media.alt}}" base-src="{{media.preview_image | image_url}}"
      quality-type={"desktop":"finest"}
      quality-percent={"desktop":100}
      data-sizes="auto"
      sizes="100vw"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1;--objf:cover"
    />
  
      
      
    

      <div class="gp-absolute gp-inset-0 gp-flex gp-cursor-pointer gp-items-center gp-justify-center gp-bg-black/50 gp-opacity-0 gp-transition-opacity gp-duration-100 group-hover:gp-opacity-100"
        style="--d: none;--d-mobile: none;--d-tablet: none;" >
        <svg
          height="100%"
          width="100%"
          xmlns="http://www.w3.org/2000/svg"
          class="gp-h-6 gp-w-6"
          viewBox="0 0 512 512"
          color="#fff"
        >
          <path
            fill="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            d="M62.2467 345.253C43.7072 326.714 29.1474 305.116 18.9714 281.057C8.42839 256.13 3.08301 229.671 3.08301 202.418C3.08301 175.165 8.43012 148.707 18.974 123.78C29.15 99.7213 43.7098 78.123 62.2485 59.5834C80.788 41.0439 102.386 26.4841 126.445 16.3081C151.372 5.76422 177.831 0.417969 205.084 0.417969C232.337 0.417969 258.794 5.76421 283.722 16.3064C307.78 26.4823 329.379 41.0422 347.918 59.5817C366.458 78.1212 381.017 99.7196 391.194 123.778C401.737 148.706 407.083 175.163 407.083 202.417C407.083 229.671 401.737 256.129 391.194 281.056C388.406 287.648 385.277 294.048 381.839 300.257L493.397 411.815C514.091 432.511 514.091 466.187 493.395 486.883L484.272 496.006C474.245 506.032 460.915 511.553 446.738 511.553C432.559 511.553 419.228 506.032 409.202 496.006L296.022 382.824C291.996 384.854 287.898 386.762 283.721 388.528C258.794 399.073 232.336 404.419 205.082 404.419C177.828 404.419 151.371 399.071 126.443 388.528C102.385 378.352 80.7863 363.793 62.2467 345.253ZM301.699 336.166C313.928 327.317 324.896 316.835 334.282 305.034C342.149 295.142 348.9 284.325 354.355 272.775C364.433 251.432 370.076 227.586 370.076 202.419C370.076 111.296 296.206 37.4253 205.083 37.4253C113.96 37.4253 40.0895 111.294 40.0895 202.418C40.0895 293.541 113.96 367.411 205.084 367.411C227.413 367.411 248.701 362.967 268.126 354.928C280.091 349.976 291.347 343.658 301.699 336.166ZM467.229 460.716C473.507 454.439 473.507 444.26 467.229 437.982L360.595 331.348C356.601 336.153 352.378 340.794 347.919 345.253C341.671 351.502 335.068 357.286 328.147 362.615L435.371 469.839C438.511 472.977 442.624 474.547 446.739 474.547C450.853 474.547 454.967 472.978 458.106 469.839L467.229 460.716ZM223.582 183.91H281.071C291.292 183.91 299.574 192.194 299.575 202.414C299.575 206.778 298.062 210.786 295.533 213.951C292.143 218.195 286.926 220.916 281.072 220.916H228.303H223.583V225.63V278.406C223.583 287.081 217.613 294.358 209.559 296.361C208.124 296.717 206.625 296.909 205.08 296.909C194.861 296.909 186.577 288.625 186.577 278.406V220.917H129.087C118.868 220.917 110.584 212.633 110.584 202.414C110.584 192.195 118.868 183.911 129.087 183.911H186.576V126.421C186.576 116.202 194.86 107.918 205.079 107.918C215.298 107.918 223.582 116.202 223.582 126.421V183.91Z"
          />
        </svg>
      </div>
    
        {% else media == null %}
          
    
  <img
      
      
      draggable="false"
      class="!gp-rounded-none"
      data-src data-srcset="https://cdn.shopify.com/s/assets/no-image-2048-5e88c1b20e087fb7bbe9a3771824e743c244f437e4f8ba93bbf7b11b53f7824c_large.gif" src width="2237" height="1678" alt="No Image"
      quality-type={"desktop":"finest"}
      quality-percent={"desktop":100}
      data-sizes="auto"
      sizes="100vw"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1;--objf:cover"
    />
  
        {% endif %}
      </div>

      {% assign isMedia= false %}
      {% if media.media_type == 'external_video' or media.media_type == 'video' or media.media_type == 'model' %}
        {% assign isMedia = true %}
      {% endif %}
      <div class="{% if isMedia %}
      gp-flex gp-justify-center gp-items-center
      {% endif %}"
      style="
        {% if isMedia %}
          --aspect: 1/1;--aspect-tablet: 1/1;--aspect-mobile: 1/1;
        {% endif %}
        --d: none;--d-tablet: none;--d-mobile: none;"
      >
        {% case media.media_type %}
          {% when 'external_video' %}
            {% assign mediaSourceVideo = media | external_video_url %}
            
    <iframe
      width="100%" height="100%"
      class="feature-video gp-w-full gp-h-full gp-relative"
      src="{{mediaSourceVideo}}"
      controls="true"
      allowfullscreen="true"
      allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture"
      frameborder="0"
      autoplay="false"
      alt="{{media.alt}}"
      
    >
    </iframe>
  
          {% when 'video' %}
            {% assign mediaSourceVideo = media.sources.last.url %}
            
  <gp-lite-html5-embed>
    
    <video
      id="gp-video-undefined"
      class=" gp-w-full lazy"
      style="width:100%;max-height:100%"
      controls
      
      
      
      title="{{media.alt}}"
      preload="none"
      data-src="{{mediaSourceVideo}}"
      src=""
      poster=""
      playsinline
    >
    
  </video>
    <div
      style="width:100%;max-height:100%"
      class="gp-absolute gp-top-0 gp-left-0  gp-w-full gp-thumbnail-video gp-hidden"
    >
      <img
        id="video-thumbnail"
        src=""
        class="gp-w-full gp-h-full gp-object-cover"
        alt="Video Thumbnail"
      ></img>
      <button
        type="button"
        class="gp-absolute gp-left-1/2 gp-top-1/2 gp-flex gp-aspect-[56/32] gp-w-14 -gp-translate-x-1/2 -gp-translate-y-1/2 gp-items-center gp-justify-center gp-rounded gp-bg-black/80 gp-transition-colors hover:gp-bg-[#ef0800]"
        aria-label="Play"
      >
        <svg class="gp-w-5 gp-text-white" viewBox="0 0 24 24">
          <path
            fill="currentColor"
            d="M5 5.274c0-1.707 1.826-2.792 3.325-1.977l12.362 6.726c1.566.853 1.566 3.101 0 3.953L8.325 20.702C6.826 21.518 5 20.432 5 18.726V5.274Z"
          />
        </svg>
      </button>
    </div>
    </gp-lite-html5-embed>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-lite-html5-embed.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
    <script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.3/dist/lazyload.min.js"></script>
    <script>
        if(lazyLoadInstance){lazyLoadInstance.update()}else{var lazyLoadInstance = new LazyLoad()}
    </script>
  
          {% when 'model' %}
            
    <model-viewer
      class="gp-w-full gp-h-full model-viewer gp-z-[90]"
      width="100%" height="100%"
      
      
      
      
      src="{% if media.sources.first.url contains '.glb' %} {{ media.sources.first.url }}{% else %}{{media.sources.last.url}}{% endif %}"
      alt="{{media.preview_image.alt}}"
      camera-controls="true"
      poster="{{media.preview_image.src | product_img_url: '1024x1024'}}"
      data-js-focus-visible=""
      data-shopify-feature="1.12"
      ar-status="not-presenting"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1">
    </model-viewer>
  
        {% endcase %}
      </div>
    </div>
    {%- endif -%}
  </div>
          {% endif %}
      </div>
    </gp-product-images-v2>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-product-images-v2.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:center"
      class="glsjFfIE4d gp-relative gp-flex gp-flex-col"
    >
      <div gp-el-wrapper style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:4px" class="gz2xWzgAQg ">
      
    {%- unless product -%}
      <p>{{ "gempages.ProductTitle.product_not_found" | t }}</p>
    {%- else -%}
      
        
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gz2xWzgAQg">
    <div
      
        class="gz2xWzgAQg "
        
      >
      <div  >
        <h1
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-product-title"
          style="--w:100%;--ta:left;--line-clamp:1;--line-clamp-tablet:1;--line-clamp-mobile:1;--c:#242424;--fs:normal;--ff:var(--g-font-heading, heading);--weight:700;--ls:0px;--size:23px;--size-tablet:23px;--size-mobile:20px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;overflow:hidden"
        >{{ product.title }}</h1>
      </div>
    </div>
    </gp-text>
    
      
    {%- endunless -%}
  
      </div>
       
      
    <div
      parentTag="Col" id="gbwyArytrZ" data-id="gbwyArytrZ"
        style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--cg:8px;--pc:start;--gtc:minmax(0, auto) minmax(0, auto);--gtc-mobile:minmax(0, auto) minmax(0, auto);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gbwyArytrZ gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:center"
      class="gUqE3j9n6G gp-relative gp-flex gp-flex-col"
    >
      
      <gp-product-price
        data-id="g0d44_rPaR"
        class="g0d44_rPaR gp-product-price group-data-[state=loading]:gp-invisible data-[hidden=true]:gp-hidden"
        gp-data='{"priceType":"regular","uid":"g0d44_rPaR","locale":"{{shop.locale}}","currency":"{{shop.currency}}","moneyFormat":"{{ shop.money_format | replace: '"', '\\"' | escape }}"}'
        
        style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:0px"
        data-hidden="false"
      >
        
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="">
    <div
      id="p-price-g0d44_rPaR"
        class=" "
        
      >
      <div  >
        <div
          type="regular"
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-price gp-money gp-decoration-g-text-1 gp-text-g-text-2"
          style="--w:100%;--tdc:text-1;--tdt:1;--ta:left;--c:var(--g-c-text-2, text-2);--fs:normal;--ff:var(--g-font-heading, heading);--weight:600;--ls:0.6px;--size:19px;--size-tablet:19px;--size-mobile:17px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;overflow:hidden"
        >
     {% if variant.price %} 
       {{ variant.price | money}}
     {% endif %}
   </div>
      </div>
    </div>
    </gp-text>
    
      </gp-product-price>
      <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-product-price.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:center"
      class="gDW2X1HlY7 gp-relative gp-flex gp-flex-col"
    >
      
      <gp-product-price
        data-id="gfDfNFKDQO"
        class="gfDfNFKDQO gp-product-price group-data-[state=loading]:gp-invisible data-[hidden=true]:gp-hidden"
        gp-data='{"priceType":"compare","uid":"gfDfNFKDQO","locale":"{{shop.locale}}","currency":"{{shop.currency}}","moneyFormat":"{{ shop.money_format | replace: '"', '\\"' | escape }}"}'
        
        style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%"
        data-hidden="{% if variant.compare_at_price > variant.price and variant.compare_at_price >= 0 %}false{% else %}true{% endif %}"
      >
        
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="">
    <div
      id="p-price-gfDfNFKDQO"
        class=" "
        
      >
      <div  >
        <div
          type="compare"
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-price gp-product-compare-price gp-line-through"
          style="--w:100%;--tdc:#B4B4B4;--tdt:1;--ta:left;--c:#B4B4B4;--fs:normal;--ff:var(--g-font-heading, heading);--weight:400;--ls:0.6px;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;overflow:hidden"
        >
      {% if variant.compare_at_price  %} 
        {{ variant.compare_at_price | money}}
      {% else %}
        
      {% endif %}
    </div>
      </div>
    </div>
    </gp-text>
    
      </gp-product-price>
      <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-product-price.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
    </div>
    </div>
   
    
    </div>
    </div>
   
    
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:center"
      class="gkDC1-U-hc gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      parentTag="Col" id="gGKma34bEC" data-id="gGKma34bEC"
        style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--mb:0px;--cg:8px;--cg-tablet:12px;--pc:start;--gtc:minmax(0, 5fr) minmax(0, 7fr);--gtc-mobile:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gGKma34bEC gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:center"
      class="gtCf6JpGPv gp-relative gp-flex gp-flex-col"
    >
      <div gp-el-wrapper style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:0px;--mb-mobile:14px" class="ghm5DouDju ">
      
  {%- assign total_combinations = 1 -%}
  {%- for option in product.options_with_values -%}
    {%- assign total_combinations = total_combinations | times: option.values.size -%}
  {%- endfor -%}
  <gp-product-variants
    data-id="ghm5DouDju"
    
    has-pre-selected="true"
    gp-data='{
      "setting":{"blankText":"Please select an option","column":{"desktop":1},"combineFullWidth":{"desktop":true},"combineHeight":"40px","combineWidth":{"desktop":"400px"},"hasPreSelected":true,"label":false,"layout":{"desktop":"vertical"},"optionAlign":{"desktop":"left"},"optionType":"singleOption","price":true,"showAsSwatches":true,"soldOutMark":true,"soldOutStyle":"disable","variantPresets":[{"optionName":"base","optionType":"rectangle_list","presets":{"color":{"height":"45px","spacing":"16px","width":{"desktop":"45px","mobile":"45px","tablet":"45px"},"optionBgColor":{"normal":"bg-3","hover":"bg-3","active":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"#E0E0E0","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"#E0E0E0","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"hover":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"active":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"dropdown":{"fullWidth":{"desktop":true,"mobile":true,"tablet":true},"height":"45px","width":{"desktop":"100%","mobile":"100%","tablet":"100%"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"#E0E0E0","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"#E0E0E0","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"#242424","hover":"#242424","normal":"#242424"}},"image":{"height":"64px","spacing":"16px","width":{"desktop":"64px","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"#E0E0E0","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"#E0E0E0","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"image_shopify":{"height":"64px","spacing":"16px","width":{"desktop":"64px","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"#E0E0E0","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"#E0E0E0","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"rectangle_list":{"height":"45px","spacing":"16px","width":{"desktop":"auto","mobile":"auto","tablet":"auto"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"#E0E0E0","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"#E0E0E0","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"#242424","hover":"#242424","normal":"#242424"}}}},{"optionName":"Scent","optionType":"dropdown","presets":{"color":{"height":"45px","spacing":"16px","width":{"desktop":"45px","mobile":"45px","tablet":"45px"},"optionBgColor":{"normal":"bg-3","hover":"bg-3","active":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"#E0E0E0","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"#E0E0E0","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"hover":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"active":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"dropdown":{"fullWidth":{"desktop":true,"mobile":true,"tablet":true},"height":"48px","width":{"desktop":"100%","mobile":"100%","tablet":"100%"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"#E0E0E0","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"#E0E0E0","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"#242424","hover":"#242424","normal":"#242424"}},"image":{"height":"64px","spacing":"16px","width":{"desktop":"64px","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"#E0E0E0","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"#E0E0E0","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"image_shopify":{"height":"64px","spacing":"16px","width":{"desktop":"64px","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"#E0E0E0","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"#E0E0E0","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"rectangle_list":{"height":"45px","spacing":"16px","width":{"desktop":"auto","mobile":"auto","tablet":"auto"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"#E0E0E0","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"#E0E0E0","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"#242424","hover":"#242424","normal":"#242424"}}}}]},
      "styles":{"align":{"desktop":"left"},"dropdownItemWidth":{"desktop":"fill","mobile":"fill","tablet":"fill"},"fixedDropdownWidth":{"desktop":"240px"},"fullWidth":{"desktop":true},"labelColor":"text-2","labelGap":"8px","labelTypo":{"attrs":{"bold":true,"color":"text-1"},"custom":{"fontSize":{"desktop":"16px","mobile":"12px","tablet":"13px"},"fontStyle":"normal","fontWeight":"400","letterSpacing":"normal","lineHeight":{"desktop":"150%","mobile":"150%","tablet":"150%"}},"type":"paragraph-2"},"marginBottom":{"desktop":"var(--g-s-xl)","mobile":"var(--g-s-xl)"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"#E0E0E0","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"#E0E0E0","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false},"optionRounded":{"active":{"radiusType":"none"},"hover":{"radiusType":"none"},"normal":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"}},"optionSpacing":"30px","optionTextColor":{"active":"#242424","hover":"#242424","normal":"#242424"},"optionTypo":{"custom":{"fontSize":{"desktop":"16px","mobile":"14px","tablet":"16px"},"fontStyle":"normal","fontVariants":["100","200","300","regular","500","600","700","800","900"],"fontWeight":"400","letterSpacing":"normal","lineHeight":{"desktop":"130%","mobile":"130%","tablet":"130%"}},"type":"paragraph-2"},"swatchAutoWidth":{"desktop":true},"swatchHeight":{"desktop":"40px"},"swatchItemWidth":{"desktop":"auto"},"swatchSpacing":"var(--g-s-m)","swatchWidth":{"desktop":"80px"},"width":{"desktop":"400px"}},
      "variants":{{product.variants | json | escape}},
      "optionsWithValues": {{product.options_with_values | json | escape}},
      "variantSelected": {{ variant | json | escape }},
      "variantInventoryQuantity": {{product.variants | map: 'inventory_quantity' | json | escape}},
      "variantInventoryPolicy": {{product.variants | map: 'inventory_policy' | json | escape}},
      "moneyFormat": {{shop.money_format | json | escape}},
      "productId": {{product.id | json | escape}},
      "productUrl": {{product.url | json | escape}},
      "productHandle": {{product.handle | json | escape}},
      "displayState": {"desktop":true,"mobile":true,"tablet":true},
      "totalVariantCombinations": {{total_combinations}},
      "firstAvailableVariant": {{product.selected_or_first_available_variant | json | escape}}
    }
  '>
    {%- assign options = product.options_with_values -%}
    {%- assign variants = product.variants -%}
    {%- if options.size == 0 or options.size == 1 and variants.size == 1 and variants[0].title == 'Default Title' and variants[0].option1 == 'Default Title' -%}
      <div></div>
    {% else %}
      <div
      class="gp-grid !gp-ml-0"
      style="--gtc:repeat(1, minmax(0, 1fr));--ta:left;--w:100%;--w-tablet:100%;--w-mobile:100%;--rg:var(--g-s-xl);--rg-mobile:var(--g-s-xl)"
    >
      
      {% assign presets = "base($2)rectangle_list($1)Scent($2)dropdown" | split: '($1)' %}
      {% assign hiddenPresetOptions = "" | split: ',' %}
      {%- for option in options -%}
        <div
        option-name="{{option.name | escape}}"
        class="gp-flex variant-inside gp-flex-col gp-items-start"

        >
          {% assign showVariantClass = 'variant-display' %}
          {% assign optionName = option.name %}
          {% for preset in presets %}
            {% assign presetDetail = preset | split: '($2)' %}
            {% if presetDetail[1] == 'dropdown' and presetDetail[0] == optionName %}
              {% assign showVariantClass = '' %}
              {% break %}
            {% endif %}
          {% endfor %}

          

          <div
              variant-option-name="{{option.name | escape}}"
              class="gp-justify-start gp-flex gp-w-full gp-flex-wrap gp-items-center variant-option-group"
              style="--rg:var(--g-s-m);--cg:var(--g-s-m)"
            >
              {%- assign values = option.values -%}
              {%- assign rootForloop = forloop.index0 -%}
              {%- if option.position == 1 -%}
                {%- assign selectedValue = variant.option1 -%}
              {%- elsif option.position == 2 -%}
                {%- assign selectedValue = variant.option2 -%}
              {%- else -%}
                {%- assign selectedValue = variant.option3 -%}
              {%- endif -%}
              
              
    {% assign optionRendered = false %}
    {%  assign swatches = shop.metafields.GEMPAGES.swatches %}
    {%  assign swatchesItems = swatches | split: '($1)'  %}
    {% for swatchesItem in swatchesItems %}
      {% assign colorArraysString = "" %}
      {% assign labelsString = "" %}
      {% assign imageUrlsString = "" %}

      {%  assign attrItems = swatchesItem | split: '($3)'  %}
      {% for attrItem in attrItems %}
        {%  assign attrs = attrItem | split: '($2)'  %}


          {% assign optionKey = attrs[0] %}
          {% assign optionValue = attrs[1] %}
          {% if optionKey == 'optionTitle' %}
                {% assign optionTitle = optionValue %}
              {% elsif optionKey == 'optionType' %}
                {% assign optionType = optionValue %}
            {% endif %}


            {% if optionKey == 'optionValues' %}

              {% assign opValueItems = optionValue | split: '($4)'  %}
              {% for opValueItem in opValueItems %}
                {% assign opValueItemAttrs = opValueItem | split: '($6)'  %}
                {% for opValueItemAttr in opValueItemAttrs %}
                  {% assign attrs = opValueItemAttr | split: '($5)'  %}
                  {% assign opValueItemKey = attrs[0] %}
                  {% assign opValueItemValue = attrs[1] %}

                  {% if opValueItemKey == 'label' %}
                    {% assign labelsString = labelsString | append: opValueItemValue %}
                    {% assign labelsString = labelsString | append: "($8)" %}
                  {% endif %}

                  {% if opValueItemKey == 'colors' %}
                    {% assign colorArraysString = colorArraysString | append: opValueItemValue %}
                    {% assign colorArraysString = colorArraysString | append: "($8)" %}
                  {% endif %}

                  {% if opValueItemKey == 'imageUrl' %}
                    {% assign imageUrlsString = imageUrlsString | append: opValueItemValue %}
                    {% assign imageUrlsString = imageUrlsString | append: "($8)" %}

                  {% endif %}
                {% endfor %}
              {% endfor %}
            {% endif %}

      {% endfor %}
      {% assign labels = labelsString | split: '($8)' %}
      {% assign colorStrings = colorArraysString | split: '($8)' %}
      {% assign imageUrls = imageUrlsString | split: '($8)' %}

      {% if optionTitle == option.name %}
      {% assign variantPresetString = "base($1)rectangle_list($2)Scent($1)dropdown" %}
      {% assign optionName = option.name | replace: "'", "&apos;" | replace: '"', "&quot;" %}
        {% assign items = variantPresetString | split:'($2)' %}
        {% assign type = 'dropdown' %}
        {%- for item in items -%}
          {% assign itemPreset = item | split:'($1)' %}
          {% if itemPreset[0] == optionName %}
            {% assign type = itemPreset[1] %}
          {% endif %}
          {% if itemPreset[0] == "base" %}
            {% assign type = itemPreset[1] %}
          {% endif %}
        {%- endfor -%}
        {% assign optionRendered = true %}
        {%- for value in values -%}
          
    {%- liquid
      assign option_disabled = true
      for variantItem in product.variants
        case option.position
          when 1
            if variantItem.available and variantItem.option1 == value
              assign option_disabled = false
            endif
          when 2
            if variantItem.available and variantItem.option2 == value
              assign option_disabled = false
            endif
          when 3
            if variantItem.available and variantItem.option3 == value
              assign option_disabled = false
            endif
        endcase
      endfor
    -%}

  
  <label
  id="{{option.name | escape}}-{{value | escape}}"
  for="{{product.id}}-ghm5DouDju-{{option.name | escape}}-{{option.position}}-{{ forloop.index }}"
  class="gp-group gp-relative option-item gp-child-item-ghm5DouDju" 
>
  <div class="gp-invisible mobile:gp-overflow-hidden mobile:!gp-max-w-[0px] !gp-max-w-[150px] gp-left-[50%] gp-translate-x-[-50%] gp-translate-y-0 gp-absolute gp-bottom-[calc(100%+20px)] gp-text-[12px] group-hover:gp-visible gp-w-max gp-bg-[#333333] gp-text-[#F9F9F9] mobile:gp-px-[0px] gp-px-[8px] gp-py-[4px] gp-rounded-[8px] gp-mb-[-10px] after:gp-content-[''] after:gp-absolute mobile:after:gp-p-[0px] after:gp-p-[7px] after:gp-left-0 after:gp-w-full after:gp-bottom-[-10px]">
    <p class="gp-text-[#F9F9F9]">{{value | escape}}</p>
    <svg class="gp-absolute gp-left-[50%] gp-translate-x-[-50%] gp-translate-y-0 gp-z-10 gp-bottom-[-4px]" width="8" height="4" viewBox="0 0 16 10" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M8 10L0 0L16 1.41326e-06L8 10Z" fill="#333333"/>
    </svg>
  </div>
  <input
    checked="{%- if option.selected_value == value -%}true{% else %}false{%- endif -%}"
    name="{{product.id}}-options-{{option.name | escape}}"
    id="{{product.id}}-ghm5DouDju-{{option.name | escape}}-{{option.position}}-{{ forloop.index }}"
    value="{{value | escape}}"
    type="radio"
    class="gp-sr-only gp-absolute gp-bottom-0 gp-right-0"
  />
  <span class="gp-sr-only">{{value | escape}}</span>
  <div   
  option-name="{{option.name | escape}}"
  option-name-value="{{value | escape}}"
  data-gp-option-value-id="{{value.id}}"
  class="option-item-inner gp-w-auto gp-h-auto {%- if option_disabled == true -%}gp-opacity-20{%- endif -%}">
  
    {% case type %}
      {% when "rectangle_list" %}
      <div
    class="
    {%- if selectedValue == value -%}
      gp-relative gp-flex gp-cursor-pointer gp-items-center gp-justify-center gp-text-center option-value-wrapper gp-overflow-hidden gp-w-full gp-h-full data-[disabled='disabled']:gp-pointer-events-none data-[disabled='disabled']:!gp-cursor-not-allowed 
         data-[hidden='false']:gp-flex
    {%- else -%}
      gp-relative gp-flex gp-cursor-pointer gp-items-center gp-justify-center gp-text-center option-value-wrapper gp-overflow-hidden gp-w-full gp-h-full data-[disabled='disabled']:gp-pointer-events-none data-[disabled='disabled']:!gp-cursor-not-allowed 
         data-[hidden='false']:gp-flex
    {%- endif -%}
    "
    style="
      {%- if selectedValue == value -%}
        --bg: var(--g-c-bg-3, bg-3);--bs: solid;--bw: 1px 1px 1px 1px;--bc: #E0E0E0;--c: #242424;--radius: var(--g-radius-none);--fs: normal;--ff: var(--g-font-body, body);--weight: 400;--ls: normal;--size: 16px;--size-tablet: 16px;--size-mobile: 14px;--lh: 130%;--lh-tablet: 130%;--lh-mobile: 130%;--h: 45px;
      {%- else -%}
        --bblr: 0px;--bbrr: 0px;--btlr: 0px;--btrr: 0px;--shadow: none;--hvr-bg: var(--g-c-bg-3, bg-3);--bg: var(--g-c-bg-3, bg-3);--bs: solid;--hvr-bs: solid;--bw: 1px 1px 1px 1px;--hvr-bw: 1px 1px 1px 1px;--bc: #E0E0E0;--hvr-bc: #E0E0E0;--hvr-c: #242424;--c: #242424;--fs: normal;--ff: var(--g-font-body, body);--weight: 400;--ls: normal;--size: 16px;--size-tablet: 16px;--size-mobile: 14px;--lh: 130%;--lh-tablet: 130%;--lh-mobile: 130%;--h: 45px;
      {%- endif -%}
      "
    id="value-wrapper-{{option.name | escape | strip}}-{{value | escape | strip}}"
    option-data="{{option.name | escape}}"
    option-value="{{value | escape}}"
    option-position="{{option.position}}"
    data-gp-option-available="{{value.available}}"
    option-type="rectangle_list"
    data-disabled="{%- if option_disabled == true -%} disabled  {%- endif -%}"
    data-hidden='true'
  >
    
      <div class="option-value gp-overflow-hidden gp-relative 
      "
      style="--pl: 16px;--pr: 16px;--pt: 8px;--pb: 8px;">
      
      <span class="gp-text-center" style="--fs:normal;--ff:var(--g-font-body, body);--weight:400;--ls:normal;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%">{{value}}</span>
      </div>
    
  </div>
      {% when "color" %}
      
    {% assign colorsString = null %}
    {% assign colors = null %}
    {% for label in labels %}
      {% if label == value %}
        {% assign colorsString = colorStrings[forloop.index0] %}
      {% endif %}
    {% endfor %}
    {% if colorsString != null %}
      {% assign colors = colorsString | split: '($7)' %}
    {% endif %}
    <div
    class="
    {%- if selectedValue == value -%}
      gp-relative gp-flex gp-cursor-pointer gp-items-center gp-justify-center gp-text-center option-value-wrapper gp-overflow-hidden gp-w-full gp-h-full data-[disabled='disabled']:gp-pointer-events-none data-[disabled='disabled']:!gp-cursor-not-allowed 
         data-[hidden='false']:gp-flex  gp-p-[4px]
    {%- else -%}
      gp-relative gp-flex gp-cursor-pointer gp-items-center gp-justify-center gp-text-center option-value-wrapper gp-overflow-hidden gp-w-full gp-h-full data-[disabled='disabled']:gp-pointer-events-none data-[disabled='disabled']:!gp-cursor-not-allowed 
         data-[hidden='false']:gp-flex  gp-p-[4px]
    {%- endif -%}
    "
    style="
      {%- if selectedValue == value -%}
        --bs: solid;--bw: 1px 1px 1px 1px;--bc: #E0E0E0;--bblr: 999999px;--bbrr: 999999px;--btlr: 999999px;--btrr: 999999px;--fs: normal;--ff: var(--g-font-body, body);--weight: 400;--ls: normal;--size: 16px;--size-tablet: 16px;--size-mobile: 14px;--lh: 130%;--lh-tablet: 130%;--lh-mobile: 130%;--h: 45px;--w: 45px;
      {%- else -%}
        --bblr: 999999px;--bbrr: 999999px;--btlr: 999999px;--btrr: 999999px;--hvr-bblr: 999999px;--hvr-bbrr: 999999px;--hvr-btlr: 999999px;--hvr-btrr: 999999px;--shadow: none;--fs: normal;--ff: var(--g-font-body, body);--weight: 400;--ls: normal;--size: 16px;--size-tablet: 16px;--size-mobile: 14px;--lh: 130%;--lh-tablet: 130%;--lh-mobile: 130%;--h: 45px;--w: 45px;
      {%- endif -%}
      "
    id="value-wrapper-{{option.name | escape | strip}}-{{value | escape | strip}}"
    option-data="{{option.name | escape}}"
    option-value="{{value | escape}}"
    option-position="{{option.position}}"
    data-gp-option-available="{{value.available}}"
    option-type="color"
    data-disabled="{%- if option_disabled == true -%} disabled  {%- endif -%}"
    data-hidden='true'
  >
    {%- if colors != null and colors.size > 0 -%} 
      {%- for color in colors -%}
      {% assign backgroundType = "background-color" %}
        {% if color contains "linear-gradient" %}
          {% assign backgroundType = "background-image" %}
        {% endif %}
        <div
          class="gp-relative gp-h-full gp-w-full gp-min-w-fit gp-flex gp-color-circle before:gp-hidden before:gp-absolute before:gp-top-[50%] before:-gp-rotate-45 before:gp-border-t before:gp-z-1 before:gp-content-[''] before:gp-w-full"
          data-test="{{ backgroundType }}: {{ color }}"
          style="{{ backgroundType }}: {{ color }}; 
    {%- if selectedValue == value -%}
      --d: flex;--jc: center;	--ai: center;--w: 100%;--h: 100%;--of: hidden;--bs: none;--bw: 1px 1px 1px 1px;--hvr-bw: 1px 1px 1px 1px;--bc: #E0E0E0;--hvr-bc: #E0E0E0;--bblr: 999999px;--bbrr: 999999px;--btlr: 999999px;--btrr: 999999px;--hvr-bblr: 999999px;--hvr-bbrr: 999999px;--hvr-btlr: 999999px;--hvr-btrr: 999999px;
    {%- else -%}
      --d: flex;--jc: center;	--ai: center;--w: 100%;--h: 100%;--of: hidden;--bs: solid;--bw: 1px 1px 1px 1px;--hvr-bw: 1px 1px 1px 1px;--bc: #E0E0E0;--hvr-bc: #E0E0E0;--bblr: 999999px;--bbrr: 999999px;--btlr: 999999px;--btrr: 999999px;--hvr-bblr: 999999px;--hvr-bbrr: 999999px;--hvr-btlr: 999999px;--hvr-btrr: 999999px;
    {%- endif -%}
    "></div>
      {%- endfor -%}
       {% else %} 
      <div class="option-value gp-overflow-hidden gp-relative 
      "
      style="
    {%- if selectedValue == value -%}
      --d: flex;--jc: center;	--ai: center;--w: 100%;--h: 100%;--of: hidden;--bs: none;--bw: 1px 1px 1px 1px;--hvr-bw: 1px 1px 1px 1px;--bc: #E0E0E0;--hvr-bc: #E0E0E0;--bblr: 999999px;--bbrr: 999999px;--btlr: 999999px;--btrr: 999999px;--hvr-bblr: 999999px;--hvr-bbrr: 999999px;--hvr-btlr: 999999px;--hvr-btrr: 999999px;
    {%- else -%}
      --d: flex;--jc: center;	--ai: center;--w: 100%;--h: 100%;--of: hidden;--bs: solid;--bw: 1px 1px 1px 1px;--hvr-bw: 1px 1px 1px 1px;--bc: #E0E0E0;--hvr-bc: #E0E0E0;--bblr: 999999px;--bbrr: 999999px;--btlr: 999999px;--btrr: 999999px;--hvr-bblr: 999999px;--hvr-bbrr: 999999px;--hvr-btlr: 999999px;--hvr-btrr: 999999px;
    {%- endif -%}
    ">
      
      <span class="gp-text-center gp-overflow-hidden gp-text-ellipsis gp-px-1" style="--fs:normal;--ff:var(--g-font-body, body);--weight:400;--ls:normal;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%">{{value}}</span>
      </div>
     {%- endif -%}
  </div>
  
      {% when "image_shopify" %}
      
      {% assign imageUrl = null %}
      {% for variant in variants %}
        {% assign valueIncludesSelectedOption = false %}
        {% for item in variant.options %}
          {% if item == value %}
          {% assign valueIncludesSelectedOption = true %}
          {% endif %}
        {% endfor %}
        {% if valueIncludesSelectedOption and variant.featured_image or variant.featured_media%}
          {% unless imageUrl %}
            {% if variant.featured_media %}
              {% assign imageUrl = variant.featured_media.preview_image.src | product_img_url: '200x'  %}
            {% else %}
              {% assign imageUrl = variant.featured_image.src | product_img_url: '200x'  %}
            {% endif %}
          {% endunless %}
        {% endif %}
      {% endfor %}
      <div
    class="
    {%- if selectedValue == value -%}
      gp-relative gp-flex gp-cursor-pointer gp-items-center gp-justify-center gp-text-center option-value-wrapper gp-overflow-hidden gp-w-full gp-h-full data-[disabled='disabled']:gp-pointer-events-none data-[disabled='disabled']:!gp-cursor-not-allowed 
         data-[hidden='false']:gp-flex
    {%- else -%}
      gp-relative gp-flex gp-cursor-pointer gp-items-center gp-justify-center gp-text-center option-value-wrapper gp-overflow-hidden gp-w-full gp-h-full data-[disabled='disabled']:gp-pointer-events-none data-[disabled='disabled']:!gp-cursor-not-allowed 
         data-[hidden='false']:gp-flex
    {%- endif -%}
    "
    style="
      {%- if selectedValue == value -%}
        --bg: var(--g-c-bg-3, bg-3);--bs: solid;--bw: 1px 1px 1px 1px;--bc: #E0E0E0;--radius: var(--g-radius-none);--fs: normal;--ff: var(--g-font-body, body);--weight: 400;--ls: normal;--size: 16px;--size-tablet: 16px;--size-mobile: 14px;--lh: 130%;--lh-tablet: 130%;--lh-mobile: 130%;--h: 64px;--w: 64px;
      {%- else -%}
        --bblr: 0px;--bbrr: 0px;--btlr: 0px;--btrr: 0px;--shadow: none;--hvr-bg: var(--g-c-bg-3, bg-3);--bg: var(--g-c-bg-3, bg-3);--bs: solid;--hvr-bs: solid;--bw: 1px 1px 1px 1px;--hvr-bw: 1px 1px 1px 1px;--bc: #E0E0E0;--hvr-bc: #E0E0E0;--fs: normal;--ff: var(--g-font-body, body);--weight: 400;--ls: normal;--size: 16px;--size-tablet: 16px;--size-mobile: 14px;--lh: 130%;--lh-tablet: 130%;--lh-mobile: 130%;--h: 64px;--w: 64px;
      {%- endif -%}
      "
    id="value-wrapper-{{option.name | escape | strip}}-{{value | escape | strip}}"
    option-data="{{option.name | escape}}"
    option-value="{{value | escape}}"
    option-position="{{option.position}}"
    data-gp-option-available="{{value.available}}"
    option-type="image_shopify"
    data-disabled="{%- if option_disabled == true -%} disabled  {%- endif -%}"
    data-hidden='true'
  >
    {%- if imageUrl != null and imageUrl != "" -%} 
      <img style="width: 64px; height: 64px" class="gp-object-cover gp-rounded-none" src="{{imageUrl}}" alt="" />
     {% else %} 
      <div class="option-value gp-overflow-hidden gp-relative gp-flex gp-flex-col gp-justify-center gp-items-center gp-gap-[6px]
      "
      >
      <svg width="15" height="12" viewBox="0 0 15 12" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="2" cy="2" r="2" fill="#D6D6D6"></circle><path d="M0.196854 10.6453L2.40968 8.05946C2.4846 7.97175 2.57719 7.9008 2.68141 7.85124C2.78562 7.80168 2.89913 7.77461 3.01452 7.77181C3.12991 7.76902 3.2446 7.79055 3.3511 7.835C3.45761 7.87945 3.55353 7.94583 3.63262 8.02981L4.66733 9.12714L8.71205 4.29464C8.79123 4.19995 8.89077 4.1243 9.00325 4.07333C9.11573 4.02236 9.23827 3.99737 9.36176 4.00022C9.48524 4.00307 9.6065 4.03369 9.71651 4.08979C9.82651 4.1459 9.92245 4.22605 9.99717 4.32429L14.8329 10.6772C14.9254 10.7989 14.982 10.9441 14.9964 11.0962C15.0107 11.2484 14.9823 11.4016 14.9144 11.5385C14.8464 11.6754 14.7415 11.7907 14.6115 11.8713C14.4815 11.952 14.3316 11.9948 14.1786 11.995L0.822048 12C0.664945 11.9999 0.511148 11.9549 0.378853 11.8703C0.246557 11.7857 0.141299 11.6649 0.0755311 11.5224C0.00976323 11.3799 -0.013762 11.2216 0.00773837 11.0661C0.0292388 10.9107 0.0948652 10.7646 0.196854 10.6453Z" fill="#D6D6D6"></path></svg>
      <span class="gp-text-center gp-overflow-hidden gp-text-ellipsis gp-px-1" style="--fs:normal;--ff:var(--g-font-body, body);--weight:400;--ls:normal;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%">{{value}}</span>
      </div>
     {%- endif -%}
  </div>
  
      {% when "image" %}
      
    {% assign imageUrl = null %}
    {% for label in labels %}
    {% if label == value %}
      {% assign imageUrl = imageUrls[forloop.index0] %}
    {% endif %}
    {% endfor %}
    <div
    class="
    {%- if selectedValue == value -%}
      gp-relative gp-flex gp-cursor-pointer gp-items-center gp-justify-center gp-text-center option-value-wrapper gp-overflow-hidden gp-w-full gp-h-full data-[disabled='disabled']:gp-pointer-events-none data-[disabled='disabled']:!gp-cursor-not-allowed 
         data-[hidden='false']:gp-flex
    {%- else -%}
      gp-relative gp-flex gp-cursor-pointer gp-items-center gp-justify-center gp-text-center option-value-wrapper gp-overflow-hidden gp-w-full gp-h-full data-[disabled='disabled']:gp-pointer-events-none data-[disabled='disabled']:!gp-cursor-not-allowed 
         data-[hidden='false']:gp-flex
    {%- endif -%}
    "
    style="
      {%- if selectedValue == value -%}
        --bg: var(--g-c-bg-3, bg-3);--bs: solid;--bw: 1px 1px 1px 1px;--bc: #E0E0E0;--radius: var(--g-radius-none);--fs: normal;--ff: var(--g-font-body, body);--weight: 400;--ls: normal;--size: 16px;--size-tablet: 16px;--size-mobile: 14px;--lh: 130%;--lh-tablet: 130%;--lh-mobile: 130%;--h: 64px;--w: 64px;
      {%- else -%}
        --bblr: 0px;--bbrr: 0px;--btlr: 0px;--btrr: 0px;--shadow: none;--hvr-bg: var(--g-c-bg-3, bg-3);--bg: var(--g-c-bg-3, bg-3);--bs: solid;--hvr-bs: solid;--bw: 1px 1px 1px 1px;--hvr-bw: 1px 1px 1px 1px;--bc: #E0E0E0;--hvr-bc: #E0E0E0;--fs: normal;--ff: var(--g-font-body, body);--weight: 400;--ls: normal;--size: 16px;--size-tablet: 16px;--size-mobile: 14px;--lh: 130%;--lh-tablet: 130%;--lh-mobile: 130%;--h: 64px;--w: 64px;
      {%- endif -%}
      "
    id="value-wrapper-{{option.name | escape | strip}}-{{value | escape | strip}}"
    option-data="{{option.name | escape}}"
    option-value="{{value | escape}}"
    option-position="{{option.position}}"
    data-gp-option-available="{{value.available}}"
    option-type="image"
    data-disabled="{%- if option_disabled == true -%} disabled  {%- endif -%}"
    data-hidden='true'
  >
    {%- if imageUrl != null and imageUrl != "" -%} 
      <img style="width: 64px; height: 64px" class="gp-object-cover gp-rounded-none" src="{{imageUrl}}" alt="" />
     {% else %} 
      <div class="option-value gp-overflow-hidden gp-relative gp-flex gp-flex-col gp-justify-center gp-items-center gp-gap-[6px]
      "
      >
      <svg width="15" height="12" viewBox="0 0 15 12" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="2" cy="2" r="2" fill="#D6D6D6"></circle><path d="M0.196854 10.6453L2.40968 8.05946C2.4846 7.97175 2.57719 7.9008 2.68141 7.85124C2.78562 7.80168 2.89913 7.77461 3.01452 7.77181C3.12991 7.76902 3.2446 7.79055 3.3511 7.835C3.45761 7.87945 3.55353 7.94583 3.63262 8.02981L4.66733 9.12714L8.71205 4.29464C8.79123 4.19995 8.89077 4.1243 9.00325 4.07333C9.11573 4.02236 9.23827 3.99737 9.36176 4.00022C9.48524 4.00307 9.6065 4.03369 9.71651 4.08979C9.82651 4.1459 9.92245 4.22605 9.99717 4.32429L14.8329 10.6772C14.9254 10.7989 14.982 10.9441 14.9964 11.0962C15.0107 11.2484 14.9823 11.4016 14.9144 11.5385C14.8464 11.6754 14.7415 11.7907 14.6115 11.8713C14.4815 11.952 14.3316 11.9948 14.1786 11.995L0.822048 12C0.664945 11.9999 0.511148 11.9549 0.378853 11.8703C0.246557 11.7857 0.141299 11.6649 0.0755311 11.5224C0.00976323 11.3799 -0.013762 11.2216 0.00773837 11.0661C0.0292388 10.9107 0.0948652 10.7646 0.196854 10.6453Z" fill="#D6D6D6"></path></svg>
      <span class="gp-text-center gp-overflow-hidden gp-text-ellipsis gp-px-1" style="--fs:normal;--ff:var(--g-font-body, body);--weight:400;--ls:normal;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%">{{value}}</span>
      </div>
     {%- endif -%}
  </div>
  
      {% else %}
    {% endcase %}
      

     
    
    
  </div>
</label>
        {%- endfor -%}
        {% if type == 'dropdown' %}
        
    <select
    aria-label={{option.name | escape}}
    autocomplete="off"
    id="p-variant-dropdown-{{option.position}}"
    name="{%- if option -%}{{option.name | escape}}{% else %}Select Option{%- endif -%}"
    option-data="{{option.name}}"
    option-type="{{optionType}}"
    option-renderer="{{optionRendered}}"
    class="gp-truncate gp-bg-auto gp-pl-4 gp-pr-6 gp-outline-none dropdown-option-item active:gp-bg-g-bg-3 hover:gp-bg-g-bg-3 gp-bg-g-bg-3 gp-outline-none gp-shadow-none"
 
    style="--shadow:none;--bg:var(--g-c-bg-3, bg-3);--bs:solid;--bw:1px 1px 1px 1px;--bc:#E0E0E0;--fs:normal;--ff:var(--g-font-body, body);--weight:400;--ls:normal;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--hvr-c:#242424;--c:#242424;--hvr-bg:var(--g-c-bg-3, bg-3);--h:40px;--w:100%;--w-tablet:100%;--w-mobile:100%;--hvr-bs:solid;--hvr-bw:1px 1px 1px 1px;--hvr-bc:#E0E0E0;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;appearance:none;background-image:url(https://cdn.shopify.com/s/files/1/1827/4239/t/1/assets/ico-select.svg?v=155563818344741998551488860031);background-repeat:no-repeat;background-position:right 16px center"
  >
  
  {%- for value in values -%}
          {%- liquid
            assign option_disabled = true
            for variantItem in product.variants
              case option.position
                when 1
                  if variantItem.available and variantItem.option1 == value
                    assign option_disabled = false
                  endif
                when 2
                  if variantItem.available and variantItem.option2 == value
                    assign option_disabled = false
                  endif
                when 3
                  if variantItem.available and variantItem.option3 == value
                    assign option_disabled = false
                  endif
              endcase
            endfor
          -%}
          {%- if option_disabled == true -%}
                    {%- if value == selectedValue -%}
                      <option disabled selected 
                        option-position="{{option.position}}"
                        
    option-data="{{option.name | escape}}"
    option-value="{{value | escape}}" 
    data-gp-option-value-id="{{value.id}}" 
    data-gp-option-available="{{value.available}}"
    value="{{value | escape}}" 
    class="option-value-wrapper" 
    key="{{value | escape}}"
  >
                          {{value}} 
                      </option>
                    {% else %}
                        <option 
                        disabled 
                        
    option-data="{{option.name | escape}}"
    option-value="{{value | escape}}" 
    data-gp-option-value-id="{{value.id}}" 
    data-gp-option-available="{{value.available}}"
    value="{{value | escape}}" 
    class="option-value-wrapper" 
    key="{{value | escape}}"
  >
                          {{value}} 
                        </option>
                    {%- endif -%}
                {%- else -%}
                {%- if value == selectedValue -%}
                  <option selected   
                      option-position="{{option.position}}" 
                      
    option-data="{{option.name | escape}}"
    option-value="{{value | escape}}" 
    data-gp-option-value-id="{{value.id}}" 
    data-gp-option-available="{{value.available}}"
    value="{{value | escape}}" 
    class="option-value-wrapper" 
    key="{{value | escape}}"
  >
                      {{value}}
                  </option>
                  {% else %}
                  <option
                      
    option-data="{{option.name | escape}}"
    option-value="{{value | escape}}" 
    data-gp-option-value-id="{{value.id}}" 
    data-gp-option-available="{{value.available}}"
    value="{{value | escape}}" 
    class="option-value-wrapper" 
    key="{{value | escape}}"
  
                      option-position="{{option.position}}">
                      {{value}} 
                  </option>
                  {%- endif -%}
                {%- endif -%}
         
        {%- endfor -%}
  </select>
    
        {% endif %}
      {% endif %}
    {% endfor %}

    {% if optionRendered == false %}
      
    {%- for value in values -%}
      
    {%- liquid
      assign option_disabled = true
      for variantItem in product.variants
        case option.position
          when 1
            if variantItem.available and variantItem.option1 == value
              assign option_disabled = false
            endif
          when 2
            if variantItem.available and variantItem.option2 == value
              assign option_disabled = false
            endif
          when 3
            if variantItem.available and variantItem.option3 == value
              assign option_disabled = false
            endif
        endcase
      endfor
    -%}

  
  <label
  id="{{option.name | escape}}-{{value | escape}}"
  for="{{product.id}}-ghm5DouDju-{{option.name | escape}}-{{option.position}}-{{ forloop.index }}"
  class="gp-group gp-relative option-item gp-child-item-ghm5DouDju" 
>
  <div class="gp-invisible mobile:gp-overflow-hidden mobile:!gp-max-w-[0px] !gp-max-w-[150px] gp-left-[50%] gp-translate-x-[-50%] gp-translate-y-0 gp-absolute gp-bottom-[calc(100%+20px)] gp-text-[12px] group-hover:gp-visible gp-w-max gp-bg-[#333333] gp-text-[#F9F9F9] mobile:gp-px-[0px] gp-px-[8px] gp-py-[4px] gp-rounded-[8px] gp-mb-[-10px] after:gp-content-[''] after:gp-absolute mobile:after:gp-p-[0px] after:gp-p-[7px] after:gp-left-0 after:gp-w-full after:gp-bottom-[-10px]">
    <p class="gp-text-[#F9F9F9]">{{value | escape}}</p>
    <svg class="gp-absolute gp-left-[50%] gp-translate-x-[-50%] gp-translate-y-0 gp-z-10 gp-bottom-[-4px]" width="8" height="4" viewBox="0 0 16 10" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M8 10L0 0L16 1.41326e-06L8 10Z" fill="#333333"/>
    </svg>
  </div>
  <input
    checked="{%- if option.selected_value == value -%}true{% else %}false{%- endif -%}"
    name="{{product.id}}-options-{{option.name | escape}}"
    id="{{product.id}}-ghm5DouDju-{{option.name | escape}}-{{option.position}}-{{ forloop.index }}"
    value="{{value | escape}}"
    type="radio"
    class="gp-sr-only gp-absolute gp-bottom-0 gp-right-0"
  />
  <span class="gp-sr-only">{{value | escape}}</span>
  <div   
  option-name="{{option.name | escape}}"
  option-name-value="{{value | escape}}"
  data-gp-option-value-id="{{value.id}}"
  class="option-item-inner gp-w-auto gp-h-auto {%- if option_disabled == true -%}gp-opacity-20{%- endif -%}">
  
    <div
    class="
    {%- if selectedValue == value -%}
      gp-relative gp-flex gp-cursor-pointer gp-items-center gp-justify-center gp-text-center option-value-wrapper gp-overflow-hidden gp-w-full gp-h-full data-[disabled='disabled']:gp-pointer-events-none data-[disabled='disabled']:!gp-cursor-not-allowed 
         data-[hidden='false']:gp-flex
    {%- else -%}
      gp-relative gp-flex gp-cursor-pointer gp-items-center gp-justify-center gp-text-center option-value-wrapper gp-overflow-hidden gp-w-full gp-h-full data-[disabled='disabled']:gp-pointer-events-none data-[disabled='disabled']:!gp-cursor-not-allowed 
         data-[hidden='false']:gp-flex
    {%- endif -%}
    "
    style="
      {%- if selectedValue == value -%}
        --bg: var(--g-c-bg-3, bg-3);--bs: solid;--bw: 1px 1px 1px 1px;--bc: #E0E0E0;--c: #242424;--radius: var(--g-radius-none);--fs: normal;--ff: var(--g-font-body, body);--weight: 400;--ls: normal;--size: 16px;--size-tablet: 16px;--size-mobile: 14px;--lh: 130%;--lh-tablet: 130%;--lh-mobile: 130%;--h: 40px;--w: 80px;
      {%- else -%}
        --bblr: 0px;--bbrr: 0px;--btlr: 0px;--btrr: 0px;--shadow: none;--hvr-bg: var(--g-c-bg-3, bg-3);--bg: var(--g-c-bg-3, bg-3);--bs: solid;--hvr-bs: solid;--bw: 1px 1px 1px 1px;--hvr-bw: 1px 1px 1px 1px;--bc: #E0E0E0;--hvr-bc: #E0E0E0;--hvr-c: #242424;--c: #242424;--fs: normal;--ff: var(--g-font-body, body);--weight: 400;--ls: normal;--size: 16px;--size-tablet: 16px;--size-mobile: 14px;--lh: 130%;--lh-tablet: 130%;--lh-mobile: 130%;--h: 40px;--w: 80px;
      {%- endif -%}
      "
    id="value-wrapper-{{option.name | escape | strip}}-{{value | escape | strip}}"
    option-data="{{option.name | escape}}"
    option-value="{{value | escape}}"
    option-position="{{option.position}}"
    data-gp-option-available="{{value.available}}"
    option-type="rectangle_list"
    data-disabled="{%- if option_disabled == true -%} disabled  {%- endif -%}"
    data-hidden='true'
  >
    
      <div class="option-value gp-overflow-hidden gp-relative 
      "
      style="--pl: 16px;--pr: 16px;--pt: 8px;--pb: 8px;">
      
      <span class="gp-text-center" style="--fs:normal;--ff:var(--g-font-body, body);--weight:400;--ls:normal;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%">{{value}}</span>
      </div>
    
  </div>
    
  </div>
</label>
    {%- endfor -%}
    
    {% endif %}
    
          </div>
      </div>
      {%- endfor -%}
    
    </div>
    {%- endif -%}

  </gp-product-variants>
  <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-product-variant-v3.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
   
      </div>
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:center"
      class="gXUt0Ozc3c gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      parentTag="Col" id="gbOvHV-KaQ" data-id="gbOvHV-KaQ"
        style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--cg:8px;--cg-mobile:14px;--pc:start;--gtc:minmax(0, 6fr) minmax(0, 6fr);--gtc-mobile:minmax(0, 6fr) minmax(0, 6fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gbOvHV-KaQ gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gaEIn1doGi gp-relative gp-flex gp-flex-col"
    >
      <div gp-el-wrapper style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%" class="go-yOx9CuC ">
      
    {%- liquid
      assign current_variant = product.selected_or_first_available_variant
      assign available = current_variant.available | default: false
    -%}
      <gp-product-quantity
        data-id="go-yOx9CuC"
        data-disabled="{%- if available -%} false {%- else -%} true {%- endif -%}"
        data-price="false"
        class="quantityClass gp-relative gp-inline-flex gp-w-full gp-bg-transparent gp-transition-all gp-duration-150 data-[disabled=true]:gp-opacity-60 "
        style="--h:48px;--h-mobile:42px;--jc:left"
      >

      
          <button
            title="Decrement"
            aria-label="decrement"
            {% if available == false %} disabled {% endif %}
            class="gp-bg-g-bg-3 gp-minus gp-flex gp-aspect-square gp-h-full gp-cursor-pointer gp-items-center gp-justify-center gp-outline-none disabled:gp-cursor-not-allowed"
            style="--w:48px;--w-mobile:42px;--bg:var(--g-c-bg-3, bg-3);--c:#575757;--bs:solid;--hvr-bs:solid;--bw:1px 1px 1px 1px;--hvr-bw:1px 1px 1px 1px;--bc:#E0E0E0;--hvr-bc:#E0E0E0;--bblr:0px;--bbrr:auto;--btlr:0px;--btrr:auto;--hvr-bblr:6px;--hvr-btlr:6px"
          >
            <span class="gp-inline-flex gp-items-center gp-justify-center" style="--w:21px">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="gp-h-full gp-w-full"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 12H6" />
              </svg>
            </span>
          </button>
      
        <input
          type="text"
          name="product-quantity"
          {% if available == false %} disabled {% endif %}
          class="gp-bg-g-bg-3 !gp-border-x-0 gp-px-4 gp-flex gp-shadow-none gp-appearance-none gp-items-center gp-border-y gp-text-center gp-outline-none gp-transition-all gp-duration-150 hover:gp-text-black disabled:gp-pointer-events-none gp-h-auto gp-rounded-none gp-shrink-[99999] gp-w-full gp-min-w-[45px]"
          style="--maxw:100%;--maxw-tablet:100%;--maxw-mobile:100%;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--c:#242424;--bg:var(--g-c-bg-3, bg-3);--bs:solid;--hvr-bs:solid;--bw:1px 1px 1px 1px;--hvr-bw:1px 1px 1px 1px;--bc:#E0E0E0;--hvr-bc:#E0E0E0"
          aria-label="Quantity"
          inputmode="numeric"
          min="{{ current_variant.quantity_rule.min }}"
          step="{{ current_variant.quantity_rule.increment }}"
          value="1"
          autocomplete='off'
        />

        
          <button
            {% if available == false %} disabled {% endif %}
            aria-label="increment"
            title="Increment"
            class="gp-bg-g-bg-3 gp-plus gp-flex gp-aspect-square gp-h-full gp-cursor-pointer gp-items-center gp-justify-center gp-outline-none gp-transition-all gp-duration-150 disabled:gp-pointer-events-none"
            style="--w:48px;--w-mobile:42px;--bg:var(--g-c-bg-3, bg-3);--c:#575757;--bs:solid;--hvr-bs:solid;--bw:1px 1px 1px 1px;--hvr-bw:1px 1px 1px 1px;--bc:#E0E0E0;--hvr-bc:#E0E0E0;--bblr:auto;--bbrr:0px;--btlr:auto;--btrr:0px;--hvr-bbrr:6px;--hvr-btrr:6px"
          >
            <span class="gp-inline-flex gp-items-center gp-justify-center" style="--w:21px">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="gp-h-full gp-w-full"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                />
              </svg>
            </span>
          </button>
        
      </gp-product-quantity>
      <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-product-quantity.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
      </div>
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gA1twGMF5O gp-relative gp-flex gp-flex-col"
    >
      
   {%- liquid
      assign inventory_quantity = variant.inventory_quantity | default: 0
    -%}
    <gp-product-button
      class="gp-product-button"
      gp-data-wrapper="true"
      gp-label-out-of-stock="{{section.settings.ggtG9mzCLbA_outOfStockLabel}}"
      gp-label-unavailable="{{section.settings.ggtG9mzCLbA_unavailableLabel}}"
      gp-data='{"setting":{"actionEffect":"continue-shopping","errorMessage":"Cannot add product to cart","successMessage":"Add product to cart successfully","enableSuccessMessage":true,"enableErrorMessage":true,"label":"Add to cart","outOfStockLabel":"Out of stock","errorType":"built-in","customURL":{"link":"/cart","target":"_self"}},"styles":{"errorTypo":{"attrs":{"color":"error"},"type":"paragraph-2"},"successTypo":{"attrs":{"color":"success"},"type":"paragraph-2"}},"disabled":"{{variant.available}}","variantID":"{{variant.id}}","totalVariant":"{{product.variants.size}}"}' 
       gp-href="{{ request.origin }}{{ routes.root_url | split: '/' | join: '/' }}/cart"
       data-variant-selection-required-message="{{}}"
    >
        
  <gp-button >
  <div
    style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
    class="{% unless variant.available %} !gp-hidden {% endunless %}"
  >
    <style>
    .gtG9mzCLbA.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: solid;
  border-width: 0px 0px 0px 0px;
  border-color: #121212;
  
      
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
    }

    .gtG9mzCLbA:hover::before {
      border-style: solid;
  border-width: 0px 0px 0px 0px;
  border-color: #4D4D4D;
  
      
    }

    .gtG9mzCLbA:hover .gp-button-icon {
      color: undefined;
    }

     .gtG9mzCLbA .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .gtG9mzCLbA:hover .gp-button-price {
      color: undefined;
    }

    .gtG9mzCLbA .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .gtG9mzCLbA .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .gtG9mzCLbA:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <button
      type="submit" data-id="gtG9mzCLbA" aria-label="Add to cart"
      name="add" gp-data-hidden="{% unless variant.available %}true{% endunless %}"
      data-state="idle"
      class="gtG9mzCLbA gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-button-atc tcustomizer-submit-button"
      style="--hvr-bg:#575757;--bg:#242424;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--w:100%;--w-tablet:100%;--w-mobile:100%;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--pl:24px;--pr:24px;--pt:14px;--pb:14px;--pl-mobile:24px;--pr-mobile:24px;--pt-mobile:12px;--pb-mobile:12px;--c:var(--g-c-text-3, text-3);--size:16px;--size-tablet:16px;--size-mobile:14px;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--tt:capitalize"
    >
        <svg
          class="gp-invisible gp-absolute gp-h-5 gp-w-5 group-data-[state=loading]/button:gp-animate-spin group-data-[state=loading]/button:gp-visible"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            class="gp-opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            stroke-width="4"
          ></circle>
          <path
            class="gp-opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          ></path>
        </svg>
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--size:16px;--size-tablet:16px;--size-mobile:14px;--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.ggtG9mzCLbA_label }}
      </span>
      </div>
    
    </button>
  </div>
  </gp-button>

        
  <gp-button >
  <div
    style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
    class="{% if variant.available %} !gp-hidden {% endif %}"
  >
    <style>
    .gtG9mzCLbA-sold-out.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: solid;
  border-width: 0px 0px 0px 0px;
  border-color: #121212;
  
      
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
    }

    .gtG9mzCLbA-sold-out:hover::before {
      border-style: solid;
  border-width: 0px 0px 0px 0px;
  border-color: #4D4D4D;
  
      
    }

    .gtG9mzCLbA-sold-out:hover .gp-button-icon {
      color: undefined;
    }

     .gtG9mzCLbA-sold-out .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .gtG9mzCLbA-sold-out:hover .gp-button-price {
      color: undefined;
    }

    .gtG9mzCLbA-sold-out .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .gtG9mzCLbA-sold-out .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .gtG9mzCLbA-sold-out:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <button
      type="button" data-id="gtG9mzCLbA" aria-label="{{section.settings.ggtG9mzCLbA_outOfStockLabel}}"
      gp-data-hidden="{% if variant.available %}true{% endif %}"
      data-state="idle"
      class="gtG9mzCLbA-sold-out gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-button-sold-out btn-disabled gp-opacity-30 gp-cursor-default"
      style="--hvr-bg:#575757;--bg:#242424;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--w:100%;--w-tablet:100%;--w-mobile:100%;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--pl:24px;--pr:24px;--pt:14px;--pb:14px;--pl-mobile:24px;--pr-mobile:24px;--pt-mobile:12px;--pb-mobile:12px;--c:var(--g-c-text-3, text-3);--size:16px;--size-tablet:16px;--size-mobile:14px;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--tt:capitalize"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--size:16px;--size-tablet:16px;--size-mobile:14px;--c:var(--g-c-text-3, text-3)"
      >
        {{section.settings.ggtG9mzCLbA_outOfStockLabel}}
      </span>
      </div>
    
    </button>
  </div>
  </gp-button>

    </gp-product-button>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-product-button.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
    </div>
    </div>
   
    
    </div>
    </div>
   
    
    </div>
    </div>
   
    
          {%- endform -%}
        </product-form>
      </gp-product>
      {%- assign product = gpBkProduct -%}
    {% else %}
      <div class="gp-text-center">{{ "gempages.Product.product_not_found" | t }}</div>
    {%- endif -%}
     <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-product.v2.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
    </div>
      </div>
    </gp-sticky>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-sticky.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  


{% schema %}
  {
    
    "name": "Section 25",
    "tag": "section",
    "class": "gps-572904028179006580 gps gpsil",
    "settings": [{"type":"paragraph","content":"Section design by GemPages. [Click here to start design](https://admin.shopify.com/store/i0pixe-10/apps/gempages-cro/app/shopify/edit?pageType=GP_PRODUCT&editorId=572751048691811510&sectionId=572904028179006580)"},{"type":"select","label":"Section Preload","id":"section_preload","options":[{"label":"On","value":"true"},{"label":"Off","value":"false"},{"label":"Off Lazy Script","value":"off_lazy_script"}],"default":"false"},{"type":"text","label":"Checksum","id":"checksum"},{"type":"html","id":"ggtG9mzCLbA_label","label":"ggtG9mzCLbA_label","default":"Add to cart"},{"type":"html","id":"ggtG9mzCLbA_outOfStockLabel","label":"ggtG9mzCLbA_outOfStockLabel","default":"Out of stock"},{"type":"html","id":"ggtG9mzCLbA_unavailableLabel","label":"ggtG9mzCLbA_unavailableLabel","default":"Unavailable"}],
    "blocks": [{"type": "@app"}],
    "locales": {}
  }
{% endschema %}
