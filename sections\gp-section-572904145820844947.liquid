

<style {% if section.settings.section_preload == "false" %} class="gps-link gps-style" type="text/disabled-css" {% endif %}>.gps-572904145820844947.gps.gpsil [style*="--ai:"]{align-items:var(--ai)}.gps-572904145820844947.gps.gpsil [style*="--bg:"]{background:var(--bg)}.gps-572904145820844947.gps.gpsil [style*="--hvr-bg:"]:hover{background:var(--hvr-bg)}.gps-572904145820844947.gps.gpsil [style*="--bga:"]{background-attachment:var(--bga)}.gps-572904145820844947.gps.gpsil [style*="--bgc:"]{background-color:var(--bgc)}.gps-572904145820844947.gps.gpsil [style*="--bgi:"]{background-image:var(--bgi)}.gps-572904145820844947.gps.gpsil [style*="--bgp:"]{background-position:var(--bgp)}.gps-572904145820844947.gps.gpsil [style*="--bgr:"]{background-repeat:var(--bgr)}.gps-572904145820844947.gps.gpsil [style*="--bgs:"]{background-size:var(--bgs)}.gps-572904145820844947.gps.gpsil [style*="--b:"]{border:var(--b)}.gps-572904145820844947.gps.gpsil [style*="--hvr-b:"]:hover{border:var(--hvr-b)}.gps-572904145820844947.gps.gpsil [style*="--bb:"]{border-bottom:var(--bb)}.gps-572904145820844947.gps.gpsil [style*="--hvr-bb:"]:hover{border-bottom:var(--hvr-bb)}.gps-572904145820844947.gps.gpsil [style*="--bc:"]{border-color:var(--bc)}.gps-572904145820844947.gps.gpsil [style*="--hvr-bc:"]:hover{border-color:var(--hvr-bc)}.gps-572904145820844947.gps.gpsil [style*="--bblr:"]{border-bottom-left-radius:var(--bblr)}.gps-572904145820844947.gps.gpsil [style*="--hvr-bblr:"]:hover{border-bottom-left-radius:var(--hvr-bblr)}.gps-572904145820844947.gps.gpsil [style*="--bbrr:"]{border-bottom-right-radius:var(--bbrr)}.gps-572904145820844947.gps.gpsil [style*="--hvr-bbrr:"]:hover{border-bottom-right-radius:var(--hvr-bbrr)}.gps-572904145820844947.gps.gpsil [style*="--radius:"]{border-radius:var(--radius)}.gps-572904145820844947.gps.gpsil [style*="--bs:"]{border-style:var(--bs)}.gps-572904145820844947.gps.gpsil [style*="--hvr-bs:"]:hover{border-style:var(--hvr-bs)}.gps-572904145820844947.gps.gpsil [style*="--bt:"]{border-top:var(--bt)}.gps-572904145820844947.gps.gpsil [style*="--hvr-bt:"]:hover{border-top:var(--hvr-bt)}.gps-572904145820844947.gps.gpsil [style*="--btlr:"]{border-top-left-radius:var(--btlr)}.gps-572904145820844947.gps.gpsil [style*="--hvr-btlr:"]:hover{border-top-left-radius:var(--hvr-btlr)}.gps-572904145820844947.gps.gpsil [style*="--btrr:"]{border-top-right-radius:var(--btrr)}.gps-572904145820844947.gps.gpsil [style*="--hvr-btrr:"]:hover{border-top-right-radius:var(--hvr-btrr)}.gps-572904145820844947.gps.gpsil [style*="--bw:"]{border-width:var(--bw)}.gps-572904145820844947.gps.gpsil [style*="--hvr-bw:"]:hover{border-width:var(--hvr-bw)}.gps-572904145820844947.gps.gpsil [style*="--bottom:"]{bottom:var(--bottom)}.gps-572904145820844947.gps.gpsil [style*="--shadow:"]{box-shadow:var(--shadow)}.gps-572904145820844947.gps.gpsil [style*="--c:"]{color:var(--c)}.gps-572904145820844947.gps.gpsil [style*="--hvr-c:"]:hover{color:var(--hvr-c)}.gps-572904145820844947.gps.gpsil [style*="--cg:"]{-moz-column-gap:var(--cg);column-gap:var(--cg)}.gps-572904145820844947.gps.gpsil [style*="--ff:"]{font-family:var(--ff)}.gps-572904145820844947.gps.gpsil [style*="--size:"]{font-size:var(--size)}.gps-572904145820844947.gps.gpsil [style*="--weight:"]{font-weight:var(--weight)}.gps-572904145820844947.gps.gpsil [style*="--fs:"]{font-style:var(--fs)}.gps-572904145820844947.gps.gpsil [style*="--gtc:"]{grid-template-columns:var(--gtc)}.gps-572904145820844947.gps.gpsil [style*="--h:"]{height:var(--h)}.gps-572904145820844947.gps.gpsil [style*="--jc:"]{justify-content:var(--jc)}.gps-572904145820844947.gps.gpsil [style*="--left:"]{left:var(--left)}.gps-572904145820844947.gps.gpsil [style*="--ls:"]{letter-spacing:var(--ls)}.gps-572904145820844947.gps.gpsil [style*="--lh:"]{line-height:var(--lh)}.gps-572904145820844947.gps.gpsil [style*="--tdt:"]{text-decoration-thickness:var(--tdt)}.gps-572904145820844947.gps.gpsil [style*="--tdc:"]{text-decoration-color:var(--tdc)}.gps-572904145820844947.gps.gpsil [style*="--m:"]{margin:var(--m)}.gps-572904145820844947.gps.gpsil [style*="--mb:"]{margin-bottom:var(--mb)}.gps-572904145820844947.gps.gpsil [style*="--mt:"]{margin-top:var(--mt)}.gps-572904145820844947.gps.gpsil [style*="--maxw:"]{max-width:var(--maxw)}.gps-572904145820844947.gps.gpsil [style*="--op:"]{opacity:var(--op)}.gps-572904145820844947.gps.gpsil [style*="--o:"]{order:var(--o)}.gps-572904145820844947.gps.gpsil [style*="--pc:"]{place-content:var(--pc)}.gps-572904145820844947.gps.gpsil [style*="--p:"]{padding:var(--p)}.gps-572904145820844947.gps.gpsil [style*="--pb:"]{padding-bottom:var(--pb)}.gps-572904145820844947.gps.gpsil [style*="--pl:"]{padding-left:var(--pl)}.gps-572904145820844947.gps.gpsil [style*="--pr:"]{padding-right:var(--pr)}.gps-572904145820844947.gps.gpsil [style*="--pt:"]{padding-top:var(--pt)}.gps-572904145820844947.gps.gpsil [style*="--pos:"]{position:var(--pos)}.gps-572904145820844947.gps.gpsil [style*="--rg:"]{row-gap:var(--rg)}.gps-572904145820844947.gps.gpsil [style*="--ta:"]{text-align:var(--ta)}.gps-572904145820844947.gps.gpsil [style*="--tt:"]{text-transform:var(--tt)}.gps-572904145820844947.gps.gpsil [style*="--top:"]{top:var(--top)}.gps-572904145820844947.gps.gpsil [style*="--t:"]{transform:var(--t)}.gps-572904145820844947.gps.gpsil [style*="--w:"]{width:var(--w)}.gps-572904145820844947.gps.gpsil [style*="--line-clamp:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp);display:-webkit-box;overflow:hidden}@media only screen and (max-width:1024px){.gps-572904145820844947.gps.gpsil [style*="--bottom-tablet:"]{bottom:var(--bottom-tablet)}.gps-572904145820844947.gps.gpsil [style*="--cg-tablet:"]{-moz-column-gap:var(--cg-tablet);column-gap:var(--cg-tablet)}.gps-572904145820844947.gps.gpsil [style*="--size-tablet:"]{font-size:var(--size-tablet)}.gps-572904145820844947.gps.gpsil [style*="--h-tablet:"]{height:var(--h-tablet)}.gps-572904145820844947.gps.gpsil [style*="--left-tablet:"]{left:var(--left-tablet)}.gps-572904145820844947.gps.gpsil [style*="--lh-tablet:"]{line-height:var(--lh-tablet)}.gps-572904145820844947.gps.gpsil [style*="--maxw-tablet:"]{max-width:var(--maxw-tablet)}.gps-572904145820844947.gps.gpsil [style*="--pb-tablet:"]{padding-bottom:var(--pb-tablet)}.gps-572904145820844947.gps.gpsil [style*="--pl-tablet:"]{padding-left:var(--pl-tablet)}.gps-572904145820844947.gps.gpsil [style*="--pr-tablet:"]{padding-right:var(--pr-tablet)}.gps-572904145820844947.gps.gpsil [style*="--pt-tablet:"]{padding-top:var(--pt-tablet)}.gps-572904145820844947.gps.gpsil [style*="--pos-tablet:"]{position:var(--pos-tablet)}.gps-572904145820844947.gps.gpsil [style*="--top-tablet:"]{top:var(--top-tablet)}.gps-572904145820844947.gps.gpsil [style*="--t-tablet:"]{transform:var(--t-tablet)}.gps-572904145820844947.gps.gpsil [style*="--w-tablet:"]{width:var(--w-tablet)}.gps-572904145820844947.gps.gpsil [style*="--line-clamp-tablet:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-tablet);display:-webkit-box;overflow:hidden}}@media only screen and (max-width:767px){.gps-572904145820844947.gps.gpsil [style*="--bottom-mobile:"]{bottom:var(--bottom-mobile)}.gps-572904145820844947.gps.gpsil [style*="--size-mobile:"]{font-size:var(--size-mobile)}.gps-572904145820844947.gps.gpsil [style*="--gtc-mobile:"]{grid-template-columns:var(--gtc-mobile)}.gps-572904145820844947.gps.gpsil [style*="--h-mobile:"]{height:var(--h-mobile)}.gps-572904145820844947.gps.gpsil [style*="--jc-mobile:"]{justify-content:var(--jc-mobile)}.gps-572904145820844947.gps.gpsil [style*="--left-mobile:"]{left:var(--left-mobile)}.gps-572904145820844947.gps.gpsil [style*="--lh-mobile:"]{line-height:var(--lh-mobile)}.gps-572904145820844947.gps.gpsil [style*="--mb-mobile:"]{margin-bottom:var(--mb-mobile)}.gps-572904145820844947.gps.gpsil [style*="--mt-mobile:"]{margin-top:var(--mt-mobile)}.gps-572904145820844947.gps.gpsil [style*="--maxw-mobile:"]{max-width:var(--maxw-mobile)}.gps-572904145820844947.gps.gpsil [style*="--pc-mobile:"]{place-content:var(--pc-mobile)}.gps-572904145820844947.gps.gpsil [style*="--pb-mobile:"]{padding-bottom:var(--pb-mobile)}.gps-572904145820844947.gps.gpsil [style*="--pl-mobile:"]{padding-left:var(--pl-mobile)}.gps-572904145820844947.gps.gpsil [style*="--pr-mobile:"]{padding-right:var(--pr-mobile)}.gps-572904145820844947.gps.gpsil [style*="--pt-mobile:"]{padding-top:var(--pt-mobile)}.gps-572904145820844947.gps.gpsil [style*="--pos-mobile:"]{position:var(--pos-mobile)}.gps-572904145820844947.gps.gpsil [style*="--rg-mobile:"]{row-gap:var(--rg-mobile)}.gps-572904145820844947.gps.gpsil [style*="--top-mobile:"]{top:var(--top-mobile)}.gps-572904145820844947.gps.gpsil [style*="--t-mobile:"]{transform:var(--t-mobile)}.gps-572904145820844947.gps.gpsil [style*="--w-mobile:"]{width:var(--w-mobile)}.gps-572904145820844947.gps.gpsil [style*="--line-clamp-mobile:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-mobile);display:-webkit-box;overflow:hidden}}.gps-572904145820844947 .gp-shadow-none{--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000}.gps-572904145820844947 .gp-invisible{visibility:hidden}.gps-572904145820844947 .gp-absolute{position:absolute}.gps-572904145820844947 .gp-relative{position:relative}.gps-572904145820844947 .gp-sticky{position:sticky}.gps-572904145820844947 .gp-z-1{z-index:1}.gps-572904145820844947 .gp-mx-auto{margin-left:auto;margin-right:auto}.gps-572904145820844947 .\!gp-ml-0{margin-left:0!important}.gps-572904145820844947 .gp-mb-0{margin-bottom:0}.gps-572904145820844947 .gp-block{display:block}.gps-572904145820844947 .gp-flex{display:flex}.gps-572904145820844947 .gp-inline-flex{display:inline-flex}.gps-572904145820844947 .gp-grid{display:grid}.gps-572904145820844947 .\!gp-hidden{display:none!important}.gps-572904145820844947 .gp-hidden{display:none}.gps-572904145820844947 .gp-aspect-square{aspect-ratio:1/1}.gps-572904145820844947 .gp-h-5{height:20px}.gps-572904145820844947 .gp-h-auto{height:auto}.gps-572904145820844947 .gp-h-full{height:100%}.gps-572904145820844947 .gp-w-5{width:20px}.gps-572904145820844947 .gp-w-full{width:100%}.gps-572904145820844947 .gp-min-w-\[45px\]{min-width:45px}.gps-572904145820844947 .gp-max-w-full{max-width:100%}.gps-572904145820844947 .gp-shrink-\[99999\]{flex-shrink:99999}.gps-572904145820844947 .gp-cursor-default{cursor:default}.gps-572904145820844947 .gp-cursor-pointer{cursor:pointer}.gps-572904145820844947 .gp-appearance-none{-webkit-appearance:none;-moz-appearance:none;appearance:none}.gps-572904145820844947 .gp-grid-rows-\[1fr\]{grid-template-rows:1fr}.gps-572904145820844947 .gp-flex-col{flex-direction:column}.gps-572904145820844947 .gp-flex-wrap{flex-wrap:wrap}.gps-572904145820844947 .gp-items-end{align-items:flex-end}.gps-572904145820844947 .gp-items-center{align-items:center}.gps-572904145820844947 .gp-justify-start{justify-content:flex-start}.gps-572904145820844947 .gp-justify-center{justify-content:center}.gps-572904145820844947 .gp-gap-3{gap:12px}.gps-572904145820844947 .gp-gap-y-0{row-gap:0}.gps-572904145820844947 .gp-overflow-hidden{overflow:hidden}.gps-572904145820844947 .gp-truncate{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.gps-572904145820844947 .gp-break-words{overflow-wrap:break-word}.gps-572904145820844947 .gp-rounded-none{border-radius:0}.gps-572904145820844947 .\!gp-border-x-0{border-left-width:0!important;border-right-width:0!important}.gps-572904145820844947 .gp-border-y{border-bottom-width:1px;border-top-width:1px}.gps-572904145820844947 .gp-border-g-line-1{border-color:var(--g-c-line-1)}.gps-572904145820844947 .gp-bg-g-bg-2{background-color:var(--g-c-bg-2)}.gps-572904145820844947 .gp-bg-g-bg-3{background-color:var(--g-c-bg-3)}.gps-572904145820844947 .gp-bg-transparent{background-color:transparent}.gps-572904145820844947 .gp-bg-auto{background-size:auto}.gps-572904145820844947 .gp-px-4{padding-left:16px;padding-right:16px}.gps-572904145820844947 .gp-pl-4{padding-left:16px}.gps-572904145820844947 .gp-pr-6{padding-right:24px}.gps-572904145820844947 .gp-text-center{text-align:center}.gps-572904145820844947 .gp-text-g-text-2{color:var(--g-c-text-2)}.gps-572904145820844947 .gp-text-g-text-3{color:var(--g-c-text-3)}.gps-572904145820844947 .gp-line-through{text-decoration-line:line-through}.gps-572904145820844947 .gp-no-underline{text-decoration-line:none}.gps-572904145820844947 .gp-decoration-g-text-1{text-decoration-color:var(--g-c-text-1)}.gps-572904145820844947 .gp-opacity-25{opacity:.25}.gps-572904145820844947 .gp-opacity-30{opacity:.3}.gps-572904145820844947 .gp-opacity-75{opacity:.75}.gps-572904145820844947 .gp-shadow-none{--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)}.gps-572904145820844947 .gp-outline-none{outline:2px solid transparent;outline-offset:2px}.gps-572904145820844947 .gp-transition-all{transition-duration:.15s;transition-property:all;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-572904145820844947 .gp-transition-colors{transition-duration:.15s;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-572904145820844947 .gp-duration-150{transition-duration:.15s}.gps-572904145820844947 .gp-duration-200{transition-duration:.2s}.gps-572904145820844947 .gp-duration-300{transition-duration:.3s}.gps-572904145820844947 .gp-ease-in-out{transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-572904145820844947 .disabled\:gp-btn-disabled:disabled{cursor:default}@media (hover:hover) and (pointer:fine){.gps-572904145820844947 .hover\:gp-bg-g-bg-3:hover{background-color:var(--g-c-bg-3)}.gps-572904145820844947 .hover\:gp-text-black:hover{--tw-text-opacity:1;color:rgb(0 0 0/var(--tw-text-opacity))}}.gps-572904145820844947 .disabled\:gp-pointer-events-none:disabled{pointer-events:none}.gps-572904145820844947 .disabled\:gp-cursor-not-allowed:disabled{cursor:not-allowed}.gps-572904145820844947 .disabled\:gp-opacity-30:disabled{opacity:.3}@media (hover:hover) and (pointer:fine){.gps-572904145820844947 .gp-group\/button:hover .group-hover\/button\:\!gp-text-inherit{color:inherit!important}}.gps-572904145820844947 .gp-group\/button:active .group-active\/button\:\!gp-text-inherit{color:inherit!important}.gps-572904145820844947 .data-\[disabled\=true\]\:gp-hidden[data-disabled=true],.gps-572904145820844947 .data-\[hidden\=true\]\:gp-hidden[data-hidden=true]{display:none}.gps-572904145820844947 .data-\[disabled\=true\]\:gp-opacity-60[data-disabled=true]{opacity:.6}.gps-572904145820844947 .gp-group\/button[data-state=loading] .group-data-\[state\=loading\]\/button\:gp-visible{visibility:visible}.gps-572904145820844947 .gp-group[data-state=loading] .group-data-\[state\=loading\]\:gp-invisible,.gps-572904145820844947 .gp-group\/button[data-state=loading] .group-data-\[state\=loading\]\/button\:gp-invisible{visibility:hidden}@keyframes gp-spin{to{transform:rotate(1turn)}}.gps-572904145820844947 .gp-group\/button[data-state=loading] .group-data-\[state\=loading\]\/button\:gp-animate-spin{animation:gp-spin 1s linear infinite}@media (max-width:1024px){.gps-572904145820844947 .tablet\:\!gp-hidden{display:none!important}.gps-572904145820844947 .tablet\:gp-hidden{display:none}}@media (max-width:767px){.gps-572904145820844947 .mobile\:\!gp-hidden{display:none!important}.gps-572904145820844947 .mobile\:gp-hidden{display:none}}.gps-572904145820844947 .\[\&_\*\]\:gp-max-w-full *{max-width:100%}.gps-572904145820844947 .\[\&_p\]\:gp-inline p{display:inline}.gps-572904145820844947 .\[\&_p\]\:gp-whitespace-pre-line p{white-space:pre-line}.gps-572904145820844947 .\[\&_p\]\:after\:gp-whitespace-pre-wrap p:after{content:var(--tw-content);white-space:pre-wrap}.gps-572904145820844947 .\[\&_p\]\:after\:gp-content-\[\'\\A\'\] p:after{--tw-content:"\A";content:var(--tw-content)}</style>

    
    <gp-sticky
    data-id="gcVur_1oLM"
      gp-data='{"uid":"gcVur_1oLM","setting":{"display":{"desktop":"after-first-cart-button"}},"advanced":{"d":{"desktop":true,"mobile":true,"tablet":true}}}'
      id="gcVur_1oLM"
      data-id="gcVur_1oLM"
      class="gcVur_1oLM {% if section.settings.section_preload == "false" %}gps-lazy {% endif %}  "
      style="margin:0 auto;--bs:none;--bw:2px 2px 2px 2px;--bc:#121212;--shadow:0px 0px 10px 0px #121212;--d:none;--d-mobile:none;--d-tablet:none;--op:97%;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--pt:var(--g-s-m);--pl:15px;--pb:0px;--pr:15px;--left:50%;--t:translateX(-50%);--left-tablet:50%;--t-tablet:translateX(-50%);--left-mobile:50%;--t-mobile:translateX(-50%);--top:auto;--bottom:0;--pos:fixed;--top-tablet:auto;--bottom-tablet:0;--pos-tablet:fixed;--top-mobile:auto;--bottom-mobile:0;--pos-mobile:fixed;--w:100%;--w-tablet:100%;--w-mobile:100%;z-index:100000"
    >
      <div 
         
        style="--bgc:#FBFBFB;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
      >
        <div
      
      
      class="g_uHJhLuTC gp-relative gp-flex gp-flex-col"
    >
      
    {%- assign gpBkProduct = product -%}
    
        {%- liquid
          if request.page_type != 'product'
            assign product = all_products['']
            assign productId = 'latest' | times: 1
            if product == empty or product == null
              paginate collections.all.products by 100000
                for item in collections.all.products
                  if item.id == productId
                    assign product = item
                  endif
                endfor
              endpaginate
            endif
          endif
        -%}
        

    {%-if product != empty and product != null -%}
      {%- assign initVariantId =  -%}
      {%- assign product_form_id = 'product-form-' | append: "gvsNGLleh1" -%}
      {%- assign variant = product.selected_or_first_available_variant -%}
      {%- assign productSelectedVariant = product.selected_or_first_available_variant -%}
      {%-if productSelectedVariant == empty or productSelectedVariant == null -%}
        {%- assign productSelectedVariant = product.selected_or_first_available_variant -%}
      {%- endif -%}
      {%-if variant == empty or variant == null -%}
        {%- assign variant = product.selected_or_first_available_variant -%}
      {%- endif -%}
      <gp-product data-uid="gvsNGLleh1" data-id="gvsNGLleh1"   gp-context='{"productId": {{ product.id }}, "preSelectedOptionIds": [], "isSyncProduct": "true", "variantSelected": {{ variant | json | escape }}, "inventoryQuantity": {{ variant.inventory_quantity }}, "quantity": 1, "formId": "{{ product_form_id }}" }'
        gp-data='{"variantSelected": {{ variant | json | escape }}, "quantity": 1, "productUrl":{{ product.url | json | escape }}, "productHandle":{{ product.handle | json | escape }}, "collectionUrl": {{ collection.url | json | escape }}, "collectionHandle": {{ collection.handle | json | escape }}}'>
        <product-form class="product-form">
          {%- form 'product', product, id: product_form_id, class: 'form contents', data-type: 'add-to-cart-form', autocomplete: 'off'  -%}
            <input type="hidden" name="id" value="{{ variant.id }}" />
            <input type="hidden" name="quantity" value="{{ quantity }}" />
            <button type="submit" onclick="return false;" style="display:none;"></button>
            
       
      
    <div
      id="gvsNGLleh1" data-id="gvsNGLleh1-row"
        style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pt:0px;--pl:0px;--pb:0px;--pr:0px;--pt-mobile:0px;--pl-mobile:0px;--pb-mobile:0px;--pr-mobile:0px;--cg:40px;--cg-tablet:12px;--pc:start;--gtc:minmax(0, 5fr) minmax(0, 7fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:100%;--w-mobile:100%"
        class="gvsNGLleh1 gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:center"
      class="gd5XJ_UlbH gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      parentTag="Col" id="gIIJ695gRt" data-id="gIIJ695gRt"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--radius:var(--g-radius-small);--mt:auto;--mt-mobile:auto;--mb-mobile:var(--g-s-l);--cg:16px;--pc:start;--gtc:minmax(0, 12fr);--gtc-mobile:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gIIJ695gRt gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--ai:normal;--jc:center;--o:0"
      class="gdiez5JMY6 gp-relative gp-flex gp-flex-col"
    >
      <div gp-el-wrapper style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mb:var(--g-s-xs);--mb-mobile:var(--g-s-xxs)" class="gtavYoTnNN ">
      
    {%- unless product -%}
      <p>{{ "gempages.ProductTitle.product_not_found" | t }}</p>
    {%- else -%}
      
        
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gtavYoTnNN">
    <div
      
        class="gtavYoTnNN "
        
      >
      <div  >
        <h1
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-product-title gp-text-g-text-2"
          style="--w:100%;--ta:left;--line-clamp:1;--line-clamp-tablet:1;--line-clamp-mobile:1;--c:var(--g-c-text-2, text-2);--fs:normal;--ff:var(--g-font-heading, heading);--weight:400;--ls:normal;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;word-break:break-word;overflow:hidden"
        >{{ product.title }}</h1>
      </div>
    </div>
    </gp-text>
    
      
    {%- endunless -%}
  
      </div>
       
      
    <div
      parentTag="Col" id="gLCo2amGRr" data-id="gLCo2amGRr"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--radius:var(--g-radius-small);--cg:8px;--pc:start;--gtc:minmax(0, auto) minmax(0, auto) minmax(0, auto);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gLCo2amGRr gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--ai:normal;--jc:center;--o:0"
      class="gpe6_W7lQT gp-relative gp-flex gp-flex-col"
    >
      
      <gp-product-price
        data-id="gryNoMZ0wH"
        class="gryNoMZ0wH gp-product-price group-data-[state=loading]:gp-invisible data-[hidden=true]:gp-hidden"
        gp-data='{"priceType":"regular","uid":"gryNoMZ0wH","locale":"{{shop.locale}}","currency":"{{shop.currency}}","moneyFormat":"{{ shop.money_format | replace: '"', '\\"' | escape }}"}'
        
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small)"
        data-hidden="false"
      >
        
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="">
    <div
      id="p-price-gryNoMZ0wH"
        class=" "
        
      >
      <div  >
        <div
          type="regular"
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-price gp-money gp-decoration-g-text-1"
          style="--w:100%;--tdc:text-1;--tdt:1;--ta:left;--c:#242424;--fs:normal;--ff:var(--g-font-heading, heading);--weight:700;--ls:normal;--size:19px;--size-tablet:19px;--size-mobile:17px;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;word-break:break-word;overflow:hidden"
        >
     {% if variant.price %} 
       {{ variant.price | money}}
     {% endif %}
   </div>
      </div>
    </div>
    </gp-text>
    
      </gp-product-price>
      <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-product-price.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:center"
      class="gglwhDWJSU gp-relative gp-flex gp-flex-col"
    >
      
      <gp-product-price
        data-id="gAauo60mQI"
        class="gAauo60mQI gp-product-price group-data-[state=loading]:gp-invisible data-[hidden=true]:gp-hidden"
        gp-data='{"priceType":"compare","uid":"gAauo60mQI","locale":"{{shop.locale}}","currency":"{{shop.currency}}","moneyFormat":"{{ shop.money_format | replace: '"', '\\"' | escape }}"}'
        
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small)"
        data-hidden="{% if variant.compare_at_price > variant.price and variant.compare_at_price >= 0 %}false{% else %}true{% endif %}"
      >
        
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="">
    <div
      id="p-price-gAauo60mQI"
        class=" "
        
      >
      <div  >
        <div
          type="compare"
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-price gp-product-compare-price gp-line-through"
          style="--w:100%;--tdc:#B4B4B4;--tdt:1;--ta:left;--c:#B4B4B4;--fs:normal;--ff:var(--g-font-heading, heading);--weight:400;--ls:normal;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;word-break:break-word;overflow:hidden"
        >
      {% if variant.compare_at_price  %} 
        {{ variant.compare_at_price | money}}
      {% else %}
        
      {% endif %}
    </div>
      </div>
    </div>
    </gp-text>
    
      </gp-product-price>
      <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-product-price.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:center"
      class="gZFkxLrosn gp-relative gp-flex gp-flex-col"
    >
      <div gp-el-wrapper style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small)" class="g2DETQXWrx ">
      
  {% liquid
    assign price = variant.price | times: 1.0
    assign salePrice = variant.compare_at_price | times: 1.0
    assign priceSave = salePrice | minus: price
    assign prefixVal = section.settings.gg2DETQXWrx_customContent_prefix
    assign suffixVal = section.settings.gg2DETQXWrx_customContent_suffix
    if salePrice == null or price == null
      assign pricePercentage = prefixVal | append: ' 0% ' | append: suffixVal
    else
         assign salePercent = priceSave | divided_by: salePrice | times: 100  | round
                assign pricePercentage = prefixVal | append: ' ' | append: salePercent | append: '% ' | append: suffixVal
              
    endif
  %}

  <gp-product-tag
  data-id="g2DETQXWrx"
    data-disabled="{%- if priceSave > 0 -%} false {%- else -%} true {%- endif -%}"
    gp-data='{"setting":{"customContent":{"prefix":"-","suffix":"off","unit":"percentage"},"translate":"customContent"}, "id": "g2DETQXWrx", "locale": "{{shop.locale}}", "currency": "{{shop.currency}}", "moneyFormat": "{{ shop.money_format | replace: '"', '\"' | escape }}"}'
    class="gp-block data-[disabled=true]:gp-hidden "
    style="--ta:left"
    price-save="{{priceSave}}"
    data-prefix="{{section.settings.gg2DETQXWrx_customContent_prefix}}"
    data-suffix="{{section.settings.gg2DETQXWrx_customContent_suffix}}"
  >
     <div class="gp-inline-flex gp-flex-wrap gp-items-end gp-gap-3" >
       <div
         class="gp-flex gp-items-center gp-w-full gp-h-full gp-bg-g-bg-2"
         style="--pl:16px;--pr:16px;--pt:16px;--pb:16px;--pl-tablet:16px;--pr-tablet:16px;--pt-tablet:16px;--pb-tablet:16px;--pl-mobile:16px;--pr-mobile:16px;--pt-mobile:16px;--pb-mobile:16px;--bs:none;--bw:0px;--bc:transparent;--c:#EA3335;--radius:var(--g-radius-small)"
       >
         
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="">
    <div
      id="p-tag-g2DETQXWrx"
        class=" "
        
      >
      <div  >
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A']"
          style="--w:100%;--ta:left;--c:#EA3335;--fs:normal;--ff:var(--g-font-heading, heading);--weight:700;--ls:normal;--size:13px;--size-tablet:13px;--size-mobile:12px;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;word-break:break-word;overflow:hidden"
        >{{pricePercentage}}</div>
      </div>
    </div>
    </gp-text>
    
       </div>
     </div>

 </gp-product-tag>
 <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-product-tag.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
   
      </div>
    </div>
    </div>
   
    
    </div>
    </div>
   
    
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:center"
      class="g7THFwMIAQ gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      parentTag="Col" id="gkXSf-tKfe" data-id="gkXSf-tKfe"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--radius:var(--g-radius-small);--mt:0px;--cg:16px;--pc:end;--pc-mobile:center;--gtc:minmax(0, auto) minmax(0, auto);--gtc-mobile:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gkXSf-tKfe gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--ai:normal;--jc:center;--jc-mobile:center;--o:0"
      class="gzv0Y4fSxg gp-relative gp-flex gp-flex-col"
    >
      <div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb-mobile:var(--g-s-l)" class="gcl-H0nAyu ">
      
  {%- assign total_combinations = 1 -%}
  {%- for option in product.options_with_values -%}
    {%- assign total_combinations = total_combinations | times: option.values.size -%}
  {%- endfor -%}
  <gp-product-variants
    data-id="gcl-H0nAyu"
    
    has-pre-selected="true"
    gp-data='{
      "setting":{"blankText":"Please select an option","column":{"desktop":1},"combineFullWidth":{"desktop":true},"combineHeight":"50px","hasPreSelected":true,"label":true,"layout":{"desktop":"vertical"},"optionAlign":{"desktop":"left"},"optionType":"groupOption","price":false,"showAsSwatches":true,"soldOutMark":true,"soldOutStyle":"line","variantPresets":[{"hide":false,"optionName":"base","optionType":"dropdown","presets":{"color":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"45px","tablet":"45px"},"optionBgColor":{"normal":"bg-3","hover":"bg-3","active":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"hover":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"active":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"dropdown":{"fullWidth":{"desktop":true,"mobile":true,"tablet":true},"height":"50px","width":{"desktop":"100%","mobile":"100%","tablet":"100%"},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"#242424","hover":"#242424","normal":"#242424"}},"image":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"image_shopify":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"rectangle_list":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":""},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"#242424","hover":"#242424","normal":"#242424"}}}},{"hide":false,"optionName":"SELECT YOUR COLOR","optionType":"color","presets":{"color":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"45px","tablet":"45px"},"optionBgColor":{"normal":"bg-3","hover":"bg-3","active":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"hover":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"active":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"dropdown":{"fullWidth":{"desktop":true,"mobile":true,"tablet":true},"height":"50px","width":{"desktop":"100%","mobile":"100%","tablet":"100%"},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"#242424","hover":"#242424","normal":"#242424"}},"image":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"image_shopify":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"rectangle_list":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":""},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"#242424","hover":"#242424","normal":"#242424"}}}},{"hide":false,"optionName":"Size","optionType":"dropdown","presets":{"color":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"45px","tablet":"45px"},"optionBgColor":{"normal":"bg-3","hover":"bg-3","active":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"hover":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"active":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"dropdown":{"fullWidth":{"desktop":true,"mobile":true,"tablet":true},"height":"50px","width":{"desktop":"100%","mobile":"100%","tablet":"100%"},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"#242424","hover":"#242424","normal":"#242424"}},"image":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"image_shopify":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"rectangle_list":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":""},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"#242424","hover":"#242424","normal":"#242424"}}}},{"hide":false,"optionName":"Color","optionType":"color","presets":{"color":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"45px","tablet":"45px"},"optionBgColor":{"normal":"bg-3","hover":"bg-3","active":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"hover":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"active":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"dropdown":{"fullWidth":{"desktop":true,"mobile":true,"tablet":true},"height":"50px","width":{"desktop":"100%","mobile":"100%","tablet":"100%"},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"#242424","hover":"#242424","normal":"#242424"}},"image":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"image_shopify":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"rectangle_list":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":""},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"#242424","hover":"#242424","normal":"#242424"}}}},{"hide":false,"optionName":"Size 2","optionType":"rectangle_list","presets":{"color":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"45px","tablet":"45px"},"optionBgColor":{"normal":"bg-3","hover":"bg-3","active":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"hover":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"active":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"dropdown":{"fullWidth":{"desktop":true,"mobile":true,"tablet":true},"height":"50px","width":{"desktop":"100%","mobile":"100%","tablet":"100%"},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"#242424","hover":"#242424","normal":"#242424"}},"image":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"image_shopify":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"rectangle_list":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":""},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"#242424","hover":"#242424","normal":"#242424"}}}},{"hide":false,"optionName":"Select Color","optionType":"image_shopify","presets":{"color":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"45px","tablet":"45px"},"optionBgColor":{"normal":"bg-3","hover":"bg-3","active":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"hover":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"active":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"dropdown":{"fullWidth":{"desktop":true,"mobile":true,"tablet":true},"height":"50px","width":{"desktop":"100%","mobile":"100%","tablet":"100%"},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"#242424","hover":"#242424","normal":"#242424"}},"image":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"image_shopify":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"rectangle_list":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":""},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"#242424","hover":"#242424","normal":"#242424"}}}},{"hide":false,"optionName":"SELECT YOUR SIZE","optionType":"dropdown","presets":{"color":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"45px","tablet":"45px"},"optionBgColor":{"normal":"bg-3","hover":"bg-3","active":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"hover":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"active":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"dropdown":{"fullWidth":{"desktop":true,"mobile":true,"tablet":true},"height":"50px","width":{"desktop":"100%","mobile":"100%","tablet":"100%"},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"#242424","hover":"#242424","normal":"#242424"}},"image":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"image_shopify":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"rectangle_list":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":""},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"#242424","hover":"#242424","normal":"#242424"}}}},{"hide":false,"optionName":"SELECT COLOR","optionType":"color","presets":{"color":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"45px","tablet":"45px"},"optionBgColor":{"normal":"bg-3","hover":"bg-3","active":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"hover":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"active":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"dropdown":{"fullWidth":{"desktop":true,"mobile":true,"tablet":true},"height":"50px","width":{"desktop":"100%","mobile":"100%","tablet":"100%"},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"#242424","hover":"#242424","normal":"#242424"}},"image":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"image_shopify":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"rectangle_list":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":""},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"#242424","hover":"#242424","normal":"#242424"}}}},{"hide":false,"optionName":"SELECT SIZE","optionType":"rectangle_list","presets":{"color":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"45px","tablet":"45px"},"optionBgColor":{"normal":"bg-3","hover":"bg-3","active":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"hover":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"active":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"dropdown":{"fullWidth":{"desktop":true,"mobile":true,"tablet":true},"height":"50px","width":{"desktop":"100%","mobile":"100%","tablet":"100%"},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"#242424","hover":"#242424","normal":"#242424"}},"image":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"image_shopify":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"rectangle_list":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":""},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"#242424","hover":"#242424","normal":"#242424"}}}},{"hide":false,"optionName":"Select Size","optionType":"rectangle_list","presets":{"color":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"45px","tablet":"45px"},"optionBgColor":{"normal":"bg-3","hover":"bg-3","active":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"hover":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"active":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"dropdown":{"fullWidth":{"desktop":true,"mobile":true,"tablet":true},"height":"50px","width":{"desktop":"100%","mobile":"100%","tablet":"100%"},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"#242424","hover":"#242424","normal":"#242424"}},"image":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"image_shopify":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"rectangle_list":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":""},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"#242424","hover":"#242424","normal":"#242424"}}}},{"hide":false,"optionName":"GemSleep Pack","optionType":"rectangle_list","presets":{"color":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"45px","tablet":"45px"},"optionBgColor":{"normal":"bg-3","hover":"bg-3","active":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"hover":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"active":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"dropdown":{"fullWidth":{"desktop":true,"mobile":true,"tablet":true},"height":"50px","width":{"desktop":"100%","mobile":"100%","tablet":"100%"},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"#242424","hover":"#242424","normal":"#242424"}},"image":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"image_shopify":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"rectangle_list":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":""},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"#242424","hover":"#242424","normal":"#242424"}}}},{"hide":false,"optionName":"Option","optionType":"rectangle_list","presets":{"color":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"45px","tablet":"45px"},"optionBgColor":{"normal":"bg-3","hover":"bg-3","active":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"hover":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"active":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"dropdown":{"fullWidth":{"desktop":true,"mobile":true,"tablet":true},"height":"50px","width":{"desktop":"100%","mobile":"100%","tablet":"100%"},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"#242424","hover":"#242424","normal":"#242424"}},"image":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"image_shopify":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"rectangle_list":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":""},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"#242424","hover":"#242424","normal":"#242424"}}}},{"hide":false,"optionName":"Camera Style","optionType":"rectangle_list","presets":{"color":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"45px","tablet":"45px"},"optionBgColor":{"normal":"bg-3","hover":"bg-3","active":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"hover":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"active":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"dropdown":{"fullWidth":{"desktop":true,"mobile":true,"tablet":true},"height":"50px","width":{"desktop":"100%","mobile":"100%","tablet":"100%"},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"#242424","hover":"#242424","normal":"#242424"}},"image":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"image_shopify":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"rectangle_list":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":""},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"#242424","hover":"#242424","normal":"#242424"}}}},{"hide":false,"optionName":"Style","optionType":"dropdown","presets":{"color":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"45px","tablet":"45px"},"optionBgColor":{"normal":"bg-3","hover":"bg-3","active":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"hover":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"active":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"dropdown":{"fullWidth":{"desktop":true,"mobile":true,"tablet":true},"height":"50px","width":{"desktop":"100%","mobile":"100%","tablet":"100%"},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"#242424","hover":"#242424","normal":"#242424"}},"image":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"image_shopify":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"rectangle_list":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":""},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"#242424","hover":"#242424","normal":"#242424"}}}},{"hide":false,"optionName":"Serving Size","optionType":"rectangle_list","presets":{"color":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"45px","tablet":"45px"},"optionBgColor":{"normal":"bg-3","hover":"bg-3","active":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"hover":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"active":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"dropdown":{"fullWidth":{"desktop":true,"mobile":true,"tablet":true},"height":"50px","width":{"desktop":"100%","mobile":"100%","tablet":"100%"},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"#242424","hover":"#242424","normal":"#242424"}},"image":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"image_shopify":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"rectangle_list":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":""},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"#242424","hover":"#242424","normal":"#242424"}}}},{"hide":false,"optionName":"Color Option","optionType":"dropdown","presets":{"color":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"45px","tablet":"45px"},"optionBgColor":{"normal":"bg-3","hover":"bg-3","active":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"hover":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"active":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"dropdown":{"fullWidth":{"desktop":true,"mobile":true,"tablet":true},"height":"50px","width":{"desktop":"100%","mobile":"100%","tablet":"100%"},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"#242424","hover":"#242424","normal":"#242424"}},"image":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"image_shopify":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"rectangle_list":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":""},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"#242424","hover":"#242424","normal":"#242424"}}}},{"hide":false,"optionName":"Colors","optionType":"color","presets":{"color":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"45px","tablet":"45px"},"optionBgColor":{"normal":"bg-3","hover":"bg-3","active":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"hover":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"active":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"dropdown":{"fullWidth":{"desktop":true,"mobile":true,"tablet":true},"height":"50px","width":{"desktop":"100%","mobile":"100%","tablet":"100%"},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"#242424","hover":"#242424","normal":"#242424"}},"image":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"image_shopify":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"rectangle_list":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":""},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"#242424","hover":"#242424","normal":"#242424"}}}},{"hide":false,"optionName":"COLOR","optionType":"rectangle_list","presets":{"color":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"45px","tablet":"45px"},"optionBgColor":{"normal":"bg-3","hover":"bg-3","active":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"hover":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"active":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"dropdown":{"fullWidth":{"desktop":true,"mobile":true,"tablet":true},"height":"50px","width":{"desktop":"100%","mobile":"100%","tablet":"100%"},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"#242424","hover":"#242424","normal":"#242424"}},"image":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"image_shopify":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"rectangle_list":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":""},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"#242424","hover":"#242424","normal":"#242424"}}}},{"hide":false,"optionName":"Size 3","optionType":"dropdown","presets":{"color":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"45px","tablet":"45px"},"optionBgColor":{"normal":"bg-3","hover":"bg-3","active":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"hover":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"active":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"dropdown":{"fullWidth":{"desktop":true,"mobile":true,"tablet":true},"height":"50px","width":{"desktop":"100%","mobile":"100%","tablet":"100%"},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"#242424","hover":"#242424","normal":"#242424"}},"image":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"image_shopify":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"rectangle_list":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":""},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"#242424","hover":"#242424","normal":"#242424"}}}},{"hide":false,"optionName":"Storage","optionType":"rectangle_list","presets":{"color":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"45px","tablet":"45px"},"optionBgColor":{"normal":"bg-3","hover":"bg-3","active":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"hover":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"active":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"dropdown":{"fullWidth":{"desktop":true,"mobile":true,"tablet":true},"height":"50px","width":{"desktop":"100%","mobile":"100%","tablet":"100%"},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"#242424","hover":"#242424","normal":"#242424"}},"image":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"image_shopify":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"rectangle_list":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":""},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"#242424","hover":"#242424","normal":"#242424"}}}}]},
      "styles":{"align":{"desktop":"left"},"dropdownItemWidth":{"desktop":"fill","mobile":"fill","tablet":"fill"},"fixedDropdownWidth":{"desktop":"240px"},"fullWidth":{"desktop":true},"labelColor":"text-2","labelSpacing":"8px","labelTypo":{"attrs":{"color":"text-1"},"type":"paragraph-2"},"marginBottom":{"desktop":"0px","mobile":"0px"},"optionBgColor":{"active":"#FFFFFF","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1","color":"#E0E0E0","isCustom":true,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false},"optionRounded":{"active":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"type":"shadow-1"}},"optionSpacing":"30px","optionTextColor":{"active":"#242424","hover":"#242424","normal":"#242424"},"optionTypo":{"custom":{"fontSize":{"desktop":"16px","mobile":"14px","tablet":"16px"},"fontStyle":"normal","fontWeight":"400","letterSpacing":"normal","lineHeight":{"desktop":"150%","mobile":"150%","tablet":"150%"}},"type":"paragraph-2"},"swatchAutoWidth":{"desktop":true},"swatchHeight":{"desktop":"50px","tablet":"50px"},"swatchItemWidth":{"desktop":"auto"},"swatchSpacing":"var(--g-s-m)","swatchWidth":{"desktop":"80px"},"width":{"desktop":"400px"}},
      "variants":{{product.variants | json | escape}},
      "optionsWithValues": {{product.options_with_values | json | escape}},
      "variantSelected": {{ variant | json | escape }},
      "variantInventoryQuantity": {{product.variants | map: 'inventory_quantity' | json | escape}},
      "variantInventoryPolicy": {{product.variants | map: 'inventory_policy' | json | escape}},
      "moneyFormat": {{shop.money_format | json | escape}},
      "productId": {{product.id | json | escape}},
      "productUrl": {{product.url | json | escape}},
      "productHandle": {{product.handle | json | escape}},
      "displayState": {"desktop":true,"mobile":true,"tablet":true},
      "totalVariantCombinations": {{total_combinations}},
      "firstAvailableVariant": {{product.selected_or_first_available_variant | json | escape}}
    }
  '>
    {%- assign options = product.options_with_values -%}
    {%- assign variants = product.variants -%}
    {%- if options.size == 0 or options.size == 1 and variants.size == 1 and variants[0].title == 'Default Title' and variants[0].option1 == 'Default Title' -%}
      <div></div>
    {% else %}
      <div
      class="gp-grid !gp-ml-0"
      style="--gtc:repeat(1, minmax(0, 1fr));--ta:left;--w:100%;--w-tablet:100%;--w-mobile:100%;--rg:0px;--rg-mobile:0px"
    >
      
    <div
        className="gp-flex gp-justify-start"
            >
            
    <select
    aria-label={{option.name | escape}}
    autocomplete="off"
    id="p-variant-group-dropdown"
    name="{%- if option -%}{{option.name | escape}}{% else %}Select Option{%- endif -%}"
    option-data="{{option.name}}"
    option-type="{{optionType}}"
    class="gp-truncate gp-bg-auto gp-pl-4 gp-pr-6 gp-outline-none dropdown-option-item hover:gp-bg-g-bg-3 gp-bg-g-bg-3 gp-outline-none gp-shadow-none"
    style="--bg:var(--g-c-bg-3, bg-3);--bs:solid;--bw:1px 1px 1px 1px;--bc:#E0E0E0;--h:50px;--fs:normal;--ff:var(--g-font-body, body);--weight:400;--ls:normal;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;--hvr-c:#242424;--c:#242424;--hvr-bg:var(--g-c-bg-3, bg-3);--w:auto;--w-tablet:auto;--w-mobile:auto;--hvr-bs:solid;--hvr-bw:1px 1px 1px 1px;--hvr-bc:#E0E0E0;--bblr:6px;--bbrr:6px;--btlr:6px;--btrr:6px;--hvr-bblr:6px;--hvr-bbrr:6px;--hvr-btlr:6px;--hvr-btrr:6px;appearance:none;background-image:url(https://cdn.shopify.com/s/files/1/1827/4239/t/1/assets/ico-select.svg?v=155563818344741998551488860031);background-repeat:no-repeat;background-position:right 16px center"
  >
  
  {%- for variantItem in variants -%}
    {%- if variantItem.id == variant.id -%}
              <option origin-price="{{variantItem.price}}" selected value="{{variantItem.id}}" key="{{variantItem.id}}">
              {{variantItem.title}} 
              </option>
            {% else %}
                <option origin-price="{{variantItem.price}}" value="{{variantItem.id}}" key="{{variantItem.id}}">
                {{variantItem.title}} 
                </option>
            {%- endif -%}
     
    {%- endfor -%}
  
  </select>
    
            </div>
    
    </div>
    {%- endif -%}

  </gp-product-variants>
  <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-product-variant-v3.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
   
      </div>
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:center;--jc-mobile:center"
      class="gb_EIXFnI7 gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      parentTag="Col" id="gl8TUXJblG" data-id="gl8TUXJblG"
        style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--cg:16px;--pc:end;--pc-mobile:center;--gtc:minmax(0, auto) minmax(0, auto);--gtc-mobile:minmax(0, 6fr) minmax(0, 6fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gl8TUXJblG gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start;--jc-mobile:center"
      class="g2jnWzGeKZ gp-relative gp-flex gp-flex-col"
    >
      <div gp-el-wrapper style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small)" class="gNaXW-H4tG ">
      
    {%- liquid
      assign current_variant = product.selected_or_first_available_variant
      assign available = current_variant.available | default: false
    -%}
      <gp-product-quantity
        data-id="gNaXW-H4tG"
        data-disabled="{%- if available -%} false {%- else -%} true {%- endif -%}"
        data-price=""
        class="quantityClass gp-relative gp-inline-flex gp-w-full gp-bg-transparent gp-transition-all gp-duration-150 data-[disabled=true]:gp-opacity-60 "
        style="--h:50px;--jc:center"
      >

      
          <button
            title="Decrement"
            aria-label="decrement"
            {% if available == false %} disabled {% endif %}
            class="gp-border-g-line-1 gp-bg-g-bg-3 gp-minus gp-flex gp-aspect-square gp-h-full gp-cursor-pointer gp-items-center gp-justify-center gp-outline-none disabled:gp-cursor-not-allowed"
            style="--w:50px;--bg:var(--g-c-bg-3, bg-3);--c:#242424;--bs:solid;--bw:1px 1px 1px 1px;--bc:var(--g-c-line-1, line-1);--bblr:6px;--bbrr:auto;--btlr:6px;--btrr:auto;--hvr-bblr:6px;--hvr-btlr:6px"
          >
            <span class="gp-inline-flex gp-items-center gp-justify-center" style="--w:16px;--w-tablet:16px;--w-mobile:16px">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="gp-h-full gp-w-full"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 12H6" />
              </svg>
            </span>
          </button>
      
        <input
          type="text"
          name="product-quantity"
          {% if available == false %} disabled {% endif %}
          class="gp-border-g-line-1 gp-bg-g-bg-3 !gp-border-x-0 gp-px-4 gp-flex gp-shadow-none gp-appearance-none gp-items-center gp-border-y gp-text-center gp-outline-none gp-transition-all gp-duration-150 hover:gp-text-black disabled:gp-pointer-events-none gp-h-auto gp-rounded-none gp-shrink-[99999] gp-w-full gp-min-w-[45px]"
          style="--maxw:90px;--maxw-tablet:50px;--maxw-mobile:100%;--fs:normal;--ff:var(--g-font-body, body);--weight:400;--ls:normal;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;--c:#242424;--bg:var(--g-c-bg-3, bg-3);--bs:solid;--bw:1px 1px 1px 1px;--bc:var(--g-c-line-1, line-1)"
          aria-label="Quantity"
          inputmode="numeric"
          min="{{ current_variant.quantity_rule.min }}"
          step="{{ current_variant.quantity_rule.increment }}"
          value="1"
          autocomplete='off'
        />

        
          <button
            {% if available == false %} disabled {% endif %}
            aria-label="increment"
            title="Increment"
            class="gp-border-g-line-1 gp-bg-g-bg-3 gp-plus gp-flex gp-aspect-square gp-h-full gp-cursor-pointer gp-items-center gp-justify-center gp-outline-none gp-transition-all gp-duration-150 disabled:gp-pointer-events-none"
            style="--w:50px;--bg:var(--g-c-bg-3, bg-3);--c:#242424;--bs:solid;--bw:1px 1px 1px 1px;--bc:var(--g-c-line-1, line-1);--bblr:auto;--bbrr:6px;--btlr:auto;--btrr:6px;--hvr-bbrr:6px;--hvr-btrr:6px"
          >
            <span class="gp-inline-flex gp-items-center gp-justify-center" style="--w:16px;--w-tablet:16px;--w-mobile:16px">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="gp-h-full gp-w-full"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                />
              </svg>
            </span>
          </button>
        
      </gp-product-quantity>
      <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-product-quantity.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
      </div>
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start;--jc-mobile:center"
      class="gl5iqyTTWz gp-relative gp-flex gp-flex-col"
    >
      
   {%- liquid
      assign inventory_quantity = variant.inventory_quantity | default: 0
    -%}
    <gp-product-button
      class="gp-product-button"
      gp-data-wrapper="true"
      gp-label-out-of-stock="{{section.settings.ggnxKlOymX3_outOfStockLabel}}"
      gp-label-unavailable="{{section.settings.ggnxKlOymX3_unavailableLabel}}"
      gp-data='{"setting":{"actionEffect":"open-cart-drawer","errorMessage":"Cannot add product to cart","successMessage":"Add product to cart successfully","enableSuccessMessage":true,"enableErrorMessage":true,"label":"Add to Cart","outOfStockLabel":"Out of stock","customURL":{}},"styles":{"errorTypo":{"attrs":{"color":"error"},"type":"paragraph-1"},"successTypo":{"attrs":{"color":"success"},"type":"paragraph-1"}},"disabled":"{{variant.available}}","variantID":"{{variant.id}}","totalVariant":"{{product.variants.size}}"}' 
       gp-href="{{ request.origin }}{{ routes.root_url | split: '/' | join: '/' }}/cart"
       data-variant-selection-required-message="{{}}"
    >
        
  <gp-button >
  <div
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--bblr:5px;--bbrr:5px;--btlr:5px;--btrr:5px;--ta:left"
    class="{% unless variant.available %} !gp-hidden {% endunless %}"
  >
    <style>
    .gnxKlOymX3.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 0px;
  border-color: transparent;
  
      border-radius: var(--g-radius-medium);
    }

    .gnxKlOymX3:hover::before {
      
      
      border-bottom-left-radius: 6px;
      border-bottom-right-radius: 6px;
      border-top-left-radius: 6px;
      border-top-right-radius: 6px;
      
    }

    .gnxKlOymX3:hover .gp-button-icon {
      color: undefined;
    }

     .gnxKlOymX3 .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .gnxKlOymX3:hover .gp-button-price {
      color: undefined;
    }

    .gnxKlOymX3 .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .gnxKlOymX3 .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .gnxKlOymX3:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <button
      type="submit" data-id="gnxKlOymX3" aria-label="Add to Cart"
      name="add" gp-data-hidden="{% unless variant.available %}true{% endunless %}"
      data-state="idle"
      class="gnxKlOymX3 gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-button-atc tcustomizer-submit-button"
      style="--hvr-bg:#424242;--bg:#242424;--radius:var(--g-radius-medium);--hvr-bblr:6px;--hvr-bbrr:6px;--hvr-btlr:6px;--hvr-btrr:6px;--pl:24px;--pr:24px;--pt:12px;--pb:12px;--pl-tablet:24px;--pr-tablet:24px;--pt-tablet:12px;--pb-tablet:12px;--pl-mobile:24px;--pr-mobile:24px;--pt-mobile:12px;--pb-mobile:12px;--w:Auto;--w-tablet:Auto;--w-mobile:100%;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--c:var(--g-c-text-3, text-3);--size:16px;--size-tablet:16px;--size-mobile:14px;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;--tt:capitalize"
    >
        <svg
          class="gp-invisible gp-absolute gp-h-5 gp-w-5 group-data-[state=loading]/button:gp-animate-spin group-data-[state=loading]/button:gp-visible"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            class="gp-opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            stroke-width="4"
          ></circle>
          <path
            class="gp-opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          ></path>
        </svg>
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--size:16px;--size-tablet:16px;--size-mobile:14px;--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.ggnxKlOymX3_label }}
      </span>
      </div>
    
    </button>
  </div>
  </gp-button>

        
  <gp-button >
  <div
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--bblr:5px;--bbrr:5px;--btlr:5px;--btrr:5px;--ta:left"
    class="{% if variant.available %} !gp-hidden {% endif %}"
  >
    <style>
    .gnxKlOymX3-sold-out.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 0px;
  border-color: transparent;
  
      border-radius: var(--g-radius-medium);
    }

    .gnxKlOymX3-sold-out:hover::before {
      
      
      border-bottom-left-radius: 6px;
      border-bottom-right-radius: 6px;
      border-top-left-radius: 6px;
      border-top-right-radius: 6px;
      
    }

    .gnxKlOymX3-sold-out:hover .gp-button-icon {
      color: undefined;
    }

     .gnxKlOymX3-sold-out .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .gnxKlOymX3-sold-out:hover .gp-button-price {
      color: undefined;
    }

    .gnxKlOymX3-sold-out .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .gnxKlOymX3-sold-out .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .gnxKlOymX3-sold-out:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <button
      type="button" data-id="gnxKlOymX3" aria-label="{{section.settings.ggnxKlOymX3_outOfStockLabel}}"
      gp-data-hidden="{% if variant.available %}true{% endif %}"
      data-state="idle"
      class="gnxKlOymX3-sold-out gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-button-sold-out btn-disabled gp-opacity-30 gp-cursor-default"
      style="--hvr-bg:#424242;--bg:#242424;--radius:var(--g-radius-medium);--hvr-bblr:6px;--hvr-bbrr:6px;--hvr-btlr:6px;--hvr-btrr:6px;--pl:24px;--pr:24px;--pt:12px;--pb:12px;--pl-tablet:24px;--pr-tablet:24px;--pt-tablet:12px;--pb-tablet:12px;--pl-mobile:24px;--pr-mobile:24px;--pt-mobile:12px;--pb-mobile:12px;--w:Auto;--w-tablet:Auto;--w-mobile:100%;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--c:var(--g-c-text-3, text-3);--size:16px;--size-tablet:16px;--size-mobile:14px;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;--tt:capitalize"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--size:16px;--size-tablet:16px;--size-mobile:14px;--c:var(--g-c-text-3, text-3)"
      >
        {{section.settings.ggnxKlOymX3_outOfStockLabel}}
      </span>
      </div>
    
    </button>
  </div>
  </gp-button>

    </gp-product-button>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-product-button.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
    </div>
    </div>
   
    
    </div>
    </div>
   
    
    </div>
    </div>
   
    
          {%- endform -%}
        </product-form>
      </gp-product>
      {%- assign product = gpBkProduct -%}
    {% else %}
      <div class="gp-text-center">{{ "gempages.Product.product_not_found" | t }}</div>
    {%- endif -%}
     <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-product.v2.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
    </div>
      </div>
    </gp-sticky>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-sticky.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  


{% schema %}
  {
    
    "name": "Section 1",
    "tag": "section",
    "class": "gps-572904145820844947 gps gpsil",
    "settings": [{"type":"paragraph","content":"Section design by GemPages. [Click here to start design](https://admin.shopify.com/store/i0pixe-10/apps/gempages-cro/app/shopify/edit?pageType=GP_PRODUCT&editorId=572751048691811510&sectionId=572904145820844947)"},{"type":"select","label":"Section Preload","id":"section_preload","options":[{"label":"On","value":"true"},{"label":"Off","value":"false"},{"label":"Off Lazy Script","value":"off_lazy_script"}],"default":"false"},{"type":"text","label":"Checksum","id":"checksum"},{"type":"html","id":"gg2DETQXWrx_customContent_prefix","label":"gg2DETQXWrx_customContent_prefix","default":"-"},{"type":"html","id":"gg2DETQXWrx_customContent_suffix","label":"gg2DETQXWrx_customContent_suffix","default":"off"},{"type":"html","id":"ggnxKlOymX3_label","label":"ggnxKlOymX3_label","default":"Add to Cart"},{"type":"html","id":"ggnxKlOymX3_outOfStockLabel","label":"ggnxKlOymX3_outOfStockLabel","default":"Out of stock"},{"type":"html","id":"ggnxKlOymX3_unavailableLabel","label":"ggnxKlOymX3_unavailableLabel","default":"Unavailable"}],
    "blocks": [{"type": "@app"}],
    "locales": {}
  }
{% endschema %}
