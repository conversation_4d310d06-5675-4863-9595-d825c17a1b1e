

<style {% if section.settings.section_preload == "false" %} class="gps-link gps-style" type="text/disabled-css" {% endif %}>.gps-572907520155190022.gps.gpsil [style*="--aspect:"]{aspect-ratio:var(--aspect)}.gps-572907520155190022.gps.gpsil [style*="--bg:"]{background:var(--bg)}.gps-572907520155190022.gps.gpsil [style*="--bga:"]{background-attachment:var(--bga)}.gps-572907520155190022.gps.gpsil [style*="--bgc:"]{background-color:var(--bgc)}.gps-572907520155190022.gps.gpsil [style*="--bgi:"]{background-image:var(--bgi)}.gps-572907520155190022.gps.gpsil [style*="--bgp:"]{background-position:var(--bgp)}.gps-572907520155190022.gps.gpsil [style*="--bgr:"]{background-repeat:var(--bgr)}.gps-572907520155190022.gps.gpsil [style*="--bgs:"]{background-size:var(--bgs)}.gps-572907520155190022.gps.gpsil [style*="--b:"]{border:var(--b)}.gps-572907520155190022.gps.gpsil [style*="--bc:"]{border-color:var(--bc)}.gps-572907520155190022.gps.gpsil [style*="--bs:"]{border-style:var(--bs)}.gps-572907520155190022.gps.gpsil [style*="--bw:"]{border-width:var(--bw)}.gps-572907520155190022.gps.gpsil [style*="--shadow:"]{box-shadow:var(--shadow)}.gps-572907520155190022.gps.gpsil [style*="--c:"]{color:var(--c)}.gps-572907520155190022.gps.gpsil [style*="--cg:"]{-moz-column-gap:var(--cg);column-gap:var(--cg)}.gps-572907520155190022.gps.gpsil [style*="--ff:"]{font-family:var(--ff)}.gps-572907520155190022.gps.gpsil [style*="--size:"]{font-size:var(--size)}.gps-572907520155190022.gps.gpsil [style*="--weight:"]{font-weight:var(--weight)}.gps-572907520155190022.gps.gpsil [style*="--fs:"]{font-style:var(--fs)}.gps-572907520155190022.gps.gpsil [style*="--gtc:"]{grid-template-columns:var(--gtc)}.gps-572907520155190022.gps.gpsil [style*="--h:"]{height:var(--h)}.gps-572907520155190022.gps.gpsil [style*="--jc:"]{justify-content:var(--jc)}.gps-572907520155190022.gps.gpsil [style*="--ls:"]{letter-spacing:var(--ls)}.gps-572907520155190022.gps.gpsil [style*="--lh:"]{line-height:var(--lh)}.gps-572907520155190022.gps.gpsil [style*="--m:"]{margin:var(--m)}.gps-572907520155190022.gps.gpsil [style*="--mb:"]{margin-bottom:var(--mb)}.gps-572907520155190022.gps.gpsil [style*="--objf:"]{-o-object-fit:var(--objf);object-fit:var(--objf)}.gps-572907520155190022.gps.gpsil [style*="--op:"]{opacity:var(--op)}.gps-572907520155190022.gps.gpsil [style*="--o:"]{order:var(--o)}.gps-572907520155190022.gps.gpsil [style*="--pc:"]{place-content:var(--pc)}.gps-572907520155190022.gps.gpsil [style*="--p:"]{padding:var(--p)}.gps-572907520155190022.gps.gpsil [style*="--pb:"]{padding-bottom:var(--pb)}.gps-572907520155190022.gps.gpsil [style*="--pl:"]{padding-left:var(--pl)}.gps-572907520155190022.gps.gpsil [style*="--pr:"]{padding-right:var(--pr)}.gps-572907520155190022.gps.gpsil [style*="--pt:"]{padding-top:var(--pt)}.gps-572907520155190022.gps.gpsil [style*="--ta:"]{text-align:var(--ta)}.gps-572907520155190022.gps.gpsil [style*="--ts:"]{text-shadow:var(--ts)}.gps-572907520155190022.gps.gpsil [style*="--tt:"]{text-transform:var(--tt)}.gps-572907520155190022.gps.gpsil [style*="--t:"]{transform:var(--t)}.gps-572907520155190022.gps.gpsil [style*="--w:"]{width:var(--w)}.gps-572907520155190022.gps.gpsil [style*="--line-clamp:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp);display:-webkit-box;overflow:hidden}@media only screen and (max-width:1024px){.gps-572907520155190022.gps.gpsil [style*="--size-tablet:"]{font-size:var(--size-tablet)}.gps-572907520155190022.gps.gpsil [style*="--h-tablet:"]{height:var(--h-tablet)}.gps-572907520155190022.gps.gpsil [style*="--lh-tablet:"]{line-height:var(--lh-tablet)}.gps-572907520155190022.gps.gpsil [style*="--pb-tablet:"]{padding-bottom:var(--pb-tablet)}.gps-572907520155190022.gps.gpsil [style*="--pl-tablet:"]{padding-left:var(--pl-tablet)}.gps-572907520155190022.gps.gpsil [style*="--pr-tablet:"]{padding-right:var(--pr-tablet)}.gps-572907520155190022.gps.gpsil [style*="--pt-tablet:"]{padding-top:var(--pt-tablet)}.gps-572907520155190022.gps.gpsil [style*="--w-tablet:"]{width:var(--w-tablet)}.gps-572907520155190022.gps.gpsil [style*="--line-clamp-tablet:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-tablet);display:-webkit-box;overflow:hidden}}@media only screen and (max-width:767px){.gps-572907520155190022.gps.gpsil [style*="--cg-mobile:"]{-moz-column-gap:var(--cg-mobile);column-gap:var(--cg-mobile)}.gps-572907520155190022.gps.gpsil [style*="--size-mobile:"]{font-size:var(--size-mobile)}.gps-572907520155190022.gps.gpsil [style*="--gtc-mobile:"]{grid-template-columns:var(--gtc-mobile)}.gps-572907520155190022.gps.gpsil [style*="--h-mobile:"]{height:var(--h-mobile)}.gps-572907520155190022.gps.gpsil [style*="--lh-mobile:"]{line-height:var(--lh-mobile)}.gps-572907520155190022.gps.gpsil [style*="--mb-mobile:"]{margin-bottom:var(--mb-mobile)}.gps-572907520155190022.gps.gpsil [style*="--pl-mobile:"]{padding-left:var(--pl-mobile)}.gps-572907520155190022.gps.gpsil [style*="--pr-mobile:"]{padding-right:var(--pr-mobile)}.gps-572907520155190022.gps.gpsil [style*="--w-mobile:"]{width:var(--w-mobile)}.gps-572907520155190022.gps.gpsil [style*="--line-clamp-mobile:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-mobile);display:-webkit-box;overflow:hidden}}.gps-572907520155190022 .gp-g-paragraph-1{font-family:var(--g-p1-ff);font-size:var(--g-p1-size);font-style:var(--g-p1-fs);font-weight:var(--g-p1-weight);letter-spacing:var(--g-p1-ls);line-height:var(--g-p1-lh)}.gps-572907520155190022 .gp-relative{position:relative}.gps-572907520155190022 .gp-mx-auto{margin-left:auto;margin-right:auto}.gps-572907520155190022 .gp-mb-0{margin-bottom:0}.gps-572907520155190022 .gp-flex{display:flex}.gps-572907520155190022 .gp-grid{display:grid}.gps-572907520155190022 .gp-contents{display:contents}.gps-572907520155190022 .\!gp-hidden{display:none!important}.gps-572907520155190022 .gp-hidden{display:none}.gps-572907520155190022 .gp-h-auto{height:auto}.gps-572907520155190022 .gp-h-full{height:100%}.gps-572907520155190022 .gp-w-full{width:100%}.gps-572907520155190022 .gp-max-w-full{max-width:100%}.gps-572907520155190022 .gp-flex-none{flex:none}.gps-572907520155190022 .gp-grid-rows-\[1fr\]{grid-template-rows:1fr}.gps-572907520155190022 .gp-flex-col{flex-direction:column}.gps-572907520155190022 .gp-gap-y-0{row-gap:0}.gps-572907520155190022 .gp-text-g-text-1{color:var(--g-c-text-1)}.gps-572907520155190022 .gp-transition-colors{transition-duration:.15s;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-572907520155190022 .gp-duration-200{transition-duration:.2s}.gps-572907520155190022 .gp-ease-in-out{transition-timing-function:cubic-bezier(.4,0,.2,1)}@media (max-width:1024px){.gps-572907520155190022 .tablet\:\!gp-hidden{display:none!important}.gps-572907520155190022 .tablet\:gp-hidden{display:none}.gps-572907520155190022 .tablet\:gp-h-auto{height:auto}.gps-572907520155190022 .tablet\:gp-flex-none{flex:none}}@media (max-width:767px){.gps-572907520155190022 .mobile\:\!gp-hidden{display:none!important}.gps-572907520155190022 .mobile\:gp-hidden{display:none}.gps-572907520155190022 .mobile\:gp-h-auto{height:auto}.gps-572907520155190022 .mobile\:gp-flex-none{flex:none}}.gps-572907520155190022 .\[\&_\*\]\:gp-max-w-full *{max-width:100%}.gps-572907520155190022 .\[\&_p\]\:gp-inline p{display:inline}.gps-572907520155190022 .\[\&_p\]\:after\:gp-whitespace-pre-wrap p:after{content:var(--tw-content);white-space:pre-wrap}.gps-572907520155190022 .\[\&_p\]\:after\:gp-content-\[\'\\A\'\] p:after{--tw-content:"\A";content:var(--tw-content)}</style>

       
      
            <section
              class="gp-mx-auto gp-max-w-full {% if section.settings.section_preload == "false" %}gps-lazy {% endif %}"
              style="--w:100%;--w-tablet:100%;--w-mobile:100%;--pl:0px;--pl-tablet:0px;--pl-mobile:0px;--pr:0px;--pr-tablet:0px;--pr-mobile:0px"
            >
              
    <div
      id="gHQvxIPbiu" data-id="gHQvxIPbiu"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pt:0px;--pl:15px;--pb:var(--g-s-4xl);--pr:15px;--pt-tablet:var(--g-s-4xl);--pl-tablet:15px;--pb-tablet:var(--g-s-4xl);--pr-tablet:15px;--cg:var(--g-s-2xl);--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:#FFFFFF;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gHQvxIPbiu gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gHecTLKhXS gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      parentTag="Col" id="gc5PqVdGPe" data-id="gc5PqVdGPe"
        style="--bs:solid;--bw:1px 1px 1px 1px;--bc:#E0E0E0;--shadow:none;--d:grid;--d-mobile:none;--d-tablet:grid;--op:100%;--mb:var(--g-s-l);--cg:auto;--pc:start;--gtc:minmax(0, 4fr) minmax(0, 4fr) minmax(0, 4fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gc5PqVdGPe gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gH4ctKur-x gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      parentTag="Col" id="gn8wjY05W5" data-id="gn8wjY05W5"
        style="--bs:solid;--bw:0px 1px 0px 0px;--bc:#E0E0E0;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pt:var(--g-s-xl);--pl:var(--g-s-xl);--pb:var(--g-s-xl);--pr:var(--g-s-xl);--cg:var(--g-s-2xl);--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gn8wjY05W5 gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="glPX43Dfg1 gp-relative gp-flex gp-flex-col"
    >
      <div
    
     data-id="gAy2yqXnOG"
    role="presentation"
    class="gp-group/image gAy2yqXnOG gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-xl);--ta:center"
    >
      <div
        class="pointer-events-auto gp-h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="border-radius:inherit;--jc:center"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" data-srcSet="{{ "gempages_572751041980793671-ec9a4d02-eea0-4840-a5fc-085941ac2b12.png" | file_url }}" srcset="{{ "gempages_572751041980793671-ec9a4d02-eea0-4840-a5fc-085941ac2b12.png" | file_url }}" />
      <source media="(max-width: 1024px)" data-srcSet="{{ "gempages_572751041980793671-ec9a4d02-eea0-4840-a5fc-085941ac2b12.png" | file_url }}" srcset="{{ "gempages_572751041980793671-ec9a4d02-eea0-4840-a5fc-085941ac2b12.png" | file_url }}" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex"
        src="{{ "gempages_572751041980793671-ec9a4d02-eea0-4840-a5fc-085941ac2b12.png" | file_url }}"
        data-src="{{ "gempages_572751041980793671-ec9a4d02-eea0-4840-a5fc-085941ac2b12.png" | file_url }}"
        width="100%"
        alt="Alt Image"
        style="--aspect:auto;--objf:cover;--w:48px;--w-tablet:48px;--w-mobile:48px;--h:auto;--h-tablet:auto;--h-mobile:auto"
      />
    
  </picture>
      </div>
  </div>
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g41yR1whRA">
    <div
      parentTag="Col"
        class="g41yR1whRA "
        style="--ta:left;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-l)"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--size:19px;--size-tablet:19px;--size-mobile:14px;--lh:130%;--lh-tablet:130%;--lh-mobile:150%;--c:#242424;word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.gg41yR1whRA_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g_niYciEOZ">
    <div
      parentTag="Col"
        class="g_niYciEOZ "
        style="--tt:default;--ta:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%"
      >
      <div class="gp-flex" style="--jc:center">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text-g-text-1 gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:center;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#575757;word-break:break-word;overflow:hidden"
        >{{ section.settings.gg_niYciEOZ_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gSrpi9nfNc gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      parentTag="Col" id="gOQLq_gCoX" data-id="gOQLq_gCoX"
        style="--bs:solid;--bw:0px 0px 0px 0px;--bc:#E0E0E0;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pt:var(--g-s-xl);--pl:var(--g-s-xl);--pb:var(--g-s-xl);--pr:var(--g-s-xl);--cg:var(--g-s-2xl);--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gOQLq_gCoX gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="g7bywvBZcX gp-relative gp-flex gp-flex-col"
    >
      <div
    
     data-id="g0H83ia7cs"
    role="presentation"
    class="gp-group/image g0H83ia7cs gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-xl);--ta:center"
    >
      <div
        class="pointer-events-auto gp-h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="border-radius:inherit;--jc:center"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" data-srcSet="{{ "gempages_572751041980793671-a6fcc8d6-911a-485a-8413-10c1662fe101.png" | file_url }}" srcset="{{ "gempages_572751041980793671-a6fcc8d6-911a-485a-8413-10c1662fe101.png" | file_url }}" />
      <source media="(max-width: 1024px)" data-srcSet="{{ "gempages_572751041980793671-a6fcc8d6-911a-485a-8413-10c1662fe101.png" | file_url }}" srcset="{{ "gempages_572751041980793671-a6fcc8d6-911a-485a-8413-10c1662fe101.png" | file_url }}" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex"
        src="{{ "gempages_572751041980793671-a6fcc8d6-911a-485a-8413-10c1662fe101.png" | file_url }}"
        data-src="{{ "gempages_572751041980793671-a6fcc8d6-911a-485a-8413-10c1662fe101.png" | file_url }}"
        width="100%"
        alt="Alt Image"
        style="--aspect:auto;--objf:cover;--w:47px;--w-tablet:47px;--w-mobile:47px;--h:auto;--h-tablet:auto;--h-mobile:auto"
      />
    
  </picture>
      </div>
  </div>
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gjvzGTUoSi">
    <div
      parentTag="Col"
        class="gjvzGTUoSi "
        style="--ta:left;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-l)"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--size:19px;--size-tablet:19px;--size-mobile:14px;--lh:130%;--lh-tablet:130%;--lh-mobile:150%;--c:#242424;word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.ggjvzGTUoSi_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gnsymVKizF">
    <div
      parentTag="Col"
        class="gnsymVKizF "
        style="--tt:default;--ta:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%"
      >
      <div class="gp-flex" style="--jc:center">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text-g-text-1 gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:center;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#575757;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggnsymVKizF_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="g2FZJGiSis gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      parentTag="Col" id="gwWGpPhkAK" data-id="gwWGpPhkAK"
        style="--bs:solid;--bw:0px 0px 0px 1px;--bc:#E0E0E0;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pt:var(--g-s-xl);--pl:var(--g-s-xl);--pb:var(--g-s-xl);--pr:var(--g-s-xl);--cg:var(--g-s-2xl);--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gwWGpPhkAK gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="g-s-21Nau1 gp-relative gp-flex gp-flex-col"
    >
      <div
    
     data-id="g1LemNjHLb"
    role="presentation"
    class="gp-group/image g1LemNjHLb gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-xl);--ta:center"
    >
      <div
        class="pointer-events-auto gp-h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="border-radius:inherit;--jc:center"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" data-srcSet="{{ "gempages_572751041980793671-4b2e563d-838d-48c0-8167-41e95d32e5d6.png" | file_url }}" srcset="{{ "gempages_572751041980793671-4b2e563d-838d-48c0-8167-41e95d32e5d6.png" | file_url }}" />
      <source media="(max-width: 1024px)" data-srcSet="{{ "gempages_572751041980793671-4b2e563d-838d-48c0-8167-41e95d32e5d6.png" | file_url }}" srcset="{{ "gempages_572751041980793671-4b2e563d-838d-48c0-8167-41e95d32e5d6.png" | file_url }}" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex"
        src="{{ "gempages_572751041980793671-4b2e563d-838d-48c0-8167-41e95d32e5d6.png" | file_url }}"
        data-src="{{ "gempages_572751041980793671-4b2e563d-838d-48c0-8167-41e95d32e5d6.png" | file_url }}"
        width="100%"
        alt="Alt Image"
        style="--aspect:auto;--objf:cover;--w:48px;--w-tablet:48px;--w-mobile:48px;--h:auto;--h-tablet:auto;--h-mobile:auto"
      />
    
  </picture>
      </div>
  </div>
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gUm0of-XjA">
    <div
      parentTag="Col"
        class="gUm0of-XjA "
        style="--ta:left;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-l)"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--size:19px;--size-tablet:19px;--size-mobile:14px;--lh:130%;--lh-tablet:130%;--lh-mobile:150%;--c:#242424;word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.ggUm0of-XjA_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="giAn4-rHGl">
    <div
      parentTag="Col"
        class="giAn4-rHGl "
        style="--tt:default;--ta:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%"
      >
      <div class="gp-flex" style="--jc:center">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text-g-text-1 gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:center;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#575757;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggiAn4-rHGl_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
    </div>
    </div>
   
    
       
      
    <div
      parentTag="Col" id="g4RKpHGnXS" data-id="g4RKpHGnXS"
        style="--bs:solid;--bw:1px 1px 1px 1px;--bc:#E0E0E0;--shadow:none;--d:none;--d-mobile:grid;--d-tablet:none;--op:100%;--mb:var(--g-s-l);--cg:auto;--cg-mobile:0px;--pc:start;--gtc:minmax(0, 4fr) minmax(0, 4fr) minmax(0, 4fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="g4RKpHGnXS gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gmViFhVTTe gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      parentTag="Col" id="gpB3VOqw-V" data-id="gpB3VOqw-V"
        style="--bs:solid;--bw:0px 0px 1px 0px;--bc:#E0E0E0;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pt:var(--g-s-xl);--pl:var(--g-s-xl);--pb:var(--g-s-xl);--pr:var(--g-s-xl);--cg:var(--g-s-2xl);--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gpB3VOqw-V gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gh3RHFQ3DN gp-relative gp-flex gp-flex-col"
    >
      <div
    
     data-id="gHYs99wiv7"
    role="presentation"
    class="gp-group/image gHYs99wiv7 gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-xl);--ta:center"
    >
      <div
        class="pointer-events-auto gp-h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="border-radius:inherit;--jc:center"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" data-srcSet="{{ "gempages_572751041980793671-108707af-5f7f-4b96-91a0-075611f95d9b.png" | file_url }}" srcset="{{ "gempages_572751041980793671-108707af-5f7f-4b96-91a0-075611f95d9b.png" | file_url }}" />
      <source media="(max-width: 1024px)" data-srcSet="{{ "gempages_572751041980793671-108707af-5f7f-4b96-91a0-075611f95d9b.png" | file_url }}" srcset="{{ "gempages_572751041980793671-108707af-5f7f-4b96-91a0-075611f95d9b.png" | file_url }}" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex"
        src="{{ "gempages_572751041980793671-108707af-5f7f-4b96-91a0-075611f95d9b.png" | file_url }}"
        data-src="{{ "gempages_572751041980793671-108707af-5f7f-4b96-91a0-075611f95d9b.png" | file_url }}"
        width="100%"
        alt="Alt Image"
        style="--aspect:auto;--objf:cover;--w:48px;--w-tablet:48px;--w-mobile:48px;--h:auto;--h-tablet:auto;--h-mobile:auto"
      />
    
  </picture>
      </div>
  </div>
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gW-4ncp2IH">
    <div
      parentTag="Col"
        class="gW-4ncp2IH "
        style="--ta:left;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-xl);--mb-mobile:14px"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--size:19px;--size-tablet:19px;--size-mobile:17px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--c:#242424;word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.ggW-4ncp2IH_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gH3f9xIdXp">
    <div
      parentTag="Col"
        class="gH3f9xIdXp "
        style="--tt:default;--ta:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%"
      >
      <div class="gp-flex" style="--jc:center">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text-g-text-1 gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:center;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#575757;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggH3f9xIdXp_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gaKXFi1RbK gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      parentTag="Col" id="gd-BmpBYr9" data-id="gd-BmpBYr9"
        style="--bs:solid;--bw:0px 0px 0px 0px;--bc:#E0E0E0;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pt:var(--g-s-xl);--pl:var(--g-s-xl);--pb:var(--g-s-xl);--pr:var(--g-s-xl);--cg:var(--g-s-2xl);--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gd-BmpBYr9 gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gxjsrh8Cs2 gp-relative gp-flex gp-flex-col"
    >
      <div
    
     data-id="gBemItJuj4"
    role="presentation"
    class="gp-group/image gBemItJuj4 gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-xl);--ta:center"
    >
      <div
        class="pointer-events-auto gp-h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="border-radius:inherit;--jc:center"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" data-srcSet="{{ "gempages_572751041980793671-9efdb4ae-ae9c-4273-b60f-9e51f7f46673.png" | file_url }}" srcset="{{ "gempages_572751041980793671-9efdb4ae-ae9c-4273-b60f-9e51f7f46673.png" | file_url }}" />
      <source media="(max-width: 1024px)" data-srcSet="{{ "gempages_572751041980793671-9efdb4ae-ae9c-4273-b60f-9e51f7f46673.png" | file_url }}" srcset="{{ "gempages_572751041980793671-9efdb4ae-ae9c-4273-b60f-9e51f7f46673.png" | file_url }}" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex"
        src="{{ "gempages_572751041980793671-9efdb4ae-ae9c-4273-b60f-9e51f7f46673.png" | file_url }}"
        data-src="{{ "gempages_572751041980793671-9efdb4ae-ae9c-4273-b60f-9e51f7f46673.png" | file_url }}"
        width="100%"
        alt="Alt Image"
        style="--aspect:auto;--objf:cover;--w:47px;--w-tablet:47px;--w-mobile:47px;--h:auto;--h-tablet:auto;--h-mobile:auto"
      />
    
  </picture>
      </div>
  </div>
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="giJmyf9Gox">
    <div
      parentTag="Col"
        class="giJmyf9Gox "
        style="--ta:left;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-xl);--mb-mobile:14px"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--size:19px;--size-tablet:19px;--size-mobile:17px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--c:#242424;word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.ggiJmyf9Gox_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gUsZtoAH8c">
    <div
      parentTag="Col"
        class="gUsZtoAH8c "
        style="--tt:default;--ta:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%"
      >
      <div class="gp-flex" style="--jc:center">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text-g-text-1 gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:center;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#575757;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggUsZtoAH8c_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gw9OWHlHvd gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      parentTag="Col" id="gfs0ELeP4w" data-id="gfs0ELeP4w"
        style="--bs:solid;--bw:1px 0px 0px 0px;--bc:#E0E0E0;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pt:var(--g-s-xl);--pl:var(--g-s-xl);--pb:var(--g-s-xl);--pr:var(--g-s-xl);--cg:var(--g-s-2xl);--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gfs0ELeP4w gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gqFbLWayXe gp-relative gp-flex gp-flex-col"
    >
      <div
    
     data-id="gIKVxyOxY1"
    role="presentation"
    class="gp-group/image gIKVxyOxY1 gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-xl);--ta:center"
    >
      <div
        class="pointer-events-auto gp-h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="border-radius:inherit;--jc:center"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" data-srcSet="{{ "gempages_572751041980793671-df1a625d-98aa-4d9b-8d50-4cd74206e4ac.png" | file_url }}" srcset="{{ "gempages_572751041980793671-df1a625d-98aa-4d9b-8d50-4cd74206e4ac.png" | file_url }}" />
      <source media="(max-width: 1024px)" data-srcSet="{{ "gempages_572751041980793671-df1a625d-98aa-4d9b-8d50-4cd74206e4ac.png" | file_url }}" srcset="{{ "gempages_572751041980793671-df1a625d-98aa-4d9b-8d50-4cd74206e4ac.png" | file_url }}" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex"
        src="{{ "gempages_572751041980793671-df1a625d-98aa-4d9b-8d50-4cd74206e4ac.png" | file_url }}"
        data-src="{{ "gempages_572751041980793671-df1a625d-98aa-4d9b-8d50-4cd74206e4ac.png" | file_url }}"
        width="100%"
        alt="Alt Image"
        style="--aspect:auto;--objf:cover;--w:48px;--w-tablet:48px;--w-mobile:48px;--h:auto;--h-tablet:auto;--h-mobile:auto"
      />
    
  </picture>
      </div>
  </div>
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g-0XrVNier">
    <div
      parentTag="Col"
        class="g-0XrVNier "
        style="--ta:left;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-xl);--mb-mobile:14px"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--size:19px;--size-tablet:19px;--size-mobile:17px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--c:#242424;word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.gg-0XrVNier_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gAzf9RRV9S">
    <div
      parentTag="Col"
        class="gAzf9RRV9S "
        style="--tt:default;--ta:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%"
      >
      <div class="gp-flex" style="--jc:center">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text-g-text-1 gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:center;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#575757;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggAzf9RRV9S_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
    </div>
    </div>
   
    
    </div>
    </div>
  
            </section>
           
    


{% schema %}
  {
    
    "name": "Section 13",
    "tag": "section",
    "class": "gps-572907520155190022 gps gpsil",
    "settings": [{"type":"paragraph","content":"Section design by GemPages. [Click here to start design](https://admin.shopify.com/store/i0pixe-10/apps/gempages-cro/app/shopify/edit?pageType=GP_PRODUCT&editorId=572751048691811510&sectionId=572907520155190022)"},{"type":"select","label":"Section Preload","id":"section_preload","options":[{"label":"On","value":"true"},{"label":"Off","value":"false"},{"label":"Off Lazy Script","value":"off_lazy_script"}],"default":"false"},{"type":"text","label":"Checksum","id":"checksum"},{"type":"html","id":"gg41yR1whRA_text","label":"gg41yR1whRA_text","default":"<p>Global Shipping</p>"},{"type":"html","id":"gg_niYciEOZ_text","label":"gg_niYciEOZ_text","default":"<p>We ship to most countries around the globe with reliable logistics partners. Fast, tracked, and secure delivery to your doorstep.</p>"},{"type":"html","id":"ggjvzGTUoSi_text","label":"ggjvzGTUoSi_text","default":"<p>Full Refund</p>"},{"type":"html","id":"ggnsymVKizF_text","label":"ggnsymVKizF_text","default":"<p>If your product aren’t perfect, return them for a full refund</p>"},{"type":"html","id":"ggUm0of-XjA_text","label":"ggUm0of-XjA_text","default":"<p>Secure Online Payment</p>"},{"type":"html","id":"ggiAn4-rHGl_text","label":"ggiAn4-rHGl_text","default":"<p>secure payment<br>worldwide</p>"},{"type":"html","id":"ggW-4ncp2IH_text","label":"ggW-4ncp2IH_text","default":"<p>Free Shipping</p>"},{"type":"html","id":"ggH3f9xIdXp_text","label":"ggH3f9xIdXp_text","default":"<p>Free shipping on any order of $150&nbsp;</p><p>or more.</p>"},{"type":"html","id":"ggiJmyf9Gox_text","label":"ggiJmyf9Gox_text","default":"<p>Full Refund</p>"},{"type":"html","id":"ggUsZtoAH8c_text","label":"ggUsZtoAH8c_text","default":"<p>If your product aren’t perfect, return them for a full refund</p>"},{"type":"html","id":"gg-0XrVNier_text","label":"gg-0XrVNier_text","default":"<p>Secure Online Payment</p>"},{"type":"html","id":"ggAzf9RRV9S_text","label":"ggAzf9RRV9S_text","default":"<p>secure payment<br>worldwide</p>"}],
    "blocks": [{"type": "@app"}],
    "locales": {}
  }
{% endschema %}
