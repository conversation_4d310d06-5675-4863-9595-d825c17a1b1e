

<style {% if section.settings.section_preload == "false" %} class="gps-link gps-style" type="text/disabled-css" {% endif %}>.gps-572919986582979809.gps.gpsil [style*="--bg:"]{background:var(--bg)}.gps-572919986582979809.gps.gpsil [style*="--bga:"]{background-attachment:var(--bga)}.gps-572919986582979809.gps.gpsil [style*="--bgc:"]{background-color:var(--bgc)}.gps-572919986582979809.gps.gpsil [style*="--bgi:"]{background-image:var(--bgi)}.gps-572919986582979809.gps.gpsil [style*="--bgp:"]{background-position:var(--bgp)}.gps-572919986582979809.gps.gpsil [style*="--bgr:"]{background-repeat:var(--bgr)}.gps-572919986582979809.gps.gpsil [style*="--bgs:"]{background-size:var(--bgs)}.gps-572919986582979809.gps.gpsil [style*="--b:"]{border:var(--b)}.gps-572919986582979809.gps.gpsil [style*="--bc:"]{border-color:var(--bc)}.gps-572919986582979809.gps.gpsil [style*="--bl:"]{border-left:var(--bl)}.gps-572919986582979809.gps.gpsil [style*="--radius:"]{border-radius:var(--radius)}.gps-572919986582979809.gps.gpsil [style*="--bs:"]{border-style:var(--bs)}.gps-572919986582979809.gps.gpsil [style*="--bw:"]{border-width:var(--bw)}.gps-572919986582979809.gps.gpsil [style*="--bottom:"]{bottom:var(--bottom)}.gps-572919986582979809.gps.gpsil [style*="--shadow:"]{box-shadow:var(--shadow)}.gps-572919986582979809.gps.gpsil [style*="--c:"]{color:var(--c)}.gps-572919986582979809.gps.gpsil [style*="--cg:"]{-moz-column-gap:var(--cg);column-gap:var(--cg)}.gps-572919986582979809.gps.gpsil [style*="--gtc:"]{grid-template-columns:var(--gtc)}.gps-572919986582979809.gps.gpsil [style*="--h:"]{height:var(--h)}.gps-572919986582979809.gps.gpsil [style*="--jc:"]{justify-content:var(--jc)}.gps-572919986582979809.gps.gpsil [style*="--left:"]{left:var(--left)}.gps-572919986582979809.gps.gpsil [style*="--m:"]{margin:var(--m)}.gps-572919986582979809.gps.gpsil [style*="--ml:"]{margin-left:var(--ml)}.gps-572919986582979809.gps.gpsil [style*="--maxw:"]{max-width:var(--maxw)}.gps-572919986582979809.gps.gpsil [style*="--minw:"]{min-width:var(--minw)}.gps-572919986582979809.gps.gpsil [style*="--objf:"]{-o-object-fit:var(--objf);object-fit:var(--objf)}.gps-572919986582979809.gps.gpsil [style*="--op:"]{opacity:var(--op)}.gps-572919986582979809.gps.gpsil [style*="--o:"]{order:var(--o)}.gps-572919986582979809.gps.gpsil [style*="--pc:"]{place-content:var(--pc)}.gps-572919986582979809.gps.gpsil [style*="--p:"]{padding:var(--p)}.gps-572919986582979809.gps.gpsil [style*="--pb:"]{padding-bottom:var(--pb)}.gps-572919986582979809.gps.gpsil [style*="--pl:"]{padding-left:var(--pl)}.gps-572919986582979809.gps.gpsil [style*="--pr:"]{padding-right:var(--pr)}.gps-572919986582979809.gps.gpsil [style*="--pt:"]{padding-top:var(--pt)}.gps-572919986582979809.gps.gpsil [style*="--right:"]{right:var(--right)}.gps-572919986582979809.gps.gpsil [style*="--ta:"]{text-align:var(--ta)}.gps-572919986582979809.gps.gpsil [style*="--top:"]{top:var(--top)}.gps-572919986582979809.gps.gpsil [style*="--t:"]{transform:var(--t)}.gps-572919986582979809.gps.gpsil [style*="--w:"]{width:var(--w)}@media only screen and (max-width:1024px){.gps-572919986582979809.gps.gpsil [style*="--bottom-tablet:"]{bottom:var(--bottom-tablet)}.gps-572919986582979809.gps.gpsil [style*="--cg-tablet:"]{-moz-column-gap:var(--cg-tablet);column-gap:var(--cg-tablet)}.gps-572919986582979809.gps.gpsil [style*="--h-tablet:"]{height:var(--h-tablet)}.gps-572919986582979809.gps.gpsil [style*="--left-tablet:"]{left:var(--left-tablet)}.gps-572919986582979809.gps.gpsil [style*="--ml-tablet:"]{margin-left:var(--ml-tablet)}.gps-572919986582979809.gps.gpsil [style*="--maxw-tablet:"]{max-width:var(--maxw-tablet)}.gps-572919986582979809.gps.gpsil [style*="--minw-tablet:"]{min-width:var(--minw-tablet)}.gps-572919986582979809.gps.gpsil [style*="--pl-tablet:"]{padding-left:var(--pl-tablet)}.gps-572919986582979809.gps.gpsil [style*="--pr-tablet:"]{padding-right:var(--pr-tablet)}.gps-572919986582979809.gps.gpsil [style*="--right-tablet:"]{right:var(--right-tablet)}.gps-572919986582979809.gps.gpsil [style*="--top-tablet:"]{top:var(--top-tablet)}.gps-572919986582979809.gps.gpsil [style*="--w-tablet:"]{width:var(--w-tablet)}}@media only screen and (max-width:767px){.gps-572919986582979809.gps.gpsil [style*="--bottom-mobile:"]{bottom:var(--bottom-mobile)}.gps-572919986582979809.gps.gpsil [style*="--cg-mobile:"]{-moz-column-gap:var(--cg-mobile);column-gap:var(--cg-mobile)}.gps-572919986582979809.gps.gpsil [style*="--h-mobile:"]{height:var(--h-mobile)}.gps-572919986582979809.gps.gpsil [style*="--left-mobile:"]{left:var(--left-mobile)}.gps-572919986582979809.gps.gpsil [style*="--ml-mobile:"]{margin-left:var(--ml-mobile)}.gps-572919986582979809.gps.gpsil [style*="--maxw-mobile:"]{max-width:var(--maxw-mobile)}.gps-572919986582979809.gps.gpsil [style*="--minw-mobile:"]{min-width:var(--minw-mobile)}.gps-572919986582979809.gps.gpsil [style*="--pl-mobile:"]{padding-left:var(--pl-mobile)}.gps-572919986582979809.gps.gpsil [style*="--pr-mobile:"]{padding-right:var(--pr-mobile)}.gps-572919986582979809.gps.gpsil [style*="--right-mobile:"]{right:var(--right-mobile)}.gps-572919986582979809.gps.gpsil [style*="--top-mobile:"]{top:var(--top-mobile)}.gps-572919986582979809.gps.gpsil [style*="--w-mobile:"]{width:var(--w-mobile)}}.gps-572919986582979809 .gp-rotate-0,.gps-572919986582979809 .gp-rotate-180,.gps-572919986582979809 .mobile\:gp-rotate-0,.gps-572919986582979809 .mobile\:gp-rotate-180,.gps-572919986582979809 .tablet\:gp-rotate-0,.gps-572919986582979809 .tablet\:gp-rotate-180{--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1}.gps-572919986582979809 .gp-shadow-md{--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000}.gps-572919986582979809 .\!gp-absolute{position:absolute!important}.gps-572919986582979809 .gp-absolute{position:absolute}.gps-572919986582979809 .gp-relative{position:relative}.gps-572919986582979809 .gp-left-0{left:0}.gps-572919986582979809 .gp-right-0{right:0}.gps-572919986582979809 .gp-z-1{z-index:1}.gps-572919986582979809 .gp-z-2{z-index:2}.gps-572919986582979809 .gp-mx-auto{margin-left:auto;margin-right:auto}.gps-572919986582979809 .gp-my-0{margin-bottom:0;margin-top:0}.gps-572919986582979809 .gp-mb-0{margin-bottom:0}.gps-572919986582979809 .gp-block{display:block}.gps-572919986582979809 .\!gp-flex{display:flex!important}.gps-572919986582979809 .gp-flex{display:flex}.gps-572919986582979809 .gp-grid{display:grid}.gps-572919986582979809 .gp-contents{display:contents}.gps-572919986582979809 .\!gp-hidden{display:none!important}.gps-572919986582979809 .gp-hidden{display:none}.gps-572919986582979809 .gp-aspect-square{aspect-ratio:1/1}.gps-572919986582979809 .gp-h-auto{height:auto}.gps-572919986582979809 .gp-h-full{height:100%}.gps-572919986582979809 .\!gp-min-h-full{min-height:100%!important}.gps-572919986582979809 .gp-w-\[12px\]{width:12px}.gps-572919986582979809 .gp-w-full{width:100%}.gps-572919986582979809 .gp-max-w-full{max-width:100%}.gps-572919986582979809 .gp-flex-none{flex:none}.gps-572919986582979809 .gp-shrink-0{flex-shrink:0}.gps-572919986582979809 .gp-rotate-0{--tw-rotate:0deg}.gps-572919986582979809 .gp-rotate-0,.gps-572919986582979809 .gp-rotate-180{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.gps-572919986582979809 .gp-rotate-180{--tw-rotate:180deg}.gps-572919986582979809 .gp-cursor-pointer{cursor:pointer}.gps-572919986582979809 .gp-select-none{-webkit-user-select:none;-moz-user-select:none;user-select:none}.gps-572919986582979809 .gp-grid-rows-\[1fr\]{grid-template-rows:1fr}.gps-572919986582979809 .\!gp-flex-row{flex-direction:row!important}.gps-572919986582979809 .gp-flex-row{flex-direction:row}.gps-572919986582979809 .gp-flex-col{flex-direction:column}.gps-572919986582979809 .\!gp-flex-nowrap{flex-wrap:nowrap!important}.gps-572919986582979809 .gp-items-center{align-items:center}.gps-572919986582979809 .gp-justify-center{justify-content:center}.gps-572919986582979809 .gp-justify-between{justify-content:space-between}.gps-572919986582979809 .gp-gap-2{gap:8px}.gps-572919986582979809 .gp-gap-y-0{row-gap:0}.gps-572919986582979809 .gp-overflow-hidden{overflow:hidden}.gps-572919986582979809 .gp-rounded-full{border-radius:9999px}.gps-572919986582979809 .gp-text-center{text-align:center}.gps-572919986582979809 .gp-text-gray-500{--tw-text-opacity:1;color:rgb(107 114 128/var(--tw-text-opacity))}.gps-572919986582979809 .gp-shadow-md{--tw-shadow:0 4px 6px -1px rgba(0,0,0,.1),0 2px 4px -2px rgba(0,0,0,.1);--tw-shadow-colored:0 4px 6px -1px var(--tw-shadow-color),0 2px 4px -2px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)}.gps-572919986582979809 .gp-transition-colors{transition-duration:.15s;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-572919986582979809 .gp-duration-200{transition-duration:.2s}.gps-572919986582979809 .gp-ease-in-out{transition-timing-function:cubic-bezier(.4,0,.2,1)}@media (max-width:1024px){.gps-572919986582979809 .tablet\:\!gp-absolute{position:absolute!important}.gps-572919986582979809 .tablet\:gp-absolute{position:absolute}.gps-572919986582979809 .tablet\:gp-left-0{left:0}.gps-572919986582979809 .tablet\:gp-right-0{right:0}.gps-572919986582979809 .tablet\:gp-z-2{z-index:2}.gps-572919986582979809 .tablet\:gp-block{display:block}.gps-572919986582979809 .tablet\:\!gp-flex{display:flex!important}.gps-572919986582979809 .tablet\:\!gp-hidden{display:none!important}.gps-572919986582979809 .tablet\:gp-hidden{display:none}.gps-572919986582979809 .tablet\:gp-h-auto{height:auto}.gps-572919986582979809 .tablet\:\!gp-min-h-full{min-height:100%!important}.gps-572919986582979809 .tablet\:gp-flex-none{flex:none}.gps-572919986582979809 .tablet\:gp-rotate-0{--tw-rotate:0deg}.gps-572919986582979809 .tablet\:gp-rotate-0,.gps-572919986582979809 .tablet\:gp-rotate-180{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.gps-572919986582979809 .tablet\:gp-rotate-180{--tw-rotate:180deg}.gps-572919986582979809 .tablet\:\!gp-flex-row{flex-direction:row!important}.gps-572919986582979809 .tablet\:gp-flex-row{flex-direction:row}.gps-572919986582979809 .tablet\:\!gp-flex-nowrap{flex-wrap:nowrap!important}.gps-572919986582979809 .tablet\:gp-px-0{padding-left:0;padding-right:0}}@media (max-width:767px){.gps-572919986582979809 .mobile\:\!gp-absolute{position:absolute!important}.gps-572919986582979809 .mobile\:gp-absolute{position:absolute}.gps-572919986582979809 .mobile\:gp-left-0{left:0}.gps-572919986582979809 .mobile\:gp-right-0{right:0}.gps-572919986582979809 .mobile\:gp-z-2{z-index:2}.gps-572919986582979809 .mobile\:gp-block{display:block}.gps-572919986582979809 .mobile\:\!gp-flex{display:flex!important}.gps-572919986582979809 .mobile\:\!gp-hidden{display:none!important}.gps-572919986582979809 .mobile\:gp-hidden{display:none}.gps-572919986582979809 .mobile\:gp-h-auto{height:auto}.gps-572919986582979809 .mobile\:\!gp-min-h-full{min-height:100%!important}.gps-572919986582979809 .mobile\:gp-flex-none{flex:none}.gps-572919986582979809 .mobile\:gp-rotate-0{--tw-rotate:0deg}.gps-572919986582979809 .mobile\:gp-rotate-0,.gps-572919986582979809 .mobile\:gp-rotate-180{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.gps-572919986582979809 .mobile\:gp-rotate-180{--tw-rotate:180deg}.gps-572919986582979809 .mobile\:\!gp-flex-row{flex-direction:row!important}.gps-572919986582979809 .mobile\:gp-flex-row{flex-direction:row}.gps-572919986582979809 .mobile\:\!gp-flex-nowrap{flex-wrap:nowrap!important}.gps-572919986582979809 .mobile\:gp-px-0{padding-left:0;padding-right:0}}.gps-572919986582979809 .\[\&\>svg\]\:gp-h-full>svg{height:100%}.gps-572919986582979809 .\[\&\>svg\]\:gp-w-full>svg{width:100%}.gps-572919986582979809 .\[\&_\*\]\:gp-max-w-full *{max-width:100%}</style>

       
      
            <section
              class="gp-mx-auto gp-max-w-full {% if section.settings.section_preload == "false" %}gps-lazy {% endif %}"
              style="--w:100%;--w-tablet:100%;--w-mobile:100%;--pl:0px;--pl-tablet:0px;--pl-mobile:0px;--pr:0px;--pr-tablet:0px;--pr-mobile:0px"
            >
              
    <div
      id="g1oeMjZb-Z" data-id="g1oeMjZb-Z"
        style="--blockPadding:base;--pt:var(--g-s-2xl);--pb:var(--g-s-2xl);--d:grid;--d-tablet:grid;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--cg:32px;--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="g1oeMjZb-Z gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gLATEPBizn gp-relative gp-flex gp-flex-col"
    >
      
    <gp-carousel data-id="grYkqiAvwV"  id="gp-root-carousel-grYkqiAvwV-{{section.id}}" class="  gp-group/carousel gp-flex" gp-data='{"id":"grYkqiAvwV-{{section.id}}","setting":{"childItem":["Slide 1","Slide 2","Slide 3","Slide 4"],"itemNumber":{"desktop":1,"tablet":1,"mobile":1},"sneakPeak":{"desktop":false},"sneakPeakType":{"desktop":"forward"},"sneakPeakOffsetForward":{"desktop":50},"sneakPeakOffsetCenter":{"desktop":50},"vertical":{"desktop":false},"controlOverContent":{"desktop":true},"dot":{"desktop":true,"tablet":true,"mobile":true},"dotStyle":{"desktop":"inside"},"dotSize":{"desktop":12},"dotGapToCarousel":{"desktop":16},"dotColor":{"desktop":"bg-1"},"dotActiveColor":{"desktop":"line-3"},"navigationStyle":{"desktop":"inside"},"arrowCustom":"<svg width=\"20\" height=\"20\" viewBox=\"0 0 20 20\" fill=\"currentColor\" xmlns=\"http://www.w3.org/2000/svg\">\n              <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M7.62204 5.13313C7.78476 4.95562 8.04858 4.95562 8.21129 5.13313L12.378 9.67859C12.5407 9.8561 12.5407 10.1439 12.378 10.3214L8.21129 14.8669C8.04858 15.0444 7.78476 15.0444 7.62204 14.8669C7.45932 14.6894 7.45932 14.4016 7.62204 14.224L11.4941 10L7.62204 5.77596C7.45932 5.59845 7.45932 5.31064 7.62204 5.13313Z\"/>\n              </svg>\n              ","arrowCustomColor":"#000000","arrowIconSize":{"desktop":80},"arrowButtonSize":{"desktop":{"shapeValue":"1/1","shapeLinked":true,"width":"32px","height":"32px","padding":{"linked":true}}},"arrowBackgroundColor":"transparent","arrowBorder":{"desktop":{"borderType":"none","border":"none","borderWidth":"1px","width":"1px 1px 1px 1px","isCustom":false}},"roundedArrow":{"desktop":{"radiusType":"small"}},"arrowGapToEachSide":"16","showWhenHover":false,"rtl":false,"autoplay":false,"autoplayTimeout":2,"pauseOnHover":true,"runPreview":false,"enableDrag":{"desktop":false},"loop":{"desktop":false},"animationMode":"ease-in","background":{"desktop":{"type":"color","color":"transparent","image":{"src":"","width":0,"height":0},"size":"cover","position":{"x":50,"y":50},"repeat":"no-repeat","attachment":"scroll"}},"label":true},"styles":{"sizeSetting":{"desktop":{"shapeLinked":false,"width":"100%","height":"auto"}},"playSpeed":500,"align":{"desktop":"center"},"borderContent":{"borderType":"none","border":"none","width":"1px 1px 1px 1px","position":"all","borderWidth":"1px","color":"#121212","isCustom":true},"roundedContent":{"radiusType":"none"},"hasActiveShadow":false,"carouselShadow":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"#121212","angle":90},"spacing":{"desktop":16},"itemPadding":{"desktop":{"padding":{"type":"custom","top":"0px","left":"0px","bottom":"0px","right":"0px"}}}},"isHiddenArrowWhenDisabled":true}' style="--jc:center">
      <div class="gp-hidden gp-aspect-square gp-w-[12px] gp-rounded-full gp-shadow-md"></div>
      <div
        
        class="gp-relative gp-my-0 tablet:gp-px-0 gp-max-w-full mobile:gp-px-0 gp-carousel-wrapper mobile:gp-block tablet:gp-block gp-block grYkqiAvwV"
        style="--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--w:100%;--w-tablet:100%;--w-mobile:100%;--h:auto;--h-tablet:auto;--h-mobile:auto"
      >
        <div
          class="gp-h-full gp-relative gp-mx-auto gp-flex gp-items-center gp-justify-between mobile:!gp-flex-row tablet:!gp-flex-row !gp-flex-row"
          style="--h:100%;--h-tablet:100%;--h-mobile:100%;gap:16px"
        >
          
    <button
      type="button"
      aria-label='Carousel Back Arrow'
      class="gp-z-1 gp-carousel-action-back gem-slider-previous grYkqiAvwV-{{section.id}} gp-carousel-arrow-grYkqiAvwV gp-items-center gp-justify-center gp-overflow-hidden gp-shrink-0  gp-cursor-pointer gp-text-gray-500 !gp-flex !gp-absolute gp-z-2 tablet:!gp-flex tablet:!gp-absolute tablet:gp-z-2 mobile:!gp-flex mobile:!gp-absolute mobile:gp-z-2"
      style="--left:16px;--top:initial;--right:initial;--bottom:;--left-tablet:16px;--top-tablet:initial;--right-tablet:initial;--bottom-tablet:;--left-mobile:16px;--top-mobile:initial;--right-mobile:initial;--bottom-mobile:;--d:flex;--d-tablet:flex;--d-mobile:flex;background-color:transparent;--w:32px;--h:32px"
      onClick={onClick}
      
    >
      <div 
  class="gp-flex gp-items-center gp-justify-center [&>svg]:gp-h-full [&>svg]:gp-w-full gp-rotate-180 tablet:gp-rotate-180 mobile:gp-rotate-180"
  style="--c:#000000;--w:80px;--w-tablet:80px;--w-mobile:80px;--h:80px;--h-tablet:80px;--h-mobile:80px"
  >
    <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd" clip-rule="evenodd" d="M7.62204 5.13313C7.78476 4.95562 8.04858 4.95562 8.21129 5.13313L12.378 9.67859C12.5407 9.8561 12.5407 10.1439 12.378 10.3214L8.21129 14.8669C8.04858 15.0444 7.78476 15.0444 7.62204 14.8669C7.45932 14.6894 7.45932 14.4016 7.62204 14.224L11.4941 10L7.62204 5.77596C7.45932 5.59845 7.45932 5.31064 7.62204 5.13313Z"/>
              </svg>
              
    </div>
      <style>
    .gp-carousel-arrow-grYkqiAvwV {
      border-radius: var(--g-radius-small);
    }
    .gp-carousel-arrow-grYkqiAvwV::before {
      content: '';
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      z-index: 10;
      border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
      border-radius: var(--g-radius-small);
    }
    @media only screen and (max-width: 1024px) and (min-width: 768px) {
      .gp-carousel-arrow-grYkqiAvwV {
        border-radius: var(--g-radius-small);
      }
      .gp-carousel-arrow-grYkqiAvwV::before {
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        border-radius: var(--g-radius-small);
      }
    }
    @media only screen and (max-width: 768px) {
      .gp-carousel-arrow-grYkqiAvwV {
        border-radius: var(--g-radius-small);
      }
      .gp-carousel-arrow-grYkqiAvwV::before {
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        border-radius: var(--g-radius-small);
      }
    }
  </style>
    </button>
  
          <div id="gp-carousel-grYkqiAvwV-{{section.id}}" class="gem-slider gp-h-full gp-overflow-hidden gp-select-none mobile:!gp-flex-nowrap tablet:!gp-flex-nowrap !gp-flex-nowrap mobile:!gp-min-h-full tablet:!gp-min-h-full !gp-min-h-full"
          style="--cg-mobile:16px;--cg-tablet:16px;--cg:16px">
          
    <div
      media-type="{{media.media_type}}"
      media-poster="{{media.preview_image.src}}"
      media-source-url="{{media.sources.first.url}}"
      media-last-source-url="{{mediaSourceUrl}}"
      external-id="{{media.external_id}}"
      grid-index="{{forloop.index}}"
      
      tag="CarouselItem" label="Carousel Item" type="component"
      style="--bgc:transparent;--minw:calc(100% / 1 - 0px);--minw-tablet:calc(100% / 1 - 0px);--minw-mobile:calc(100% / 1 - 0px);--maxw:calc(100% / 1 - 0px);--maxw-tablet:calc(100% / 1 - 0px);--maxw-mobile:calc(100% / 1 - 0px);--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--ml:0px;--ml-tablet:0px;--ml-mobile:0px;--pl:0px;--pr:0px;--pt:0px;--pb:0px"
      class="gem-slider-item gp-w-full gem-slider-item- gp-child-item-grYkqiAvwV gi1ZyVGHXB"
      data-index="0"
    >
      <div
        class="gp-w-full gp-h-full",
        style="--shadow:none"
      >
      
    <gp-carousel data-id="glEz8kkeKN"  id="gp-root-carousel-glEz8kkeKN-{{section.id}}" class="  gp-group/carousel gp-flex" gp-data='{"id":"glEz8kkeKN-{{section.id}}","setting":{"childItem":["Slide 1","Slide 2"],"itemNumber":{"desktop":1,"tablet":1,"mobile":1},"sneakPeak":{"desktop":false},"sneakPeakType":{"desktop":"forward"},"sneakPeakOffsetForward":{"desktop":50},"sneakPeakOffsetCenter":{"desktop":50},"vertical":{"desktop":false},"controlOverContent":{"desktop":true},"dot":{"desktop":true,"tablet":true,"mobile":true},"dotStyle":{"desktop":"inside"},"dotSize":{"desktop":12},"dotGapToCarousel":{"desktop":16},"dotColor":{"desktop":"bg-1"},"dotActiveColor":{"desktop":"line-3"},"navigationStyle":{"desktop":"inside"},"arrowCustom":"<svg width=\"20\" height=\"20\" viewBox=\"0 0 20 20\" fill=\"currentColor\" xmlns=\"http://www.w3.org/2000/svg\">\n              <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M7.62204 5.13313C7.78476 4.95562 8.04858 4.95562 8.21129 5.13313L12.378 9.67859C12.5407 9.8561 12.5407 10.1439 12.378 10.3214L8.21129 14.8669C8.04858 15.0444 7.78476 15.0444 7.62204 14.8669C7.45932 14.6894 7.45932 14.4016 7.62204 14.224L11.4941 10L7.62204 5.77596C7.45932 5.59845 7.45932 5.31064 7.62204 5.13313Z\"/>\n              </svg>\n              ","arrowCustomColor":"#000000","arrowIconSize":{"desktop":24},"arrowButtonSize":{"desktop":{"shapeValue":"1/1","shapeLinked":true,"width":"32px","height":"32px","padding":{"linked":true}}},"arrowBackgroundColor":"transparent","arrowBorder":{"desktop":{"borderType":"none","border":"none","borderWidth":"1px","width":"1px 1px 1px 1px","isCustom":false}},"roundedArrow":{"desktop":{"radiusType":"small"}},"arrowGapToEachSide":"16","showWhenHover":false,"rtl":false,"autoplay":false,"autoplayTimeout":2,"pauseOnHover":true,"runPreview":false,"enableDrag":{"desktop":false},"loop":{"desktop":false},"animationMode":"ease-in","background":{"desktop":{"type":"color","color":"transparent","image":{"src":"","width":0,"height":0},"size":"cover","position":{"x":50,"y":50},"repeat":"no-repeat","attachment":"scroll"}},"label":true},"styles":{"sizeSetting":{"desktop":{"shapeLinked":false,"width":"100%","height":"auto"}},"playSpeed":500,"align":{"desktop":"center"},"borderContent":{"borderType":"none","border":"none","width":"1px 1px 1px 1px","position":"all","borderWidth":"1px","color":"#121212","isCustom":true},"roundedContent":{"radiusType":"none"},"hasActiveShadow":false,"carouselShadow":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"#121212","angle":90},"spacing":{"desktop":16},"itemPadding":{"desktop":{"padding":{"type":"custom","top":"0px","left":"0px","bottom":"0px","right":"0px"}}}},"isHiddenArrowWhenDisabled":true}' style="--jc:center">
      <div class="gp-hidden gp-aspect-square gp-w-[12px] gp-rounded-full gp-shadow-md"></div>
      <div
        
        class="gp-relative gp-my-0 tablet:gp-px-0 gp-max-w-full mobile:gp-px-0 gp-carousel-wrapper mobile:gp-block tablet:gp-block gp-block glEz8kkeKN"
        style="--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--w:100%;--w-tablet:100%;--w-mobile:100%;--h:auto;--h-tablet:auto;--h-mobile:auto"
      >
        <div
          class="gp-h-full gp-relative gp-mx-auto gp-flex gp-items-center gp-justify-between mobile:!gp-flex-row tablet:!gp-flex-row !gp-flex-row"
          style="--h:100%;--h-tablet:100%;--h-mobile:100%;gap:16px"
        >
          
    <button
      type="button"
      aria-label='Carousel Back Arrow'
      class="gp-z-1 gp-carousel-action-back gem-slider-previous glEz8kkeKN-{{section.id}} gp-carousel-arrow-glEz8kkeKN gp-items-center gp-justify-center gp-overflow-hidden gp-shrink-0  gp-cursor-pointer gp-text-gray-500 !gp-flex !gp-absolute gp-z-2 tablet:!gp-flex tablet:!gp-absolute tablet:gp-z-2 mobile:!gp-flex mobile:!gp-absolute mobile:gp-z-2"
      style="--left:16px;--top:initial;--right:initial;--bottom:;--left-tablet:16px;--top-tablet:initial;--right-tablet:initial;--bottom-tablet:;--left-mobile:16px;--top-mobile:initial;--right-mobile:initial;--bottom-mobile:;--d:flex;--d-tablet:flex;--d-mobile:flex;background-color:transparent;--w:32px;--h:32px"
      onClick={onClick}
      
    >
      <div 
  class="gp-flex gp-items-center gp-justify-center [&>svg]:gp-h-full [&>svg]:gp-w-full gp-rotate-180 tablet:gp-rotate-180 mobile:gp-rotate-180"
  style="--c:#000000;--w:24px;--w-tablet:24px;--w-mobile:24px;--h:24px;--h-tablet:24px;--h-mobile:24px"
  >
    <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd" clip-rule="evenodd" d="M7.62204 5.13313C7.78476 4.95562 8.04858 4.95562 8.21129 5.13313L12.378 9.67859C12.5407 9.8561 12.5407 10.1439 12.378 10.3214L8.21129 14.8669C8.04858 15.0444 7.78476 15.0444 7.62204 14.8669C7.45932 14.6894 7.45932 14.4016 7.62204 14.224L11.4941 10L7.62204 5.77596C7.45932 5.59845 7.45932 5.31064 7.62204 5.13313Z"/>
              </svg>
              
    </div>
      <style>
    .gp-carousel-arrow-glEz8kkeKN {
      border-radius: var(--g-radius-small);
    }
    .gp-carousel-arrow-glEz8kkeKN::before {
      content: '';
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      z-index: 10;
      border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
      border-radius: var(--g-radius-small);
    }
    @media only screen and (max-width: 1024px) and (min-width: 768px) {
      .gp-carousel-arrow-glEz8kkeKN {
        border-radius: var(--g-radius-small);
      }
      .gp-carousel-arrow-glEz8kkeKN::before {
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        border-radius: var(--g-radius-small);
      }
    }
    @media only screen and (max-width: 768px) {
      .gp-carousel-arrow-glEz8kkeKN {
        border-radius: var(--g-radius-small);
      }
      .gp-carousel-arrow-glEz8kkeKN::before {
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        border-radius: var(--g-radius-small);
      }
    }
  </style>
    </button>
  
          <div id="gp-carousel-glEz8kkeKN-{{section.id}}" class="gem-slider gp-h-full gp-overflow-hidden gp-select-none mobile:!gp-flex-nowrap tablet:!gp-flex-nowrap !gp-flex-nowrap mobile:!gp-min-h-full tablet:!gp-min-h-full !gp-min-h-full"
          style="--cg-mobile:16px;--cg-tablet:16px;--cg:16px">
          
    <div
      media-type="{{media.media_type}}"
      media-poster="{{media.preview_image.src}}"
      media-source-url="{{media.sources.first.url}}"
      media-last-source-url="{{mediaSourceUrl}}"
      external-id="{{media.external_id}}"
      grid-index="{{forloop.index}}"
      
      tag="CarouselItem" label="Carousel Item" type="component"
      style="--bgc:transparent;--minw:calc(100% / 1 - 0px);--minw-tablet:calc(100% / 1 - 0px);--minw-mobile:calc(100% / 1 - 0px);--maxw:calc(100% / 1 - 0px);--maxw-tablet:calc(100% / 1 - 0px);--maxw-mobile:calc(100% / 1 - 0px);--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--ml:0px;--ml-tablet:0px;--ml-mobile:0px;--pl:0px;--pr:0px;--pt:0px;--pb:0px"
      class="gem-slider-item gp-w-full gem-slider-item- gp-child-item-glEz8kkeKN gQu_ZovTzo"
      data-index="0"
    >
      <div
        class="gp-w-full gp-h-full",
        style="--shadow:none"
      >
      <div
    
     data-id="goZM4Klu0w"
    role="presentation"
    class="gp-group/image goZM4Klu0w gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--ta:center"
    >
      <div
        class="pointer-events-auto gp-h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="--shadow:none;border-radius:inherit;--jc:center"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" data-srcSet="{{ "gempages_572751041980793671-7e5b9e93-fc24-474e-81ac-05996c6a08ab.png" | file_url }}" srcset="{{ "gempages_572751041980793671-7e5b9e93-fc24-474e-81ac-05996c6a08ab.png" | file_url }}" />
      <source media="(max-width: 1024px)" data-srcSet="{{ "gempages_572751041980793671-7e5b9e93-fc24-474e-81ac-05996c6a08ab.png" | file_url }}" srcset="{{ "gempages_572751041980793671-7e5b9e93-fc24-474e-81ac-05996c6a08ab.png" | file_url }}" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex"
        src="{{ "gempages_572751041980793671-7e5b9e93-fc24-474e-81ac-05996c6a08ab.png" | file_url }}"
        data-src="{{ "gempages_572751041980793671-7e5b9e93-fc24-474e-81ac-05996c6a08ab.png" | file_url }}"
        width="100%"
        alt=""
        style="--objf:cover;--w:1200px;--w-tablet:1200px;--w-mobile:1200px;--h:auto;--h-tablet:auto;--h-mobile:auto;--b:none;--bc:#000000;--bw:1px 1px 1px 1px;--radiusType:none;--shadow:none"
      />
    
  </picture>
      </div>
  </div>
      </div>
    </div>
  
    <div
      media-type="{{media.media_type}}"
      media-poster="{{media.preview_image.src}}"
      media-source-url="{{media.sources.first.url}}"
      media-last-source-url="{{mediaSourceUrl}}"
      external-id="{{media.external_id}}"
      grid-index="{{forloop.index}}"
      
      tag="CarouselItem" label="Carousel Item" type="component"
      style="--bgc:transparent;--minw:calc(100% / 1 - 0px);--minw-tablet:calc(100% / 1 - 0px);--minw-mobile:calc(100% / 1 - 0px);--maxw:calc(100% / 1 - 0px);--maxw-tablet:calc(100% / 1 - 0px);--maxw-mobile:calc(100% / 1 - 0px);--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--ml:0px;--ml-tablet:0px;--ml-mobile:0px;--pl:0px;--pr:0px;--pt:0px;--pb:0px"
      class="gem-slider-item gp-w-full gem-slider-item- gp-child-item-glEz8kkeKN gO_r9GbBwy"
      data-index="1"
    >
      <div
        class="gp-w-full gp-h-full",
        style="--shadow:none"
      >
      
      </div>
    </div>
  
          </div>
          
    <button
      type="button"
      aria-label='Carousel Next Arrow'
      class="gp-z-1 gp-carousel-action-next gem-slider-next glEz8kkeKN-{{section.id}} gp-carousel-arrow-glEz8kkeKN gp-items-center gp-justify-center gp-overflow-hidden gp-shrink-0  gp-cursor-pointer gp-text-gray-500 !gp-flex !gp-absolute gp-z-2 tablet:!gp-flex tablet:!gp-absolute tablet:gp-z-2 mobile:!gp-flex mobile:!gp-absolute mobile:gp-z-2"
      style="--left:initial;--top:;--right:16px;--bottom:initial;--left-tablet:initial;--top-tablet:;--right-tablet:16px;--bottom-tablet:initial;--left-mobile:initial;--top-mobile:;--right-mobile:16px;--bottom-mobile:initial;--d:flex;--d-tablet:flex;--d-mobile:flex;background-color:transparent;--w:32px;--h:32px"
      onClick={onClick}
      
    >
      <div 
  class="gp-flex gp-items-center gp-justify-center [&>svg]:gp-h-full [&>svg]:gp-w-full gp-rotate-0 tablet:gp-rotate-0 mobile:gp-rotate-0"
  style="--c:#000000;--w:24px;--w-tablet:24px;--w-mobile:24px;--h:24px;--h-tablet:24px;--h-mobile:24px"
  >
    <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd" clip-rule="evenodd" d="M7.62204 5.13313C7.78476 4.95562 8.04858 4.95562 8.21129 5.13313L12.378 9.67859C12.5407 9.8561 12.5407 10.1439 12.378 10.3214L8.21129 14.8669C8.04858 15.0444 7.78476 15.0444 7.62204 14.8669C7.45932 14.6894 7.45932 14.4016 7.62204 14.224L11.4941 10L7.62204 5.77596C7.45932 5.59845 7.45932 5.31064 7.62204 5.13313Z"/>
              </svg>
              
    </div>
      <style>
    .gp-carousel-arrow-glEz8kkeKN {
      border-radius: var(--g-radius-small);
    }
    .gp-carousel-arrow-glEz8kkeKN::before {
      content: '';
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      z-index: 10;
      border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
      border-radius: var(--g-radius-small);
    }
    @media only screen and (max-width: 1024px) and (min-width: 768px) {
      .gp-carousel-arrow-glEz8kkeKN {
        border-radius: var(--g-radius-small);
      }
      .gp-carousel-arrow-glEz8kkeKN::before {
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        border-radius: var(--g-radius-small);
      }
    }
    @media only screen and (max-width: 768px) {
      .gp-carousel-arrow-glEz8kkeKN {
        border-radius: var(--g-radius-small);
      }
      .gp-carousel-arrow-glEz8kkeKN::before {
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        border-radius: var(--g-radius-small);
      }
    }
  </style>
    </button>
  
        </div>
        <div
    class="gp-items-center gp-justify-center gp-gap-2 gp-text-center gp-carousel-dot-container gp-carousel-dot-container-glEz8kkeKN-{{section.id}} gp-absolute gp-flex-row gp-left-0 gp-right-0 tablet:gp-absolute tablet:gp-flex-row tablet:gp-left-0 tablet:gp-right-0 mobile:gp-absolute mobile:gp-flex-row mobile:gp-left-0 mobile:gp-right-0"
    style="--bottom:16px;--bottom-tablet:16px;--bottom-mobile:16px;--d:flex;--d-tablet:flex;--d-mobile:flex"
 ></div>
      </div>
    </gp-carousel>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-carousel-v2.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>

  
      </div>
    </div>
  
    <div
      media-type="{{media.media_type}}"
      media-poster="{{media.preview_image.src}}"
      media-source-url="{{media.sources.first.url}}"
      media-last-source-url="{{mediaSourceUrl}}"
      external-id="{{media.external_id}}"
      grid-index="{{forloop.index}}"
      
      tag="CarouselItem" label="Carousel Item" type="component"
      style="--bgc:transparent;--minw:calc(100% / 1 - 0px);--minw-tablet:calc(100% / 1 - 0px);--minw-mobile:calc(100% / 1 - 0px);--maxw:calc(100% / 1 - 0px);--maxw-tablet:calc(100% / 1 - 0px);--maxw-mobile:calc(100% / 1 - 0px);--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--ml:0px;--ml-tablet:0px;--ml-mobile:0px;--pl:0px;--pr:0px;--pt:0px;--pb:0px"
      class="gem-slider-item gp-w-full gem-slider-item- gp-child-item-grYkqiAvwV gXtqH9X8cz"
      data-index="1"
    >
      <div
        class="gp-w-full gp-h-full",
        style="--shadow:none"
      >
      <div
    
     data-id="gjIZZ-tQbL"
    role="presentation"
    class="gp-group/image gjIZZ-tQbL gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--ta:center"
    >
      <div
        class="pointer-events-auto gp-h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="--shadow:none;border-radius:inherit;--jc:center"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" data-srcSet="{{ "gempages_572751041980793671-91536b10-714f-4f5a-bb78-0eece33bf3b3.jpg" | file_url }}" srcset="{{ "gempages_572751041980793671-91536b10-714f-4f5a-bb78-0eece33bf3b3.jpg" | file_url }}" />
      <source media="(max-width: 1024px)" data-srcSet="{{ "gempages_572751041980793671-91536b10-714f-4f5a-bb78-0eece33bf3b3.jpg" | file_url }}" srcset="{{ "gempages_572751041980793671-91536b10-714f-4f5a-bb78-0eece33bf3b3.jpg" | file_url }}" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex"
        src="{{ "gempages_572751041980793671-91536b10-714f-4f5a-bb78-0eece33bf3b3.jpg" | file_url }}"
        data-src="{{ "gempages_572751041980793671-91536b10-714f-4f5a-bb78-0eece33bf3b3.jpg" | file_url }}"
        width="100%"
        alt=""
        style="--objf:cover;--w:1200px;--w-tablet:1200px;--w-mobile:1200px;--h:auto;--h-tablet:auto;--h-mobile:auto;--b:none;--bc:#000000;--bw:1px 1px 1px 1px;--radiusType:none;--shadow:none"
      />
    
  </picture>
      </div>
  </div>
      </div>
    </div>
  
    <div
      media-type="{{media.media_type}}"
      media-poster="{{media.preview_image.src}}"
      media-source-url="{{media.sources.first.url}}"
      media-last-source-url="{{mediaSourceUrl}}"
      external-id="{{media.external_id}}"
      grid-index="{{forloop.index}}"
      
      tag="CarouselItem" label="Carousel Item" type="component"
      style="--bgc:transparent;--minw:calc(100% / 1 - 0px);--minw-tablet:calc(100% / 1 - 0px);--minw-mobile:calc(100% / 1 - 0px);--maxw:calc(100% / 1 - 0px);--maxw-tablet:calc(100% / 1 - 0px);--maxw-mobile:calc(100% / 1 - 0px);--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--ml:0px;--ml-tablet:0px;--ml-mobile:0px;--pl:0px;--pr:0px;--pt:0px;--pb:0px"
      class="gem-slider-item gp-w-full gem-slider-item- gp-child-item-grYkqiAvwV g6HI8UzE2C"
      data-index="2"
    >
      <div
        class="gp-w-full gp-h-full",
        style="--shadow:none"
      >
      <div
    
     data-id="gNn8jTxf92"
    role="presentation"
    class="gp-group/image gNn8jTxf92 gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--ta:center"
    >
      <div
        class="pointer-events-auto gp-h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="--shadow:none;border-radius:inherit;--jc:center"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" data-srcSet="{{ "gempages_572751041980793671-46d594c1-7254-4d64-be23-fd65ca4d6c3c.jpg" | file_url }}" srcset="{{ "gempages_572751041980793671-46d594c1-7254-4d64-be23-fd65ca4d6c3c.jpg" | file_url }}" />
      <source media="(max-width: 1024px)" data-srcSet="{{ "gempages_572751041980793671-46d594c1-7254-4d64-be23-fd65ca4d6c3c.jpg" | file_url }}" srcset="{{ "gempages_572751041980793671-46d594c1-7254-4d64-be23-fd65ca4d6c3c.jpg" | file_url }}" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex"
        src="{{ "gempages_572751041980793671-46d594c1-7254-4d64-be23-fd65ca4d6c3c.jpg" | file_url }}"
        data-src="{{ "gempages_572751041980793671-46d594c1-7254-4d64-be23-fd65ca4d6c3c.jpg" | file_url }}"
        width="100%"
        alt=""
        style="--objf:cover;--w:1200px;--w-tablet:1200px;--w-mobile:1200px;--h:auto;--h-tablet:auto;--h-mobile:auto;--b:none;--bc:#000000;--bw:1px 1px 1px 1px;--radiusType:none;--shadow:none"
      />
    
  </picture>
      </div>
  </div>
      </div>
    </div>
  
    <div
      media-type="{{media.media_type}}"
      media-poster="{{media.preview_image.src}}"
      media-source-url="{{media.sources.first.url}}"
      media-last-source-url="{{mediaSourceUrl}}"
      external-id="{{media.external_id}}"
      grid-index="{{forloop.index}}"
      
      tag="CarouselItem" label="Carousel Item" type="component"
      style="--bgc:transparent;--minw:calc(100% / 1 - 0px);--minw-tablet:calc(100% / 1 - 0px);--minw-mobile:calc(100% / 1 - 0px);--maxw:calc(100% / 1 - 0px);--maxw-tablet:calc(100% / 1 - 0px);--maxw-mobile:calc(100% / 1 - 0px);--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--ml:0px;--ml-tablet:0px;--ml-mobile:0px;--pl:0px;--pr:0px;--pt:0px;--pb:0px"
      class="gem-slider-item gp-w-full gem-slider-item- gp-child-item-grYkqiAvwV gBpfWOs6F9"
      data-index="3"
    >
      <div
        class="gp-w-full gp-h-full",
        style="--shadow:none"
      >
      <div
    
     data-id="gQE6cHvs70"
    role="presentation"
    class="gp-group/image gQE6cHvs70 gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--ta:center"
    >
      <div
        class="pointer-events-auto gp-h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="--shadow:none;border-radius:inherit;--jc:center"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" data-srcSet="{{ "gempages_572751041980793671-d00ebead-e07b-4247-832d-48b6e8e0d6f9.jpg" | file_url }}" srcset="{{ "gempages_572751041980793671-d00ebead-e07b-4247-832d-48b6e8e0d6f9.jpg" | file_url }}" />
      <source media="(max-width: 1024px)" data-srcSet="{{ "gempages_572751041980793671-d00ebead-e07b-4247-832d-48b6e8e0d6f9.jpg" | file_url }}" srcset="{{ "gempages_572751041980793671-d00ebead-e07b-4247-832d-48b6e8e0d6f9.jpg" | file_url }}" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex"
        src="{{ "gempages_572751041980793671-d00ebead-e07b-4247-832d-48b6e8e0d6f9.jpg" | file_url }}"
        data-src="{{ "gempages_572751041980793671-d00ebead-e07b-4247-832d-48b6e8e0d6f9.jpg" | file_url }}"
        width="100%"
        alt=""
        style="--objf:cover;--w:1200px;--w-tablet:1200px;--w-mobile:1200px;--h:auto;--h-tablet:auto;--h-mobile:auto;--b:none;--bc:#000000;--bw:1px 1px 1px 1px;--radiusType:none;--shadow:none"
      />
    
  </picture>
      </div>
  </div>
      </div>
    </div>
  
          </div>
          
    <button
      type="button"
      aria-label='Carousel Next Arrow'
      class="gp-z-1 gp-carousel-action-next gem-slider-next grYkqiAvwV-{{section.id}} gp-carousel-arrow-grYkqiAvwV gp-items-center gp-justify-center gp-overflow-hidden gp-shrink-0  gp-cursor-pointer gp-text-gray-500 !gp-flex !gp-absolute gp-z-2 tablet:!gp-flex tablet:!gp-absolute tablet:gp-z-2 mobile:!gp-flex mobile:!gp-absolute mobile:gp-z-2"
      style="--left:initial;--top:;--right:16px;--bottom:initial;--left-tablet:initial;--top-tablet:;--right-tablet:16px;--bottom-tablet:initial;--left-mobile:initial;--top-mobile:;--right-mobile:16px;--bottom-mobile:initial;--d:flex;--d-tablet:flex;--d-mobile:flex;background-color:transparent;--w:32px;--h:32px"
      onClick={onClick}
      
    >
      <div 
  class="gp-flex gp-items-center gp-justify-center [&>svg]:gp-h-full [&>svg]:gp-w-full gp-rotate-0 tablet:gp-rotate-0 mobile:gp-rotate-0"
  style="--c:#000000;--w:80px;--w-tablet:80px;--w-mobile:80px;--h:80px;--h-tablet:80px;--h-mobile:80px"
  >
    <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd" clip-rule="evenodd" d="M7.62204 5.13313C7.78476 4.95562 8.04858 4.95562 8.21129 5.13313L12.378 9.67859C12.5407 9.8561 12.5407 10.1439 12.378 10.3214L8.21129 14.8669C8.04858 15.0444 7.78476 15.0444 7.62204 14.8669C7.45932 14.6894 7.45932 14.4016 7.62204 14.224L11.4941 10L7.62204 5.77596C7.45932 5.59845 7.45932 5.31064 7.62204 5.13313Z"/>
              </svg>
              
    </div>
      <style>
    .gp-carousel-arrow-grYkqiAvwV {
      border-radius: var(--g-radius-small);
    }
    .gp-carousel-arrow-grYkqiAvwV::before {
      content: '';
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      z-index: 10;
      border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
      border-radius: var(--g-radius-small);
    }
    @media only screen and (max-width: 1024px) and (min-width: 768px) {
      .gp-carousel-arrow-grYkqiAvwV {
        border-radius: var(--g-radius-small);
      }
      .gp-carousel-arrow-grYkqiAvwV::before {
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        border-radius: var(--g-radius-small);
      }
    }
    @media only screen and (max-width: 768px) {
      .gp-carousel-arrow-grYkqiAvwV {
        border-radius: var(--g-radius-small);
      }
      .gp-carousel-arrow-grYkqiAvwV::before {
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        border-radius: var(--g-radius-small);
      }
    }
  </style>
    </button>
  
        </div>
        <div
    class="gp-items-center gp-justify-center gp-gap-2 gp-text-center gp-carousel-dot-container gp-carousel-dot-container-grYkqiAvwV-{{section.id}} gp-absolute gp-flex-row gp-left-0 gp-right-0 tablet:gp-absolute tablet:gp-flex-row tablet:gp-left-0 tablet:gp-right-0 mobile:gp-absolute mobile:gp-flex-row mobile:gp-left-0 mobile:gp-right-0"
    style="--bottom:16px;--bottom-tablet:16px;--bottom-mobile:16px;--d:flex;--d-tablet:flex;--d-mobile:flex"
 ></div>
      </div>
    </gp-carousel>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-carousel-v2.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>

  
    </div>
    </div>
  
            </section>
           
    


{% schema %}
  {
    
    "name": "Section 10",
    "tag": "section",
    "class": "gps-572919986582979809 gps gpsil",
    "settings": [{"type":"paragraph","content":"Section design by GemPages. [Click here to start design](https://admin.shopify.com/store/i0pixe-10/apps/gempages-cro/app/shopify/edit?pageType=GP_PRODUCT&editorId=572751048691811510&sectionId=572919986582979809)"},{"type":"select","label":"Section Preload","id":"section_preload","options":[{"label":"On","value":"true"},{"label":"Off","value":"false"},{"label":"Off Lazy Script","value":"off_lazy_script"}],"default":"false"},{"type":"text","label":"Checksum","id":"checksum"}],
    "blocks": [{"type": "@app"}],
    "locales": {}
  }
{% endschema %}
