

<style {% if section.settings.section_preload == "false" %} class="gps-link gps-style" type="text/disabled-css" {% endif %}>.gps-573018541838566150.gps.gpsil [style*="--ai:"]{align-items:var(--ai)}.gps-573018541838566150.gps.gpsil [style*="--aspect:"]{aspect-ratio:var(--aspect)}.gps-573018541838566150.gps.gpsil [style*="--bg:"]{background:var(--bg)}.gps-573018541838566150.gps.gpsil [style*="--hvr-bg:"]:hover{background:var(--hvr-bg)}.gps-573018541838566150.gps.gpsil [style*="--bga:"]{background-attachment:var(--bga)}.gps-573018541838566150.gps.gpsil [style*="--bgc:"]{background-color:var(--bgc)}.gps-573018541838566150.gps.gpsil [style*="--bgi:"]{background-image:var(--bgi)}.gps-573018541838566150.gps.gpsil [style*="--bgp:"]{background-position:var(--bgp)}.gps-573018541838566150.gps.gpsil [style*="--bgr:"]{background-repeat:var(--bgr)}.gps-573018541838566150.gps.gpsil [style*="--bgs:"]{background-size:var(--bgs)}.gps-573018541838566150.gps.gpsil [style*="--b:"]{border:var(--b)}.gps-573018541838566150.gps.gpsil [style*="--hvr-b:"]:hover{border:var(--hvr-b)}.gps-573018541838566150.gps.gpsil [style*="--bb:"]{border-bottom:var(--bb)}.gps-573018541838566150.gps.gpsil [style*="--hvr-bb:"]:hover{border-bottom:var(--hvr-bb)}.gps-573018541838566150.gps.gpsil [style*="--bc:"]{border-color:var(--bc)}.gps-573018541838566150.gps.gpsil [style*="--hvr-bc:"]:hover{border-color:var(--hvr-bc)}.gps-573018541838566150.gps.gpsil [style*="--bblr:"]{border-bottom-left-radius:var(--bblr)}.gps-573018541838566150.gps.gpsil [style*="--hvr-bblr:"]:hover{border-bottom-left-radius:var(--hvr-bblr)}.gps-573018541838566150.gps.gpsil [style*="--bbrr:"]{border-bottom-right-radius:var(--bbrr)}.gps-573018541838566150.gps.gpsil [style*="--hvr-bbrr:"]:hover{border-bottom-right-radius:var(--hvr-bbrr)}.gps-573018541838566150.gps.gpsil [style*="--radius:"]{border-radius:var(--radius)}.gps-573018541838566150.gps.gpsil [style*="--bs:"]{border-style:var(--bs)}.gps-573018541838566150.gps.gpsil [style*="--hvr-bs:"]:hover{border-style:var(--hvr-bs)}.gps-573018541838566150.gps.gpsil [style*="--bt:"]{border-top:var(--bt)}.gps-573018541838566150.gps.gpsil [style*="--hvr-bt:"]:hover{border-top:var(--hvr-bt)}.gps-573018541838566150.gps.gpsil [style*="--btlr:"]{border-top-left-radius:var(--btlr)}.gps-573018541838566150.gps.gpsil [style*="--hvr-btlr:"]:hover{border-top-left-radius:var(--hvr-btlr)}.gps-573018541838566150.gps.gpsil [style*="--btrr:"]{border-top-right-radius:var(--btrr)}.gps-573018541838566150.gps.gpsil [style*="--hvr-btrr:"]:hover{border-top-right-radius:var(--hvr-btrr)}.gps-573018541838566150.gps.gpsil [style*="--bw:"]{border-width:var(--bw)}.gps-573018541838566150.gps.gpsil [style*="--hvr-bw:"]:hover{border-width:var(--hvr-bw)}.gps-573018541838566150.gps.gpsil [style*="--bottom:"]{bottom:var(--bottom)}.gps-573018541838566150.gps.gpsil [style*="--shadow:"]{box-shadow:var(--shadow)}.gps-573018541838566150.gps.gpsil [style*="--c:"]{color:var(--c)}.gps-573018541838566150.gps.gpsil [style*="--hvr-c:"]:hover{color:var(--hvr-c)}.gps-573018541838566150.gps.gpsil [style*="--cg:"]{-moz-column-gap:var(--cg);column-gap:var(--cg)}.gps-573018541838566150.gps.gpsil [style*="--ff:"]{font-family:var(--ff)}.gps-573018541838566150.gps.gpsil [style*="--size:"]{font-size:var(--size)}.gps-573018541838566150.gps.gpsil [style*="--weight:"]{font-weight:var(--weight)}.gps-573018541838566150.gps.gpsil [style*="--fs:"]{font-style:var(--fs)}.gps-573018541838566150.gps.gpsil [style*="--gg:"]{grid-gap:var(--gg)}.gps-573018541838566150.gps.gpsil [style*="--gtc:"]{grid-template-columns:var(--gtc)}.gps-573018541838566150.gps.gpsil [style*="--h:"]{height:var(--h)}.gps-573018541838566150.gps.gpsil [style*="--jc:"]{justify-content:var(--jc)}.gps-573018541838566150.gps.gpsil [style*="--left:"]{left:var(--left)}.gps-573018541838566150.gps.gpsil [style*="--ls:"]{letter-spacing:var(--ls)}.gps-573018541838566150.gps.gpsil [style*="--lh:"]{line-height:var(--lh)}.gps-573018541838566150.gps.gpsil [style*="--tdt:"]{text-decoration-thickness:var(--tdt)}.gps-573018541838566150.gps.gpsil [style*="--tdc:"]{text-decoration-color:var(--tdc)}.gps-573018541838566150.gps.gpsil [style*="--m:"]{margin:var(--m)}.gps-573018541838566150.gps.gpsil [style*="--mb:"]{margin-bottom:var(--mb)}.gps-573018541838566150.gps.gpsil [style*="--mt:"]{margin-top:var(--mt)}.gps-573018541838566150.gps.gpsil [style*="--maxw:"]{max-width:var(--maxw)}.gps-573018541838566150.gps.gpsil [style*="--objf:"]{-o-object-fit:var(--objf);object-fit:var(--objf)}.gps-573018541838566150.gps.gpsil [style*="--op:"]{opacity:var(--op)}.gps-573018541838566150.gps.gpsil [style*="--o:"]{order:var(--o)}.gps-573018541838566150.gps.gpsil [style*="--pc:"]{place-content:var(--pc)}.gps-573018541838566150.gps.gpsil [style*="--p:"]{padding:var(--p)}.gps-573018541838566150.gps.gpsil [style*="--pb:"]{padding-bottom:var(--pb)}.gps-573018541838566150.gps.gpsil [style*="--pl:"]{padding-left:var(--pl)}.gps-573018541838566150.gps.gpsil [style*="--pr:"]{padding-right:var(--pr)}.gps-573018541838566150.gps.gpsil [style*="--pt:"]{padding-top:var(--pt)}.gps-573018541838566150.gps.gpsil [style*="--pos:"]{position:var(--pos)}.gps-573018541838566150.gps.gpsil [style*="--rg:"]{row-gap:var(--rg)}.gps-573018541838566150.gps.gpsil [style*="--ta:"]{text-align:var(--ta)}.gps-573018541838566150.gps.gpsil [style*="--tt:"]{text-transform:var(--tt)}.gps-573018541838566150.gps.gpsil [style*="--top:"]{top:var(--top)}.gps-573018541838566150.gps.gpsil [style*="--t:"]{transform:var(--t)}.gps-573018541838566150.gps.gpsil [style*="--w:"]{width:var(--w)}@media only screen and (max-width:1024px){.gps-573018541838566150.gps.gpsil [style*="--aspect-tablet:"]{aspect-ratio:var(--aspect-tablet)}.gps-573018541838566150.gps.gpsil [style*="--bottom-tablet:"]{bottom:var(--bottom-tablet)}.gps-573018541838566150.gps.gpsil [style*="--cg-tablet:"]{-moz-column-gap:var(--cg-tablet);column-gap:var(--cg-tablet)}.gps-573018541838566150.gps.gpsil [style*="--size-tablet:"]{font-size:var(--size-tablet)}.gps-573018541838566150.gps.gpsil [style*="--gtc-tablet:"]{grid-template-columns:var(--gtc-tablet)}.gps-573018541838566150.gps.gpsil [style*="--h-tablet:"]{height:var(--h-tablet)}.gps-573018541838566150.gps.gpsil [style*="--jc-tablet:"]{justify-content:var(--jc-tablet)}.gps-573018541838566150.gps.gpsil [style*="--left-tablet:"]{left:var(--left-tablet)}.gps-573018541838566150.gps.gpsil [style*="--lh-tablet:"]{line-height:var(--lh-tablet)}.gps-573018541838566150.gps.gpsil [style*="--maxw-tablet:"]{max-width:var(--maxw-tablet)}.gps-573018541838566150.gps.gpsil [style*="--o-tablet:"]{order:var(--o-tablet)}.gps-573018541838566150.gps.gpsil [style*="--pb-tablet:"]{padding-bottom:var(--pb-tablet)}.gps-573018541838566150.gps.gpsil [style*="--pl-tablet:"]{padding-left:var(--pl-tablet)}.gps-573018541838566150.gps.gpsil [style*="--pr-tablet:"]{padding-right:var(--pr-tablet)}.gps-573018541838566150.gps.gpsil [style*="--pt-tablet:"]{padding-top:var(--pt-tablet)}.gps-573018541838566150.gps.gpsil [style*="--pos-tablet:"]{position:var(--pos-tablet)}.gps-573018541838566150.gps.gpsil [style*="--top-tablet:"]{top:var(--top-tablet)}.gps-573018541838566150.gps.gpsil [style*="--t-tablet:"]{transform:var(--t-tablet)}.gps-573018541838566150.gps.gpsil [style*="--w-tablet:"]{width:var(--w-tablet)}}@media only screen and (max-width:767px){.gps-573018541838566150.gps.gpsil [style*="--aspect-mobile:"]{aspect-ratio:var(--aspect-mobile)}.gps-573018541838566150.gps.gpsil [style*="--bottom-mobile:"]{bottom:var(--bottom-mobile)}.gps-573018541838566150.gps.gpsil [style*="--size-mobile:"]{font-size:var(--size-mobile)}.gps-573018541838566150.gps.gpsil [style*="--gtc-mobile:"]{grid-template-columns:var(--gtc-mobile)}.gps-573018541838566150.gps.gpsil [style*="--h-mobile:"]{height:var(--h-mobile)}.gps-573018541838566150.gps.gpsil [style*="--jc-mobile:"]{justify-content:var(--jc-mobile)}.gps-573018541838566150.gps.gpsil [style*="--left-mobile:"]{left:var(--left-mobile)}.gps-573018541838566150.gps.gpsil [style*="--lh-mobile:"]{line-height:var(--lh-mobile)}.gps-573018541838566150.gps.gpsil [style*="--mb-mobile:"]{margin-bottom:var(--mb-mobile)}.gps-573018541838566150.gps.gpsil [style*="--mt-mobile:"]{margin-top:var(--mt-mobile)}.gps-573018541838566150.gps.gpsil [style*="--maxw-mobile:"]{max-width:var(--maxw-mobile)}.gps-573018541838566150.gps.gpsil [style*="--o-mobile:"]{order:var(--o-mobile)}.gps-573018541838566150.gps.gpsil [style*="--pc-mobile:"]{place-content:var(--pc-mobile)}.gps-573018541838566150.gps.gpsil [style*="--pb-mobile:"]{padding-bottom:var(--pb-mobile)}.gps-573018541838566150.gps.gpsil [style*="--pl-mobile:"]{padding-left:var(--pl-mobile)}.gps-573018541838566150.gps.gpsil [style*="--pr-mobile:"]{padding-right:var(--pr-mobile)}.gps-573018541838566150.gps.gpsil [style*="--pt-mobile:"]{padding-top:var(--pt-mobile)}.gps-573018541838566150.gps.gpsil [style*="--pos-mobile:"]{position:var(--pos-mobile)}.gps-573018541838566150.gps.gpsil [style*="--rg-mobile:"]{row-gap:var(--rg-mobile)}.gps-573018541838566150.gps.gpsil [style*="--top-mobile:"]{top:var(--top-mobile)}.gps-573018541838566150.gps.gpsil [style*="--t-mobile:"]{transform:var(--t-mobile)}.gps-573018541838566150.gps.gpsil [style*="--w-mobile:"]{width:var(--w-mobile)}}.gps-573018541838566150 .-gp-translate-x-1\/2,.gps-573018541838566150 .-gp-translate-y-1\/2{--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1}.gps-573018541838566150 .gp-shadow-none{--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000}.gps-573018541838566150 .gp-invisible{visibility:hidden}.gps-573018541838566150 .gp-absolute{position:absolute}.gps-573018541838566150 .gp-relative{position:relative}.gps-573018541838566150 .gp-sticky{position:sticky}.gps-573018541838566150 .gp-left-0{left:0}.gps-573018541838566150 .gp-left-1\/2{left:50%}.gps-573018541838566150 .gp-top-0{top:0}.gps-573018541838566150 .gp-top-1\/2{top:50%}.gps-573018541838566150 .gp-z-0{z-index:0}.gps-573018541838566150 .gp-z-1{z-index:1}.gps-573018541838566150 .gp-z-\[90\]{z-index:90}.gps-573018541838566150 .\!gp-m-0{margin:0!important}.gps-573018541838566150 .gp-mx-auto{margin-left:auto;margin-right:auto}.gps-573018541838566150 .\!gp-ml-0{margin-left:0!important}.gps-573018541838566150 .gp-mb-0{margin-bottom:0}.gps-573018541838566150 .gp-block{display:block}.gps-573018541838566150 .gp-flex{display:flex}.gps-573018541838566150 .gp-inline-flex{display:inline-flex}.gps-573018541838566150 .gp-grid{display:grid}.gps-573018541838566150 .\!gp-hidden{display:none!important}.gps-573018541838566150 .gp-hidden{display:none}.gps-573018541838566150 .gp-aspect-\[56\/32\]{aspect-ratio:56/32}.gps-573018541838566150 .gp-aspect-square{aspect-ratio:1/1}.gps-573018541838566150 .gp-h-0{height:0}.gps-573018541838566150 .gp-h-5{height:20px}.gps-573018541838566150 .gp-h-auto{height:auto}.gps-573018541838566150 .gp-h-full{height:100%}.gps-573018541838566150 .\!gp-w-full{width:100%!important}.gps-573018541838566150 .gp-w-14{width:56px}.gps-573018541838566150 .gp-w-5{width:20px}.gps-573018541838566150 .gp-w-full{width:100%}.gps-573018541838566150 .gp-min-w-\[45px\]{min-width:45px}.gps-573018541838566150 .\!gp-max-w-full{max-width:100%!important}.gps-573018541838566150 .gp-max-w-full{max-width:100%}.gps-573018541838566150 .gp-shrink-\[99999\]{flex-shrink:99999}.gps-573018541838566150 .-gp-translate-x-1\/2{--tw-translate-x:-50%}.gps-573018541838566150 .-gp-translate-x-1\/2,.gps-573018541838566150 .-gp-translate-y-1\/2{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.gps-573018541838566150 .-gp-translate-y-1\/2{--tw-translate-y:-50%}.gps-573018541838566150 .gp-cursor-default{cursor:default}.gps-573018541838566150 .gp-cursor-pointer{cursor:pointer}.gps-573018541838566150 .gp-appearance-none{-webkit-appearance:none;-moz-appearance:none;appearance:none}.gps-573018541838566150 .gp-grid-rows-\[1fr\]{grid-template-rows:1fr}.gps-573018541838566150 .gp-flex-col{flex-direction:column}.gps-573018541838566150 .gp-flex-wrap{flex-wrap:wrap}.gps-573018541838566150 .gp-items-start{align-items:flex-start}.gps-573018541838566150 .gp-items-end{align-items:flex-end}.gps-573018541838566150 .gp-items-center{align-items:center}.gps-573018541838566150 .gp-justify-start{justify-content:flex-start}.gps-573018541838566150 .gp-justify-center{justify-content:center}.gps-573018541838566150 .gp-gap-3{gap:12px}.gps-573018541838566150 .gp-gap-y-0{row-gap:0}.gps-573018541838566150 .gp-overflow-hidden{overflow:hidden}.gps-573018541838566150 .gp-truncate{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.gps-573018541838566150 .gp-break-words{overflow-wrap:break-word}.gps-573018541838566150 .\!gp-rounded-none{border-radius:0!important}.gps-573018541838566150 .gp-rounded{border-radius:4px}.gps-573018541838566150 .gp-rounded-none{border-radius:0}.gps-573018541838566150 .\!gp-border-x-0{border-left-width:0!important;border-right-width:0!important}.gps-573018541838566150 .gp-border-y{border-bottom-width:1px;border-top-width:1px}.gps-573018541838566150 .gp-border-g-line-1{border-color:var(--g-c-line-1)}.gps-573018541838566150 .gp-border-g-line-2{border-color:var(--g-c-line-2)}.gps-573018541838566150 .gp-bg-black\/80{background-color:rgba(0,0,0,.8)}.gps-573018541838566150 .gp-bg-g-bg-2{background-color:var(--g-c-bg-2)}.gps-573018541838566150 .gp-bg-g-bg-3{background-color:var(--g-c-bg-3)}.gps-573018541838566150 .gp-bg-transparent{background-color:transparent}.gps-573018541838566150 .gp-bg-auto{background-size:auto}.gps-573018541838566150 .gp-object-cover{-o-object-fit:cover;object-fit:cover}.gps-573018541838566150 .gp-px-4{padding-left:16px;padding-right:16px}.gps-573018541838566150 .\!gp-pb-0{padding-bottom:0!important}.gps-573018541838566150 .gp-pl-4{padding-left:16px}.gps-573018541838566150 .gp-pr-6{padding-right:24px}.gps-573018541838566150 .gp-text-center{text-align:center}.gps-573018541838566150 .gp-text-g-text-2{color:var(--g-c-text-2)}.gps-573018541838566150 .gp-text-g-text-3{color:var(--g-c-text-3)}.gps-573018541838566150 .gp-text-white{--tw-text-opacity:1;color:rgb(255 255 255/var(--tw-text-opacity))}.gps-573018541838566150 .gp-line-through{text-decoration-line:line-through}.gps-573018541838566150 .gp-no-underline{text-decoration-line:none}.gps-573018541838566150 .gp-decoration-g-text-1{text-decoration-color:var(--g-c-text-1)}.gps-573018541838566150 .gp-opacity-25{opacity:.25}.gps-573018541838566150 .gp-opacity-30{opacity:.3}.gps-573018541838566150 .gp-opacity-75{opacity:.75}.gps-573018541838566150 .gp-shadow-none{--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)}.gps-573018541838566150 .gp-outline-none{outline:2px solid transparent;outline-offset:2px}.gps-573018541838566150 .gp-transition-all{transition-duration:.15s;transition-property:all;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-573018541838566150 .gp-transition-colors{transition-duration:.15s;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-573018541838566150 .gp-transition-opacity{transition-duration:.15s;transition-property:opacity;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-573018541838566150 .gp-duration-150{transition-duration:.15s}.gps-573018541838566150 .gp-duration-200{transition-duration:.2s}.gps-573018541838566150 .gp-duration-300{transition-duration:.3s}.gps-573018541838566150 .gp-ease-in-out{transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-573018541838566150 .disabled\:gp-btn-disabled:disabled{cursor:default}@media (hover:hover) and (pointer:fine){.gps-573018541838566150 .hover\:gp-border-g-line-3:hover{border-color:var(--g-c-line-3)}.gps-573018541838566150 .hover\:gp-bg-\[\#ef0800\]:hover{--tw-bg-opacity:1;background-color:rgb(239 8 0/var(--tw-bg-opacity))}.gps-573018541838566150 .hover\:gp-bg-g-bg-3:hover{background-color:var(--g-c-bg-3)}.gps-573018541838566150 .hover\:gp-text-black:hover{--tw-text-opacity:1;color:rgb(0 0 0/var(--tw-text-opacity))}.gps-573018541838566150 .hover\:gp-text-g-text-2:hover{color:var(--g-c-text-2)}}.gps-573018541838566150 .active\:gp-bg-g-bg-3:active{background-color:var(--g-c-bg-3)}.gps-573018541838566150 .active\:gp-text-g-text-2:active{color:var(--g-c-text-2)}.gps-573018541838566150 .disabled\:gp-pointer-events-none:disabled{pointer-events:none}.gps-573018541838566150 .disabled\:gp-cursor-not-allowed:disabled{cursor:not-allowed}.gps-573018541838566150 .disabled\:gp-opacity-30:disabled{opacity:.3}@media (hover:hover) and (pointer:fine){.gps-573018541838566150 .gp-group\/button:hover .group-hover\/button\:\!gp-text-inherit{color:inherit!important}}.gps-573018541838566150 .gp-group\/button:active .group-active\/button\:\!gp-text-inherit{color:inherit!important}.gps-573018541838566150 .data-\[disabled\=true\]\:gp-hidden[data-disabled=true],.gps-573018541838566150 .data-\[hidden\=true\]\:gp-hidden[data-hidden=true]{display:none}.gps-573018541838566150 .data-\[disabled\=true\]\:gp-opacity-60[data-disabled=true]{opacity:.6}.gps-573018541838566150 .gp-group\/button[data-state=loading] .group-data-\[state\=loading\]\/button\:gp-visible{visibility:visible}.gps-573018541838566150 .gp-group[data-state=loading] .group-data-\[state\=loading\]\:gp-invisible,.gps-573018541838566150 .gp-group\/button[data-state=loading] .group-data-\[state\=loading\]\/button\:gp-invisible{visibility:hidden}@keyframes gp-spin{to{transform:rotate(1turn)}}.gps-573018541838566150 .gp-group\/button[data-state=loading] .group-data-\[state\=loading\]\/button\:gp-animate-spin{animation:gp-spin 1s linear infinite}@media (max-width:1024px){.gps-573018541838566150 .tablet\:\!gp-hidden{display:none!important}.gps-573018541838566150 .tablet\:gp-hidden{display:none}}@media (max-width:767px){.gps-573018541838566150 .mobile\:\!gp-hidden{display:none!important}.gps-573018541838566150 .mobile\:gp-hidden{display:none}}.gps-573018541838566150 .\[\&_\*\]\:gp-max-w-full *{max-width:100%}.gps-573018541838566150 .\[\&_p\]\:gp-inline p{display:inline}.gps-573018541838566150 .\[\&_p\]\:gp-whitespace-pre-line p{white-space:pre-line}.gps-573018541838566150 .\[\&_p\]\:after\:gp-whitespace-pre-wrap p:after{content:var(--tw-content);white-space:pre-wrap}.gps-573018541838566150 .\[\&_p\]\:after\:gp-content-\[\'\\A\'\] p:after{--tw-content:"\A";content:var(--tw-content)}</style>

    
    <gp-sticky
    data-id="gCTsNu3AN4"
      gp-data='{"uid":"gCTsNu3AN4","setting":{"display":{"desktop":"after-first-cart-button"}},"advanced":{"d":{"desktop":true,"mobile":true,"tablet":true}}}'
      id="gCTsNu3AN4"
      data-id="gCTsNu3AN4"
      class="gCTsNu3AN4 {% if section.settings.section_preload == "false" %}gps-lazy {% endif %}  "
      style="margin:0 auto;--bs:solid;--bw:0px;--bc:#121212;--shadow:0px 0px 10px 0px #0000001a;--d:none;--d-mobile:none;--d-tablet:none;--op:100%;--pt:var(--g-s-m);--pl:15px;--pb:var(--g-s-m);--pr:15px;--left:50%;--t:translateX(-50%);--left-tablet:50%;--t-tablet:translateX(-50%);--left-mobile:50%;--t-mobile:translateX(-50%);--top:auto;--bottom:0;--pos:fixed;--top-tablet:auto;--bottom-tablet:0;--pos-tablet:fixed;--top-mobile:auto;--bottom-mobile:0;--pos-mobile:fixed;--w:100%;--w-tablet:100%;--w-mobile:100%;z-index:100000"
    >
      <div 
         
        style="--bgc:#FBFBFB;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
      >
        <div
      
      
      class="gtKvviobhy gp-relative gp-flex gp-flex-col"
    >
      {% render 'gp-section-573018541838566150-0', product: product, variant: variant, product_form_id: product_form_id, productSelectedVariant: productSelectedVariant, form: form  %}
    </div>
      </div>
    </gp-sticky>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-sticky.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  


{% schema %}
  {
    
    "name": "Section 18",
    "tag": "section",
    "class": "gps-573018541838566150 gps gpsil",
    "settings": [{"type":"paragraph","content":"Section design by GemPages. [Click here to start design](https://admin.shopify.com/store/i0pixe-10/apps/gempages-cro/app/shopify/edit?pageType=GP_PRODUCT&editorId=572751048691811510&sectionId=573018541838566150)"},{"type":"select","label":"Section Preload","id":"section_preload","options":[{"label":"On","value":"true"},{"label":"Off","value":"false"},{"label":"Off Lazy Script","value":"off_lazy_script"}],"default":"false"},{"type":"text","label":"Checksum","id":"checksum"},{"type":"html","id":"ggauyiDElGt_customContent_prefix","label":"ggauyiDElGt_customContent_prefix","default":"-"},{"type":"html","id":"ggauyiDElGt_customContent_suffix","label":"ggauyiDElGt_customContent_suffix","default":"off"},{"type":"html","id":"gg9w2uvXmgR_label","label":"gg9w2uvXmgR_label","default":"Add to Cart"},{"type":"html","id":"gg9w2uvXmgR_outOfStockLabel","label":"gg9w2uvXmgR_outOfStockLabel","default":"Out of stock"},{"type":"html","id":"gg9w2uvXmgR_unavailableLabel","label":"gg9w2uvXmgR_unavailableLabel","default":"Unavailable"}],
    "blocks": [{"type": "@app"}],
    "locales": {}
  }
{% endschema %}
