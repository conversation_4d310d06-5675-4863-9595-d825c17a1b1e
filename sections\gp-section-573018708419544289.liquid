

<style {% if section.settings.section_preload == "false" %} class="gps-link gps-style" type="text/disabled-css" {% endif %}>.gps-573018708419544289.gps.gpsil [style*="--ai:"]{align-items:var(--ai)}.gps-573018708419544289.gps.gpsil [style*="--aspect:"]{aspect-ratio:var(--aspect)}.gps-573018708419544289.gps.gpsil [style*="--bg:"]{background:var(--bg)}.gps-573018708419544289.gps.gpsil [style*="--hvr-bg:"]:hover{background:var(--hvr-bg)}.gps-573018708419544289.gps.gpsil [style*="--bga:"]{background-attachment:var(--bga)}.gps-573018708419544289.gps.gpsil [style*="--bgc:"]{background-color:var(--bgc)}.gps-573018708419544289.gps.gpsil [style*="--bgi:"]{background-image:var(--bgi)}.gps-573018708419544289.gps.gpsil [style*="--bgp:"]{background-position:var(--bgp)}.gps-573018708419544289.gps.gpsil [style*="--bgr:"]{background-repeat:var(--bgr)}.gps-573018708419544289.gps.gpsil [style*="--bgs:"]{background-size:var(--bgs)}.gps-573018708419544289.gps.gpsil [style*="--b:"]{border:var(--b)}.gps-573018708419544289.gps.gpsil [style*="--hvr-b:"]:hover{border:var(--hvr-b)}.gps-573018708419544289.gps.gpsil [style*="--bb:"]{border-bottom:var(--bb)}.gps-573018708419544289.gps.gpsil [style*="--hvr-bb:"]:hover{border-bottom:var(--hvr-bb)}.gps-573018708419544289.gps.gpsil [style*="--bc:"]{border-color:var(--bc)}.gps-573018708419544289.gps.gpsil [style*="--bblr:"]{border-bottom-left-radius:var(--bblr)}.gps-573018708419544289.gps.gpsil [style*="--hvr-bblr:"]:hover{border-bottom-left-radius:var(--hvr-bblr)}.gps-573018708419544289.gps.gpsil [style*="--bbrr:"]{border-bottom-right-radius:var(--bbrr)}.gps-573018708419544289.gps.gpsil [style*="--hvr-bbrr:"]:hover{border-bottom-right-radius:var(--hvr-bbrr)}.gps-573018708419544289.gps.gpsil [style*="--radius:"]{border-radius:var(--radius)}.gps-573018708419544289.gps.gpsil [style*="--bs:"]{border-style:var(--bs)}.gps-573018708419544289.gps.gpsil [style*="--bt:"]{border-top:var(--bt)}.gps-573018708419544289.gps.gpsil [style*="--hvr-bt:"]:hover{border-top:var(--hvr-bt)}.gps-573018708419544289.gps.gpsil [style*="--btlr:"]{border-top-left-radius:var(--btlr)}.gps-573018708419544289.gps.gpsil [style*="--hvr-btlr:"]:hover{border-top-left-radius:var(--hvr-btlr)}.gps-573018708419544289.gps.gpsil [style*="--btrr:"]{border-top-right-radius:var(--btrr)}.gps-573018708419544289.gps.gpsil [style*="--hvr-btrr:"]:hover{border-top-right-radius:var(--hvr-btrr)}.gps-573018708419544289.gps.gpsil [style*="--bw:"]{border-width:var(--bw)}.gps-573018708419544289.gps.gpsil [style*="--bottom:"]{bottom:var(--bottom)}.gps-573018708419544289.gps.gpsil [style*="--shadow:"]{box-shadow:var(--shadow)}.gps-573018708419544289.gps.gpsil [style*="--c:"]{color:var(--c)}.gps-573018708419544289.gps.gpsil [style*="--cg:"]{-moz-column-gap:var(--cg);column-gap:var(--cg)}.gps-573018708419544289.gps.gpsil [style*="--ff:"]{font-family:var(--ff)}.gps-573018708419544289.gps.gpsil [style*="--size:"]{font-size:var(--size)}.gps-573018708419544289.gps.gpsil [style*="--weight:"]{font-weight:var(--weight)}.gps-573018708419544289.gps.gpsil [style*="--fs:"]{font-style:var(--fs)}.gps-573018708419544289.gps.gpsil [style*="--gg:"]{grid-gap:var(--gg)}.gps-573018708419544289.gps.gpsil [style*="--gtc:"]{grid-template-columns:var(--gtc)}.gps-573018708419544289.gps.gpsil [style*="--h:"]{height:var(--h)}.gps-573018708419544289.gps.gpsil [style*="--jc:"]{justify-content:var(--jc)}.gps-573018708419544289.gps.gpsil [style*="--left:"]{left:var(--left)}.gps-573018708419544289.gps.gpsil [style*="--ls:"]{letter-spacing:var(--ls)}.gps-573018708419544289.gps.gpsil [style*="--lh:"]{line-height:var(--lh)}.gps-573018708419544289.gps.gpsil [style*="--tdt:"]{text-decoration-thickness:var(--tdt)}.gps-573018708419544289.gps.gpsil [style*="--tdc:"]{text-decoration-color:var(--tdc)}.gps-573018708419544289.gps.gpsil [style*="--m:"]{margin:var(--m)}.gps-573018708419544289.gps.gpsil [style*="--mb:"]{margin-bottom:var(--mb)}.gps-573018708419544289.gps.gpsil [style*="--mt:"]{margin-top:var(--mt)}.gps-573018708419544289.gps.gpsil [style*="--maxw:"]{max-width:var(--maxw)}.gps-573018708419544289.gps.gpsil [style*="--objf:"]{-o-object-fit:var(--objf);object-fit:var(--objf)}.gps-573018708419544289.gps.gpsil [style*="--op:"]{opacity:var(--op)}.gps-573018708419544289.gps.gpsil [style*="--o:"]{order:var(--o)}.gps-573018708419544289.gps.gpsil [style*="--pc:"]{place-content:var(--pc)}.gps-573018708419544289.gps.gpsil [style*="--p:"]{padding:var(--p)}.gps-573018708419544289.gps.gpsil [style*="--pb:"]{padding-bottom:var(--pb)}.gps-573018708419544289.gps.gpsil [style*="--pl:"]{padding-left:var(--pl)}.gps-573018708419544289.gps.gpsil [style*="--pr:"]{padding-right:var(--pr)}.gps-573018708419544289.gps.gpsil [style*="--pt:"]{padding-top:var(--pt)}.gps-573018708419544289.gps.gpsil [style*="--pos:"]{position:var(--pos)}.gps-573018708419544289.gps.gpsil [style*="--ta:"]{text-align:var(--ta)}.gps-573018708419544289.gps.gpsil [style*="--tt:"]{text-transform:var(--tt)}.gps-573018708419544289.gps.gpsil [style*="--top:"]{top:var(--top)}.gps-573018708419544289.gps.gpsil [style*="--t:"]{transform:var(--t)}.gps-573018708419544289.gps.gpsil [style*="--w:"]{width:var(--w)}.gps-573018708419544289.gps.gpsil [style*="--line-clamp:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp);display:-webkit-box;overflow:hidden}@media only screen and (max-width:1024px){.gps-573018708419544289.gps.gpsil [style*="--aspect-tablet:"]{aspect-ratio:var(--aspect-tablet)}.gps-573018708419544289.gps.gpsil [style*="--bottom-tablet:"]{bottom:var(--bottom-tablet)}.gps-573018708419544289.gps.gpsil [style*="--cg-tablet:"]{-moz-column-gap:var(--cg-tablet);column-gap:var(--cg-tablet)}.gps-573018708419544289.gps.gpsil [style*="--size-tablet:"]{font-size:var(--size-tablet)}.gps-573018708419544289.gps.gpsil [style*="--gtc-tablet:"]{grid-template-columns:var(--gtc-tablet)}.gps-573018708419544289.gps.gpsil [style*="--h-tablet:"]{height:var(--h-tablet)}.gps-573018708419544289.gps.gpsil [style*="--jc-tablet:"]{justify-content:var(--jc-tablet)}.gps-573018708419544289.gps.gpsil [style*="--left-tablet:"]{left:var(--left-tablet)}.gps-573018708419544289.gps.gpsil [style*="--lh-tablet:"]{line-height:var(--lh-tablet)}.gps-573018708419544289.gps.gpsil [style*="--maxw-tablet:"]{max-width:var(--maxw-tablet)}.gps-573018708419544289.gps.gpsil [style*="--o-tablet:"]{order:var(--o-tablet)}.gps-573018708419544289.gps.gpsil [style*="--pb-tablet:"]{padding-bottom:var(--pb-tablet)}.gps-573018708419544289.gps.gpsil [style*="--pl-tablet:"]{padding-left:var(--pl-tablet)}.gps-573018708419544289.gps.gpsil [style*="--pr-tablet:"]{padding-right:var(--pr-tablet)}.gps-573018708419544289.gps.gpsil [style*="--pt-tablet:"]{padding-top:var(--pt-tablet)}.gps-573018708419544289.gps.gpsil [style*="--pos-tablet:"]{position:var(--pos-tablet)}.gps-573018708419544289.gps.gpsil [style*="--top-tablet:"]{top:var(--top-tablet)}.gps-573018708419544289.gps.gpsil [style*="--t-tablet:"]{transform:var(--t-tablet)}.gps-573018708419544289.gps.gpsil [style*="--w-tablet:"]{width:var(--w-tablet)}.gps-573018708419544289.gps.gpsil [style*="--line-clamp-tablet:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-tablet);display:-webkit-box;overflow:hidden}}@media only screen and (max-width:767px){.gps-573018708419544289.gps.gpsil [style*="--aspect-mobile:"]{aspect-ratio:var(--aspect-mobile)}.gps-573018708419544289.gps.gpsil [style*="--bottom-mobile:"]{bottom:var(--bottom-mobile)}.gps-573018708419544289.gps.gpsil [style*="--size-mobile:"]{font-size:var(--size-mobile)}.gps-573018708419544289.gps.gpsil [style*="--gtc-mobile:"]{grid-template-columns:var(--gtc-mobile)}.gps-573018708419544289.gps.gpsil [style*="--h-mobile:"]{height:var(--h-mobile)}.gps-573018708419544289.gps.gpsil [style*="--jc-mobile:"]{justify-content:var(--jc-mobile)}.gps-573018708419544289.gps.gpsil [style*="--left-mobile:"]{left:var(--left-mobile)}.gps-573018708419544289.gps.gpsil [style*="--lh-mobile:"]{line-height:var(--lh-mobile)}.gps-573018708419544289.gps.gpsil [style*="--mb-mobile:"]{margin-bottom:var(--mb-mobile)}.gps-573018708419544289.gps.gpsil [style*="--mt-mobile:"]{margin-top:var(--mt-mobile)}.gps-573018708419544289.gps.gpsil [style*="--maxw-mobile:"]{max-width:var(--maxw-mobile)}.gps-573018708419544289.gps.gpsil [style*="--o-mobile:"]{order:var(--o-mobile)}.gps-573018708419544289.gps.gpsil [style*="--pc-mobile:"]{place-content:var(--pc-mobile)}.gps-573018708419544289.gps.gpsil [style*="--pb-mobile:"]{padding-bottom:var(--pb-mobile)}.gps-573018708419544289.gps.gpsil [style*="--pl-mobile:"]{padding-left:var(--pl-mobile)}.gps-573018708419544289.gps.gpsil [style*="--pr-mobile:"]{padding-right:var(--pr-mobile)}.gps-573018708419544289.gps.gpsil [style*="--pt-mobile:"]{padding-top:var(--pt-mobile)}.gps-573018708419544289.gps.gpsil [style*="--pos-mobile:"]{position:var(--pos-mobile)}.gps-573018708419544289.gps.gpsil [style*="--top-mobile:"]{top:var(--top-mobile)}.gps-573018708419544289.gps.gpsil [style*="--t-mobile:"]{transform:var(--t-mobile)}.gps-573018708419544289.gps.gpsil [style*="--w-mobile:"]{width:var(--w-mobile)}.gps-573018708419544289.gps.gpsil [style*="--line-clamp-mobile:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-mobile);display:-webkit-box;overflow:hidden}}.gps-573018708419544289 .-gp-translate-x-1\/2,.gps-573018708419544289 .-gp-translate-y-1\/2{--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1}.gps-573018708419544289 .gp-shadow-none{--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000}.gps-573018708419544289 .gp-g-s-small{padding:4px 16px}.gps-573018708419544289 .gp-invisible{visibility:hidden}.gps-573018708419544289 .gp-absolute{position:absolute}.gps-573018708419544289 .gp-relative{position:relative}.gps-573018708419544289 .gp-sticky{position:sticky}.gps-573018708419544289 .gp-inset-0{inset:0}.gps-573018708419544289 .gp-left-0{left:0}.gps-573018708419544289 .gp-left-1\/2{left:50%}.gps-573018708419544289 .gp-top-0{top:0}.gps-573018708419544289 .gp-top-1\/2{top:50%}.gps-573018708419544289 .gp-z-0{z-index:0}.gps-573018708419544289 .gp-z-1{z-index:1}.gps-573018708419544289 .gp-z-\[90\]{z-index:90}.gps-573018708419544289 .\!gp-m-0{margin:0!important}.gps-573018708419544289 .gp-mx-auto{margin-left:auto;margin-right:auto}.gps-573018708419544289 .gp-mb-0{margin-bottom:0}.gps-573018708419544289 .gp-block{display:block}.gps-573018708419544289 .gp-flex{display:flex}.gps-573018708419544289 .gp-inline-flex{display:inline-flex}.gps-573018708419544289 .gp-grid{display:grid}.gps-573018708419544289 .\!gp-hidden{display:none!important}.gps-573018708419544289 .gp-hidden{display:none}.gps-573018708419544289 .gp-aspect-\[56\/32\]{aspect-ratio:56/32}.gps-573018708419544289 .gp-aspect-square{aspect-ratio:1/1}.gps-573018708419544289 .gp-h-0{height:0}.gps-573018708419544289 .gp-h-5{height:20px}.gps-573018708419544289 .gp-h-auto{height:auto}.gps-573018708419544289 .gp-h-full{height:100%}.gps-573018708419544289 .\!gp-w-full{width:100%!important}.gps-573018708419544289 .gp-w-14{width:56px}.gps-573018708419544289 .gp-w-5{width:20px}.gps-573018708419544289 .gp-w-full{width:100%}.gps-573018708419544289 .gp-w-max{width:-moz-max-content;width:max-content}.gps-573018708419544289 .gp-min-w-\[45px\]{min-width:45px}.gps-573018708419544289 .gp-min-w-max{min-width:-moz-max-content;min-width:max-content}.gps-573018708419544289 .\!gp-max-w-full{max-width:100%!important}.gps-573018708419544289 .\!gp-max-w-max{max-width:-moz-max-content!important;max-width:max-content!important}.gps-573018708419544289 .gp-max-w-full{max-width:100%}.gps-573018708419544289 .gp-flex-1{flex:1 1 0%}.gps-573018708419544289 .gp-shrink-\[99999\]{flex-shrink:99999}.gps-573018708419544289 .-gp-translate-x-1\/2{--tw-translate-x:-50%}.gps-573018708419544289 .-gp-translate-x-1\/2,.gps-573018708419544289 .-gp-translate-y-1\/2{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.gps-573018708419544289 .-gp-translate-y-1\/2{--tw-translate-y:-50%}.gps-573018708419544289 .gp-cursor-default{cursor:default}.gps-573018708419544289 .gp-cursor-pointer{cursor:pointer}.gps-573018708419544289 .gp-appearance-none{-webkit-appearance:none;-moz-appearance:none;appearance:none}.gps-573018708419544289 .gp-grid-rows-\[1fr\]{grid-template-rows:1fr}.gps-573018708419544289 .gp-flex-col{flex-direction:column}.gps-573018708419544289 .gp-flex-wrap{flex-wrap:wrap}.gps-573018708419544289 .gp-items-start{align-items:flex-start}.gps-573018708419544289 .gp-items-end{align-items:flex-end}.gps-573018708419544289 .gp-items-center{align-items:center}.gps-573018708419544289 .gp-justify-center{justify-content:center}.gps-573018708419544289 .gp-gap-3{gap:12px}.gps-573018708419544289 .gp-gap-y-0{row-gap:0}.gps-573018708419544289 .gp-overflow-hidden{overflow:hidden}.gps-573018708419544289 .gp-break-words{overflow-wrap:break-word}.gps-573018708419544289 .\!gp-rounded-none{border-radius:0!important}.gps-573018708419544289 .gp-rounded{border-radius:4px}.gps-573018708419544289 .gp-rounded-none{border-radius:0}.gps-573018708419544289 .\!gp-border-x-0{border-left-width:0!important;border-right-width:0!important}.gps-573018708419544289 .gp-border-y{border-bottom-width:1px;border-top-width:1px}.gps-573018708419544289 .gp-border-g-line-1{border-color:var(--g-c-line-1)}.gps-573018708419544289 .gp-bg-black\/80{background-color:rgba(0,0,0,.8)}.gps-573018708419544289 .gp-bg-g-bg-2{background-color:var(--g-c-bg-2)}.gps-573018708419544289 .gp-bg-g-bg-3{background-color:var(--g-c-bg-3)}.gps-573018708419544289 .gp-bg-transparent{background-color:transparent}.gps-573018708419544289 .gp-object-cover{-o-object-fit:cover;object-fit:cover}.gps-573018708419544289 .gp-px-4{padding-left:16px;padding-right:16px}.gps-573018708419544289 .\!gp-pb-0{padding-bottom:0!important}.gps-573018708419544289 .gp-text-center{text-align:center}.gps-573018708419544289 .gp-text-g-text-2{color:var(--g-c-text-2)}.gps-573018708419544289 .gp-text-g-text-3{color:var(--g-c-text-3)}.gps-573018708419544289 .gp-text-white{--tw-text-opacity:1;color:rgb(255 255 255/var(--tw-text-opacity))}.gps-573018708419544289 .gp-line-through{text-decoration-line:line-through}.gps-573018708419544289 .gp-no-underline{text-decoration-line:none}.gps-573018708419544289 .gp-decoration-g-text-1{text-decoration-color:var(--g-c-text-1)}.gps-573018708419544289 .gp-opacity-25{opacity:.25}.gps-573018708419544289 .gp-opacity-30{opacity:.3}.gps-573018708419544289 .gp-opacity-75{opacity:.75}.gps-573018708419544289 .gp-shadow-none{--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)}.gps-573018708419544289 .gp-outline-none{outline:2px solid transparent;outline-offset:2px}.gps-573018708419544289 .gp-transition-all{transition-duration:.15s;transition-property:all;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-573018708419544289 .gp-transition-colors{transition-duration:.15s;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-573018708419544289 .gp-transition-opacity{transition-duration:.15s;transition-property:opacity;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-573018708419544289 .gp-duration-150{transition-duration:.15s}.gps-573018708419544289 .gp-duration-200{transition-duration:.2s}.gps-573018708419544289 .gp-duration-300{transition-duration:.3s}.gps-573018708419544289 .gp-ease-in-out{transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-573018708419544289 .disabled\:gp-btn-disabled:disabled{cursor:default}@media (hover:hover) and (pointer:fine){.gps-573018708419544289 .hover\:gp-bg-\[\#ef0800\]:hover{--tw-bg-opacity:1;background-color:rgb(239 8 0/var(--tw-bg-opacity))}.gps-573018708419544289 .hover\:gp-text-black:hover{--tw-text-opacity:1;color:rgb(0 0 0/var(--tw-text-opacity))}}.gps-573018708419544289 .disabled\:gp-pointer-events-none:disabled{pointer-events:none}.gps-573018708419544289 .disabled\:gp-cursor-not-allowed:disabled{cursor:not-allowed}.gps-573018708419544289 .disabled\:gp-opacity-30:disabled{opacity:.3}@media (hover:hover) and (pointer:fine){.gps-573018708419544289 .gp-group\/button:hover .group-hover\/button\:\!gp-text-inherit{color:inherit!important}}.gps-573018708419544289 .gp-group\/button:active .group-active\/button\:\!gp-text-inherit{color:inherit!important}.gps-573018708419544289 .data-\[disabled\=true\]\:gp-hidden[data-disabled=true],.gps-573018708419544289 .data-\[hidden\=true\]\:gp-hidden[data-hidden=true]{display:none}.gps-573018708419544289 .data-\[disabled\=true\]\:gp-opacity-60[data-disabled=true]{opacity:.6}.gps-573018708419544289 .gp-group\/button[data-state=loading] .group-data-\[state\=loading\]\/button\:gp-visible{visibility:visible}.gps-573018708419544289 .gp-group[data-state=loading] .group-data-\[state\=loading\]\:gp-invisible,.gps-573018708419544289 .gp-group\/button[data-state=loading] .group-data-\[state\=loading\]\/button\:gp-invisible{visibility:hidden}@keyframes gp-spin{to{transform:rotate(1turn)}}.gps-573018708419544289 .gp-group\/button[data-state=loading] .group-data-\[state\=loading\]\/button\:gp-animate-spin{animation:gp-spin 1s linear infinite}@media (max-width:1024px){.gps-573018708419544289 .tablet\:\!gp-hidden{display:none!important}.gps-573018708419544289 .tablet\:gp-hidden{display:none}}@media (max-width:767px){.gps-573018708419544289 .mobile\:\!gp-hidden{display:none!important}.gps-573018708419544289 .mobile\:gp-hidden{display:none}}.gps-573018708419544289 .\[\&_\*\]\:gp-max-w-full *{max-width:100%}.gps-573018708419544289 .\[\&_p\]\:gp-inline p{display:inline}.gps-573018708419544289 .\[\&_p\]\:gp-whitespace-pre-line p{white-space:pre-line}.gps-573018708419544289 .\[\&_p\]\:after\:gp-whitespace-pre-wrap p:after{content:var(--tw-content);white-space:pre-wrap}.gps-573018708419544289 .\[\&_p\]\:after\:gp-content-\[\'\\A\'\] p:after{--tw-content:"\A";content:var(--tw-content)}</style>

    
    <gp-sticky
    data-id="gWh-eVRzch"
      gp-data='{"uid":"gWh-eVRzch","setting":{"display":{"desktop":"after-first-cart-button"}},"advanced":{"d":{"desktop":true,"mobile":true,"tablet":true}}}'
      id="gWh-eVRzch"
      data-id="gWh-eVRzch"
      class="gWh-eVRzch {% if section.settings.section_preload == "false" %}gps-lazy {% endif %}  "
      style="margin:0 auto;--bs:solid;--bw:0px;--bc:#121212;--shadow:0px 0px 10px 0px #0000001a;--d:none;--d-mobile:none;--d-tablet:none;--op:97%;--pt:var(--g-s-m);--pl:15px;--pb:0px;--pr:15px;--left:50%;--t:translateX(-50%);--left-tablet:50%;--t-tablet:translateX(-50%);--left-mobile:50%;--t-mobile:translateX(-50%);--top:auto;--bottom:0;--pos:fixed;--top-tablet:auto;--bottom-tablet:0;--pos-tablet:fixed;--top-mobile:auto;--bottom-mobile:0;--pos-mobile:fixed;--w:100%;--w-tablet:100%;--w-mobile:100%;z-index:100000"
    >
      <div 
         
        style="--bgc:#FFFFFF;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
      >
        <div
      
      
      class="gpDHZWLZs- gp-relative gp-flex gp-flex-col"
    >
      
    {%- assign gpBkProduct = product -%}
    
        {%- liquid
          if request.page_type != 'product'
            assign product = all_products['']
            assign productId = 'latest' | times: 1
            if product == empty or product == null
              paginate collections.all.products by 100000
                for item in collections.all.products
                  if item.id == productId
                    assign product = item
                  endif
                endfor
              endpaginate
            endif
          endif
        -%}
        

    {%-if product != empty and product != null -%}
      {%- assign initVariantId =  -%}
      {%- assign product_form_id = 'product-form-' | append: "gQy9hRPeUA" -%}
      {%- assign variant = product.selected_or_first_available_variant -%}
      {%- assign productSelectedVariant = product.selected_or_first_available_variant -%}
      {%-if productSelectedVariant == empty or productSelectedVariant == null -%}
        {%- assign productSelectedVariant = product.selected_or_first_available_variant -%}
      {%- endif -%}
      {%-if variant == empty or variant == null -%}
        {%- assign variant = product.selected_or_first_available_variant -%}
      {%- endif -%}
      <gp-product data-uid="gQy9hRPeUA" data-id="gQy9hRPeUA"   gp-context='{"productId": {{ product.id }}, "preSelectedOptionIds": [], "isSyncProduct": "true", "variantSelected": {{ variant | json | escape }}, "inventoryQuantity": {{ variant.inventory_quantity }}, "quantity": 1, "formId": "{{ product_form_id }}" }'
        gp-data='{"variantSelected": {{ variant | json | escape }}, "quantity": 1, "productUrl":{{ product.url | json | escape }}, "productHandle":{{ product.handle | json | escape }}, "collectionUrl": {{ collection.url | json | escape }}, "collectionHandle": {{ collection.handle | json | escape }}}'>
        <product-form class="product-form">
          {%- form 'product', product, id: product_form_id, class: 'form contents', data-type: 'add-to-cart-form', autocomplete: 'off'  -%}
            <input type="hidden" name="id" value="{{ variant.id }}" />
            <input type="hidden" name="quantity" value="{{ quantity }}" />
            <button type="submit" onclick="return false;" style="display:none;"></button>
            
       
      
    <div
      id="gQy9hRPeUA" data-id="gQy9hRPeUA-row"
        style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pt:0px;--pl:0px;--pb:0px;--pr:0px;--pt-mobile:0px;--pl-mobile:0px;--pb-mobile:0px;--pr-mobile:0px;--cg:40px;--cg-tablet:12px;--pc:start;--gtc:minmax(0, 5fr) minmax(0, 7fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:100%;--w-mobile:100%"
        class="gQy9hRPeUA gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:center"
      class="gh9rA7U31F gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      parentTag="Col" id="gy1l-hGZYb" data-id="gy1l-hGZYb"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--radius:var(--g-radius-small);--mt:auto;--mt-mobile:auto;--mb-mobile:var(--g-s-xl);--cg:16px;--pc:start;--gtc:minmax(0, 2fr) minmax(0, 10fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gy1l-hGZYb gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--ai:normal;--jc:center;--o:0"
      class="ggjGy7_jA6 gp-relative gp-flex gp-flex-col"
    >
      
  {% assign featured_image = product.featured_image %}
  {%- if variant != null and variant.featured_image != null -%}
  {% assign featured_image = variant.featured_image %}
  {%- endif -%}
    <gp-product-images-v2
      gp-data='
    {
      "id":"g6dPamx6HZ",
      "pageContext": {"pageType":"GP_PRODUCT","sectionName":"","isPreviewing":false,"isOptimizePlan":false,"isTranslateWithLocale":false,"isLazyLoadSection":true,"isLazyLoadImage":false,"hasCollectionHandle":true,"numberOfProductOfProductList":0,"pageBackground":"","fontType":"google","primaryLocale":"","enableLazyLoadImage":false},
      "setting":{"arrowIcon":"","borderActive":{"borderType":"none","borderWidth":"1px","color":"#000000","isCustom":"false","width":"1px 1px 1px 1px"},"clickOpenLightBox":{"desktop":false,"mobile":false,"tablet":false},"dragToScroll":true,"ftArrowIcon":"<svg width=\"100%\" height=\"100%\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n    <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M12 0C18.6274 0 24 5.37258 24 12C24 18.6274 18.6274 24 12 24C5.37258 24 0 18.6274 0 12C0 5.37258 5.37258 0 12 0ZM10.6828 8.13017C10.5266 7.95661 10.2734 7.95661 10.1172 8.13017C9.96095 8.30374 9.96095 8.58515 10.1172 8.75871L13.0343 12L10.1172 15.2413C9.96095 15.4149 9.96095 15.6963 10.1172 15.8698C10.2734 16.0434 10.5266 16.0434 10.6828 15.8698L13.8828 12.3143C14.0391 12.1407 14.0391 11.8593 13.8828 11.6857L10.6828 8.13017Z\" fill=\"currentColor\"/>\n    </svg>","ftArrowIconColor":"#000000","ftClickOpenLightBox":{"desktop":"none"},"ftDotActiveColor":{"desktop":"line-3"},"ftDotColor":{"desktop":"bg-1"},"ftDotGapToCarousel":{"desktop":16},"ftDotSize":{"desktop":12},"ftDotStyle":{"desktop":"none"},"ftNavigationPosition":{"desktop":"none","mobile":"none","tablet":"none"},"hoverEffect":"none","loop":{"desktop":true},"navigationPosition":{"desktop":"inside","mobile":"inside","tablet":"inside"},"otherImage":0,"pauseOnHover":true,"speed":1,"type":{"desktop":"images","mobile":"images","tablet":"images"},"typeDisplay":"all-images","zoom":1.5,"zoomType":"default"},
      "styles":{"align":{"desktop":"center","mobile":"center","tablet":"center"},"aspectHeight":{"desktop":0},"aspectWidth":{"desktop":0},"dotActiveColor":{"desktop":"brand"},"dotColor":{"desktop":"brand"},"ftAspectHeight":{"desktop":0},"ftAspectWidth":{"desktop":0},"ftLayout":{"desktop":"fill"},"ftShape":{"desktop":{"shape":"original","shapeLinked":true,"width":"56px"},"mobile":{"shape":"original","shapeLinked":true,"width":"56px"},"tablet":{"shape":"original","shapeLinked":true,"width":"56px"}},"height":{"desktop":"100px","mobile":"100px","tablet":"100px"},"itemSpacing":{"desktop":"10px"},"layout":{"desktop":"cover"},"position":{"desktop":"only-feature","mobile":"only-feature","tablet":"only-feature"},"ratioLayout":{"desktop":{},"mobile":{},"tablet":{}},"ratioLayoutRight":{"desktop":{},"mobile":{},"tablet":{}},"shape":{"desktop":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1","width":"100%"},"mobile":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1"},"tablet":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1"}},"shapeFor1Col":{"desktop":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1","width":"100%"},"mobile":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1"},"tablet":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1"}},"shapeFor2Col":{"desktop":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1","width":"50%"},"mobile":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1"},"tablet":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1"}},"shapeForBottom":{"desktop":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1","width":"20%"},"mobile":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1"},"tablet":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1"}},"shapeForFtOnly":{"desktop":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1"},"mobile":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1"},"tablet":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1"}},"spacing":{"desktop":"10px"},"verticalLayout":{"desktop":false}}, "productUrl":{{product.url | json | escape}}, "product":{{product | json | escape}}, "collectionUrl": {{ collection.url | json | escape }}, "collection": {{ collection | json | escape}}
    }
  '
      section-id="{{section.id}}"
      style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small)"
      data-id="g6dPamx6HZ"
      class="g6dPamx6HZ gp-overflow-hidden"
    >
      <div
        class="{%- if product.media.size > 1 -%} gp-grid gp-w-full !gp-m-0 gp-relative {%- else -%} gp-w-full !gp-m-0 gp-relative {%- endif -%}"
        {%- if product.media.size > 1 -%}
        style="--gtc:minmax(0, 12fr);--gtc-tablet:minmax(0, 12fr);--gtc-mobile:minmax(0, 12fr);--gg:10px"
        {%- else -%}
        style="--gtc:minmax(0, 12fr);--gg:10px"
        {%- endif -%}
      >
        
    {% capture featureImageOnlyOne %}
      
        {% assign featureMedia = variant.featured_media %}
        {% unless featureMedia %}
        {% assign featureMedia = product.featured_media %}
        {% endunless %}
        {% if product.media.size > 1 %}
          
          {% assign featureMedia = variant.featured_media %}
          {% unless featureMedia %}
          {% assign featureMedia = product.featured_media %}
          {% endunless %}
        
        {% endif %}
        {% assign largestRatio = 0 %}
        {% assign height = featureMedia.height | times: 1.0 %}
        {% assign width = featureMedia.width | times: 1.0 %}
        {% assign ratio = height | divided_by: width %}
        {% if ratio > largestRatio %}
          {% assign largestRatio = ratio %}
        {% endif %}
        {% assign productImageWidth = 0 %}
        {% case featureMedia.media_type %}
          {% when 'image' %}
            {% assign productImageWidth = featureMedia.width %}
          {% else %}
            {% assign productImageWidth = featureMedia.preview_image.width %}
        {% endcase %}
        {% if featureMedia == null %}
        {% assign productImageWidth = 1600 %}
        {% endif %}
        <div 
          class='gp-feature-image-carousel gp-feature-image-only' 
          style="--o:0;--o-tablet:0;--o-mobile:0;--d:flex;--d-mobile:flex;--d-tablet:flex;--jc:center;--jc-tablet:center;--jc-mobile:center"
        >
          <div 
            class="gp-relative"
            style="--w:56px;--w-tablet:56px;--w-mobile:56px"
          >
            
      
    
            <div 
              type="gp-feature-image-only"
              product-id="{{product.id}}"
              product-media="{{product.media.size}}"
              class="gp-image-item gp-ft-image-item gp-group gp-z-0 gp-flex !gp-max-w-full !gp-w-full gp-relative gp-items-start gp-justify-center gp-overflow-hidden gp-featured-image-wrapper"
              style="--h:auto;--h-tablet:auto;--h-mobile:auto;width:{{productImageWidth}}px"
            >
              <div 
                class="gp-w-full  {% if featureMedia == null or featureMedia.media_type == 'image' %} {{ 'gp-h-0' }} {% else %} {{ 'gp-h-full !gp-pb-0' }} {% endif %} gp-relative" 
                style="--pb: calc(({%if largestRatio == 0%} 100 / 100 {%else%} {{largestRatio}} {%endif%})*100%);--pb-tablet: calc(({%if largestRatio == 0%} 100 / 100 {%else%} {{largestRatio}} {%endif%})*100%);--pb-mobile: calc(({%if largestRatio == 0%} 100 / 100 {%else%} {{largestRatio}} {%endif%})*100%); {% if featureMedia.media_type == 'video' or featureMedia.media_type == 'external_video' %} {{ 'display: flex; align-items: center; justify-content: center' }} {% endif %}"
              >
                
    {% case featureMedia.media_type %}
      {% when 'image' %}
        
      
    {% assign src = featureMedia.src %}
    
    
  <img
      id="{%- if featureMedia != null -%}
              {{featureMedia.id}}
            {%- endif -%}"
      
      draggable="false"
      class="gp-w-full gp-h-full gp-absolute gp-top-0 gp-left-0 featured-image-only gp-cursor-pointer !gp-rounded-none gp-w-full gp-transition-opacity {{shouldHidden}}"
      data-src="{{src | image_url}}" data-srcset="{%- if src != null -%}
              {{src | img_url: '480x480'}} 480w, {{src | img_url: '768x768'}} 768w,{{src | img_url: '1024x1024'}} 1024w,{{src | img_url: '1440x1440'}} 1440w
            {%- else -%}
              {{ 'https://cdn.shopify.com/s/assets/no-image-2048-5e88c1b20e087fb7bbe9a3771824e743c244f437e4f8ba93bbf7b11b53f7824c_large.gif' }}
            {%- endif -%}" src="{{src | image_url}}" width="{{featureMedia.width}}" height="{{featureMedia.height}}" alt="{{featureMedia.alt}}" base-src="{{src | image_url}}"
      quality-type={}
      quality-percent={}
      data-sizes="auto"
      sizes="100vw"
      style="--aspect:{{featureMedia.width}}/{{featureMedia.height}};--aspect-tablet:{{featureMedia.width}}/{{featureMedia.height}};--aspect-mobile:{{featureMedia.width}}/{{featureMedia.height}};--objf:fill"
    />
  
      
      
    
      {% when 'external_video' %}
        {% assign mediaSourceVideo = featureMedia | external_video_url %}
        
    <iframe
      width="100%" height="100%"
      class="feature-video gp-w-full gp-h-full gp-relative"
      src="{{mediaSourceVideo}}"
      controls="true"
      allowfullscreen="true"
      allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture"
      frameborder="0"
      autoplay="false"
      alt="{{featureMedia.alt}}"
      style="--aspect:;--aspect-tablet:;--aspect-mobile:"
    >
    </iframe>
  
      {% when 'video' %}
        {% assign mediaSourceVideo = featureMedia.sources.last.url %}
        
  <gp-lite-html5-embed>
    
    <video
      id="gp-video-undefined"
      class=" gp-w-full lazy"
      style="width:100%;max-height:100%"
      controls
      
      
      
      title="{{featureMedia.alt}}"
      preload="none"
      data-src="{{mediaSourceVideo}}"
      src=""
      poster=""
      playsinline
    >
    
  </video>
    <div
      style="width:100%;max-height:100%"
      class="gp-absolute gp-top-0 gp-left-0  gp-w-full gp-thumbnail-video gp-hidden"
    >
      <img
        id="video-thumbnail"
        src=""
        class="gp-w-full gp-h-full gp-object-cover"
        alt="Video Thumbnail"
      ></img>
      <button
        type="button"
        class="gp-absolute gp-left-1/2 gp-top-1/2 gp-flex gp-aspect-[56/32] gp-w-14 -gp-translate-x-1/2 -gp-translate-y-1/2 gp-items-center gp-justify-center gp-rounded gp-bg-black/80 gp-transition-colors hover:gp-bg-[#ef0800]"
        aria-label="Play"
      >
        <svg class="gp-w-5 gp-text-white" viewBox="0 0 24 24">
          <path
            fill="currentColor"
            d="M5 5.274c0-1.707 1.826-2.792 3.325-1.977l12.362 6.726c1.566.853 1.566 3.101 0 3.953L8.325 20.702C6.826 21.518 5 20.432 5 18.726V5.274Z"
          />
        </svg>
      </button>
    </div>
    </gp-lite-html5-embed>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-lite-html5-embed.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
    <script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.3/dist/lazyload.min.js"></script>
    <script>
        if(lazyLoadInstance){lazyLoadInstance.update()}else{var lazyLoadInstance = new LazyLoad()}
    </script>
  
      {% when 'model' %}
        
    <model-viewer
      class="gp-w-full gp-h-full model-viewer gp-z-[90]"
      width="100%"
      
      
      
      
      src="{% if featureMedia.sources.first.url contains '.glb' %}{{ featureMedia.sources.first.url }}{% else %}{{featureMedia.sources.last.url}}{% endif %}"
      alt="{{featureMedia.preview_image.alt}}"
      camera-controls="true"
      poster="{{featureMedia.preview_image.src | product_img_url: '1024x1024'}}"
      data-js-focus-visible=""
      data-shopify-feature="1.12"
      ar-status="not-presenting"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1">
    </model-viewer>
  
      {% else %}
        
    
  <img
      
      
      draggable="false"
      class="gp-w-full gp-h-full gp-absolute gp-top-0 gp-left-0 featured-image-only gp-cursor-pointer !gp-rounded-none"
      data-src data-srcset="https://cdn.shopify.com/s/assets/no-image-2048-5e88c1b20e087fb7bbe9a3771824e743c244f437e4f8ba93bbf7b11b53f7824c_large.gif" src width="2237" height="1678" alt="No Image"
      quality-type={}
      quality-percent={}
      data-sizes="auto"
      sizes="100vw"
      style="--aspect:2237/1678;--aspect-tablet:2237/1678;--aspect-mobile:2237/1678;--objf:fill"
    />
  
    {% endcase %}
    
              </div>
            </div>
          </div>
        </div>
      
    {% endcapture %}
    {% if product.media.size > 1 %}
      
      {{ featureImageOnlyOne }}
    {% else %}
      {{ featureImageOnlyOne }}
    {% endif %}
  
         
      </div>
    </gp-product-images-v2>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-product-images-v2.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:center"
      class="gbiFtdz20I gp-relative gp-flex gp-flex-col"
    >
      <div gp-el-wrapper style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mb:var(--g-s-xs)" class="gXKz9v591v ">
      
    {%- unless product -%}
      <p>{{ "gempages.ProductTitle.product_not_found" | t }}</p>
    {%- else -%}
      
        
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gXKz9v591v">
    <div
      
        class="gXKz9v591v "
        
      >
      <div  >
        <h1
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-product-title gp-text-g-text-2"
          style="--w:100%;--ta:left;--line-clamp-tablet:1;--line-clamp-mobile:1;--c:var(--g-c-text-2, text-2);--fs:normal;--ff:var(--g-font-heading, heading);--weight:400;--ls:normal;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;word-break:break-word;overflow:hidden"
        >{{ product.title }}</h1>
      </div>
    </div>
    </gp-text>
    
      
    {%- endunless -%}
  
      </div>
       
      
    <div
      parentTag="Col" id="gGnAEQAwhB" data-id="gGnAEQAwhB"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--radius:var(--g-radius-small);--cg:8px;--pc:start;--gtc:minmax(0, auto) minmax(0, auto) minmax(0, auto);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gGnAEQAwhB gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--ai:normal;--jc:center;--o:0"
      class="gwfNy8rj0J gp-relative gp-flex gp-flex-col"
    >
      
      <gp-product-price
        data-id="gTARe7fveY"
        class="gTARe7fveY gp-product-price group-data-[state=loading]:gp-invisible data-[hidden=true]:gp-hidden"
        gp-data='{"priceType":"regular","uid":"gTARe7fveY","locale":"{{shop.locale}}","currency":"{{shop.currency}}","moneyFormat":"{{ shop.money_format | replace: '"', '\\"' | escape }}"}'
        
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small)"
        data-hidden="false"
      >
        
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="">
    <div
      id="p-price-gTARe7fveY"
        class=" "
        
      >
      <div  >
        <div
          type="regular"
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-price gp-money gp-decoration-g-text-1"
          style="--w:100%;--tdc:text-1;--tdt:1;--ta:left;--c:#242424;--fs:normal;--ff:var(--g-font-heading, heading);--weight:700;--ls:normal;--size:19px;--size-tablet:19px;--size-mobile:17px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;overflow:hidden"
        >
     {% if variant.price %} 
       {{ variant.price | money}}
     {% endif %}
   </div>
      </div>
    </div>
    </gp-text>
    
      </gp-product-price>
      <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-product-price.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:center"
      class="gZF91zX1mt gp-relative gp-flex gp-flex-col"
    >
      
      <gp-product-price
        data-id="gjRNj__SNb"
        class="gjRNj__SNb gp-product-price group-data-[state=loading]:gp-invisible data-[hidden=true]:gp-hidden"
        gp-data='{"priceType":"compare","uid":"gjRNj__SNb","locale":"{{shop.locale}}","currency":"{{shop.currency}}","moneyFormat":"{{ shop.money_format | replace: '"', '\\"' | escape }}"}'
        
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small)"
        data-hidden="{% if variant.compare_at_price > variant.price and variant.compare_at_price >= 0 %}false{% else %}true{% endif %}"
      >
        
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="">
    <div
      id="p-price-gjRNj__SNb"
        class=" "
        
      >
      <div  >
        <div
          type="compare"
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-price gp-product-compare-price gp-line-through"
          style="--w:100%;--tdc:#B4B4B4;--tdt:1;--ta:left;--c:#B4B4B4;--fs:normal;--ff:var(--g-font-heading, heading);--weight:400;--ls:normal;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;word-break:break-word;overflow:hidden"
        >
      {% if variant.compare_at_price  %} 
        {{ variant.compare_at_price | money}}
      {% else %}
        
      {% endif %}
    </div>
      </div>
    </div>
    </gp-text>
    
      </gp-product-price>
      <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-product-price.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:center"
      class="gsmfkLOJR8 gp-relative gp-flex gp-flex-col"
    >
      <div gp-el-wrapper style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small)" class="guESLP4dUZ ">
      
  {% liquid
    assign price = variant.price | times: 1.0
    assign salePrice = variant.compare_at_price | times: 1.0
    assign priceSave = salePrice | minus: price
    assign prefixVal = section.settings.gguESLP4dUZ_customContent_prefix
    assign suffixVal = section.settings.gguESLP4dUZ_customContent_suffix
    if salePrice == null or price == null
      assign pricePercentage = prefixVal | append: ' 0% ' | append: suffixVal
    else
         assign salePercent = priceSave | divided_by: salePrice | times: 100  | round
                assign pricePercentage = prefixVal | append: ' ' | append: salePercent | append: '% ' | append: suffixVal
              
    endif
  %}

  <gp-product-tag
  data-id="guESLP4dUZ"
    data-disabled="{%- if priceSave > 0 -%} false {%- else -%} true {%- endif -%}"
    gp-data='{"setting":{"customContent":{"prefix":"-","suffix":"off","unit":"percentage"},"translate":"customContent"}, "id": "guESLP4dUZ", "locale": "{{shop.locale}}", "currency": "{{shop.currency}}", "moneyFormat": "{{ shop.money_format | replace: '"', '\"' | escape }}"}'
    class="gp-block data-[disabled=true]:gp-hidden "
    style="--ta:left"
    price-save="{{priceSave}}"
    data-prefix="{{section.settings.gguESLP4dUZ_customContent_prefix}}"
    data-suffix="{{section.settings.gguESLP4dUZ_customContent_suffix}}"
  >
     <div class="gp-inline-flex gp-flex-wrap gp-items-end gp-gap-3" >
       <div
         class="gp-flex gp-items-center gp-w-full gp-h-full gp-bg-g-bg-2"
         style="--pl:16px;--pr:16px;--pt:16px;--pb:16px;--pl-tablet:16px;--pr-tablet:16px;--pt-tablet:16px;--pb-tablet:16px;--pl-mobile:16px;--pr-mobile:16px;--pt-mobile:16px;--pb-mobile:16px;--bs:none;--bw:0px;--bc:transparent;--c:#EA3335;--radius:var(--g-radius-small)"
       >
         
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="">
    <div
      id="p-tag-guESLP4dUZ"
        class=" "
        
      >
      <div  >
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A']"
          style="--w:100%;--ta:left;--c:#EA3335;--fs:normal;--ff:var(--g-font-heading, heading);--weight:700;--ls:normal;--size:13px;--size-tablet:13px;--size-mobile:12px;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;word-break:break-word;overflow:hidden"
        >{{pricePercentage}}</div>
      </div>
    </div>
    </gp-text>
    
       </div>
     </div>

 </gp-product-tag>
 <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-product-tag.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
   
      </div>
    </div>
    </div>
   
    
    </div>
    </div>
   
    
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:center"
      class="grAkXtT6pW gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      parentTag="Col" id="g3ycXDUERv" data-id="g3ycXDUERv"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--radius:var(--g-radius-small);--mt:0px;--cg:var(--g-s-3xl);--pc:start;--pc-mobile:center;--gtc:minmax(0, 5fr) minmax(0, 7fr);--gtc-tablet:minmax(0, 5fr) minmax(0, 7fr);--gtc-mobile:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="g3ycXDUERv gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--ai:normal;--jc:center;--jc-mobile:center;--o:0"
      class="g7UYooqjM_ gp-relative gp-flex gp-flex-col"
    >
      <div gp-el-wrapper style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:none;--d-tablet:block;--op:100%" class="g2WfssvPRi ">
      
    <gp-countdown-timer
      data-id="g2WfssvPRi"
     class="gp-flex "
     gp-data='{"behaviour":"evergreen","dateStandard":1684980818013,"dayLabel":"Days","enableDay":true,"enableHour":true,"enableMinute":true,"enableSecond":true,"enableWeek":false,"hourLabel":"Hrs","minuteLabel":"Mins","redirectUrl":{"link":"#","target":"_blank"},"secondLabel":"Secs","timeZone":"UTC+7","timerDaily":{"format":"am","hours":120,"mins":30},"timerEverygreen":{"days":6,"endTime":1751604571318,"hours":3,"mins":0,"startTime":1684376018013},"translate":"dayLabel,hourLabel,minuteLabel,secondLabel,weekLabel","weekLabel":"Weeks","uid":"g2WfssvPRi","builderData":{"advanced":{"border":{"desktop":{"normal":{"border":"none","borderType":"none","borderWidth":{},"color":"transparent","isCustom":false,"width":"0px"}}},"boxShadow":{"desktop":{"normal":{"angle":90,"blur":"4px","color":"rgba(18, 18, 18, 0.12)","distance":"2px","spread":"0px","type":"shadow-1"}}},"d":{"desktop":true,"mobile":false,"tablet":true},"hasBoxShadow":{"desktop":{"normal":false}},"op":{"desktop":"100%"},"rounded":{"desktop":{"normal":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"none"}}},"spacing-setting":{"desktop":{"margin":{},"padding":""},"mobile":{"margin":"","padding":""},"tablet":{"margin":"","padding":""}}},"label":"CountDown Timer","settings":{"behaviour":"evergreen","dateStandard":1684980818013,"dayLabel":"Days","enableDay":true,"enableHour":true,"enableMinute":true,"enableSecond":true,"enableWeek":false,"hourLabel":"Hrs","minuteLabel":"Mins","redirectUrl":{"link":"#","target":"_blank"},"secondLabel":"Secs","timeZone":"UTC+7","timerDaily":{"format":"am","hours":120,"mins":30},"timerEverygreen":{"days":6,"endTime":1751604571318,"hours":3,"mins":0,"startTime":1684376018013},"translate":"dayLabel,hourLabel,minuteLabel,secondLabel,weekLabel","weekLabel":"Weeks"},"styles":{"backgroundItemColor":"rgba(243, 243, 243, 0)","borderState":{"normal":{"border":"none","borderType":"none","color":"transparent","isCustom":false,"width":"0px"}},"colorLabel":"#B4B4B4","colorNumber":"text-2","horizontalGap":"0px","itemPadding":{"type":"small"},"labelTypo":{"attrs":{"color":"#B4B4B4"},"custom":{"fontSize":{"desktop":"13px","mobile":"12px","tablet":"13px"},"fontStyle":"normal","fontWeight":"400","letterSpacing":"normal","lineHeight":{"desktop":"150%","mobile":"150%","tablet":"150%"}},"type":"paragraph-1"},"labelTypography":{"custom":{"desktop":{"fontSize":"13px","fontStyle":"normal","fontWeight":"700","letterSpacing":"0px","lineHeight":"150%"},"mobile":{"fontSize":"16px","letterSpacing":"0px"},"tablet":{"fontSize":"16px","lineHeight":"150%"}}},"numTypo":{"attrs":{"color":"#242424"},"custom":{"fontSize":{"desktop":"23px","mobile":"20px","tablet":"23px"},"fontStyle":"normal","fontWeight":"700","letterSpacing":"normal","lineHeight":{"desktop":"130%","mobile":"130%","tablet":"130%"}},"type":"heading-2"},"numTypography":{"custom":{"desktop":{"fontSize":"28px","fontStyle":"normal","fontWeight":"600","letterSpacing":"0px","lineHeight":"130%"},"mobile":{"fontSize":"24px","fontWeight":"400","letterSpacing":"0px"},"tablet":{"fontSize":"19px","fontWeight":"600","letterSpacing":"0px","lineHeight":"130%"}}},"roundedState":{"normal":{"radiusType":"none"}},"textAlign":{"desktop":"left"},"verticalGap":"-5px"},"tag":"Countdown","uid":"g2WfssvPRi","childrens":[],"type":"component"}}'
     gp-href="#"
     style="--jc:left"
     >
      <div id="section-countdown" class="gp-flex gp-flex-wrap gp-w-max gp-relative" style="gap:0px;--jc:left">
        <a
            href="#"
            target="_blank"
            aria-label="Countdown link"
            class="gp-absolute gp-inset-0 gp-z-1"
          > </a>
          
          
    <div
        style="--bs:none;--bw:0px;--bc:transparent;--bg:rgba(243, 243, 243, 0)"
    class="gp-flex-1 gp-text-center gp-min-w-max !gp-max-w-max gp-g-s-small">
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g2WfssvPRi-text">
    <div
      id="day"
        class="g2WfssvPRi "
        style="--mb:-5px;--c:var(--g-c-text-2, text-2)"
      >
      <div  >
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-countdown-number"
          style="--w:100%;--fs:normal;--ff:var(--g-font-heading, heading);--weight:700;--ls:normal;--size:23px;--size-tablet:23px;--size-mobile:20px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--c:#242424;word-break:break-word;overflow:hidden"
        >00</div>
      </div>
    </div>
    </gp-text>
    
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g2WfssvPRi-text">
    <div
      
        class="g2WfssvPRi "
        style="--c:#B4B4B4"
      >
      <div  >
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A']"
          style="--w:100%;--fs:normal;--ff:var(--g-font-body, body);--weight:400;--ls:normal;--size:13px;--size-tablet:13px;--size-mobile:12px;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;--c:#B4B4B4;word-break:break-word;overflow:hidden"
        >{{ section.settings.gg2WfssvPRi_dayLabel }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
    
          
    <div
        style="--bs:none;--bw:0px;--bc:transparent;--bg:rgba(243, 243, 243, 0)"
    class="gp-flex-1 gp-text-center gp-min-w-max !gp-max-w-max gp-g-s-small">
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g2WfssvPRi-text">
    <div
      id="hour"
        class="g2WfssvPRi "
        style="--mb:-5px;--c:var(--g-c-text-2, text-2)"
      >
      <div  >
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-countdown-number"
          style="--w:100%;--fs:normal;--ff:var(--g-font-heading, heading);--weight:700;--ls:normal;--size:23px;--size-tablet:23px;--size-mobile:20px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--c:#242424;word-break:break-word;overflow:hidden"
        >00</div>
      </div>
    </div>
    </gp-text>
    
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g2WfssvPRi-text">
    <div
      
        class="g2WfssvPRi "
        style="--c:#B4B4B4"
      >
      <div  >
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A']"
          style="--w:100%;--fs:normal;--ff:var(--g-font-body, body);--weight:400;--ls:normal;--size:13px;--size-tablet:13px;--size-mobile:12px;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;--c:#B4B4B4;word-break:break-word;overflow:hidden"
        >{{ section.settings.gg2WfssvPRi_hourLabel }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
    
          
    <div
        style="--bs:none;--bw:0px;--bc:transparent;--bg:rgba(243, 243, 243, 0)"
    class="gp-flex-1 gp-text-center gp-min-w-max !gp-max-w-max gp-g-s-small">
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g2WfssvPRi-text">
    <div
      id="minute"
        class="g2WfssvPRi "
        style="--mb:-5px;--c:var(--g-c-text-2, text-2)"
      >
      <div  >
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-countdown-number"
          style="--w:100%;--fs:normal;--ff:var(--g-font-heading, heading);--weight:700;--ls:normal;--size:23px;--size-tablet:23px;--size-mobile:20px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--c:#242424;word-break:break-word;overflow:hidden"
        >00</div>
      </div>
    </div>
    </gp-text>
    
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g2WfssvPRi-text">
    <div
      
        class="g2WfssvPRi "
        style="--c:#B4B4B4"
      >
      <div  >
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A']"
          style="--w:100%;--fs:normal;--ff:var(--g-font-body, body);--weight:400;--ls:normal;--size:13px;--size-tablet:13px;--size-mobile:12px;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;--c:#B4B4B4;word-break:break-word;overflow:hidden"
        >{{ section.settings.gg2WfssvPRi_minuteLabel }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
    
          
    <div
        style="--bs:none;--bw:0px;--bc:transparent;--bg:rgba(243, 243, 243, 0)"
    class="gp-flex-1 gp-text-center gp-min-w-max !gp-max-w-max gp-g-s-small">
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g2WfssvPRi-text">
    <div
      id="second"
        class="g2WfssvPRi "
        style="--mb:-5px;--c:var(--g-c-text-2, text-2)"
      >
      <div  >
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-countdown-number"
          style="--w:100%;--fs:normal;--ff:var(--g-font-heading, heading);--weight:700;--ls:normal;--size:23px;--size-tablet:23px;--size-mobile:20px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--c:#242424;word-break:break-word;overflow:hidden"
        >00</div>
      </div>
    </div>
    </gp-text>
    
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g2WfssvPRi-text">
    <div
      
        class="g2WfssvPRi "
        style="--c:#B4B4B4"
      >
      <div  >
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A']"
          style="--w:100%;--fs:normal;--ff:var(--g-font-body, body);--weight:400;--ls:normal;--size:13px;--size-tablet:13px;--size-mobile:12px;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;--c:#B4B4B4;word-break:break-word;overflow:hidden"
        >{{ section.settings.gg2WfssvPRi_secondLabel }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
    
      </div>
     </gp-countdown-timer>
     <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-countdown.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
      </div>
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:center;--jc-mobile:center"
      class="gNn43W1WqI gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      parentTag="Col" id="gP_tjjfOZV" data-id="gP_tjjfOZV"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--cg:var(--g-s-l);--pc:start;--gtc:minmax(0, 6fr) minmax(0, 6fr);--gtc-tablet:minmax(0, 6fr) minmax(0, 6fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gP_tjjfOZV gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gk1RLif6gK gp-relative gp-flex gp-flex-col"
    >
      <div gp-el-wrapper style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small)" class="g-D4_UHcHj ">
      
    {%- liquid
      assign current_variant = product.selected_or_first_available_variant
      assign available = current_variant.available | default: false
    -%}
      <gp-product-quantity
        data-id="g-D4_UHcHj"
        data-disabled="{%- if available -%} false {%- else -%} true {%- endif -%}"
        data-price=""
        class="quantityClass gp-relative gp-inline-flex gp-w-full gp-bg-transparent gp-transition-all gp-duration-150 data-[disabled=true]:gp-opacity-60 "
        style="--h:50px;--jc:center"
      >

      
          <button
            title="Decrement"
            aria-label="decrement"
            {% if available == false %} disabled {% endif %}
            class="gp-border-g-line-1 gp-bg-g-bg-3 gp-minus gp-flex gp-aspect-square gp-h-full gp-cursor-pointer gp-items-center gp-justify-center gp-outline-none disabled:gp-cursor-not-allowed"
            style="--w:50px;--w-tablet:40px;--bg:var(--g-c-bg-3, bg-3);--c:#242424;--bs:solid;--bw:1px 1px 1px 1px;--bc:var(--g-c-line-1, line-1);--bblr:6px;--bbrr:auto;--btlr:6px;--btrr:auto;--hvr-bblr:6px;--hvr-btlr:6px"
          >
            <span class="gp-inline-flex gp-items-center gp-justify-center" style="--w:21px;--w-tablet:21px;--w-mobile:21px">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="gp-h-full gp-w-full"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 12H6" />
              </svg>
            </span>
          </button>
      
        <input
          type="text"
          name="product-quantity"
          {% if available == false %} disabled {% endif %}
          class="gp-border-g-line-1 gp-bg-g-bg-3 !gp-border-x-0 gp-px-4 gp-flex gp-shadow-none gp-appearance-none gp-items-center gp-border-y gp-text-center gp-outline-none gp-transition-all gp-duration-150 hover:gp-text-black disabled:gp-pointer-events-none gp-h-auto gp-rounded-none gp-shrink-[99999] gp-w-full gp-min-w-[45px]"
          style="--maxw:100%;--maxw-tablet:100%;--maxw-mobile:100%;--fs:normal;--ff:var(--g-font-body, body);--weight:400;--ls:normal;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;--c:#242424;--bg:var(--g-c-bg-3, bg-3);--bs:solid;--bw:1px 1px 1px 1px;--bc:var(--g-c-line-1, line-1)"
          aria-label="Quantity"
          inputmode="numeric"
          min="{{ current_variant.quantity_rule.min }}"
          step="{{ current_variant.quantity_rule.increment }}"
          value="1"
          autocomplete='off'
        />

        
          <button
            {% if available == false %} disabled {% endif %}
            aria-label="increment"
            title="Increment"
            class="gp-border-g-line-1 gp-bg-g-bg-3 gp-plus gp-flex gp-aspect-square gp-h-full gp-cursor-pointer gp-items-center gp-justify-center gp-outline-none gp-transition-all gp-duration-150 disabled:gp-pointer-events-none"
            style="--w:50px;--w-tablet:40px;--bg:var(--g-c-bg-3, bg-3);--c:#242424;--bs:solid;--bw:1px 1px 1px 1px;--bc:var(--g-c-line-1, line-1);--bblr:auto;--bbrr:6px;--btlr:auto;--btrr:6px;--hvr-bbrr:6px;--hvr-btrr:6px"
          >
            <span class="gp-inline-flex gp-items-center gp-justify-center" style="--w:21px;--w-tablet:21px;--w-mobile:21px">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="gp-h-full gp-w-full"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                />
              </svg>
            </span>
          </button>
        
      </gp-product-quantity>
      <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-product-quantity.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
      </div>
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="ghGcICBadL gp-relative gp-flex gp-flex-col"
    >
      
   {%- liquid
      assign inventory_quantity = variant.inventory_quantity | default: 0
    -%}
    <gp-product-button
      class="gp-product-button"
      gp-data-wrapper="true"
      gp-label-out-of-stock="{{section.settings.ggtVlo4lWkM_outOfStockLabel}}"
      gp-label-unavailable="{{section.settings.ggtVlo4lWkM_unavailableLabel}}"
      gp-data='{"setting":{"actionEffect":"open-cart-drawer","errorMessage":"Cannot add product to cart","successMessage":"Add product to cart successfully","enableSuccessMessage":true,"enableErrorMessage":true,"label":"Add to Cart","outOfStockLabel":"Out of stock","customURL":{}},"styles":{"errorTypo":{"attrs":{"color":"error"},"type":"paragraph-1"},"successTypo":{"attrs":{"color":"success"},"type":"paragraph-1"}},"disabled":"{{variant.available}}","variantID":"{{variant.id}}","totalVariant":"{{product.variants.size}}"}' 
       gp-href="{{ request.origin }}{{ routes.root_url | split: '/' | join: '/' }}/cart"
       data-variant-selection-required-message="{{}}"
    >
        
  <gp-button >
  <div
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--bblr:5px;--bbrr:5px;--btlr:5px;--btrr:5px;--ta:left"
    class="{% unless variant.available %} !gp-hidden {% endunless %}"
  >
    <style>
    .gtVlo4lWkM.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 0px;
  border-color: transparent;
  
      border-radius: var(--g-radius-medium);
    }

    .gtVlo4lWkM:hover::before {
      
      
    }

    .gtVlo4lWkM:hover .gp-button-icon {
      color: undefined;
    }

     .gtVlo4lWkM .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .gtVlo4lWkM:hover .gp-button-price {
      color: undefined;
    }

    .gtVlo4lWkM .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .gtVlo4lWkM .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .gtVlo4lWkM:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <button
      type="submit" data-id="gtVlo4lWkM" aria-label="Add to Cart"
      name="add" gp-data-hidden="{% unless variant.available %}true{% endunless %}"
      data-state="idle"
      class="gtVlo4lWkM gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-button-atc tcustomizer-submit-button"
      style="--hvr-bg:#424242;--bg:#242424;--radius:var(--g-radius-medium);--pl:24px;--pr:24px;--pt:12px;--pb:12px;--pl-tablet:24px;--pr-tablet:24px;--pt-tablet:12px;--pb-tablet:12px;--pl-mobile:24px;--pr-mobile:24px;--pt-mobile:12px;--pb-mobile:12px;--w:100%;--w-tablet:100%;--w-mobile:Auto;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--c:var(--g-c-text-3, text-3);--size:16px;--size-tablet:16px;--size-mobile:14px;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;--tt:capitalize"
    >
        <svg
          class="gp-invisible gp-absolute gp-h-5 gp-w-5 group-data-[state=loading]/button:gp-animate-spin group-data-[state=loading]/button:gp-visible"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            class="gp-opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            stroke-width="4"
          ></circle>
          <path
            class="gp-opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          ></path>
        </svg>
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--size:16px;--size-tablet:16px;--size-mobile:14px;--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.ggtVlo4lWkM_label }}
      </span>
      </div>
    
    </button>
  </div>
  </gp-button>

        
  <gp-button >
  <div
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--bblr:5px;--bbrr:5px;--btlr:5px;--btrr:5px;--ta:left"
    class="{% if variant.available %} !gp-hidden {% endif %}"
  >
    <style>
    .gtVlo4lWkM-sold-out.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 0px;
  border-color: transparent;
  
      border-radius: var(--g-radius-medium);
    }

    .gtVlo4lWkM-sold-out:hover::before {
      
      
    }

    .gtVlo4lWkM-sold-out:hover .gp-button-icon {
      color: undefined;
    }

     .gtVlo4lWkM-sold-out .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .gtVlo4lWkM-sold-out:hover .gp-button-price {
      color: undefined;
    }

    .gtVlo4lWkM-sold-out .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .gtVlo4lWkM-sold-out .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .gtVlo4lWkM-sold-out:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <button
      type="button" data-id="gtVlo4lWkM" aria-label="{{section.settings.ggtVlo4lWkM_outOfStockLabel}}"
      gp-data-hidden="{% if variant.available %}true{% endif %}"
      data-state="idle"
      class="gtVlo4lWkM-sold-out gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-button-sold-out btn-disabled gp-opacity-30 gp-cursor-default"
      style="--hvr-bg:#424242;--bg:#242424;--radius:var(--g-radius-medium);--pl:24px;--pr:24px;--pt:12px;--pb:12px;--pl-tablet:24px;--pr-tablet:24px;--pt-tablet:12px;--pb-tablet:12px;--pl-mobile:24px;--pr-mobile:24px;--pt-mobile:12px;--pb-mobile:12px;--w:100%;--w-tablet:100%;--w-mobile:Auto;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--c:var(--g-c-text-3, text-3);--size:16px;--size-tablet:16px;--size-mobile:14px;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;--tt:capitalize"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--size:16px;--size-tablet:16px;--size-mobile:14px;--c:var(--g-c-text-3, text-3)"
      >
        {{section.settings.ggtVlo4lWkM_outOfStockLabel}}
      </span>
      </div>
    
    </button>
  </div>
  </gp-button>

    </gp-product-button>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-product-button.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
    </div>
    </div>
   
    
    </div>
    </div>
   
    
    </div>
    </div>
   
    
          {%- endform -%}
        </product-form>
      </gp-product>
      {%- assign product = gpBkProduct -%}
    {% else %}
      <div class="gp-text-center">{{ "gempages.Product.product_not_found" | t }}</div>
    {%- endif -%}
     <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-product.v2.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
    </div>
      </div>
    </gp-sticky>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-sticky.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  


{% schema %}
  {
    
    "name": "Section 1",
    "tag": "section",
    "class": "gps-573018708419544289 gps gpsil",
    "settings": [{"type":"paragraph","content":"Section design by GemPages. [Click here to start design](https://admin.shopify.com/store/i0pixe-10/apps/gempages-cro/app/shopify/edit?pageType=GP_PRODUCT&editorId=572751048691811510&sectionId=573018708419544289)"},{"type":"select","label":"Section Preload","id":"section_preload","options":[{"label":"On","value":"true"},{"label":"Off","value":"false"},{"label":"Off Lazy Script","value":"off_lazy_script"}],"default":"false"},{"type":"text","label":"Checksum","id":"checksum"},{"type":"html","id":"gguESLP4dUZ_customContent_prefix","label":"gguESLP4dUZ_customContent_prefix","default":"-"},{"type":"html","id":"gguESLP4dUZ_customContent_suffix","label":"gguESLP4dUZ_customContent_suffix","default":"off"},{"type":"html","id":"gg2WfssvPRi_dayLabel","label":"gg2WfssvPRi_dayLabel","default":"Days"},{"type":"html","id":"gg2WfssvPRi_hourLabel","label":"gg2WfssvPRi_hourLabel","default":"Hrs"},{"type":"html","id":"gg2WfssvPRi_minuteLabel","label":"gg2WfssvPRi_minuteLabel","default":"Mins"},{"type":"html","id":"gg2WfssvPRi_secondLabel","label":"gg2WfssvPRi_secondLabel","default":"Secs"},{"type":"html","id":"gg2WfssvPRi_weekLabel","label":"gg2WfssvPRi_weekLabel","default":"Weeks"},{"type":"html","id":"ggtVlo4lWkM_label","label":"ggtVlo4lWkM_label","default":"Add to Cart"},{"type":"html","id":"ggtVlo4lWkM_outOfStockLabel","label":"ggtVlo4lWkM_outOfStockLabel","default":"Out of stock"},{"type":"html","id":"ggtVlo4lWkM_unavailableLabel","label":"ggtVlo4lWkM_unavailableLabel","default":"Unavailable"}],
    "blocks": [{"type": "@app"}],
    "locales": {}
  }
{% endschema %}
