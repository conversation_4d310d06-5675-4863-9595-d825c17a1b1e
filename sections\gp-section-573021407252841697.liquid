

<style {% if section.settings.section_preload == "false" %} class="gps-link gps-style" type="text/disabled-css" {% endif %}>.gps-573021407252841697.gps.gpsil [style*="--aspect:"]{aspect-ratio:var(--aspect)}.gps-573021407252841697.gps.gpsil [style*="--bg:"]{background:var(--bg)}.gps-573021407252841697.gps.gpsil [style*="--hvr-bg:"]:hover{background:var(--hvr-bg)}.gps-573021407252841697.gps.gpsil [style*="--bga:"]{background-attachment:var(--bga)}.gps-573021407252841697.gps.gpsil [style*="--bgc:"]{background-color:var(--bgc)}.gps-573021407252841697.gps.gpsil [style*="--bgi:"]{background-image:var(--bgi)}.gps-573021407252841697.gps.gpsil [style*="--bgp:"]{background-position:var(--bgp)}.gps-573021407252841697.gps.gpsil [style*="--bgr:"]{background-repeat:var(--bgr)}.gps-573021407252841697.gps.gpsil [style*="--bgs:"]{background-size:var(--bgs)}.gps-573021407252841697.gps.gpsil [style*="--b:"]{border:var(--b)}.gps-573021407252841697.gps.gpsil [style*="--hvr-b:"]:hover{border:var(--hvr-b)}.gps-573021407252841697.gps.gpsil [style*="--bc:"]{border-color:var(--bc)}.gps-573021407252841697.gps.gpsil [style*="--bs:"]{border-style:var(--bs)}.gps-573021407252841697.gps.gpsil [style*="--bw:"]{border-width:var(--bw)}.gps-573021407252841697.gps.gpsil [style*="--shadow:"]{box-shadow:var(--shadow)}.gps-573021407252841697.gps.gpsil [style*="--c:"]{color:var(--c)}.gps-573021407252841697.gps.gpsil [style*="--cg:"]{-moz-column-gap:var(--cg);column-gap:var(--cg)}.gps-573021407252841697.gps.gpsil [style*="--ff:"]{font-family:var(--ff)}.gps-573021407252841697.gps.gpsil [style*="--size:"]{font-size:var(--size)}.gps-573021407252841697.gps.gpsil [style*="--weight:"]{font-weight:var(--weight)}.gps-573021407252841697.gps.gpsil [style*="--fs:"]{font-style:var(--fs)}.gps-573021407252841697.gps.gpsil [style*="--gtc:"]{grid-template-columns:var(--gtc)}.gps-573021407252841697.gps.gpsil [style*="--h:"]{height:var(--h)}.gps-573021407252841697.gps.gpsil [style*="--jc:"]{justify-content:var(--jc)}.gps-573021407252841697.gps.gpsil [style*="--ls:"]{letter-spacing:var(--ls)}.gps-573021407252841697.gps.gpsil [style*="--lh:"]{line-height:var(--lh)}.gps-573021407252841697.gps.gpsil [style*="--m:"]{margin:var(--m)}.gps-573021407252841697.gps.gpsil [style*="--mb:"]{margin-bottom:var(--mb)}.gps-573021407252841697.gps.gpsil [style*="--maxh:"]{max-height:var(--maxh)}.gps-573021407252841697.gps.gpsil [style*="--objf:"]{-o-object-fit:var(--objf);object-fit:var(--objf)}.gps-573021407252841697.gps.gpsil [style*="--op:"]{opacity:var(--op)}.gps-573021407252841697.gps.gpsil [style*="--o:"]{order:var(--o)}.gps-573021407252841697.gps.gpsil [style*="--pc:"]{place-content:var(--pc)}.gps-573021407252841697.gps.gpsil [style*="--p:"]{padding:var(--p)}.gps-573021407252841697.gps.gpsil [style*="--pb:"]{padding-bottom:var(--pb)}.gps-573021407252841697.gps.gpsil [style*="--pl:"]{padding-left:var(--pl)}.gps-573021407252841697.gps.gpsil [style*="--pr:"]{padding-right:var(--pr)}.gps-573021407252841697.gps.gpsil [style*="--pt:"]{padding-top:var(--pt)}.gps-573021407252841697.gps.gpsil [style*="--ta:"]{text-align:var(--ta)}.gps-573021407252841697.gps.gpsil [style*="--tt:"]{text-transform:var(--tt)}.gps-573021407252841697.gps.gpsil [style*="--t:"]{transform:var(--t)}.gps-573021407252841697.gps.gpsil [style*="--w:"]{width:var(--w)}.gps-573021407252841697.gps.gpsil [style*="--line-clamp:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp);display:-webkit-box;overflow:hidden}@media only screen and (max-width:1024px){.gps-573021407252841697.gps.gpsil [style*="--size-tablet:"]{font-size:var(--size-tablet)}.gps-573021407252841697.gps.gpsil [style*="--gtc-tablet:"]{grid-template-columns:var(--gtc-tablet)}.gps-573021407252841697.gps.gpsil [style*="--h-tablet:"]{height:var(--h-tablet)}.gps-573021407252841697.gps.gpsil [style*="--jc-tablet:"]{justify-content:var(--jc-tablet)}.gps-573021407252841697.gps.gpsil [style*="--lh-tablet:"]{line-height:var(--lh-tablet)}.gps-573021407252841697.gps.gpsil [style*="--maxh-tablet:"]{max-height:var(--maxh-tablet)}.gps-573021407252841697.gps.gpsil [style*="--pb-tablet:"]{padding-bottom:var(--pb-tablet)}.gps-573021407252841697.gps.gpsil [style*="--pl-tablet:"]{padding-left:var(--pl-tablet)}.gps-573021407252841697.gps.gpsil [style*="--pr-tablet:"]{padding-right:var(--pr-tablet)}.gps-573021407252841697.gps.gpsil [style*="--pt-tablet:"]{padding-top:var(--pt-tablet)}.gps-573021407252841697.gps.gpsil [style*="--ta-tablet:"]{text-align:var(--ta-tablet)}.gps-573021407252841697.gps.gpsil [style*="--w-tablet:"]{width:var(--w-tablet)}.gps-573021407252841697.gps.gpsil [style*="--line-clamp-tablet:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-tablet);display:-webkit-box;overflow:hidden}}@media only screen and (max-width:767px){.gps-573021407252841697.gps.gpsil [style*="--cg-mobile:"]{-moz-column-gap:var(--cg-mobile);column-gap:var(--cg-mobile)}.gps-573021407252841697.gps.gpsil [style*="--size-mobile:"]{font-size:var(--size-mobile)}.gps-573021407252841697.gps.gpsil [style*="--gtc-mobile:"]{grid-template-columns:var(--gtc-mobile)}.gps-573021407252841697.gps.gpsil [style*="--h-mobile:"]{height:var(--h-mobile)}.gps-573021407252841697.gps.gpsil [style*="--jc-mobile:"]{justify-content:var(--jc-mobile)}.gps-573021407252841697.gps.gpsil [style*="--lh-mobile:"]{line-height:var(--lh-mobile)}.gps-573021407252841697.gps.gpsil [style*="--mb-mobile:"]{margin-bottom:var(--mb-mobile)}.gps-573021407252841697.gps.gpsil [style*="--maxh-mobile:"]{max-height:var(--maxh-mobile)}.gps-573021407252841697.gps.gpsil [style*="--pb-mobile:"]{padding-bottom:var(--pb-mobile)}.gps-573021407252841697.gps.gpsil [style*="--pl-mobile:"]{padding-left:var(--pl-mobile)}.gps-573021407252841697.gps.gpsil [style*="--pr-mobile:"]{padding-right:var(--pr-mobile)}.gps-573021407252841697.gps.gpsil [style*="--pt-mobile:"]{padding-top:var(--pt-mobile)}.gps-573021407252841697.gps.gpsil [style*="--ta-mobile:"]{text-align:var(--ta-mobile)}.gps-573021407252841697.gps.gpsil [style*="--w-mobile:"]{width:var(--w-mobile)}.gps-573021407252841697.gps.gpsil [style*="--line-clamp-mobile:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-mobile);display:-webkit-box;overflow:hidden}}.gps-573021407252841697 .-gp-translate-y-1\/2,.gps-573021407252841697 .gp-translate-x-1\/2{--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1}.gps-573021407252841697 .focus-visible\:gp-shadow-none,.gps-573021407252841697 .focus\:\!gp-shadow-none,.gps-573021407252841697 .gp-shadow-md,.gps-573021407252841697 .gp-shadow-xl,.gps-573021407252841697 .hover\:\!gp-shadow-none{--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000}.gps-573021407252841697 .gp-fixed{position:fixed}.gps-573021407252841697 .gp-absolute{position:absolute}.gps-573021407252841697 .gp-relative{position:relative}.gps-573021407252841697 .gp-inset-0{inset:0}.gps-573021407252841697 .gp-right-0{right:0}.gps-573021407252841697 .gp-top-0{top:0}.gps-573021407252841697 .gp-top-\[27px\]{top:27px}.gps-573021407252841697 .gp-z-1{z-index:1}.gps-573021407252841697 .gp-z-100{z-index:100}.gps-573021407252841697 .gp-z-50{z-index:50}.gps-573021407252841697 .gp-z-\[999999\]{z-index:999999}.gps-573021407252841697 .gp-mx-auto{margin-left:auto;margin-right:auto}.gps-573021407252841697 .gp-mb-0{margin-bottom:0}.gps-573021407252841697 .gp-block{display:block}.gps-573021407252841697 .gp-inline-block{display:inline-block}.gps-573021407252841697 .gp-flex{display:flex}.gps-573021407252841697 .gp-inline-flex{display:inline-flex}.gps-573021407252841697 .gp-grid{display:grid}.gps-573021407252841697 .gp-contents{display:contents}.gps-573021407252841697 .\!gp-hidden{display:none!important}.gps-573021407252841697 .gp-hidden{display:none}.gps-573021407252841697 .\!gp-h-auto{height:auto!important}.gps-573021407252841697 .gp-h-auto{height:auto}.gps-573021407252841697 .gp-h-full{height:100%}.gps-573021407252841697 .gp-h-screen{height:100vh}.gps-573021407252841697 .\!gp-max-h-screen{max-height:100vh!important}.gps-573021407252841697 .gp-max-h-full{max-height:100%}.gps-573021407252841697 .gp-w-full{width:100%}.gps-573021407252841697 .gp-max-w-full{max-width:100%}.gps-573021407252841697 .gp-flex-none{flex:none}.gps-573021407252841697 .-gp-translate-y-1\/2{--tw-translate-y:-50%}.gps-573021407252841697 .-gp-translate-y-1\/2,.gps-573021407252841697 .gp-translate-x-1\/2{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.gps-573021407252841697 .gp-translate-x-1\/2{--tw-translate-x:50%}@keyframes gp-zoomIn{0%{opacity:0;transform:scale(.7)}to{opacity:1;transform:scale(1)}}.gps-573021407252841697 .gp-animate-\[zoomIn_0\.3s_ease-in\]{animation:gp-zoomIn .3s ease-in}.gps-573021407252841697 .gp-grid-rows-\[1fr\]{grid-template-rows:1fr}.gps-573021407252841697 .gp-flex-col{flex-direction:column}.gps-573021407252841697 .gp-items-center{align-items:center}.gps-573021407252841697 .gp-justify-center{justify-content:center}.gps-573021407252841697 .gp-gap-y-0{row-gap:0}.gps-573021407252841697 .gp-overflow-hidden{overflow:hidden}.gps-573021407252841697 .gp-overflow-y-auto{overflow-y:auto}.gps-573021407252841697 .gp-overflow-x-hidden{overflow-x:hidden}.gps-573021407252841697 .gp-break-words{overflow-wrap:break-word}.gps-573021407252841697 .gp-rounded{border-radius:4px}.gps-573021407252841697 .gp-rounded-none{border-radius:0}.gps-573021407252841697 .gp-bg-transparent{background-color:transparent}.gps-573021407252841697 .\!gp-bg-none{background-image:none!important}.gps-573021407252841697 .gp-p-\[16px\]{padding:16px}.gps-573021407252841697 .gp-align-middle{vertical-align:middle}.gps-573021407252841697 .gp-text-\[14px\]{font-size:14px}.gps-573021407252841697 .gp-leading-normal{line-height:1.5}.gps-573021407252841697 .gp-text-g-text-1{color:var(--g-c-text-1)}.gps-573021407252841697 .gp-text-g-text-2{color:var(--g-c-text-2)}.gps-573021407252841697 .gp-text-g-text-3{color:var(--g-c-text-3)}.gps-573021407252841697 .gp-no-underline{text-decoration-line:none}.gps-573021407252841697 .gp-shadow-md{--tw-shadow:0 4px 6px -1px rgba(0,0,0,.1),0 2px 4px -2px rgba(0,0,0,.1);--tw-shadow-colored:0 4px 6px -1px var(--tw-shadow-color),0 2px 4px -2px var(--tw-shadow-color)}.gps-573021407252841697 .gp-shadow-md,.gps-573021407252841697 .gp-shadow-xl{box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)}.gps-573021407252841697 .gp-shadow-xl{--tw-shadow:0 20px 25px -5px rgba(0,0,0,.1),0 8px 10px -6px rgba(0,0,0,.1);--tw-shadow-colored:0 20px 25px -5px var(--tw-shadow-color),0 8px 10px -6px var(--tw-shadow-color)}.gps-573021407252841697 .\!gp-outline-none{outline:2px solid transparent!important;outline-offset:2px!important}.gps-573021407252841697 .gp-outline-none{outline:2px solid transparent;outline-offset:2px}.gps-573021407252841697 .gp-transition-all{transition-duration:.15s;transition-property:all;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-573021407252841697 .gp-transition-colors{transition-duration:.15s;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-573021407252841697 .gp-duration-100{transition-duration:.1s}.gps-573021407252841697 .gp-duration-200{transition-duration:.2s}.gps-573021407252841697 .gp-duration-300{transition-duration:.3s}.gps-573021407252841697 .gp-ease-in-out{transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-573021407252841697 .disabled\:gp-btn-disabled:disabled{cursor:default}@media (hover:hover) and (pointer:fine){.gps-573021407252841697 .hover\:\!gp-shadow-none:hover{--tw-shadow:0 0 #0000!important;--tw-shadow-colored:0 0 #0000!important;box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)!important}}.gps-573021407252841697 .focus\:\!gp-shadow-none:focus{--tw-shadow:0 0 #0000!important;--tw-shadow-colored:0 0 #0000!important;box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)!important}.gps-573021407252841697 .focus-visible\:gp-shadow-none:focus-visible{--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)}.gps-573021407252841697 .focus-visible\:gp-outline-none:focus-visible{outline:2px solid transparent;outline-offset:2px}.gps-573021407252841697 .disabled\:gp-pointer-events-none:disabled{pointer-events:none}.gps-573021407252841697 .disabled\:gp-opacity-30:disabled{opacity:.3}@media (hover:hover) and (pointer:fine){.gps-573021407252841697 .gp-group\/button:hover .group-hover\/button\:\!gp-text-inherit{color:inherit!important}}.gps-573021407252841697 .gp-group\/button:active .group-active\/button\:\!gp-text-inherit{color:inherit!important}.gps-573021407252841697 .gp-group\/button[data-state=loading] .group-data-\[state\=loading\]\/button\:gp-invisible{visibility:hidden}@media (max-width:1024px){.gps-573021407252841697 .tablet\:\!gp-hidden{display:none!important}.gps-573021407252841697 .tablet\:gp-hidden{display:none}.gps-573021407252841697 .tablet\:gp-h-auto{height:auto}.gps-573021407252841697 .tablet\:gp-flex-none{flex:none}}@media (max-width:767px){.gps-573021407252841697 .mobile\:\!gp-hidden{display:none!important}.gps-573021407252841697 .mobile\:gp-hidden{display:none}.gps-573021407252841697 .mobile\:gp-h-auto{height:auto}.gps-573021407252841697 .mobile\:gp-flex-none{flex:none}}.gps-573021407252841697 .\[\&_\*\]\:gp-max-w-full *{max-width:100%}.gps-573021407252841697 .\[\&_p\]\:gp-inline p{display:inline}.gps-573021407252841697 .\[\&_p\]\:gp-whitespace-pre-line p{white-space:pre-line}.gps-573021407252841697 .\[\&_p\]\:after\:gp-whitespace-pre-wrap p:after{content:var(--tw-content);white-space:pre-wrap}.gps-573021407252841697 .\[\&_p\]\:after\:gp-content-\[\'\\A\'\] p:after{--tw-content:"\A";content:var(--tw-content)}</style>

    <gp-dialog class="gps-lazy" data-id="gj-A9Y1Rrv" style="--d:contents;--d-tablet:contents;--d-mobile:contents" data-gp-setting='{"dialogId":"gj-A9Y1Rrv","trigger":"none","triggerTime":10,"frequency":3,"triggerPercentage":10,"mobileTrigger":"none","mobileTriggerTime":10,"closeOnClickOutside":true,"frequencyMode":"after-period-time","display":{"desktop":true,"tablet":true,"mobile":true}}'>
      <dialog class=" gp-dialog gp-bg-transparent" aria-label="Popup 1" data-gp-dialog-id="gj-A9Y1Rrv"
        style="z-index:100001"
      >
        <div
          aria-label="Backdrop"
          class="gp-fixed gp-inset-0 gp-z-50 gp-flex gp-overflow-y-auto gp-overflow-x-hidden gp-items-center gp-justify-center"
        >
        
            <div
              class="gp-fixed gp-inset-0 gp-block gp-transition-all gp-duration-100"
              data-gp-dialog-overlay
              style="--op:0.49;--bgc:#121212"
            ></div>
        
          <span class="gp-inline-block gp-h-screen gp-align-middle"></span>
          <div
            role="dialog"
            aria-label="Dialog content"
            class="gp-relative gp-max-h-full gp-max-w-full  gp-animate-[zoomIn_0.3s_ease-in] gp-shadow-xl"
            style="--mb:0px;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--w:1300px;--w-tablet:1300px;--w-mobile:100%;--h:900px;--h-tablet:900px;--h-mobile:900px;--maxh:;--maxh-tablet:;--maxh-mobile:"
          >
            <button
              type="button"
              data-gp-dialog-close
              tabindex="-1"
              class="gp-absolute gp-top-0 gp-right-0 gp-z-[999999] gp-inline-flex gp-items-center gp-justify-center gp-outline-none focus-visible:gp-shadow-none focus-visible:gp-outline-none gp-translate-x-1/2 -gp-translate-y-1/2 gp-shadow-md"
              style="--w:45px;--h:45px;--c:var(--g-c-line-3, line-3);--bgc:#ffffff"
            >
              <svg
                viewBox="0 0 12 12"
                fill="currentColor"
                style="--w:12px;--h:12px"
              >
                <path d="M7.1,6l4.5,4.5l-1.1,1.1L6,7.1l-4.5,4.5l-1.1-1.1L4.9,6L0.5,1.5l1.1-1.1L6,4.9l4.5-4.5l1.1,1.1L7.1,6z"></path>
              </svg>
            </button>
            <div
              style="--pt:0px;--pl:0px;--pb:0px;--pr:0px;--shadow:none;--op:100%;--h:900px;--h-tablet:900px;--h-mobile:900px;--maxh:;--maxh-tablet:;--maxh-mobile:;border-radius:inherit;--bgc:#FFFFFF"
              aria-label="Dialog body"
              class="gp-h-full gp-w-full gp-overflow-y-auto gp-overflow-x-hidden !gp-max-h-screen">
                <div>
                  
       
      
    <div
      parentTag="Dialog" id="gADAiD7bdt" data-id="gADAiD7bdt"
        style="--d:grid;--d-tablet:grid;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--cg:8px;--pc:start;--gtc:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--h:auto;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gADAiD7bdt gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gqDKp6_G8A gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      parentTag="Col" id="gjrbfSIorA" data-id="gjrbfSIorA"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pt:var(--g-s-4xl);--pl:15px;--pb:var(--g-s-4xl);--pr:15px;--pt-tablet:var(--g-s-4xl);--pl-tablet:15px;--pb-tablet:var(--g-s-4xl);--pr-tablet:15px;--cg:var(--g-s-2xl);--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:#FFFFFF;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gjrbfSIorA gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gk5RySKGNL gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      parentTag="Col" id="g0ENC5XqIn" data-id="g0ENC5XqIn"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pt:var(--g-s-4xl);--pl:15px;--pb:var(--g-s-4xl);--pr:15px;--pt-tablet:var(--g-s-4xl);--pl-tablet:15px;--pb-tablet:var(--g-s-4xl);--pr-tablet:15px;--cg:var(--g-s-2xl);--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:#FFFFFF;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="g0ENC5XqIn gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:center"
      class="gu7OdgdoSs gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      parentTag="Col" id="gyidfpNMC-" data-id="gyidfpNMC-"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--cg:30px;--cg-mobile:0px;--pc:start;--gtc:minmax(0, 6fr) minmax(0, 6fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gyidfpNMC- gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="ggu_mmj5Di gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      parentTag="Col" id="gH5xT10-uu" data-id="gH5xT10-uu"
        style="--bs:solid;--bw:1px 1px 1px 1px;--bc:#EEEEEE;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pt:118px;--pl:var(--g-s-3xl);--pb:118px;--pr:var(--g-s-3xl);--mb-mobile:var(--g-s-2xl);--pt-mobile:var(--g-s-3xl);--pl-mobile:15px;--pb-mobile:var(--g-s-3xl);--pr-mobile:15px;--pt-tablet:37px;--pl-tablet:var(--g-s-3xl);--pb-tablet:36px;--pr-tablet:var(--g-s-3xl);--cg:var(--g-s-2xl);--pc:start;--gtc:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gH5xT10-uu gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gooaDkr4tZ gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gSjbnVlWNn">
    <div
      parentTag="Col"
        class="gSjbnVlWNn "
        style="--tt:default;--ta:center;--ta-mobile:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-s)"
      >
      <div class="gp-flex" style="--jc:center;--jc-mobile:center">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text-g-text-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:center;--ta-mobile:center;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#242424;--fs:normal;--ff:var(--g-font-body, body);--ls:0px;--size:19px;--size-tablet:19px;--size-mobile:17px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--weight:bold;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggSjbnVlWNn_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gKcuo1nz-h">
    <div
      parentTag="Col"
        class="gKcuo1nz-h "
        style="--tt:default;--ta:center;--ta-tablet:center;--ta-mobile:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-3xl);--mb-mobile:var(--g-s-l)"
      >
      <div class="gp-flex" style="--jc:center;--jc-tablet:center;--jc-mobile:center">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text-g-text-2 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:center;--ta-tablet:center;--ta-mobile:center;--c:#242424;--fs:normal;--ff:var(--g-font-heading, heading);--weight:700;--ls:normal;--size:33px;--size-tablet:33px;--size-mobile:29px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggKcuo1nz-h_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
       
      
    <div
      parentTag="Col" id="gWG4KlUsRN" data-id="gWG4KlUsRN"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:none;--d-mobile:grid;--d-tablet:none;--op:100%;--mb:var(--g-s-l);--mb-mobile:var(--g-s-3xl);--cg:var(--g-s-2xl);--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gWG4KlUsRN gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start;--jc-mobile:center"
      class="gZln4_nROt gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="ghqMkTDDUg">
    <div
      parentTag="Col"
        class="ghqMkTDDUg "
        style="--tt:default;--ta:left;--ta-mobile:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-l);--mb-mobile:0px"
      >
      <div class="gp-flex" style="--jc:left;--jc-mobile:center">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--ta-mobile:center;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#575757;--size:14px;--lh:150%;--fs:normal;--weight:400;--ls:0px;--size-tablet:11px;--size-mobile:14px;word-break:break-word;overflow:hidden"
        >{{ section.settings.gghqMkTDDUg_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    <div gp-el-wrapper style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pt:0px;--pl:0px;--pb:0px;--pr:0px" class="gQWP40pYdt ">
      
    <gp-form
      id="gQWP40pYdt"
      data-id="gQWP40pYdt"
      
      data-submit-action=''
      data-callback='{"link":"","target":"_self"}'
    >
      <a
        hidden
        id="gp-form-callback-gQWP40pYdt"
        href="" target=""
      >
      </a>
      {% form 'customer', class: 'gp-form-gQWP40pYdt ', id: 'contact_form_gQWP40pYdt' %}
        {% if form.errors %}
          <div
            id="gp-form-error-gQWP40pYdt"
            class="gp-form-message gp-hidden gp-top-[27px] gp-z-100 gp-fixed gp-rounded gp-p-[16px] gp-text-[14px] gp-leading-normal"
            style="background-color:#FFE9E9;left:50%;transform:translateX(-50%);color:#EA3335"
          >
            {{ section.settings.ggQWP40pYdt_errorMessage }}
          </div>
        {% endif %}
        <div popover id="my-popover-gQWP40pYdt">
        <style>
            #my-popover-gQWP40pYdt::backdrop {
              width: fit-content;
              height: fit-content;
            }
        </style>
        <div
          id="gp-form-success-gQWP40pYdt"
          class="gp-form-message gp-hidden gp-top-[27px] gp-z-100 gp-fixed gp-rounded gp-p-[16px] gp-text-[14px] gp-leading-normal"
          style="background-color:#F2FFEC;left:50%;transform:translateX(-50%);color:#52C41A"
        >
          {{ section.settings.ggQWP40pYdt_successMessage }}
        </div></div>
        
       
      
    <div
      parentTag="Newsletter" id="gXS8GT8FDZ" data-id="gXS8GT8FDZ"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--cg:0px;--cg-mobile:0px;--pc:start;--gtc:minmax(0, 9fr) minmax(0, 3fr);--gtc-tablet:minmax(0, 8fr) minmax(0, 4fr);--gtc-mobile:minmax(0, 8fr) minmax(0, 4fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gXS8GT8FDZ gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gSbkHsccTL gp-relative gp-flex gp-flex-col"
    >
      
    <div
    data-id="gDrsi4-W01"
      class="gDrsi4-W01"
      style="--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
    >
      <input
        type="email"
        class="gp-form-item !gp-outline-none !gp-h-auto focus:!gp-shadow-none hover:!gp-shadow-none !gp-bg-none"
        style="--fs:normal;--ff:var(--g-font-body, body);--weight:400;--ls:normal;--size:19px;--size-tablet:19px;--size-mobile:14px;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;--pl:32px;--pr:32px;--pt:10px;--pb:10px;--w:100%;--w-tablet:100%;--w-mobile:100%;--bs:solid;--bw:1px 1px 1px 1px;--bc:#E0E0E0;--shadow:none"
        placeholder="{{ section.settings.ggDrsi4-W01_placeholder }}"
        {% if true %}
        required
        {% endif %}
        name="contact[email]"
        value=""
        autocomplete="email"
      ></input>
    </div>
  
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="g-L3fnmr_A gp-relative gp-flex gp-flex-col"
    >
      
    
  <gp-button >
  <div
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
    
  >
    <style>
    .gSHydB8uWL.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: solid;
  border-width: 0px 0px 0px 0px;
  border-color: var(--g-c-brand, brand);
  
      
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
    }

    .gSHydB8uWL:hover::before {
      border-style: solid;
  border-width: 0px 0px 0px 0px;
  border-color: var(--g-c-brand, brand);
  
      
    }

    .gSHydB8uWL:hover .gp-button-icon {
      color: undefined;
    }

     .gSHydB8uWL .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .gSHydB8uWL:hover .gp-button-price {
      color: undefined;
    }

    .gSHydB8uWL .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .gSHydB8uWL .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .gSHydB8uWL:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <button
      type="submit" data-id="gSHydB8uWL" aria-label="Subscribe"
      
      data-state="idle"
      class="gSHydB8uWL gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3"
      style="--hvr-bg:#424242;--bg:#242424;--pl:19px;--pr:19px;--pt:13px;--pb:13px;--pl-tablet:19px;--pr-tablet:19px;--pt-tablet:13px;--pb-tablet:13px;--pl-mobile:19px;--pr-mobile:19px;--pt-mobile:12px;--pb-mobile:12px;--w:100%;--w-tablet:100%;--w-mobile:100%;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--c:var(--g-c-text-3, text-3);--size:19px;--size-tablet:19px;--size-mobile:14px;--fs:normal;--ff:var(--g-font-body, body);--ls:normal;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--weight:bold"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--size:19px;--size-tablet:19px;--size-mobile:14px;--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.ggSHydB8uWL_label }}
      </span>
      </div>
    
    </button>
  </div>
  </gp-button>

  
    </div>
    </div>
   
    
      {% endform %}
    </gp-form>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-form.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
      </div>
    </div>
    </div>
   
    
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gXesTrBEDd gp-relative gp-flex gp-flex-col"
    >
      <div
    
     data-id="gVwGU4qqFX"
    role="presentation"
    class="gp-group/image gVwGU4qqFX gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
    >
      <div
        class="pointer-events-auto gp-h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="border-radius:inherit;--jc:left"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" data-srcSet="{{ "gempages_572751041980793671-dcb6fa42-afc3-4b84-b012-a94c8bf5464f.jpg" | file_url }}" srcset="{{ "gempages_572751041980793671-dcb6fa42-afc3-4b84-b012-a94c8bf5464f.jpg" | file_url }}" />
      <source media="(max-width: 1024px)" data-srcSet="{{ "gempages_572751041980793671-dcb6fa42-afc3-4b84-b012-a94c8bf5464f.jpg" | file_url }}" srcset="{{ "gempages_572751041980793671-dcb6fa42-afc3-4b84-b012-a94c8bf5464f.jpg" | file_url }}" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex"
        src="{{ "gempages_572751041980793671-dcb6fa42-afc3-4b84-b012-a94c8bf5464f.jpg" | file_url }}"
        data-src="{{ "gempages_572751041980793671-dcb6fa42-afc3-4b84-b012-a94c8bf5464f.jpg" | file_url }}"
        width="100%"
        alt="Alt Image"
        style="--aspect:auto;--objf:fill;--w:100%;--w-tablet:100%;--w-mobile:100%;--h:auto;--h-tablet:auto;--h-mobile:auto"
      />
    
  </picture>
      </div>
  </div>
    </div>
    </div>
   
    
    </div>
    </div>
   
    
    </div>
    </div>
   
    
    </div>
    </div>
   
    
                </div>
            </div>
          </div>
        </div>
      </dialog>
    </gp-dialog>
    <script src="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-dialog.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  


{% schema %}
  {
    
    "name": "Section 16",
    "tag": "section",
    "class": "gps-573021407252841697 gps gpsil",
    "settings": [{"type":"paragraph","content":"Section design by GemPages. [Click here to start design](https://admin.shopify.com/store/i0pixe-10/apps/gempages-cro/app/shopify/edit?pageType=GP_PRODUCT&editorId=572751048691811510&sectionId=573021407252841697)"},{"type":"select","label":"Section Preload","id":"section_preload","options":[{"label":"On","value":"true"},{"label":"Off","value":"false"},{"label":"Off Lazy Script","value":"off_lazy_script"}],"default":"false"},{"type":"text","label":"Checksum","id":"checksum"},{"type":"html","id":"ggSjbnVlWNn_text","label":"ggSjbnVlWNn_text","default":"<p>GET DISSCOUNT <span style=\"color:#FF0202;\">10% OFF</span></p>"},{"type":"html","id":"ggKcuo1nz-h_text","label":"ggKcuo1nz-h_text","default":"<p>SUBSCRIBE TO NEWSLETTER</p>"},{"type":"html","id":"gghqMkTDDUg_text","label":"gghqMkTDDUg_text","default":"<p>Sign up to be the first to hear about exclusive deals, special offers and upcoming collections</p>"},{"type":"html","id":"ggQWP40pYdt_successMessage","label":"ggQWP40pYdt_successMessage","default":"Thanks for contacting us. We'll get back to you as soon as possible."},{"type":"html","id":"ggQWP40pYdt_errorMessage","label":"ggQWP40pYdt_errorMessage","default":"Can’t send email. Please try again later."},{"type":"html","id":"ggDrsi4-W01_placeholder","label":"ggDrsi4-W01_placeholder","default":"Enter your email"},{"type":"html","id":"ggSHydB8uWL_label","label":"ggSHydB8uWL_label","default":"Subscribe"}],
    "blocks": [{"type": "@app"}],
    "locales": {}
  }
{% endschema %}
