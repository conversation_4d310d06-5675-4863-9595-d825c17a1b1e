

<style {% if section.settings.section_preload == "false" %} class="gps-link gps-style" type="text/disabled-css" {% endif %}>.gps-573021407269618804.gps.gpsil [style*="--bg:"]{background:var(--bg)}.gps-573021407269618804.gps.gpsil [style*="--bgc:"]{background-color:var(--bgc)}.gps-573021407269618804.gps.gpsil [style*="--b:"]{border:var(--b)}.gps-573021407269618804.gps.gpsil [style*="--bc:"]{border-color:var(--bc)}.gps-573021407269618804.gps.gpsil [style*="--bs:"]{border-style:var(--bs)}.gps-573021407269618804.gps.gpsil [style*="--bw:"]{border-width:var(--bw)}.gps-573021407269618804.gps.gpsil [style*="--shadow:"]{box-shadow:var(--shadow)}.gps-573021407269618804.gps.gpsil [style*="--c:"]{color:var(--c)}.gps-573021407269618804.gps.gpsil [style*="--h:"]{height:var(--h)}.gps-573021407269618804.gps.gpsil [style*="--m:"]{margin:var(--m)}.gps-573021407269618804.gps.gpsil [style*="--maxh:"]{max-height:var(--maxh)}.gps-573021407269618804.gps.gpsil [style*="--minh:"]{min-height:var(--minh)}.gps-573021407269618804.gps.gpsil [style*="--op:"]{opacity:var(--op)}.gps-573021407269618804.gps.gpsil [style*="--o:"]{order:var(--o)}.gps-573021407269618804.gps.gpsil [style*="--p:"]{padding:var(--p)}.gps-573021407269618804.gps.gpsil [style*="--pb:"]{padding-bottom:var(--pb)}.gps-573021407269618804.gps.gpsil [style*="--pl:"]{padding-left:var(--pl)}.gps-573021407269618804.gps.gpsil [style*="--pr:"]{padding-right:var(--pr)}.gps-573021407269618804.gps.gpsil [style*="--pt:"]{padding-top:var(--pt)}.gps-573021407269618804.gps.gpsil [style*="--w:"]{width:var(--w)}@media only screen and (max-width:1024px){.gps-573021407269618804.gps.gpsil [style*="--h-tablet:"]{height:var(--h-tablet)}.gps-573021407269618804.gps.gpsil [style*="--maxh-tablet:"]{max-height:var(--maxh-tablet)}.gps-573021407269618804.gps.gpsil [style*="--w-tablet:"]{width:var(--w-tablet)}}@media only screen and (max-width:767px){.gps-573021407269618804.gps.gpsil [style*="--h-mobile:"]{height:var(--h-mobile)}.gps-573021407269618804.gps.gpsil [style*="--maxh-mobile:"]{max-height:var(--maxh-mobile)}.gps-573021407269618804.gps.gpsil [style*="--w-mobile:"]{width:var(--w-mobile)}}.gps-573021407269618804 .focus-visible\:gp-shadow-none,.gps-573021407269618804 .gp-shadow-xl{--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000}.gps-573021407269618804 .gp-fixed{position:fixed}.gps-573021407269618804 .gp-absolute{position:absolute}.gps-573021407269618804 .gp-relative{position:relative}.gps-573021407269618804 .gp-inset-0{inset:0}.gps-573021407269618804 .gp-right-0{right:0}.gps-573021407269618804 .gp-top-0{top:0}.gps-573021407269618804 .gp-z-50{z-index:50}.gps-573021407269618804 .gp-z-\[999999\]{z-index:999999}.gps-573021407269618804 .gp-block{display:block}.gps-573021407269618804 .gp-inline-block{display:inline-block}.gps-573021407269618804 .gp-flex{display:flex}.gps-573021407269618804 .gp-inline-flex{display:inline-flex}.gps-573021407269618804 .\!gp-hidden{display:none!important}.gps-573021407269618804 .gp-hidden{display:none}.gps-573021407269618804 .gp-h-full{height:100%}.gps-573021407269618804 .gp-h-screen{height:100vh}.gps-573021407269618804 .\!gp-max-h-screen{max-height:100vh!important}.gps-573021407269618804 .gp-max-h-full{max-height:100%}.gps-573021407269618804 .gp-w-full{width:100%}.gps-573021407269618804 .gp-max-w-full{max-width:100%}@keyframes gp-fadeIn{0%{opacity:0}to{opacity:1}}.gps-573021407269618804 .gp-animate-\[fadeIn_0\.4s_ease-in\]{animation:gp-fadeIn .4s ease-in}.gps-573021407269618804 .gp-items-center{align-items:center}.gps-573021407269618804 .gp-justify-center{justify-content:center}.gps-573021407269618804 .gp-overflow-hidden{overflow:hidden}.gps-573021407269618804 .gp-overflow-y-auto{overflow-y:auto}.gps-573021407269618804 .gp-overflow-x-hidden{overflow-x:hidden}.gps-573021407269618804 .gp-bg-transparent{background-color:transparent}.gps-573021407269618804 .gp-align-middle{vertical-align:middle}.gps-573021407269618804 .gp-shadow-xl{--tw-shadow:0 20px 25px -5px rgba(0,0,0,.1),0 8px 10px -6px rgba(0,0,0,.1);--tw-shadow-colored:0 20px 25px -5px var(--tw-shadow-color),0 8px 10px -6px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)}.gps-573021407269618804 .gp-outline-none{outline:2px solid transparent;outline-offset:2px}.gps-573021407269618804 .gp-transition-all{transition-duration:.15s;transition-property:all;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-573021407269618804 .gp-duration-100{transition-duration:.1s}.gps-573021407269618804 .focus-visible\:gp-shadow-none:focus-visible{--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)}.gps-573021407269618804 .focus-visible\:gp-outline-none:focus-visible{outline:2px solid transparent;outline-offset:2px}@media (max-width:1024px){.gps-573021407269618804 .tablet\:\!gp-hidden{display:none!important}.gps-573021407269618804 .tablet\:gp-hidden{display:none}}@media (max-width:767px){.gps-573021407269618804 .mobile\:\!gp-hidden{display:none!important}.gps-573021407269618804 .mobile\:gp-hidden{display:none}}</style>

    <gp-dialog class="gps-lazy" data-id="gEbrpZ3lJk" style="--d:contents;--d-tablet:contents;--d-mobile:contents" data-gp-setting='{"dialogId":"gEbrpZ3lJk","trigger":"none","triggerTime":10,"frequency":3,"triggerPercentage":10,"mobileTrigger":"none","mobileTriggerTime":10,"closeOnClickOutside":true,"frequencyMode":"once","display":{"desktop":true,"tablet":true,"mobile":true}}'>
      <dialog class=" gp-dialog gp-bg-transparent" aria-label="Popup 2" data-gp-dialog-id="gEbrpZ3lJk"
        style="z-index:100001"
      >
        <div
          aria-label="Backdrop"
          class="gp-fixed gp-inset-0 gp-z-50 gp-flex gp-overflow-y-auto gp-overflow-x-hidden gp-items-center gp-justify-center"
        >
        
            <div
              class="gp-fixed gp-inset-0 gp-block gp-transition-all gp-duration-100"
              data-gp-dialog-overlay
              style="--op:0.7;--bgc:#121212"
            ></div>
        
          <span class="gp-inline-block gp-h-screen gp-align-middle"></span>
          <div
            role="dialog"
            aria-label="Dialog content"
            class="gp-relative gp-max-h-full gp-max-w-full  gp-animate-[fadeIn_0.4s_ease-in] gp-shadow-xl gp-overflow-hidden"
            style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--w:600px;--w-tablet:600px;--w-mobile:100%;--minh:300px;--h-tablet:300px;--h-mobile:300px;--maxh:;--maxh-tablet:;--maxh-mobile:"
          >
            <button
              type="button"
              data-gp-dialog-close
              tabindex="-1"
              class="gp-absolute gp-top-0 gp-right-0 gp-z-[999999] gp-inline-flex gp-items-center gp-justify-center gp-outline-none focus-visible:gp-shadow-none focus-visible:gp-outline-none"
              style="--w:32px;--h:32px;--c:var(--g-c-line-3, line-3);--bgc:#ffffff"
            >
              <svg
                viewBox="0 0 12 12"
                fill="currentColor"
                style="--w:12px;--h:12px"
              >
                <path d="M7.1,6l4.5,4.5l-1.1,1.1L6,7.1l-4.5,4.5l-1.1-1.1L4.9,6L0.5,1.5l1.1-1.1L6,4.9l4.5-4.5l1.1,1.1L7.1,6z"></path>
              </svg>
            </button>
            <div
              style="--pt:var(--g-s-2xl);--pl:var(--g-s-2xl);--pb:var(--g-s-2xl);--pr:var(--g-s-2xl);--shadow:none;--op:100%;--minh:300px;--h-tablet:300px;--h-mobile:300px;--maxh:;--maxh-tablet:;--maxh-mobile:;border-radius:inherit;--bgc:#FFFFFF"
              aria-label="Dialog body"
              class="gp-h-full gp-w-full gp-overflow-y-auto gp-overflow-x-hidden !gp-max-h-screen">
                <div>
                  
                </div>
            </div>
          </div>
        </div>
      </dialog>
    </gp-dialog>
    <script src="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-dialog.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  


{% schema %}
  {
    
    "name": "Section 18",
    "tag": "section",
    "class": "gps-573021407269618804 gps gpsil",
    "settings": [{"type":"paragraph","content":"Section design by GemPages. [Click here to start design](https://admin.shopify.com/store/i0pixe-10/apps/gempages-cro/app/shopify/edit?pageType=GP_PRODUCT&editorId=572751048691811510&sectionId=573021407269618804)"},{"type":"select","label":"Section Preload","id":"section_preload","options":[{"label":"On","value":"true"},{"label":"Off","value":"false"},{"label":"Off Lazy Script","value":"off_lazy_script"}],"default":"false"},{"type":"text","label":"Checksum","id":"checksum"}],
    "blocks": [{"type": "@app"}],
    "locales": {}
  }
{% endschema %}
