

<style {% if section.settings.section_preload == "false" %} class="gps-link gps-style" type="text/disabled-css" {% endif %}>.gps-573038060820759443.gps.gpsil [style*="--bg:"]{background:var(--bg)}.gps-573038060820759443.gps.gpsil [style*="--hvr-bg:"]:hover{background:var(--hvr-bg)}.gps-573038060820759443.gps.gpsil [style*="--bga:"]{background-attachment:var(--bga)}.gps-573038060820759443.gps.gpsil [style*="--bgc:"]{background-color:var(--bgc)}.gps-573038060820759443.gps.gpsil [style*="--bgi:"]{background-image:var(--bgi)}.gps-573038060820759443.gps.gpsil [style*="--bgp:"]{background-position:var(--bgp)}.gps-573038060820759443.gps.gpsil [style*="--bgr:"]{background-repeat:var(--bgr)}.gps-573038060820759443.gps.gpsil [style*="--bgs:"]{background-size:var(--bgs)}.gps-573038060820759443.gps.gpsil [style*="--b:"]{border:var(--b)}.gps-573038060820759443.gps.gpsil [style*="--hvr-b:"]:hover{border:var(--hvr-b)}.gps-573038060820759443.gps.gpsil [style*="--bb:"]{border-bottom:var(--bb)}.gps-573038060820759443.gps.gpsil [style*="--bc:"]{border-color:var(--bc)}.gps-573038060820759443.gps.gpsil [style*="--bblr:"]{border-bottom-left-radius:var(--bblr)}.gps-573038060820759443.gps.gpsil [style*="--bbrr:"]{border-bottom-right-radius:var(--bbrr)}.gps-573038060820759443.gps.gpsil [style*="--bs:"]{border-style:var(--bs)}.gps-573038060820759443.gps.gpsil [style*="--bt:"]{border-top:var(--bt)}.gps-573038060820759443.gps.gpsil [style*="--btlr:"]{border-top-left-radius:var(--btlr)}.gps-573038060820759443.gps.gpsil [style*="--btrr:"]{border-top-right-radius:var(--btrr)}.gps-573038060820759443.gps.gpsil [style*="--bw:"]{border-width:var(--bw)}.gps-573038060820759443.gps.gpsil [style*="--bottom:"]{bottom:var(--bottom)}.gps-573038060820759443.gps.gpsil [style*="--shadow:"]{box-shadow:var(--shadow)}.gps-573038060820759443.gps.gpsil [style*="--c:"]{color:var(--c)}.gps-573038060820759443.gps.gpsil [style*="--ff:"]{font-family:var(--ff)}.gps-573038060820759443.gps.gpsil [style*="--size:"]{font-size:var(--size)}.gps-573038060820759443.gps.gpsil [style*="--weight:"]{font-weight:var(--weight)}.gps-573038060820759443.gps.gpsil [style*="--fs:"]{font-style:var(--fs)}.gps-573038060820759443.gps.gpsil [style*="--h:"]{height:var(--h)}.gps-573038060820759443.gps.gpsil [style*="--left:"]{left:var(--left)}.gps-573038060820759443.gps.gpsil [style*="--ls:"]{letter-spacing:var(--ls)}.gps-573038060820759443.gps.gpsil [style*="--lh:"]{line-height:var(--lh)}.gps-573038060820759443.gps.gpsil [style*="--m:"]{margin:var(--m)}.gps-573038060820759443.gps.gpsil [style*="--mb:"]{margin-bottom:var(--mb)}.gps-573038060820759443.gps.gpsil [style*="--mr:"]{margin-right:var(--mr)}.gps-573038060820759443.gps.gpsil [style*="--op:"]{opacity:var(--op)}.gps-573038060820759443.gps.gpsil [style*="--o:"]{order:var(--o)}.gps-573038060820759443.gps.gpsil [style*="--p:"]{padding:var(--p)}.gps-573038060820759443.gps.gpsil [style*="--pb:"]{padding-bottom:var(--pb)}.gps-573038060820759443.gps.gpsil [style*="--pl:"]{padding-left:var(--pl)}.gps-573038060820759443.gps.gpsil [style*="--pr:"]{padding-right:var(--pr)}.gps-573038060820759443.gps.gpsil [style*="--pt:"]{padding-top:var(--pt)}.gps-573038060820759443.gps.gpsil [style*="--pos:"]{position:var(--pos)}.gps-573038060820759443.gps.gpsil [style*="--ta:"]{text-align:var(--ta)}.gps-573038060820759443.gps.gpsil [style*="--top:"]{top:var(--top)}.gps-573038060820759443.gps.gpsil [style*="--t:"]{transform:var(--t)}.gps-573038060820759443.gps.gpsil [style*="--w:"]{width:var(--w)}@media only screen and (max-width:1024px){.gps-573038060820759443.gps.gpsil [style*="--bottom-tablet:"]{bottom:var(--bottom-tablet)}.gps-573038060820759443.gps.gpsil [style*="--size-tablet:"]{font-size:var(--size-tablet)}.gps-573038060820759443.gps.gpsil [style*="--h-tablet:"]{height:var(--h-tablet)}.gps-573038060820759443.gps.gpsil [style*="--left-tablet:"]{left:var(--left-tablet)}.gps-573038060820759443.gps.gpsil [style*="--lh-tablet:"]{line-height:var(--lh-tablet)}.gps-573038060820759443.gps.gpsil [style*="--pos-tablet:"]{position:var(--pos-tablet)}.gps-573038060820759443.gps.gpsil [style*="--top-tablet:"]{top:var(--top-tablet)}.gps-573038060820759443.gps.gpsil [style*="--t-tablet:"]{transform:var(--t-tablet)}.gps-573038060820759443.gps.gpsil [style*="--w-tablet:"]{width:var(--w-tablet)}}@media only screen and (max-width:767px){.gps-573038060820759443.gps.gpsil [style*="--bottom-mobile:"]{bottom:var(--bottom-mobile)}.gps-573038060820759443.gps.gpsil [style*="--size-mobile:"]{font-size:var(--size-mobile)}.gps-573038060820759443.gps.gpsil [style*="--h-mobile:"]{height:var(--h-mobile)}.gps-573038060820759443.gps.gpsil [style*="--left-mobile:"]{left:var(--left-mobile)}.gps-573038060820759443.gps.gpsil [style*="--lh-mobile:"]{line-height:var(--lh-mobile)}.gps-573038060820759443.gps.gpsil [style*="--pos-mobile:"]{position:var(--pos-mobile)}.gps-573038060820759443.gps.gpsil [style*="--top-mobile:"]{top:var(--top-mobile)}.gps-573038060820759443.gps.gpsil [style*="--t-mobile:"]{transform:var(--t-mobile)}.gps-573038060820759443.gps.gpsil [style*="--w-mobile:"]{width:var(--w-mobile)}}.gps-573038060820759443 .\!gp-pointer-events-none{pointer-events:none!important}.gps-573038060820759443 .gp-relative{position:relative}.gps-573038060820759443 .gp-sticky{position:sticky}.gps-573038060820759443 .gp-z-1{z-index:1}.gps-573038060820759443 .gp-flex{display:flex}.gps-573038060820759443 .gp-inline-flex{display:inline-flex}.gps-573038060820759443 .\!gp-hidden{display:none!important}.gps-573038060820759443 .gp-hidden{display:none}.gps-573038060820759443 .gp-h-full{height:100%}.gps-573038060820759443 .gp-max-w-full{max-width:100%}.gps-573038060820759443 .gp-shrink-0{flex-shrink:0}.gps-573038060820759443 .gp-flex-col{flex-direction:column}.gps-573038060820759443 .gp-items-center{align-items:center}.gps-573038060820759443 .gp-justify-center{justify-content:center}.gps-573038060820759443 .gp-overflow-hidden{overflow:hidden}.gps-573038060820759443 .gp-break-words{overflow-wrap:break-word}.gps-573038060820759443 .gp-rounded-none{border-radius:0}.gps-573038060820759443 .gp-text-center{text-align:center}.gps-573038060820759443 .gp-text-g-text-3{color:var(--g-c-text-3)}.gps-573038060820759443 .gp-no-underline{text-decoration-line:none}.gps-573038060820759443 .gp-transition-colors{transition-duration:.15s;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-573038060820759443 .gp-duration-300{transition-duration:.3s}.gps-573038060820759443 .disabled\:gp-btn-disabled:disabled{cursor:default}.gps-573038060820759443 .disabled\:gp-pointer-events-none:disabled{pointer-events:none}.gps-573038060820759443 .disabled\:gp-opacity-30:disabled{opacity:.3}@media (hover:hover) and (pointer:fine){.gps-573038060820759443 .gp-group\/button:hover .group-hover\/button\:\!gp-text-inherit{color:inherit!important}}.gps-573038060820759443 .gp-group\/button:active .group-active\/button\:\!gp-text-inherit{color:inherit!important}.gps-573038060820759443 .gp-group\/button[data-state=loading] .group-data-\[state\=loading\]\/button\:gp-invisible{visibility:hidden}@media (max-width:1024px){.gps-573038060820759443 .tablet\:\!gp-hidden{display:none!important}.gps-573038060820759443 .tablet\:gp-hidden{display:none}}@media (max-width:767px){.gps-573038060820759443 .mobile\:\!gp-hidden{display:none!important}.gps-573038060820759443 .mobile\:gp-hidden{display:none}}.gps-573038060820759443 .\[\&\>svg\]\:\!gp-h-\[var\(--height-desktop\)\]>svg{height:var(--height-desktop)!important}.gps-573038060820759443 .\[\&\>svg\]\:\!gp-w-auto>svg{width:auto!important}@media (max-width:1024px){.gps-573038060820759443 .tablet\:\[\&\>svg\]\:\!gp-h-\[var\(--height-tablet\)\]>svg{height:var(--height-tablet)!important}}@media (max-width:767px){.gps-573038060820759443 .mobile\:\[\&\>svg\]\:\!gp-h-\[var\(--height-mobile\)\]>svg{height:var(--height-mobile)!important}}.gps-573038060820759443 .\[\&_gp-button_a\]\:\!gp-pointer-events-auto gp-button a{pointer-events:auto!important}.gps-573038060820759443 .\[\&_p\]\:gp-whitespace-pre-line p{white-space:pre-line}</style>

    
    <gp-sticky
    data-id="go16Wlh6qk"
      gp-data='{"uid":"go16Wlh6qk","setting":{"display":{"desktop":"always"}},"advanced":{"d":{"desktop":true,"tablet":true,"mobile":true}}}'
      id="go16Wlh6qk"
      data-id="go16Wlh6qk"
      class="go16Wlh6qk {% if section.settings.section_preload == "false" %}gps-lazy {% endif %}  !gp-pointer-events-none [&_gp-button_a]:!gp-pointer-events-auto"
      style="margin:0 auto;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--left:50%;--t:translateX(-50%);--left-tablet:50%;--t-tablet:translateX(-50%);--left-mobile:50%;--t-mobile:translateX(-50%);--top:auto;--bottom:0;--pos:fixed;--top-tablet:auto;--bottom-tablet:0;--pos-tablet:fixed;--top-mobile:auto;--bottom-mobile:0;--pos-mobile:fixed;--w:96%;--w-tablet:100%;--w-mobile:100%;z-index:100000"
    >
      <div 
         
        style="--bgc:#00000000;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
      >
        <div
      
      
      class="gwm4-fc7az gp-relative gp-flex gp-flex-col"
    >
      
  <gp-button >
  <div
    style="--mb:var(--g-s-s);--mr:50px;--pb:110px;--pr:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--ta:right"
    
  >
    <style>
    .g1q27485p4.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
      
      border-bottom-left-radius: 999999px;
      border-bottom-right-radius: 999999px;
      border-top-left-radius: 999999px;
      border-top-right-radius: 999999px;
      
    }

    .g1q27485p4:hover::before {
      
      
    }

    .g1q27485p4:hover .gp-button-icon {
      color: undefined;
    }

     .g1q27485p4 .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .g1q27485p4:hover .gp-button-price {
      color: undefined;
    }

    .g1q27485p4 .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .g1q27485p4 .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .g1q27485p4:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <a
      href="#scroll-to-top" target="_self" data-id="g1q27485p4" aria-label
      
      data-state="idle"
      class="g1q27485p4 gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-text-center"
      style="--bg:#000000;--hvr-bg:#000000;--bblr:999999px;--bbrr:999999px;--btlr:999999px;--btrr:999999px;--shadow:none;--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--pl:12px;--pr:12px;--pt:12px;--pb:12px;--c:var(--g-c-text-3, text-3);--size:24px;--size-tablet:14px;--size-mobile:14px;--fs:normal;--ff:var(--g-font-Assistant, 'Assistant'), var(--g-font-body, body);--weight:400;--ls:0.6px;--lh:140%;--lh-tablet:140%;--lh-mobile:140%"
    >
      
    <div
    class="gp-inline-flex">
    <span
        class="gp-inline-flex gp-button-icon gp-transition-colors gp-duration-300 gp-shrink-0 gp-items-center gp-justify-center group-data-[state=loading]/button:gp-invisible gp-z-1 [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
        style="--height-desktop:1em;--height-tablet:1em;--height-mobile:1em"
      ><svg height="20" width="20" xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 256 256" fill="currentColor" data-id="508817550824243560">
              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M208.49,143.51a12,12,0,0,1-17,17L140,109V224a12,12,0,0,1-24,0V109L64.49,160.49a12,12,0,0,1-17-17l72-72a12,12,0,0,1,17,0ZM216,28H40a12,12,0,0,0,0,24H216a12,12,0,0,0,0-24Z" /></svg></span>
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--size:24px;--size-tablet:14px;--size-mobile:14px;--c:var(--g-c-text-3, text-3)"
      >
        
      </span>
      </div>
    
    </a>
  </div>
  </gp-button>

    </div>
      </div>
    </gp-sticky>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-sticky.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
<style {% if section.settings.section_preload == "false" %} class="gps-link" type="text/disabled-css" {% endif %}>@font-face {
  font-family: 'Assistant';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/assistant/v23/2sDPZGJYnIjSi6H75xkZZE1I0yCmYzzQtuZnIGaV2Q.woff) format('woff');
}
/* hebrew */
@font-face {
  font-family: 'Assistant';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/assistant/v23/2sDPZGJYnIjSi6H75xkZZE1I0yCmYzzQtuZnIGSV2YOrrM95Dylg.woff) format('woff');
  unicode-range: U+0307-0308, U+0590-05FF, U+200C-2010, U+20AA, U+25CC, U+FB1D-FB4F;
}
/* latin-ext */
@font-face {
  font-family: 'Assistant';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/assistant/v23/2sDPZGJYnIjSi6H75xkZZE1I0yCmYzzQtuZnIGiV2YOrrM95Dylg.woff) format('woff');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Assistant';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/assistant/v23/2sDPZGJYnIjSi6H75xkZZE1I0yCmYzzQtuZnIGaV2YOrrM95Dw.woff) format('woff');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
</style>

{% schema %}
  {
    
    "name": "Section 17",
    "tag": "section",
    "class": "gps-573038060820759443 gps gpsil",
    "settings": [{"type":"paragraph","content":"Section design by GemPages. [Click here to start design](https://admin.shopify.com/store/i0pixe-10/apps/gempages-cro/app/shopify/edit?pageType=GP_PRODUCT&editorId=572751048691811510&sectionId=573038060820759443)"},{"type":"select","label":"Section Preload","id":"section_preload","options":[{"label":"On","value":"true"},{"label":"Off","value":"false"},{"label":"Off Lazy Script","value":"off_lazy_script"}],"default":"false"},{"type":"text","label":"Checksum","id":"checksum"}],
    "blocks": [{"type": "@app"}],
    "locales": {}
  }
{% endschema %}
