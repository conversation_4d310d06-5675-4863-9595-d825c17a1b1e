

<style {% if section.settings.section_preload == "false" %} class="gps-link gps-style" type="text/disabled-css" {% endif %}>.gps-573039469586809972.gps.gpsil [style*="--ai:"]{align-items:var(--ai)}.gps-573039469586809972.gps.gpsil [style*="--aspect:"]{aspect-ratio:var(--aspect)}.gps-573039469586809972.gps.gpsil [style*="--bg:"]{background:var(--bg)}.gps-573039469586809972.gps.gpsil [style*="--hvr-bg:"]:hover{background:var(--hvr-bg)}.gps-573039469586809972.gps.gpsil [style*="--bga:"]{background-attachment:var(--bga)}.gps-573039469586809972.gps.gpsil [style*="--bgc:"]{background-color:var(--bgc)}.gps-573039469586809972.gps.gpsil [style*="--bgi:"]{background-image:var(--bgi)}.gps-573039469586809972.gps.gpsil [style*="--bgp:"]{background-position:var(--bgp)}.gps-573039469586809972.gps.gpsil [style*="--bgr:"]{background-repeat:var(--bgr)}.gps-573039469586809972.gps.gpsil [style*="--bgs:"]{background-size:var(--bgs)}.gps-573039469586809972.gps.gpsil [style*="--b:"]{border:var(--b)}.gps-573039469586809972.gps.gpsil [style*="--hvr-b:"]:hover{border:var(--hvr-b)}.gps-573039469586809972.gps.gpsil [style*="--bb:"]{border-bottom:var(--bb)}.gps-573039469586809972.gps.gpsil [style*="--bbw:"]{border-bottom-width:var(--bbw)}.gps-573039469586809972.gps.gpsil [style*="--bc:"]{border-color:var(--bc)}.gps-573039469586809972.gps.gpsil [style*="--hvr-bc:"]:hover{border-color:var(--hvr-bc)}.gps-573039469586809972.gps.gpsil [style*="--bblr:"]{border-bottom-left-radius:var(--bblr)}.gps-573039469586809972.gps.gpsil [style*="--bbrr:"]{border-bottom-right-radius:var(--bbrr)}.gps-573039469586809972.gps.gpsil [style*="--radius:"]{border-radius:var(--radius)}.gps-573039469586809972.gps.gpsil [style*="--bs:"]{border-style:var(--bs)}.gps-573039469586809972.gps.gpsil [style*="--hvr-bs:"]:hover{border-style:var(--hvr-bs)}.gps-573039469586809972.gps.gpsil [style*="--bt:"]{border-top:var(--bt)}.gps-573039469586809972.gps.gpsil [style*="--btlr:"]{border-top-left-radius:var(--btlr)}.gps-573039469586809972.gps.gpsil [style*="--btrr:"]{border-top-right-radius:var(--btrr)}.gps-573039469586809972.gps.gpsil [style*="--bw:"]{border-width:var(--bw)}.gps-573039469586809972.gps.gpsil [style*="--hvr-bw:"]:hover{border-width:var(--hvr-bw)}.gps-573039469586809972.gps.gpsil [style*="--shadow:"]{box-shadow:var(--shadow)}.gps-573039469586809972.gps.gpsil [style*="--c:"]{color:var(--c)}.gps-573039469586809972.gps.gpsil [style*="--hvr-c:"]:hover{color:var(--hvr-c)}.gps-573039469586809972.gps.gpsil [style*="--cg:"]{-moz-column-gap:var(--cg);column-gap:var(--cg)}.gps-573039469586809972.gps.gpsil [style*="--fd:"]{flex-direction:var(--fd)}.gps-573039469586809972.gps.gpsil [style*="--ff:"]{font-family:var(--ff)}.gps-573039469586809972.gps.gpsil [style*="--size:"]{font-size:var(--size)}.gps-573039469586809972.gps.gpsil [style*="--weight:"]{font-weight:var(--weight)}.gps-573039469586809972.gps.gpsil [style*="--fs:"]{font-style:var(--fs)}.gps-573039469586809972.gps.gpsil [style*="--gg:"]{grid-gap:var(--gg)}.gps-573039469586809972.gps.gpsil [style*="--gtc:"]{grid-template-columns:var(--gtc)}.gps-573039469586809972.gps.gpsil [style*="--h:"]{height:var(--h)}.gps-573039469586809972.gps.gpsil [style*="--jc:"]{justify-content:var(--jc)}.gps-573039469586809972.gps.gpsil [style*="--ls:"]{letter-spacing:var(--ls)}.gps-573039469586809972.gps.gpsil [style*="--lh:"]{line-height:var(--lh)}.gps-573039469586809972.gps.gpsil [style*="--m:"]{margin:var(--m)}.gps-573039469586809972.gps.gpsil [style*="--mb:"]{margin-bottom:var(--mb)}.gps-573039469586809972.gps.gpsil [style*="--mr:"]{margin-right:var(--mr)}.gps-573039469586809972.gps.gpsil [style*="--mt:"]{margin-top:var(--mt)}.gps-573039469586809972.gps.gpsil [style*="--minw:"]{min-width:var(--minw)}.gps-573039469586809972.gps.gpsil [style*="--objf:"]{-o-object-fit:var(--objf);object-fit:var(--objf)}.gps-573039469586809972.gps.gpsil [style*="--op:"]{opacity:var(--op)}.gps-573039469586809972.gps.gpsil [style*="--o:"]{order:var(--o)}.gps-573039469586809972.gps.gpsil [style*="--pc:"]{place-content:var(--pc)}.gps-573039469586809972.gps.gpsil [style*="--p:"]{padding:var(--p)}.gps-573039469586809972.gps.gpsil [style*="--pb:"]{padding-bottom:var(--pb)}.gps-573039469586809972.gps.gpsil [style*="--pl:"]{padding-left:var(--pl)}.gps-573039469586809972.gps.gpsil [style*="--pr:"]{padding-right:var(--pr)}.gps-573039469586809972.gps.gpsil [style*="--pt:"]{padding-top:var(--pt)}.gps-573039469586809972.gps.gpsil [style*="--ta:"]{text-align:var(--ta)}.gps-573039469586809972.gps.gpsil [style*="--tt:"]{text-transform:var(--tt)}.gps-573039469586809972.gps.gpsil [style*="--t:"]{transform:var(--t)}.gps-573039469586809972.gps.gpsil [style*="--w:"]{width:var(--w)}.gps-573039469586809972.gps.gpsil [style*="--line-clamp:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp);display:-webkit-box;overflow:hidden}@media only screen and (max-width:1024px){.gps-573039469586809972.gps.gpsil [style*="--bbw-tablet:"]{border-bottom-width:var(--bbw-tablet)}.gps-573039469586809972.gps.gpsil [style*="--size-tablet:"]{font-size:var(--size-tablet)}.gps-573039469586809972.gps.gpsil [style*="--h-tablet:"]{height:var(--h-tablet)}.gps-573039469586809972.gps.gpsil [style*="--lh-tablet:"]{line-height:var(--lh-tablet)}.gps-573039469586809972.gps.gpsil [style*="--pb-tablet:"]{padding-bottom:var(--pb-tablet)}.gps-573039469586809972.gps.gpsil [style*="--pl-tablet:"]{padding-left:var(--pl-tablet)}.gps-573039469586809972.gps.gpsil [style*="--pr-tablet:"]{padding-right:var(--pr-tablet)}.gps-573039469586809972.gps.gpsil [style*="--pt-tablet:"]{padding-top:var(--pt-tablet)}.gps-573039469586809972.gps.gpsil [style*="--w-tablet:"]{width:var(--w-tablet)}.gps-573039469586809972.gps.gpsil [style*="--line-clamp-tablet:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-tablet);display:-webkit-box;overflow:hidden}}@media only screen and (max-width:767px){.gps-573039469586809972.gps.gpsil [style*="--bbw-mobile:"]{border-bottom-width:var(--bbw-mobile)}.gps-573039469586809972.gps.gpsil [style*="--size-mobile:"]{font-size:var(--size-mobile)}.gps-573039469586809972.gps.gpsil [style*="--gg-mobile:"]{grid-gap:var(--gg-mobile)}.gps-573039469586809972.gps.gpsil [style*="--gtc-mobile:"]{grid-template-columns:var(--gtc-mobile)}.gps-573039469586809972.gps.gpsil [style*="--h-mobile:"]{height:var(--h-mobile)}.gps-573039469586809972.gps.gpsil [style*="--lh-mobile:"]{line-height:var(--lh-mobile)}.gps-573039469586809972.gps.gpsil [style*="--mb-mobile:"]{margin-bottom:var(--mb-mobile)}.gps-573039469586809972.gps.gpsil [style*="--mt-mobile:"]{margin-top:var(--mt-mobile)}.gps-573039469586809972.gps.gpsil [style*="--pc-mobile:"]{place-content:var(--pc-mobile)}.gps-573039469586809972.gps.gpsil [style*="--pb-mobile:"]{padding-bottom:var(--pb-mobile)}.gps-573039469586809972.gps.gpsil [style*="--pl-mobile:"]{padding-left:var(--pl-mobile)}.gps-573039469586809972.gps.gpsil [style*="--pr-mobile:"]{padding-right:var(--pr-mobile)}.gps-573039469586809972.gps.gpsil [style*="--pt-mobile:"]{padding-top:var(--pt-mobile)}.gps-573039469586809972.gps.gpsil [style*="--w-mobile:"]{width:var(--w-mobile)}.gps-573039469586809972.gps.gpsil [style*="--line-clamp-mobile:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-mobile);display:-webkit-box;overflow:hidden}}.gps-573039469586809972 .-gp-rotate-90,.gps-573039469586809972 .gp-rotate-180,.gps-573039469586809972 .gp-rotate-90{--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1}.gps-573039469586809972 .focus\:\!gp-shadow-none,.gps-573039469586809972 .hover\:\!gp-shadow-none{--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000}.gps-573039469586809972 .gp-g-paragraph-1{font-family:var(--g-p1-ff);font-size:var(--g-p1-size);font-style:var(--g-p1-fs);font-weight:var(--g-p1-weight);letter-spacing:var(--g-p1-ls);line-height:var(--g-p1-lh)}.gps-573039469586809972 .gp-fixed{position:fixed}.gps-573039469586809972 .gp-relative{position:relative}.gps-573039469586809972 .gp-top-\[27px\]{top:27px}.gps-573039469586809972 .gp-z-1{z-index:1}.gps-573039469586809972 .gp-z-100{z-index:100}.gps-573039469586809972 .gp-mx-auto{margin-left:auto;margin-right:auto}.gps-573039469586809972 .gp-mb-0{margin-bottom:0}.gps-573039469586809972 .gp-block{display:block}.gps-573039469586809972 .gp-flex{display:flex}.gps-573039469586809972 .gp-inline-flex{display:inline-flex}.gps-573039469586809972 .gp-grid{display:grid}.gps-573039469586809972 .gp-contents{display:contents}.gps-573039469586809972 .\!gp-hidden{display:none!important}.gps-573039469586809972 .gp-hidden{display:none}.gps-573039469586809972 .\!gp-h-auto{height:auto!important}.gps-573039469586809972 .gp-h-auto{height:auto}.gps-573039469586809972 .gp-h-full{height:100%}.gps-573039469586809972 .gp-min-h-0{min-height:0}.gps-573039469586809972 .gp-w-full{width:100%}.gps-573039469586809972 .gp-max-w-full{max-width:100%}.gps-573039469586809972 .gp-flex-none{flex:none}.gps-573039469586809972 .-gp-rotate-90{--tw-rotate:-90deg}.gps-573039469586809972 .-gp-rotate-90,.gps-573039469586809972 .gp-rotate-180{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.gps-573039469586809972 .gp-rotate-180{--tw-rotate:180deg}.gps-573039469586809972 .gp-rotate-90{--tw-rotate:90deg;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.gps-573039469586809972 .gp-cursor-pointer{cursor:pointer}.gps-573039469586809972 .gp-grid-rows-\[1fr\]{grid-template-rows:1fr}.gps-573039469586809972 .gp-flex-col{flex-direction:column}.gps-573039469586809972 .gp-items-center{align-items:center}.gps-573039469586809972 .gp-justify-start{justify-content:flex-start}.gps-573039469586809972 .gp-justify-center{justify-content:center}.gps-573039469586809972 .gp-gap-y-0{row-gap:0}.gps-573039469586809972 .gp-overflow-hidden{overflow:hidden}.gps-573039469586809972 .gp-overflow-clip{overflow:clip}.gps-573039469586809972 .gp-break-words{overflow-wrap:break-word}.gps-573039469586809972 .gp-rounded{border-radius:4px}.gps-573039469586809972 .gp-rounded-none{border-radius:0}.gps-573039469586809972 .\!gp-bg-none{background-image:none!important}.gps-573039469586809972 .gp-p-\[16px\]{padding:16px}.gps-573039469586809972 .gp-text-center{text-align:center}.gps-573039469586809972 .gp-text-\[14px\]{font-size:14px}.gps-573039469586809972 .gp-leading-normal{line-height:1.5}.gps-573039469586809972 .gp-text-g-bg-3{color:var(--g-c-bg-3)}.gps-573039469586809972 .gp-text-g-text-3{color:var(--g-c-text-3)}.gps-573039469586809972 .gp-no-underline{text-decoration-line:none}.gps-573039469586809972 .\!gp-outline-none{outline:2px solid transparent!important;outline-offset:2px!important}.gps-573039469586809972 .gp-transition-all{transition-duration:.15s;transition-property:all;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-573039469586809972 .gp-transition-colors{transition-duration:.15s;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-573039469586809972 .gp-duration-200{transition-duration:.2s}.gps-573039469586809972 .gp-duration-300{transition-duration:.3s}.gps-573039469586809972 .gp-duration-500{transition-duration:.5s}.gps-573039469586809972 .gp-ease-in-out{transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-573039469586809972 .disabled\:gp-btn-disabled:disabled{cursor:default}@media (hover:hover) and (pointer:fine){.gps-573039469586809972 .hover\:gp-text-g-text-3:hover{color:var(--g-c-text-3)}.gps-573039469586809972 .hover\:\!gp-shadow-none:hover{--tw-shadow:0 0 #0000!important;--tw-shadow-colored:0 0 #0000!important;box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)!important}}.gps-573039469586809972 .focus\:\!gp-shadow-none:focus{--tw-shadow:0 0 #0000!important;--tw-shadow-colored:0 0 #0000!important;box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)!important}.gps-573039469586809972 .disabled\:gp-pointer-events-none:disabled{pointer-events:none}.gps-573039469586809972 .disabled\:gp-opacity-30:disabled{opacity:.3}@media (hover:hover) and (pointer:fine){.gps-573039469586809972 .gp-group\/button:hover .group-hover\/button\:\!gp-text-inherit{color:inherit!important}}.gps-573039469586809972 .gp-group\/button:active .group-active\/button\:\!gp-text-inherit{color:inherit!important}.gps-573039469586809972 .gp-group\/button[data-state=loading] .group-data-\[state\=loading\]\/button\:gp-invisible{visibility:hidden}@media (max-width:1024px){.gps-573039469586809972 .tablet\:gp-block{display:block}.gps-573039469586809972 .tablet\:\!gp-hidden{display:none!important}.gps-573039469586809972 .tablet\:gp-hidden{display:none}.gps-573039469586809972 .tablet\:gp-h-auto{height:auto}.gps-573039469586809972 .tablet\:gp-flex-none{flex:none}}@media (max-width:767px){.gps-573039469586809972 .mobile\:gp-block{display:block}.gps-573039469586809972 .mobile\:\!gp-hidden{display:none!important}.gps-573039469586809972 .mobile\:gp-hidden{display:none}.gps-573039469586809972 .mobile\:gp-h-auto{height:auto}.gps-573039469586809972 .mobile\:gp-flex-none{flex:none}}.gps-573039469586809972 .\[\&\>svg\]\:\!gp-h-\[var\(--height-iconCollapseSize\)\]>svg{height:var(--height-iconCollapseSize)!important}.gps-573039469586809972 .\[\&\>svg\]\:\!gp-w-auto>svg{width:auto!important}.gps-573039469586809972 .\[\&_\*\]\:gp-max-w-full *{max-width:100%}.gps-573039469586809972 .\[\&_p\]\:gp-inline p{display:inline}.gps-573039469586809972 .\[\&_p\]\:gp-whitespace-pre-line p{white-space:pre-line}.gps-573039469586809972 .\[\&_p\]\:after\:gp-whitespace-pre-wrap p:after{content:var(--tw-content);white-space:pre-wrap}.gps-573039469586809972 .\[\&_p\]\:after\:gp-content-\[\'\\A\'\] p:after{--tw-content:"\A";content:var(--tw-content)}</style>

       
      
            <section
              class="gp-mx-auto gp-max-w-full {% if section.settings.section_preload == "false" %}gps-lazy {% endif %}"
              style="--w:100%;--w-tablet:100%;--w-mobile:100%;--pl:0px;--pl-tablet:0px;--pl-mobile:0px;--pr:0px;--pr-tablet:0px;--pr-mobile:0px"
            >
              
    <div
      id="gJcwg5BFFW" data-id="gJcwg5BFFW"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pt:var(--g-s-4xl);--pt-mobile:var(--g-s-4xl);--pl-mobile:0px;--pr-mobile:0px;--pt-tablet:var(--g-s-4xl);--pl-tablet:0px;--pr-tablet:0px;--cg:0px;--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:#242424;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gJcwg5BFFW gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--ai:normal;--jc:start;--o:0"
      class="gPSbJhq1SE gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      parentTag="Col" id="gTG9AcO51E" data-id="gTG9AcO51E"
        style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--mb:0px;--pt:0px;--pl:15px;--pb:0px;--pr:15px;--cg:32px;--pc:start;--gtc:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gTG9AcO51E gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gastIxvqtL gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gQ3-1U74rr">
    <div
      parentTag="Col"
        class="gQ3-1U74rr "
        style="--tt:default;--ta:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(undefined)"
      >
      <div class="gp-flex" style="--jc:center">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text-g-bg-3 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:center;--c:#FFFFFF;--fs:normal;--ff:var(--g-font-heading, heading);--weight:700;--ls:normal;--size:33px;--size-tablet:33px;--size-mobile:29px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggQ3-1U74rr_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gooBT7q7gh">
    <div
      parentTag="Col"
        class="gooBT7q7gh "
        style="--tt:default;--ta:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mt:var(--g-s-l);--pl-mobile:15px;--pr-mobile:15px"
      >
      <div class="gp-flex" style="--jc:center">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text-g-text-3 gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:center;--line-clamp:0;--line-clamp-tablet:0;--line-clamp-mobile:0;--c:#FFFFFF;word-break:break-word;--radius:var(--g-radius-small);overflow:hidden"
        >{{ section.settings.ggooBT7q7gh_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    <div gp-el-wrapper style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pt:var(--g-s-2xl);--pl:0px;--pb:24px;--pr:0px;--pt-mobile:var(--g-s-2xl);--pl-mobile:0px;--pb-mobile:24px;--pr-mobile:0px" class="gfvkX2QVVy ">
      
    <gp-form
      id="gfvkX2QVVy"
      data-id="gfvkX2QVVy"
      
      data-submit-action=''
      data-callback='{"link":"","target":"_self"}'
    >
      <a
        hidden
        id="gp-form-callback-gfvkX2QVVy"
        href="" target=""
      >
      </a>
      {% form 'customer', class: 'gp-form-gfvkX2QVVy ', id: 'contact_form_gfvkX2QVVy' %}
        {% if form.errors %}
          <div
            id="gp-form-error-gfvkX2QVVy"
            class="gp-form-message gp-hidden gp-top-[27px] gp-z-100 gp-fixed gp-rounded gp-p-[16px] gp-text-[14px] gp-leading-normal"
            style="background-color:#FFE9E9;left:50%;transform:translateX(-50%);color:#EA3335"
          >
            {{ section.settings.ggfvkX2QVVy_errorMessage }}
          </div>
        {% endif %}
        <div popover id="my-popover-gfvkX2QVVy">
        <style>
            #my-popover-gfvkX2QVVy::backdrop {
              width: fit-content;
              height: fit-content;
            }
        </style>
        <div
          id="gp-form-success-gfvkX2QVVy"
          class="gp-form-message gp-hidden gp-top-[27px] gp-z-100 gp-fixed gp-rounded gp-p-[16px] gp-text-[14px] gp-leading-normal"
          style="background-color:#F2FFEC;left:50%;transform:translateX(-50%);color:#52C41A"
        >
          {{ section.settings.ggfvkX2QVVy_successMessage }}
        </div></div>
        
       
      
    <div
      parentTag="Newsletter" id="g5eXPgI_C7" data-id="g5eXPgI_C7"
        style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--mb:0px;--cg:0px;--pc:start;--gtc:minmax(0, 9fr) minmax(0, 3fr);--gtc-mobile:minmax(0, 8fr) minmax(0, 4fr);--w:570px;--w-tablet:570px;--w-mobile:100%;--bgc:#FFFFFF;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="g5eXPgI_C7 gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gRwB8mcpDU gp-relative gp-flex gp-flex-col"
    >
      
    <div
    data-id="gN9SXHSpQf"
      class="gN9SXHSpQf"
      style="--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
    >
      <input
        type="email"
        class="gp-form-item gp-g-paragraph-1 !gp-outline-none !gp-h-auto focus:!gp-shadow-none hover:!gp-shadow-none !gp-bg-none"
        style="--pl:16px;--pr:16px;--pt:12px;--pb:12px;--w:100%;--w-tablet:313px;--w-mobile:100%;--bs:none;--bw:0px 0px 0px 0px;--bc:transparent;--shadow:none"
        placeholder="{{ section.settings.ggN9SXHSpQf_placeholder }}"
        {% if true %}
        required
        {% endif %}
        name="contact[email]"
        value=""
        autocomplete="email"
      ></input>
    </div>
  
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="giz1-5uj-C gp-relative gp-flex gp-flex-col"
    >
      
    
  <gp-button >
  <div
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--ta:left"
    
  >
    <style>
    .goDo9_9yAp.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 0px 0px 0px 0px;
  border-color: transparent;
  
      
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
    }

    .goDo9_9yAp:hover::before {
      border-style: none;
  border-width: 0px 0px 0px 0px;
  border-color: transparent;
  
      
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
    }

    .goDo9_9yAp:hover .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

     .goDo9_9yAp .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .goDo9_9yAp:hover .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .goDo9_9yAp .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .goDo9_9yAp .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .goDo9_9yAp:hover .gp-product-dot-price {
      color: var(--g-c-text-3, text-3);
    }
  </style>
    <button
      type="submit" data-id="goDo9_9yAp" aria-label="Subscribe"
      
      data-state="idle"
      class="goDo9_9yAp gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none hover:gp-text-g-text-3 gp-text-g-text-3 gp-g-paragraph-1"
      style="--hvr-bg:rgba(36, 36, 36, 0.8);--bg:#2352E7;--pl:24px;--pr:24px;--pt:12px;--pb:12px;--pl-tablet:24px;--pr-tablet:24px;--pt-tablet:12px;--pb-tablet:12px;--pl-mobile:24px;--pr-mobile:24px;--pt-mobile:12px;--pb-mobile:12px;--w:100%;--w-tablet:100%;--w-mobile:100%;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--hvr-c:var(--g-c-text-3, text-3);--c:var(--g-c-text-3, text-3)"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--hvr-c:var(--g-c-text-3, text-3);--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.ggoDo9_9yAp_label }}
      </span>
      </div>
    
    </button>
  </div>
  </gp-button>

  
    </div>
    </div>
   
    
      {% endform %}
    </gp-form>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-form.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
      </div>
    </div>
    </div>
   
    
       
      
    <div
      parentTag="Col" id="gLmp4P8y-j" data-id="gLmp4P8y-j"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:none;--d-tablet:grid;--op:100%;--radius:var(--g-radius-small);--pt:var(--g-s-xl);--pl:15px;--pb:30px;--pr:15px;--pt-mobile:30px;--pl-mobile:var(--g-s-l);--pb-mobile:30px;--pr-mobile:var(--g-s-l);--cg:29px;--pc:start;--gtc:minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr);--gtc-mobile:minmax(0, 6fr) minmax(0, 6fr);--w:var(--g-ct-w, 1200px);--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gLmp4P8y-j gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--ai:normal;--jc:start;--o:0"
      class="gIaNmqJGlL gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gsyQUdO5qq">
    <div
      parentTag="Col"
        class="gsyQUdO5qq "
        style="--tt:default;--ta:left;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(undefined)"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text-g-bg-3 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#FFFFFF;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--size:19px;--size-tablet:19px;--size-mobile:14px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggsyQUdO5qq_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
  <gp-button >
  <div
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mt:var(--g-s-xl);--ta:left"
    
  >
    <style>
    .gRATab67XW.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 0px;
  border-color: transparent;
  
      border-radius: var(--g-radius-small);
    }

    .gRATab67XW:hover::before {
      
      
    }

    .gRATab67XW:hover .gp-button-icon {
      color: undefined;
    }

     .gRATab67XW .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .gRATab67XW:hover .gp-button-price {
      color: undefined;
    }

    .gRATab67XW .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .gRATab67XW .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .gRATab67XW:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <a
      href="#" target="_self" data-id="gRATab67XW" aria-label="<p>About Us</p>"
      
      data-state="idle"
      class="gRATab67XW gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-text-center gp-g-paragraph-1"
      style="--hvr-bg:transparent;--bg:transparent;--radius:var(--g-radius-small);--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--c:var(--g-c-text-3, text-3)"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.ggRATab67XW_label }}
      </span>
      </div>
    
    </a>
  </div>
  </gp-button>

  <gp-button >
  <div
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mt:var(--g-s-l);--ta:left"
    
  >
    <style>
    .g4FT1jArF7.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 0px;
  border-color: transparent;
  
      border-radius: var(--g-radius-small);
    }

    .g4FT1jArF7:hover::before {
      
      
    }

    .g4FT1jArF7:hover .gp-button-icon {
      color: undefined;
    }

     .g4FT1jArF7 .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .g4FT1jArF7:hover .gp-button-price {
      color: undefined;
    }

    .g4FT1jArF7 .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .g4FT1jArF7 .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .g4FT1jArF7:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <a
      href="#" target="_self" data-id="g4FT1jArF7" aria-label="<p>FAQ</p>"
      
      data-state="idle"
      class="g4FT1jArF7 gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-text-center gp-g-paragraph-1"
      style="--hvr-bg:transparent;--bg:transparent;--radius:var(--g-radius-small);--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--c:var(--g-c-text-3, text-3)"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.gg4FT1jArF7_label }}
      </span>
      </div>
    
    </a>
  </div>
  </gp-button>

  <gp-button >
  <div
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mt:var(--g-s-l);--ta:left"
    
  >
    <style>
    .gr_svpW299.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 0px;
  border-color: transparent;
  
      border-radius: var(--g-radius-small);
    }

    .gr_svpW299:hover::before {
      
      
    }

    .gr_svpW299:hover .gp-button-icon {
      color: undefined;
    }

     .gr_svpW299 .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .gr_svpW299:hover .gp-button-price {
      color: undefined;
    }

    .gr_svpW299 .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .gr_svpW299 .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .gr_svpW299:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <a
      href="#" target="_self" data-id="gr_svpW299" aria-label="<p>Terms &amp; Conditions</p>"
      
      data-state="idle"
      class="gr_svpW299 gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-text-center gp-g-paragraph-1"
      style="--hvr-bg:transparent;--bg:transparent;--radius:var(--g-radius-small);--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--c:var(--g-c-text-3, text-3)"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.ggr_svpW299_label }}
      </span>
      </div>
    
    </a>
  </div>
  </gp-button>

  <gp-button >
  <div
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mt:var(--g-s-l);--ta:left"
    
  >
    <style>
    .ggcxnhVXcR.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 0px;
  border-color: transparent;
  
      border-radius: var(--g-radius-small);
    }

    .ggcxnhVXcR:hover::before {
      
      
    }

    .ggcxnhVXcR:hover .gp-button-icon {
      color: undefined;
    }

     .ggcxnhVXcR .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .ggcxnhVXcR:hover .gp-button-price {
      color: undefined;
    }

    .ggcxnhVXcR .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .ggcxnhVXcR .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .ggcxnhVXcR:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <a
      href="#" target="_self" data-id="ggcxnhVXcR" aria-label="<p>Privacy Policy</p>"
      
      data-state="idle"
      class="ggcxnhVXcR gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-text-center gp-g-paragraph-1"
      style="--hvr-bg:transparent;--bg:transparent;--radius:var(--g-radius-small);--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--c:var(--g-c-text-3, text-3)"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.gggcxnhVXcR_label }}
      </span>
      </div>
    
    </a>
  </div>
  </gp-button>

    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gBGQ_D3SnZ gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gUfkeB_Wxl">
    <div
      parentTag="Col"
        class="gUfkeB_Wxl "
        style="--tt:default;--ta:left;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(undefined)"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text-g-bg-3 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#FFFFFF;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--size:19px;--size-tablet:19px;--size-mobile:14px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggUfkeB_Wxl_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
  <gp-button >
  <div
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mt:var(--g-s-xl);--ta:left"
    
  >
    <style>
    .gagJ7v5K9g.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 0px;
  border-color: transparent;
  
      border-radius: var(--g-radius-small);
    }

    .gagJ7v5K9g:hover::before {
      
      
    }

    .gagJ7v5K9g:hover .gp-button-icon {
      color: undefined;
    }

     .gagJ7v5K9g .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .gagJ7v5K9g:hover .gp-button-price {
      color: undefined;
    }

    .gagJ7v5K9g .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .gagJ7v5K9g .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .gagJ7v5K9g:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <a
      href="#" target="_self" data-id="gagJ7v5K9g" aria-label="<p>Home</p>"
      
      data-state="idle"
      class="gagJ7v5K9g gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-text-center gp-g-paragraph-1"
      style="--hvr-bg:transparent;--bg:transparent;--radius:var(--g-radius-small);--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--c:var(--g-c-text-3, text-3)"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.ggagJ7v5K9g_label }}
      </span>
      </div>
    
    </a>
  </div>
  </gp-button>

  <gp-button >
  <div
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mt:var(--g-s-l);--ta:left"
    
  >
    <style>
    .gJKkJoW6X9.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 0px;
  border-color: transparent;
  
      border-radius: var(--g-radius-small);
    }

    .gJKkJoW6X9:hover::before {
      
      
    }

    .gJKkJoW6X9:hover .gp-button-icon {
      color: undefined;
    }

     .gJKkJoW6X9 .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .gJKkJoW6X9:hover .gp-button-price {
      color: undefined;
    }

    .gJKkJoW6X9 .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .gJKkJoW6X9 .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .gJKkJoW6X9:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <a
      href="#" target="_self" data-id="gJKkJoW6X9" aria-label="<p><u>3D Printing</u></p>"
      
      data-state="idle"
      class="gJKkJoW6X9 gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-text-center gp-g-paragraph-1"
      style="--hvr-bg:transparent;--bg:transparent;--radius:var(--g-radius-small);--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--c:var(--g-c-text-3, text-3)"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.ggJKkJoW6X9_label }}
      </span>
      </div>
    
    </a>
  </div>
  </gp-button>

  <gp-button >
  <div
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mt:var(--g-s-l);--ta:left"
    
  >
    <style>
    .gFiKU4uExT.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 0px;
  border-color: transparent;
  
      border-radius: var(--g-radius-small);
    }

    .gFiKU4uExT:hover::before {
      
      
    }

    .gFiKU4uExT:hover .gp-button-icon {
      color: undefined;
    }

     .gFiKU4uExT .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .gFiKU4uExT:hover .gp-button-price {
      color: undefined;
    }

    .gFiKU4uExT .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .gFiKU4uExT .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .gFiKU4uExT:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <a
      href="#" target="_self" data-id="gFiKU4uExT" aria-label="<p><u>Accessories</u></p>"
      
      data-state="idle"
      class="gFiKU4uExT gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-text-center gp-g-paragraph-1"
      style="--hvr-bg:transparent;--bg:transparent;--radius:var(--g-radius-small);--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--c:var(--g-c-text-3, text-3)"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.ggFiKU4uExT_label }}
      </span>
      </div>
    
    </a>
  </div>
  </gp-button>

  <gp-button >
  <div
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mt:var(--g-s-l);--ta:left"
    
  >
    <style>
    .g5XQOqL3GM.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 0px;
  border-color: transparent;
  
      border-radius: var(--g-radius-small);
    }

    .g5XQOqL3GM:hover::before {
      
      
    }

    .g5XQOqL3GM:hover .gp-button-icon {
      color: undefined;
    }

     .g5XQOqL3GM .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .g5XQOqL3GM:hover .gp-button-price {
      color: undefined;
    }

    .g5XQOqL3GM .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .g5XQOqL3GM .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .g5XQOqL3GM:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <a
      href="#" target="_self" data-id="g5XQOqL3GM" aria-label="<p><u>Support</u></p>"
      
      data-state="idle"
      class="g5XQOqL3GM gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-text-center gp-g-paragraph-1"
      style="--hvr-bg:transparent;--bg:transparent;--radius:var(--g-radius-small);--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--c:var(--g-c-text-3, text-3)"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.gg5XQOqL3GM_label }}
      </span>
      </div>
    
    </a>
  </div>
  </gp-button>

    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gZkqez8WPw gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="go7PuYIFnB">
    <div
      parentTag="Col"
        class="go7PuYIFnB "
        style="--tt:default;--ta:left;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(undefined)"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text-g-bg-3 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#FFFFFF;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--size:19px;--size-tablet:19px;--size-mobile:14px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggo7PuYIFnB_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
  <gp-button >
  <div
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mt:var(--g-s-xl);--ta:left"
    
  >
    <style>
    .grFgKAFWB2.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 0px;
  border-color: transparent;
  
      border-radius: var(--g-radius-small);
    }

    .grFgKAFWB2:hover::before {
      
      
    }

    .grFgKAFWB2:hover .gp-button-icon {
      color: undefined;
    }

     .grFgKAFWB2 .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .grFgKAFWB2:hover .gp-button-price {
      color: undefined;
    }

    .grFgKAFWB2 .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .grFgKAFWB2 .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .grFgKAFWB2:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <a
      href="#" target="_self" data-id="grFgKAFWB2" aria-label="Customer Service"
      
      data-state="idle"
      class="grFgKAFWB2 gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-text-center gp-g-paragraph-1"
      style="--hvr-bg:transparent;--bg:transparent;--radius:var(--g-radius-small);--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--c:var(--g-c-text-3, text-3)"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.ggrFgKAFWB2_label }}
      </span>
      </div>
    
    </a>
  </div>
  </gp-button>

  <gp-button >
  <div
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mt:var(--g-s-l);--ta:left"
    
  >
    <style>
    .g0v1ZTj8II.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 0px;
  border-color: transparent;
  
      border-radius: var(--g-radius-small);
    }

    .g0v1ZTj8II:hover::before {
      
      
    }

    .g0v1ZTj8II:hover .gp-button-icon {
      color: undefined;
    }

     .g0v1ZTj8II .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .g0v1ZTj8II:hover .gp-button-price {
      color: undefined;
    }

    .g0v1ZTj8II .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .g0v1ZTj8II .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .g0v1ZTj8II:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <a
      href="#" target="_self" data-id="g0v1ZTj8II" aria-label="Returns & Exchanges"
      
      data-state="idle"
      class="g0v1ZTj8II gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-text-center gp-g-paragraph-1"
      style="--hvr-bg:transparent;--bg:transparent;--radius:var(--g-radius-small);--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--c:var(--g-c-text-3, text-3)"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.gg0v1ZTj8II_label }}
      </span>
      </div>
    
    </a>
  </div>
  </gp-button>

  <gp-button >
  <div
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mt:var(--g-s-l);--ta:left"
    
  >
    <style>
    .g92hVOZCa_.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 0px;
  border-color: transparent;
  
      border-radius: var(--g-radius-small);
    }

    .g92hVOZCa_:hover::before {
      
      
    }

    .g92hVOZCa_:hover .gp-button-icon {
      color: undefined;
    }

     .g92hVOZCa_ .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .g92hVOZCa_:hover .gp-button-price {
      color: undefined;
    }

    .g92hVOZCa_ .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .g92hVOZCa_ .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .g92hVOZCa_:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <a
      href="#" target="_self" data-id="g92hVOZCa_" aria-label="FAQs"
      
      data-state="idle"
      class="g92hVOZCa_ gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-text-center gp-g-paragraph-1"
      style="--hvr-bg:transparent;--bg:transparent;--radius:var(--g-radius-small);--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--c:var(--g-c-text-3, text-3)"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.gg92hVOZCa__label }}
      </span>
      </div>
    
    </a>
  </div>
  </gp-button>

  <gp-button >
  <div
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mt:var(--g-s-l);--ta:left"
    
  >
    <style>
    .gxzan03qxn.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 0px;
  border-color: transparent;
  
      border-radius: var(--g-radius-small);
    }

    .gxzan03qxn:hover::before {
      
      
    }

    .gxzan03qxn:hover .gp-button-icon {
      color: undefined;
    }

     .gxzan03qxn .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .gxzan03qxn:hover .gp-button-price {
      color: undefined;
    }

    .gxzan03qxn .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .gxzan03qxn .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .gxzan03qxn:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <a
      href="#" target="_self" data-id="gxzan03qxn" aria-label="Contact Us"
      
      data-state="idle"
      class="gxzan03qxn gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-text-center gp-g-paragraph-1"
      style="--hvr-bg:transparent;--bg:transparent;--radius:var(--g-radius-small);--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--c:var(--g-c-text-3, text-3)"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.ggxzan03qxn_label }}
      </span>
      </div>
    
    </a>
  </div>
  </gp-button>

    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gTRzfID0I2 gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g1LEIGFRdl">
    <div
      parentTag="Col"
        class="g1LEIGFRdl "
        style="--tt:default;--ta:left;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(undefined)"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text-g-bg-3 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#FFFFFF;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--size:19px;--size-tablet:19px;--size-mobile:14px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;overflow:hidden"
        >{{ section.settings.gg1LEIGFRdl_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gdWNromoJb">
    <div
      parentTag="Col"
        class="gdWNromoJb "
        style="--tt:default;--ta:left;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mt:var(--g-s-xl)"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text-g-text-3 gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:0;--line-clamp-tablet:0;--line-clamp-mobile:0;--c:#FFFFFF;word-break:break-word;--radius:var(--g-radius-small);overflow:hidden"
        >{{ section.settings.ggdWNromoJb_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g4obQbt7bl">
    <div
      parentTag="Col"
        class="g4obQbt7bl "
        style="--tt:default;--ta:left;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mt:var(--g-s-l)"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text-g-text-3 gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:0;--line-clamp-tablet:0;--line-clamp-mobile:0;--c:#FFFFFF;word-break:break-word;--radius:var(--g-radius-small);overflow:hidden"
        >{{ section.settings.gg4obQbt7bl_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
       
      
    <div
      parentTag="Col" id="gsVBsZgWdp" data-id="gsVBsZgWdp"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--radius:var(--g-radius-small);--mt:var(--g-s-l);--cg:24px;--pc:start;--gtc:minmax(0, auto) minmax(0, auto) minmax(0, auto) minmax(0, auto);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gsVBsZgWdp gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--ai:normal;--jc:start;--o:0"
      class="geZzDsjrPf gp-relative gp-flex gp-flex-col"
    >
      <div
    
     data-id="g3TWBDX97_"
    role="presentation"
    class="gp-group/image g3TWBDX97_ gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--ta:left"
    >
      <a
        class="pointer-events-auto gp-h-full gp-flex"
        href="https://www.facebook.com/groups/911760202804295" target="_self" title="Image Title"
        class="gp-h-full gp-w-full"
        style="border-radius:inherit;--jc:left"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" data-srcSet="https://ucarecdn.com/41bcb53a-329b-4d38-864a-290b550b82f5/-/format/auto/" srcset="https://ucarecdn.com/41bcb53a-329b-4d38-864a-290b550b82f5/-/format/auto/" />
      <source media="(max-width: 1024px)" data-srcSet="https://ucarecdn.com/41bcb53a-329b-4d38-864a-290b550b82f5/-/format/auto/" srcset="https://ucarecdn.com/41bcb53a-329b-4d38-864a-290b550b82f5/-/format/auto/" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex"
        src="https://ucarecdn.com/41bcb53a-329b-4d38-864a-290b550b82f5/-/format/auto/"
        data-src="https://ucarecdn.com/41bcb53a-329b-4d38-864a-290b550b82f5/-/format/auto/"
        width="100%"
        alt="Alt Image"
        style="--aspect:auto;--objf:cover;--w:9px;--w-tablet:9px;--w-mobile:9px;--h:auto;--h-tablet:auto;--h-mobile:auto"
      />
    
  </picture>
      </a>
  </div>
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="ggG-6Ialt1 gp-relative gp-flex gp-flex-col"
    >
      <div
    
     data-id="gE9ijQRDvV"
    role="presentation"
    class="gp-group/image gE9ijQRDvV gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mb:0px;--ta:left"
    >
      <a
        class="pointer-events-auto gp-h-full gp-flex"
        href="https://www.youtube.com/@zhuhaibeier" target="_blank" title="Image Title"
        class="gp-h-full gp-w-full"
        style="border-radius:inherit;--jc:left"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" data-srcSet="https://ucarecdn.com/4191bd2e-dbe8-46a6-9f71-5fa0281afe39/-/format/auto/" srcset="https://ucarecdn.com/4191bd2e-dbe8-46a6-9f71-5fa0281afe39/-/format/auto/" />
      <source media="(max-width: 1024px)" data-srcSet="https://ucarecdn.com/4191bd2e-dbe8-46a6-9f71-5fa0281afe39/-/format/auto/" srcset="https://ucarecdn.com/4191bd2e-dbe8-46a6-9f71-5fa0281afe39/-/format/auto/" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex"
        src="https://ucarecdn.com/4191bd2e-dbe8-46a6-9f71-5fa0281afe39/-/format/auto/"
        data-src="https://ucarecdn.com/4191bd2e-dbe8-46a6-9f71-5fa0281afe39/-/format/auto/"
        width="100%"
        alt="Alt Image"
        style="--aspect:auto;--objf:cover;--w:20px;--w-tablet:20px;--w-mobile:20px;--h:auto;--h-tablet:auto;--h-mobile:auto"
      />
    
  </picture>
      </a>
  </div>
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start;--d:none;--d-tablet:none;--d-mobile:none"
      class="gMbx2GoAuv gp-relative gp-flex gp-flex-col"
    >
      
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start;--d:none;--d-tablet:none;--d-mobile:none"
      class="gSFym1MFMv gp-relative gp-flex gp-flex-col"
    >
      
    </div>
    </div>
   
    
    </div>
    </div>
   
    
       
      
    <div
      parentTag="Col" id="gqUkXqpRVM" data-id="gqUkXqpRVM"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:none;--d-mobile:grid;--d-tablet:none;--op:100%;--radius:var(--g-radius-small);--pt:30px;--pb:30px;--pt-mobile:10px;--pl-mobile:15px;--pb-mobile:10px;--pr-mobile:15px;--cg:0px;--pc:start;--gtc:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gqUkXqpRVM gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--ai:normal;--jc:start;--o:0"
      class="gFz2iX5bAm gp-relative gp-flex gp-flex-col"
    >
      <div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-l)" class="g8PgmiWhZM ">
      
    <gp-accordion
      data-id="g8PgmiWhZM"
     uid="g8PgmiWhZM"
      class="gp-flex gp-w-full gp-flex-col "
      style="--gg:null;--gg-mobile:18px;border-radius:inherit"
      gp-data='{"setting":{"iconSvg":"<svg width=\"17\" height=\"16\" viewBox=\"0 0 17 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                  <path d=\"M5.64645 2.64645C5.84171 2.45118 6.15829 2.45118 6.35355 2.64645L11.3536 7.64645C11.5488 7.84171 11.5488 8.15829 11.3536 8.35355L6.35355 13.3536C6.15829 13.5488 5.84171 13.5488 5.64645 13.3536C5.45118 13.1583 5.45118 12.8417 5.64645 12.6464L10.2929 8L5.64645 3.35355C5.45118 3.15829 5.45118 2.84171 5.64645 2.64645Z\" fill=\"currentColor\"/>\n                  </svg>","isIconPlus":false,"activeKey":1,"expanded":false,"expandItem":false,"iconPosition":"right","iconGlobalSize":{"desktop":{"gap":"16px","height":"16px","width":"16px"}},"layoutHeader":"text-only","expandedMode":"single","configIconSize":16,"parentUid":"g8PgmiWhZM","childListNumber":[],"chidlrenUid":["gYF0NC1Evp","gHnO159qeK","gQuKjuOxEB","gjnxBBpZpa"]},"styles":{"color":{"active":"#FFFFFF","hover":"#FFFFFF","normal":"#FFFFFF"},"headerBorder":{"active":{"border":"solid","borderType":"style-1","borderWidth":"1px","color":"rgba(224, 224, 224, 0.1)","isCustom":true,"position":"bottom","width":"0px 0px 1px 0px"},"hover":{"border":"solid","borderType":"style-1","borderWidth":"1px","color":"rgba(224, 224, 224, 0.1)","isCustom":true,"position":"bottom","width":"0px 0px 1px 0px"},"normal":{"border":"solid","borderType":"style-1","borderWidth":"1px","color":"rgba(224, 224, 224, 0.1)","isCustom":true,"position":"bottom","width":"0px 0px 1px 0px"}},"fullWidth":{"desktop":true},"width":{"desktop":"1170px"},"itemHeaderSpacing":{"custom":{"mobile":{"horizontal":"0px","vertical":"10px"}}},"textColor":{"active":"#FFFFFF","hover":"#FFFFFF","normal":"#FFFFFF"},"iconColor":{"active":"#121212","hover":"#121212","normal":"#121212"},"contentSizePadding":{"desktop":{"gap":null,"padding":{"bottom":null,"left":null,"right":null,"top":null,"type":"custom"}},"mobile":{"gap":"18px","padding":{"bottom":"0px","left":"0px","right":"0px","top":"0px","type":"custom"}},"tablet":{"padding":{"bottom":null,"left":null,"right":null,"top":null,"type":"custom"}}},"headerContentPadding":{"desktop":{"padding":{"bottom":null,"left":null,"right":null,"top":null,"type":"custom"}},"mobile":{"padding":{"bottom":"10px","left":"0px","right":"0px","top":"10px","type":"custom"}},"tablet":{"padding":{"bottom":null,"left":null,"right":null,"top":null,"type":"custom"}}},"widthHeightSize":{"desktop":{"width":"100%"},"mobile":{"width":"100%"},"tablet":{"width":"100%"}}},"uid":"g8PgmiWhZM"}'
    >
    <div class="gp-hidden gp-rotate-90 -gp-rotate-90 gp-rotate-180"></div>
    <div
                class="gp-flex"
                style="--jc:left"
              >
                
  <div class="gp-overflow-clip" style="--w:100%;--w-tablet:100%;--w-mobile:100%;--height-iconCollapseSize:16px;--height-configIconSize:16px;--bs:solid;--bw:0px 0px 0px 0px;--bc:#121212">
    <div
      data-index="0"
      class="g8PgmiWhZM gp-accordion-item gp-overflow-hidden gp-child-item-g8PgmiWhZM"
      style="scrollBehavior:smooth"
    >
      <div
        aria-hidden
        uid="gYF0NC1Evp"
        id="gYF0NC1Evp"
        data-index="0"
        class="g8PgmiWhZM gp-accordion-item-g8PgmiWhZM-0 gp-flex gp-items-center gp-accordion-item_header gp-cursor-pointer"
        style="--pl:null;--pr:null;--pt:null;--pb:null;--pl-tablet:null;--pr-tablet:null;--pt-tablet:null;--pb-tablet:null;--pl-mobile:0px;--pr-mobile:0px;--pt-mobile:10px;--pb-mobile:10px;--fd:row-reverse;--jc:space-between;gap:16px;--hvr-c:#FFFFFF;--c:#FFFFFF;--bs:solid;--hvr-bs:solid;--bw:0px 0px 1px 0px;--hvr-bw:0px 0px 1px 0px;--bc:rgba(224, 224, 224, 0.1);--hvr-bc:rgba(224, 224, 224, 0.1)"
      >
       <style class="accordion-style">.gp-accordion-item-g8PgmiWhZM-0:hover 
      {
        .gp-collapsible-icon { 
          color: #FFFFFF !important;
        }

        .gp-icon { 
            color: #121212 !important;
        }
      }
    </style>
        <div class="gp-inline-flex">
        <span
            style="--hvr-c:#FFFFFF;--c:#FFFFFF;width:16px;height:16px"
            data-index="0"
            class="g8PgmiWhZM gp-accordion-item_icon gp-collapsible-icon gp-rotate-90 gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden gp-transition-all gp-duration-200 [&>svg]:!gp-h-[var(--height-iconCollapseSize)] [&>svg]:!gp-w-auto"
          ><svg width="17" height="16" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M5.64645 2.64645C5.84171 2.45118 6.15829 2.45118 6.35355 2.64645L11.3536 7.64645C11.5488 7.84171 11.5488 8.15829 11.3536 8.35355L6.35355 13.3536C6.15829 13.5488 5.84171 13.5488 5.64645 13.3536C5.45118 13.1583 5.45118 12.8417 5.64645 12.6464L10.2929 8L5.64645 3.35355C5.45118 3.15829 5.45118 2.84171 5.64645 2.64645Z" fill="currentColor"/>
                  </svg></span>

        </div>
      <div class="gp-flex gp-items-center gp-w-full" style="--gg:16px">
        
        
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="">
    <div
      
        class=" "
        
      >
      <div  >
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-overflow-hidden"
          style="--w:100%;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--size:19px;--size-tablet:19px;--size-mobile:17px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;overflow:hidden"
        >{{ section.settings.gg8PgmiWhZM_childItem_0 }}</div>
      </div>
    </div>
    </gp-text>
    
        
      </div>
      </div>
      <div
        uid="gYF0NC1Evp"
        data-index="0"
        data-show="false"
        class="g8PgmiWhZM gp-accordion-item_body gp-transition-all gp-grid gp-duration-500 gp-overflow-hidden"
        style="grid-template-rows:0fr;grid-template-columns:minmax(auto, 100%)"
      >
        <div
        uid="gYF0NC1Evp"
        data-index="0"
        class="g8PgmiWhZM gp-accordion-item_body-inner gp-min-h-0 gp-transition-all gp-duration-500"  style="--pl:null;--pr:null;--pl-tablet:null;--pr-tablet:null;--pl-mobile:0px;--pr-mobile:0px" >
            <div
      label="Block" tag="Col" type="component"
      
      class="gIrh__NJhh gp-relative gp-flex gp-flex-col"
    >
      
  <gp-button >
  <div
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mt:var(--g-s-xl);--ta:left"
    
  >
    <style>
    .gvGc8VDsoz.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 0px;
  border-color: transparent;
  
      border-radius: var(--g-radius-small);
    }

    .gvGc8VDsoz:hover::before {
      
      
    }

    .gvGc8VDsoz:hover .gp-button-icon {
      color: undefined;
    }

     .gvGc8VDsoz .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .gvGc8VDsoz:hover .gp-button-price {
      color: undefined;
    }

    .gvGc8VDsoz .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .gvGc8VDsoz .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .gvGc8VDsoz:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <a
      href="#" target="_self" data-id="gvGc8VDsoz" aria-label="About"
      
      data-state="idle"
      class="gvGc8VDsoz gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-text-center gp-g-paragraph-1"
      style="--hvr-bg:transparent;--bg:transparent;--radius:var(--g-radius-small);--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--c:var(--g-c-text-3, text-3)"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.ggvGc8VDsoz_label }}
      </span>
      </div>
    
    </a>
  </div>
  </gp-button>

  <gp-button >
  <div
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mt:var(--g-s-l);--ta:left"
    
  >
    <style>
    .gQq_kny8z_.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 0px;
  border-color: transparent;
  
      border-radius: var(--g-radius-small);
    }

    .gQq_kny8z_:hover::before {
      
      
    }

    .gQq_kny8z_:hover .gp-button-icon {
      color: undefined;
    }

     .gQq_kny8z_ .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .gQq_kny8z_:hover .gp-button-price {
      color: undefined;
    }

    .gQq_kny8z_ .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .gQq_kny8z_ .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .gQq_kny8z_:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <a
      href="#" target="_self" data-id="gQq_kny8z_" aria-label="Events"
      
      data-state="idle"
      class="gQq_kny8z_ gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-text-center gp-g-paragraph-1"
      style="--hvr-bg:transparent;--bg:transparent;--radius:var(--g-radius-small);--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--c:var(--g-c-text-3, text-3)"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.ggQq_kny8z__label }}
      </span>
      </div>
    
    </a>
  </div>
  </gp-button>

  <gp-button >
  <div
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mt:var(--g-s-l);--ta:left"
    
  >
    <style>
    .ggQOp0RgL9.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 0px;
  border-color: transparent;
  
      border-radius: var(--g-radius-small);
    }

    .ggQOp0RgL9:hover::before {
      
      
    }

    .ggQOp0RgL9:hover .gp-button-icon {
      color: undefined;
    }

     .ggQOp0RgL9 .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .ggQOp0RgL9:hover .gp-button-price {
      color: undefined;
    }

    .ggQOp0RgL9 .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .ggQOp0RgL9 .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .ggQOp0RgL9:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <a
      href="#" target="_self" data-id="ggQOp0RgL9" aria-label="Rentals"
      
      data-state="idle"
      class="ggQOp0RgL9 gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-text-center gp-g-paragraph-1"
      style="--hvr-bg:transparent;--bg:transparent;--radius:var(--g-radius-small);--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--c:var(--g-c-text-3, text-3)"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.gggQOp0RgL9_label }}
      </span>
      </div>
    
    </a>
  </div>
  </gp-button>

  <gp-button >
  <div
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mt:var(--g-s-l);--ta:left"
    
  >
    <style>
    .gLr00DN-vM.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 0px;
  border-color: transparent;
  
      border-radius: var(--g-radius-small);
    }

    .gLr00DN-vM:hover::before {
      
      
    }

    .gLr00DN-vM:hover .gp-button-icon {
      color: undefined;
    }

     .gLr00DN-vM .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .gLr00DN-vM:hover .gp-button-price {
      color: undefined;
    }

    .gLr00DN-vM .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .gLr00DN-vM .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .gLr00DN-vM:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <a
      href="#" target="_self" data-id="gLr00DN-vM" aria-label="Features"
      
      data-state="idle"
      class="gLr00DN-vM gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-text-center gp-g-paragraph-1"
      style="--hvr-bg:transparent;--bg:transparent;--radius:var(--g-radius-small);--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--c:var(--g-c-text-3, text-3)"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.ggLr00DN-vM_label }}
      </span>
      </div>
    
    </a>
  </div>
  </gp-button>

    </div>
        </div>
      </div>
    </div>
  </div>
  
              </div>
            <div
                class="gp-flex"
                style="--jc:left"
              >
                
  <div class="gp-overflow-clip" style="--w:100%;--w-tablet:100%;--w-mobile:100%;--height-iconCollapseSize:16px;--height-configIconSize:16px;--bs:solid;--bw:0px 0px 0px 0px;--bc:#121212">
    <div
      data-index="1"
      class="g8PgmiWhZM gp-accordion-item gp-overflow-hidden gp-child-item-g8PgmiWhZM"
      style="scrollBehavior:smooth"
    >
      <div
        aria-hidden
        uid="gHnO159qeK"
        id="gHnO159qeK"
        data-index="1"
        class="g8PgmiWhZM gp-accordion-item-g8PgmiWhZM-1 gp-flex gp-items-center gp-accordion-item_header gp-cursor-pointer"
        style="--pl:null;--pr:null;--pt:null;--pb:null;--pl-tablet:null;--pr-tablet:null;--pt-tablet:null;--pb-tablet:null;--pl-mobile:0px;--pr-mobile:0px;--pt-mobile:10px;--pb-mobile:10px;--fd:row-reverse;--jc:space-between;gap:16px;--hvr-c:#FFFFFF;--c:#FFFFFF;--bs:solid;--hvr-bs:solid;--bw:0px 0px 1px 0px;--hvr-bw:0px 0px 1px 0px;--bc:rgba(224, 224, 224, 0.1);--hvr-bc:rgba(224, 224, 224, 0.1)"
      >
       <style class="accordion-style">.gp-accordion-item-g8PgmiWhZM-1:hover 
      {
        .gp-collapsible-icon { 
          color: #FFFFFF !important;
        }

        .gp-icon { 
            color: #121212 !important;
        }
      }
    </style>
        <div class="gp-inline-flex">
        <span
            style="--hvr-c:#FFFFFF;--c:#FFFFFF;width:16px;height:16px"
            data-index="1"
            class="g8PgmiWhZM gp-accordion-item_icon gp-collapsible-icon gp-rotate-90 gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden gp-transition-all gp-duration-200 [&>svg]:!gp-h-[var(--height-iconCollapseSize)] [&>svg]:!gp-w-auto"
          ><svg width="17" height="16" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M5.64645 2.64645C5.84171 2.45118 6.15829 2.45118 6.35355 2.64645L11.3536 7.64645C11.5488 7.84171 11.5488 8.15829 11.3536 8.35355L6.35355 13.3536C6.15829 13.5488 5.84171 13.5488 5.64645 13.3536C5.45118 13.1583 5.45118 12.8417 5.64645 12.6464L10.2929 8L5.64645 3.35355C5.45118 3.15829 5.45118 2.84171 5.64645 2.64645Z" fill="currentColor"/>
                  </svg></span>

        </div>
      <div class="gp-flex gp-items-center gp-w-full" style="--gg:16px">
        
        
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="">
    <div
      
        class=" "
        
      >
      <div  >
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-overflow-hidden"
          style="--w:100%;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--size:19px;--size-tablet:19px;--size-mobile:17px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;overflow:hidden"
        >{{ section.settings.gg8PgmiWhZM_childItem_1 }}</div>
      </div>
    </div>
    </gp-text>
    
        
      </div>
      </div>
      <div
        uid="gHnO159qeK"
        data-index="1"
        data-show="false"
        class="g8PgmiWhZM gp-accordion-item_body gp-transition-all gp-grid gp-duration-500 gp-overflow-hidden"
        style="grid-template-rows:0fr;grid-template-columns:minmax(auto, 100%)"
      >
        <div
        uid="gHnO159qeK"
        data-index="1"
        class="g8PgmiWhZM gp-accordion-item_body-inner gp-min-h-0 gp-transition-all gp-duration-500"  style="--pl:null;--pr:null;--pl-tablet:null;--pr-tablet:null;--pl-mobile:0px;--pr-mobile:0px" >
            <div
      label="Block" tag="Col" type="component"
      
      class="gEKFlFxVTQ gp-relative gp-flex gp-flex-col"
    >
      
  <gp-button >
  <div
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mt:var(--g-s-xl);--ta:left"
    
  >
    <style>
    .gg6HZiOGVS.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 0px;
  border-color: transparent;
  
      border-radius: var(--g-radius-small);
    }

    .gg6HZiOGVS:hover::before {
      
      
    }

    .gg6HZiOGVS:hover .gp-button-icon {
      color: undefined;
    }

     .gg6HZiOGVS .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .gg6HZiOGVS:hover .gp-button-price {
      color: undefined;
    }

    .gg6HZiOGVS .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .gg6HZiOGVS .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .gg6HZiOGVS:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <a
      href="#" target="_self" data-id="gg6HZiOGVS" aria-label="Men"
      
      data-state="idle"
      class="gg6HZiOGVS gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-text-center gp-g-paragraph-1"
      style="--hvr-bg:transparent;--bg:transparent;--radius:var(--g-radius-small);--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--c:var(--g-c-text-3, text-3)"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.ggg6HZiOGVS_label }}
      </span>
      </div>
    
    </a>
  </div>
  </gp-button>

  <gp-button >
  <div
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mt:var(--g-s-l);--ta:left"
    
  >
    <style>
    .gZuQowhjZ-.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 0px;
  border-color: transparent;
  
      border-radius: var(--g-radius-small);
    }

    .gZuQowhjZ-:hover::before {
      
      
    }

    .gZuQowhjZ-:hover .gp-button-icon {
      color: undefined;
    }

     .gZuQowhjZ- .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .gZuQowhjZ-:hover .gp-button-price {
      color: undefined;
    }

    .gZuQowhjZ- .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .gZuQowhjZ- .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .gZuQowhjZ-:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <a
      href="#" target="_self" data-id="gZuQowhjZ-" aria-label="Women"
      
      data-state="idle"
      class="gZuQowhjZ- gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-text-center gp-g-paragraph-1"
      style="--hvr-bg:transparent;--bg:transparent;--radius:var(--g-radius-small);--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--c:var(--g-c-text-3, text-3)"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.ggZuQowhjZ-_label }}
      </span>
      </div>
    
    </a>
  </div>
  </gp-button>

  <gp-button >
  <div
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mt:var(--g-s-l);--ta:left"
    
  >
    <style>
    .gye8JvypHv.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 0px;
  border-color: transparent;
  
      border-radius: var(--g-radius-small);
    }

    .gye8JvypHv:hover::before {
      
      
    }

    .gye8JvypHv:hover .gp-button-icon {
      color: undefined;
    }

     .gye8JvypHv .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .gye8JvypHv:hover .gp-button-price {
      color: undefined;
    }

    .gye8JvypHv .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .gye8JvypHv .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .gye8JvypHv:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <a
      href="#" target="_self" data-id="gye8JvypHv" aria-label="Footweat"
      
      data-state="idle"
      class="gye8JvypHv gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-text-center gp-g-paragraph-1"
      style="--hvr-bg:transparent;--bg:transparent;--radius:var(--g-radius-small);--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--c:var(--g-c-text-3, text-3)"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.ggye8JvypHv_label }}
      </span>
      </div>
    
    </a>
  </div>
  </gp-button>

  <gp-button >
  <div
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mt:var(--g-s-l);--ta:left"
    
  >
    <style>
    .gey-GpmVRK.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 0px;
  border-color: transparent;
  
      border-radius: var(--g-radius-small);
    }

    .gey-GpmVRK:hover::before {
      
      
    }

    .gey-GpmVRK:hover .gp-button-icon {
      color: undefined;
    }

     .gey-GpmVRK .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .gey-GpmVRK:hover .gp-button-price {
      color: undefined;
    }

    .gey-GpmVRK .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .gey-GpmVRK .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .gey-GpmVRK:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <a
      href="#" target="_self" data-id="gey-GpmVRK" aria-label="Brands"
      
      data-state="idle"
      class="gey-GpmVRK gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-text-center gp-g-paragraph-1"
      style="--hvr-bg:transparent;--bg:transparent;--radius:var(--g-radius-small);--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--c:var(--g-c-text-3, text-3)"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.ggey-GpmVRK_label }}
      </span>
      </div>
    
    </a>
  </div>
  </gp-button>

    </div>
        </div>
      </div>
    </div>
  </div>
  
              </div>
            <div
                class="gp-flex"
                style="--jc:left"
              >
                
  <div class="gp-overflow-clip" style="--w:100%;--w-tablet:100%;--w-mobile:100%;--height-iconCollapseSize:16px;--height-configIconSize:16px;--bs:solid;--bw:0px 0px 0px 0px;--bc:#121212">
    <div
      data-index="2"
      class="g8PgmiWhZM gp-accordion-item gp-overflow-hidden gp-child-item-g8PgmiWhZM"
      style="scrollBehavior:smooth"
    >
      <div
        aria-hidden
        uid="gQuKjuOxEB"
        id="gQuKjuOxEB"
        data-index="2"
        class="g8PgmiWhZM gp-accordion-item-g8PgmiWhZM-2 gp-flex gp-items-center gp-accordion-item_header gp-cursor-pointer"
        style="--pl:null;--pr:null;--pt:null;--pb:null;--pl-tablet:null;--pr-tablet:null;--pt-tablet:null;--pb-tablet:null;--pl-mobile:0px;--pr-mobile:0px;--pt-mobile:10px;--pb-mobile:10px;--fd:row-reverse;--jc:space-between;gap:16px;--hvr-c:#FFFFFF;--c:#FFFFFF;--bs:solid;--hvr-bs:solid;--bw:0px 0px 1px 0px;--hvr-bw:0px 0px 1px 0px;--bc:rgba(224, 224, 224, 0.1);--hvr-bc:rgba(224, 224, 224, 0.1)"
      >
       <style class="accordion-style">.gp-accordion-item-g8PgmiWhZM-2:hover 
      {
        .gp-collapsible-icon { 
          color: #FFFFFF !important;
        }

        .gp-icon { 
            color: #121212 !important;
        }
      }
    </style>
        <div class="gp-inline-flex">
        <span
            style="--hvr-c:#FFFFFF;--c:#FFFFFF;width:16px;height:16px"
            data-index="2"
            class="g8PgmiWhZM gp-accordion-item_icon gp-collapsible-icon gp-rotate-90 gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden gp-transition-all gp-duration-200 [&>svg]:!gp-h-[var(--height-iconCollapseSize)] [&>svg]:!gp-w-auto"
          ><svg width="17" height="16" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M5.64645 2.64645C5.84171 2.45118 6.15829 2.45118 6.35355 2.64645L11.3536 7.64645C11.5488 7.84171 11.5488 8.15829 11.3536 8.35355L6.35355 13.3536C6.15829 13.5488 5.84171 13.5488 5.64645 13.3536C5.45118 13.1583 5.45118 12.8417 5.64645 12.6464L10.2929 8L5.64645 3.35355C5.45118 3.15829 5.45118 2.84171 5.64645 2.64645Z" fill="currentColor"/>
                  </svg></span>

        </div>
      <div class="gp-flex gp-items-center gp-w-full" style="--gg:16px">
        
        
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="">
    <div
      
        class=" "
        
      >
      <div  >
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-overflow-hidden"
          style="--w:100%;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--size:19px;--size-tablet:19px;--size-mobile:17px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;overflow:hidden"
        >{{ section.settings.gg8PgmiWhZM_childItem_2 }}</div>
      </div>
    </div>
    </gp-text>
    
        
      </div>
      </div>
      <div
        uid="gQuKjuOxEB"
        data-index="2"
        data-show="false"
        class="g8PgmiWhZM gp-accordion-item_body gp-transition-all gp-grid gp-duration-500 gp-overflow-hidden"
        style="grid-template-rows:0fr;grid-template-columns:minmax(auto, 100%)"
      >
        <div
        uid="gQuKjuOxEB"
        data-index="2"
        class="g8PgmiWhZM gp-accordion-item_body-inner gp-min-h-0 gp-transition-all gp-duration-500"  style="--pl:null;--pr:null;--pl-tablet:null;--pr-tablet:null;--pl-mobile:0px;--pr-mobile:0px" >
            <div
      label="Block" tag="Col" type="component"
      
      class="g-z8MGsPms gp-relative gp-flex gp-flex-col"
    >
      
  <gp-button >
  <div
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mt:var(--g-s-xl);--ta:left"
    
  >
    <style>
    .giqPw9m64R.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 0px;
  border-color: transparent;
  
      border-radius: var(--g-radius-small);
    }

    .giqPw9m64R:hover::before {
      
      
    }

    .giqPw9m64R:hover .gp-button-icon {
      color: undefined;
    }

     .giqPw9m64R .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .giqPw9m64R:hover .gp-button-price {
      color: undefined;
    }

    .giqPw9m64R .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .giqPw9m64R .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .giqPw9m64R:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <a
      href="#" target="_self" data-id="giqPw9m64R" aria-label="Customer Service"
      
      data-state="idle"
      class="giqPw9m64R gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-text-center gp-g-paragraph-1"
      style="--hvr-bg:transparent;--bg:transparent;--radius:var(--g-radius-small);--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--c:var(--g-c-text-3, text-3)"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.ggiqPw9m64R_label }}
      </span>
      </div>
    
    </a>
  </div>
  </gp-button>

  <gp-button >
  <div
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mt:var(--g-s-l);--ta:left"
    
  >
    <style>
    .gvfxs-sOuu.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 0px;
  border-color: transparent;
  
      border-radius: var(--g-radius-small);
    }

    .gvfxs-sOuu:hover::before {
      
      
    }

    .gvfxs-sOuu:hover .gp-button-icon {
      color: undefined;
    }

     .gvfxs-sOuu .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .gvfxs-sOuu:hover .gp-button-price {
      color: undefined;
    }

    .gvfxs-sOuu .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .gvfxs-sOuu .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .gvfxs-sOuu:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <a
      href="#" target="_self" data-id="gvfxs-sOuu" aria-label="Returns & Exchanges"
      
      data-state="idle"
      class="gvfxs-sOuu gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-text-center gp-g-paragraph-1"
      style="--hvr-bg:transparent;--bg:transparent;--radius:var(--g-radius-small);--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--c:var(--g-c-text-3, text-3)"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.ggvfxs-sOuu_label }}
      </span>
      </div>
    
    </a>
  </div>
  </gp-button>

  <gp-button >
  <div
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mt:var(--g-s-l);--ta:left"
    
  >
    <style>
    .gaqfY_HoMF.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 0px;
  border-color: transparent;
  
      border-radius: var(--g-radius-small);
    }

    .gaqfY_HoMF:hover::before {
      
      
    }

    .gaqfY_HoMF:hover .gp-button-icon {
      color: undefined;
    }

     .gaqfY_HoMF .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .gaqfY_HoMF:hover .gp-button-price {
      color: undefined;
    }

    .gaqfY_HoMF .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .gaqfY_HoMF .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .gaqfY_HoMF:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <a
      href="#" target="_self" data-id="gaqfY_HoMF" aria-label="FAQs"
      
      data-state="idle"
      class="gaqfY_HoMF gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-text-center gp-g-paragraph-1"
      style="--hvr-bg:transparent;--bg:transparent;--radius:var(--g-radius-small);--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--c:var(--g-c-text-3, text-3)"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.ggaqfY_HoMF_label }}
      </span>
      </div>
    
    </a>
  </div>
  </gp-button>

  <gp-button >
  <div
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mt:var(--g-s-l);--ta:left"
    
  >
    <style>
    .g-oCW7sNRJ.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 0px;
  border-color: transparent;
  
      border-radius: var(--g-radius-small);
    }

    .g-oCW7sNRJ:hover::before {
      
      
    }

    .g-oCW7sNRJ:hover .gp-button-icon {
      color: undefined;
    }

     .g-oCW7sNRJ .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .g-oCW7sNRJ:hover .gp-button-price {
      color: undefined;
    }

    .g-oCW7sNRJ .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .g-oCW7sNRJ .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .g-oCW7sNRJ:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <a
      href="#" target="_self" data-id="g-oCW7sNRJ" aria-label="Contact Us"
      
      data-state="idle"
      class="g-oCW7sNRJ gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-text-center gp-g-paragraph-1"
      style="--hvr-bg:transparent;--bg:transparent;--radius:var(--g-radius-small);--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--c:var(--g-c-text-3, text-3)"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.gg-oCW7sNRJ_label }}
      </span>
      </div>
    
    </a>
  </div>
  </gp-button>

    </div>
        </div>
      </div>
    </div>
  </div>
  
              </div>
            <div
                class="gp-flex"
                style="--jc:left"
              >
                
  <div class="gp-overflow-clip" style="--w:100%;--w-tablet:100%;--w-mobile:100%;--height-iconCollapseSize:16px;--height-configIconSize:16px;--bs:solid;--bw:0px 0px 0px 0px;--bc:#121212">
    <div
      data-index="3"
      class="g8PgmiWhZM gp-accordion-item gp-overflow-hidden gp-child-item-g8PgmiWhZM"
      style="scrollBehavior:smooth"
    >
      <div
        aria-hidden
        uid="gjnxBBpZpa"
        id="gjnxBBpZpa"
        data-index="3"
        class="g8PgmiWhZM gp-accordion-item-g8PgmiWhZM-3 gp-flex gp-items-center gp-accordion-item_header gp-cursor-pointer"
        style="--pl:null;--pr:null;--pt:null;--pb:null;--pl-tablet:null;--pr-tablet:null;--pt-tablet:null;--pb-tablet:null;--pl-mobile:0px;--pr-mobile:0px;--pt-mobile:10px;--pb-mobile:10px;--fd:row-reverse;--jc:space-between;gap:16px;--hvr-c:#FFFFFF;--c:#FFFFFF;--bs:solid;--hvr-bs:solid;--bw:0px 0px 1px 0px;--hvr-bw:0px 0px 1px 0px;--bc:rgba(224, 224, 224, 0.1);--hvr-bc:rgba(224, 224, 224, 0.1)"
      >
       <style class="accordion-style">.gp-accordion-item-g8PgmiWhZM-3:hover 
      {
        .gp-collapsible-icon { 
          color: #FFFFFF !important;
        }

        .gp-icon { 
            color: #121212 !important;
        }
      }
    </style>
        <div class="gp-inline-flex">
        <span
            style="--hvr-c:#FFFFFF;--c:#FFFFFF;width:16px;height:16px"
            data-index="3"
            class="g8PgmiWhZM gp-accordion-item_icon gp-collapsible-icon gp-rotate-90 gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden gp-transition-all gp-duration-200 [&>svg]:!gp-h-[var(--height-iconCollapseSize)] [&>svg]:!gp-w-auto"
          ><svg width="17" height="16" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M5.64645 2.64645C5.84171 2.45118 6.15829 2.45118 6.35355 2.64645L11.3536 7.64645C11.5488 7.84171 11.5488 8.15829 11.3536 8.35355L6.35355 13.3536C6.15829 13.5488 5.84171 13.5488 5.64645 13.3536C5.45118 13.1583 5.45118 12.8417 5.64645 12.6464L10.2929 8L5.64645 3.35355C5.45118 3.15829 5.45118 2.84171 5.64645 2.64645Z" fill="currentColor"/>
                  </svg></span>

        </div>
      <div class="gp-flex gp-items-center gp-w-full" style="--gg:16px">
        
        
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="">
    <div
      
        class=" "
        
      >
      <div  >
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-overflow-hidden"
          style="--w:100%;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--size:19px;--size-tablet:19px;--size-mobile:17px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;overflow:hidden"
        >{{ section.settings.gg8PgmiWhZM_childItem_3 }}</div>
      </div>
    </div>
    </gp-text>
    
        
      </div>
      </div>
      <div
        uid="gjnxBBpZpa"
        data-index="3"
        data-show="false"
        class="g8PgmiWhZM gp-accordion-item_body gp-transition-all gp-grid gp-duration-500 gp-overflow-hidden"
        style="grid-template-rows:0fr;grid-template-columns:minmax(auto, 100%)"
      >
        <div
        uid="gjnxBBpZpa"
        data-index="3"
        class="g8PgmiWhZM gp-accordion-item_body-inner gp-min-h-0 gp-transition-all gp-duration-500"  style="--pl:null;--pr:null;--pl-tablet:null;--pr-tablet:null;--pl-mobile:0px;--pr-mobile:0px" >
            <div
      tag="Col" type="component"
      style="--ai:normal;--jc:normal;--o:0"
      class="gcqAh3beFI gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g9ivC_4L2Y">
    <div
      parentTag="Col"
        class="g9ivC_4L2Y "
        style="--tt:default;--ta:left;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mt:var(--g-s-xl)"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text-g-text-3 gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:0;--line-clamp-tablet:0;--line-clamp-mobile:0;--c:var(--g-c-text-3, text-3);word-break:break-word;--radius:var(--g-radius-small);overflow:hidden"
        >{{ section.settings.gg9ivC_4L2Y_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g7OYNwF5UU">
    <div
      parentTag="Col"
        class="g7OYNwF5UU "
        style="--tt:default;--ta:left;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mt:var(--g-s-l);--mt-mobile:var(--g-s-l);--mb-mobile:0px"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text-g-text-3 gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:0;--line-clamp-tablet:0;--line-clamp-mobile:0;--c:var(--g-c-text-3, text-3);word-break:break-word;--radius:var(--g-radius-small);overflow:hidden"
        >{{ section.settings.gg7OYNwF5UU_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="grz15_xA7F">
    <div
      parentTag="Col"
        class="grz15_xA7F "
        style="--tt:default;--ta:left;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mt:var(--g-s-l)"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text-g-text-3 gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:0;--line-clamp-tablet:0;--line-clamp-mobile:0;--c:var(--g-c-text-3, text-3);word-break:break-word;--radius:var(--g-radius-small);overflow:hidden"
        >{{ section.settings.ggrz15_xA7F_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
       
      
    <div
      parentTag="Col" id="gvACrNfalv" data-id="gvACrNfalv"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--radius:var(--g-radius-small);--mt:var(--g-s-l);--cg:24px;--pc:start;--gtc:minmax(0, auto) minmax(0, auto) minmax(0, auto) minmax(0, auto);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gvACrNfalv gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--ai:normal;--jc:start;--o:0"
      class="gUvYqG3BQ0 gp-relative gp-flex gp-flex-col"
    >
      <div
    
     data-id="gbjAwaydqL"
    role="presentation"
    class="gp-group/image gbjAwaydqL gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--ta:left"
    >
      <div
        class="pointer-events-auto gp-h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="border-radius:inherit;--jc:left"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" data-srcSet="https://ucarecdn.com/41bcb53a-329b-4d38-864a-290b550b82f5/-/format/auto/" srcset="https://ucarecdn.com/41bcb53a-329b-4d38-864a-290b550b82f5/-/format/auto/" />
      <source media="(max-width: 1024px)" data-srcSet="https://ucarecdn.com/41bcb53a-329b-4d38-864a-290b550b82f5/-/format/auto/" srcset="https://ucarecdn.com/41bcb53a-329b-4d38-864a-290b550b82f5/-/format/auto/" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex"
        src="https://ucarecdn.com/41bcb53a-329b-4d38-864a-290b550b82f5/-/format/auto/"
        data-src="https://ucarecdn.com/41bcb53a-329b-4d38-864a-290b550b82f5/-/format/auto/"
        width="100%"
        alt="Alt Image"
        style="--aspect:auto;--objf:cover;--w:9px;--w-tablet:9px;--w-mobile:9px;--h:auto;--h-tablet:auto;--h-mobile:auto"
      />
    
  </picture>
      </div>
  </div>
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gehL1yt-yw gp-relative gp-flex gp-flex-col"
    >
      <div
    
     data-id="gZq8X6aY7f"
    role="presentation"
    class="gp-group/image gZq8X6aY7f gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--ta:left"
    >
      <div
        class="pointer-events-auto gp-h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="border-radius:inherit;--jc:left"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" data-srcSet="https://ucarecdn.com/d4711445-4efd-4fcc-be4b-dc5ab891386f/-/format/auto/" srcset="https://ucarecdn.com/d4711445-4efd-4fcc-be4b-dc5ab891386f/-/format/auto/" />
      <source media="(max-width: 1024px)" data-srcSet="https://ucarecdn.com/d4711445-4efd-4fcc-be4b-dc5ab891386f/-/format/auto/" srcset="https://ucarecdn.com/d4711445-4efd-4fcc-be4b-dc5ab891386f/-/format/auto/" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex"
        src="https://ucarecdn.com/d4711445-4efd-4fcc-be4b-dc5ab891386f/-/format/auto/"
        data-src="https://ucarecdn.com/d4711445-4efd-4fcc-be4b-dc5ab891386f/-/format/auto/"
        width="100%"
        alt="Alt Image"
        style="--aspect:auto;--objf:cover;--w:16px;--w-tablet:16px;--w-mobile:16px;--h:auto;--h-tablet:auto;--h-mobile:auto"
      />
    
  </picture>
      </div>
  </div>
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gxo67RaaBN gp-relative gp-flex gp-flex-col"
    >
      <div
    
     data-id="gb86PqBvWj"
    role="presentation"
    class="gp-group/image gb86PqBvWj gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--ta:left"
    >
      <div
        class="pointer-events-auto gp-h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="border-radius:inherit;--jc:left"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" data-srcSet="https://ucarecdn.com/91a9e7a8-52a4-4698-b378-d1d7ce6d859d/-/format/auto/" srcset="https://ucarecdn.com/91a9e7a8-52a4-4698-b378-d1d7ce6d859d/-/format/auto/" />
      <source media="(max-width: 1024px)" data-srcSet="https://ucarecdn.com/91a9e7a8-52a4-4698-b378-d1d7ce6d859d/-/format/auto/" srcset="https://ucarecdn.com/91a9e7a8-52a4-4698-b378-d1d7ce6d859d/-/format/auto/" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex"
        src="https://ucarecdn.com/91a9e7a8-52a4-4698-b378-d1d7ce6d859d/-/format/auto/"
        data-src="https://ucarecdn.com/91a9e7a8-52a4-4698-b378-d1d7ce6d859d/-/format/auto/"
        width="100%"
        alt="Alt Image"
        style="--aspect:auto;--objf:cover;--w:18px;--w-tablet:18px;--w-mobile:18px;--h:auto;--h-tablet:auto;--h-mobile:auto"
      />
    
  </picture>
      </div>
  </div>
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gJQz-vDp2i gp-relative gp-flex gp-flex-col"
    >
      <div
    
     data-id="g2nJVHZQ3k"
    role="presentation"
    class="gp-group/image g2nJVHZQ3k gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--ta:left"
    >
      <div
        class="pointer-events-auto gp-h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="border-radius:inherit;--jc:left"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" data-srcSet="https://ucarecdn.com/4191bd2e-dbe8-46a6-9f71-5fa0281afe39/-/format/auto/" srcset="https://ucarecdn.com/4191bd2e-dbe8-46a6-9f71-5fa0281afe39/-/format/auto/" />
      <source media="(max-width: 1024px)" data-srcSet="https://ucarecdn.com/4191bd2e-dbe8-46a6-9f71-5fa0281afe39/-/format/auto/" srcset="https://ucarecdn.com/4191bd2e-dbe8-46a6-9f71-5fa0281afe39/-/format/auto/" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex"
        src="https://ucarecdn.com/4191bd2e-dbe8-46a6-9f71-5fa0281afe39/-/format/auto/"
        data-src="https://ucarecdn.com/4191bd2e-dbe8-46a6-9f71-5fa0281afe39/-/format/auto/"
        width="100%"
        alt="Alt Image"
        style="--aspect:auto;--objf:cover;--w:20px;--w-tablet:20px;--w-mobile:20px;--h:auto;--h-tablet:auto;--h-mobile:auto"
      />
    
  </picture>
      </div>
  </div>
    </div>
    </div>
   
    
    </div>
        </div>
      </div>
    </div>
  </div>
  
              </div>
            
    </gp-accordion>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-accordion.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
      </div>
    </div>
    </div>
   
    <div gp-el-wrapper style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--pt:16px;--pb:16px" class="gP56v12y5v ">
      
    <div
    data-id="gP56v12y5v"
      class="gp-flex gp-justify-start"
    >
      <div
        style="--w:100%;--w-tablet:100%;--w-mobile:100%;--bs:solid;--bbw:1px;--bbw-tablet:1px;--bbw-mobile:1px;--t:gp-rotate(0deg);--bc:rgba(224, 224, 224, 0.1)"
        class="gp-block tablet:gp-block mobile:gp-block"
      ></div>
      <div
        class="gp-items-center gp-hidden tablet:gp-hidden mobile:gp-hidden"
        style="--w:100%;--w-tablet:100%;--w-mobile:100%;--bc:rgba(224, 224, 224, 0.1);--t:gp-rotate(0deg)"
      >
        
    <div
      class="gp-hidden"
      style="--w:100%;--w-tablet:100%;--w-mobile:100%;--bc:rgba(224, 224, 224, 0.1);--bs:solid;--bbw:1px;--bbw-tablet:1px;--bbw-mobile:1px;--minw:100px"
    ></div>
  
        
            <span
              class="gp-inline-flex gp-items-center gp-justify-center gp-flex-none"
              style="--mr:var(--g-s-xs);--w:20px;--h:20px;--t:gp-rotate(0);--c:var(--g-c-text-1, text-1)"
            >
              <svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M428.8 137.6h-86.177a115.52 115.52 0 0 0 2.176-22.4c0-47.914-35.072-83.2-92-83.2-45.314 0-57.002 48.537-75.707 78.784-7.735 12.413-16.994 23.317-25.851 33.253l-.131.146-.129.148C135.662 161.807 127.764 168 120.8 168h-2.679c-5.747-4.952-13.536-8-22.12-8H32c-17.673 0-32 12.894-32 28.8v230.4C0 435.106 14.327 448 32 448h64c8.584 0 16.373-3.048 22.12-8h2.679c28.688 0 67.137 40 127.2 40h21.299c62.542 0 98.8-38.658 99.94-91.145 12.482-17.813 18.491-40.785 15.985-62.791A93.148 93.148 0 0 0 393.152 304H428.8c45.435 0 83.2-37.584 83.2-83.2 0-45.099-38.101-83.2-83.2-83.2zm0 118.4h-91.026c12.837 14.669 14.415 42.825-4.95 61.05 11.227 19.646 1.687 45.624-12.925 53.625 6.524 39.128-10.076 61.325-50.6 61.325H248c-45.491 0-77.21-35.913-120-39.676V215.571c25.239-2.964 42.966-21.222 59.075-39.596 11.275-12.65 21.725-25.3 30.799-39.875C232.355 112.712 244.006 80 252.8 80c23.375 0 44 8.8 44 35.2 0 35.2-26.4 53.075-26.4 70.4h158.4c18.425 0 35.2 16.5 35.2 35.2 0 18.975-16.225 35.2-35.2 35.2zM88 384c0 13.255-10.745 24-24 24s-24-10.745-24-24 10.745-24 24-24 24 10.745 24 24z" /></svg>
            </span>
          
        
            <span
              class="gp-inline-flex gp-items-center gp-justify-center gp-flex-none gp-g-paragraph-1"
              style="--tt:horizontal;--c:var(--g-c-text-1, text-1);--mr:var(--g-s-s)"
            >
              Title
            </span>
          
        
    <div
      class="gp-flex"
      style="--w:100%;--w-tablet:100%;--w-mobile:100%;--bc:rgba(224, 224, 224, 0.1);--bs:solid;--bbw:1px;--bbw-tablet:1px;--bbw-mobile:1px;--minw:100px"
    ></div>
  
      </div>
    </div>
  
      </div>
       
      
    <div
      parentTag="Col" id="gV1vYT48ph" data-id="gV1vYT48ph"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--radius:var(--g-radius-small);--pt:30px;--pl:15px;--pb:30px;--pr:15px;--pt-mobile:var(--g-s-l);--pl-mobile:15px;--pb-mobile:30px;--pr-mobile:15px;--cg:30px;--pc:start;--gtc:minmax(0, 6fr) minmax(0, 6fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gV1vYT48ph gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--ai:normal;--jc:start;--o:0"
      class="g4M98N3HzI gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gHn6yRi_jE">
    <div
      parentTag="Col"
        class="gHn6yRi_jE "
        style="--tt:default;--ta:left;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small)"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text-g-text-3 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:0;--line-clamp-tablet:0;--line-clamp-mobile:0;--c:rgba(255, 255, 255, 0.8);--fs:normal;--ff:var(--g-font-body, body);--weight:400;--ls:normal;--size:13px;--size-tablet:13px;--size-mobile:12px;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;word-break:break-word;--radius:var(--g-radius-small);overflow:hidden"
        >{{ section.settings.ggHn6yRi_jE_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gij1U_fAQo gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      parentTag="Col" id="gcoZ84Sqg7" data-id="gcoZ84Sqg7"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--radius:var(--g-radius-small);--mt-mobile:14px;--cg:8px;--pc:end;--pc-mobile:start;--gtc:minmax(0, auto) minmax(0, auto) minmax(0, auto) minmax(0, auto) minmax(0, auto);--w:var(--g-ct-w, 1200px);--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gcoZ84Sqg7 gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--ai:normal;--jc:between;--o:0"
      class="gehXh6478Z gp-relative gp-flex gp-flex-col"
    >
      <div
    
     data-id="gMR7tkeSAo"
    role="presentation"
    class="gp-group/image gMR7tkeSAo gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ta:left"
    >
      <div
        class="pointer-events-auto gp-h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="border-radius:inherit;--jc:left"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" data-srcSet="https://ucarecdn.com/7c3de24c-f954-47b9-a7a4-0901c996a852/-/format/auto/" srcset="https://ucarecdn.com/7c3de24c-f954-47b9-a7a4-0901c996a852/-/format/auto/" />
      <source media="(max-width: 1024px)" data-srcSet="https://ucarecdn.com/7c3de24c-f954-47b9-a7a4-0901c996a852/-/format/auto/" srcset="https://ucarecdn.com/7c3de24c-f954-47b9-a7a4-0901c996a852/-/format/auto/" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex"
        src="https://ucarecdn.com/7c3de24c-f954-47b9-a7a4-0901c996a852/-/format/auto/"
        data-src="https://ucarecdn.com/7c3de24c-f954-47b9-a7a4-0901c996a852/-/format/auto/"
        width="100%"
        alt="Alt Image"
        style="--objf:cover;--w:38px;--w-tablet:38px;--w-mobile:38px;--h:auto;--h-tablet:auto;--h-mobile:auto"
      />
    
  </picture>
      </div>
  </div>
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:between"
      class="g06b4ZJF54 gp-relative gp-flex gp-flex-col"
    >
      <div
    
     data-id="gwVpFYSSoX"
    role="presentation"
    class="gp-group/image gwVpFYSSoX gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ta:left"
    >
      <div
        class="pointer-events-auto gp-h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="border-radius:inherit;--jc:left"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" data-srcSet="https://ucarecdn.com/65397dd7-5ca3-44ee-9b19-3ebd215dcf38/" srcset="https://ucarecdn.com/65397dd7-5ca3-44ee-9b19-3ebd215dcf38/" />
      <source media="(max-width: 1024px)" data-srcSet="https://ucarecdn.com/65397dd7-5ca3-44ee-9b19-3ebd215dcf38/" srcset="https://ucarecdn.com/65397dd7-5ca3-44ee-9b19-3ebd215dcf38/" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex"
        src="https://ucarecdn.com/65397dd7-5ca3-44ee-9b19-3ebd215dcf38/"
        data-src="https://ucarecdn.com/65397dd7-5ca3-44ee-9b19-3ebd215dcf38/"
        width="100%"
        alt="Alt Image"
        style="--objf:cover;--w:38px;--w-tablet:38px;--w-mobile:38px;--h:auto;--h-tablet:auto;--h-mobile:auto"
      />
    
  </picture>
      </div>
  </div>
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:between"
      class="godvDNpz4G gp-relative gp-flex gp-flex-col"
    >
      <div
    
     data-id="ghG-VtG18-"
    role="presentation"
    class="gp-group/image ghG-VtG18- gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ta:left"
    >
      <div
        class="pointer-events-auto gp-h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="border-radius:inherit;--jc:left"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" data-srcSet="https://ucarecdn.com/85fc0e7b-3a48-41b8-adc2-5e6983a7fb4e/" srcset="https://ucarecdn.com/85fc0e7b-3a48-41b8-adc2-5e6983a7fb4e/" />
      <source media="(max-width: 1024px)" data-srcSet="https://ucarecdn.com/85fc0e7b-3a48-41b8-adc2-5e6983a7fb4e/" srcset="https://ucarecdn.com/85fc0e7b-3a48-41b8-adc2-5e6983a7fb4e/" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex"
        src="https://ucarecdn.com/85fc0e7b-3a48-41b8-adc2-5e6983a7fb4e/"
        data-src="https://ucarecdn.com/85fc0e7b-3a48-41b8-adc2-5e6983a7fb4e/"
        width="100%"
        alt="Alt Image"
        style="--objf:cover;--w:38px;--w-tablet:38px;--w-mobile:38px;--h:auto;--h-tablet:auto;--h-mobile:auto"
      />
    
  </picture>
      </div>
  </div>
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:between"
      class="guAJZF97xY gp-relative gp-flex gp-flex-col"
    >
      <div
    
     data-id="gO5oUQuCxK"
    role="presentation"
    class="gp-group/image gO5oUQuCxK gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ta:left"
    >
      <div
        class="pointer-events-auto gp-h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="border-radius:inherit;--jc:left"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" data-srcSet="https://ucarecdn.com/5bebff9f-2a1d-45fa-b559-bbcb57969fe4/" srcset="https://ucarecdn.com/5bebff9f-2a1d-45fa-b559-bbcb57969fe4/" />
      <source media="(max-width: 1024px)" data-srcSet="https://ucarecdn.com/5bebff9f-2a1d-45fa-b559-bbcb57969fe4/" srcset="https://ucarecdn.com/5bebff9f-2a1d-45fa-b559-bbcb57969fe4/" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex"
        src="https://ucarecdn.com/5bebff9f-2a1d-45fa-b559-bbcb57969fe4/"
        data-src="https://ucarecdn.com/5bebff9f-2a1d-45fa-b559-bbcb57969fe4/"
        width="100%"
        alt="Alt Image"
        style="--objf:cover;--w:38px;--w-tablet:38px;--w-mobile:38px;--h:auto;--h-tablet:auto;--h-mobile:auto"
      />
    
  </picture>
      </div>
  </div>
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:between"
      class="gtc4lhZmxr gp-relative gp-flex gp-flex-col"
    >
      <div
    
     data-id="g_szY46RXe"
    role="presentation"
    class="gp-group/image g_szY46RXe gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ta:left"
    >
      <div
        class="pointer-events-auto gp-h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="border-radius:inherit;--jc:left"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" data-srcSet="https://ucarecdn.com/c5993898-7e4e-4974-a8a6-22c5213dded3/" srcset="https://ucarecdn.com/c5993898-7e4e-4974-a8a6-22c5213dded3/" />
      <source media="(max-width: 1024px)" data-srcSet="https://ucarecdn.com/c5993898-7e4e-4974-a8a6-22c5213dded3/" srcset="https://ucarecdn.com/c5993898-7e4e-4974-a8a6-22c5213dded3/" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex"
        src="https://ucarecdn.com/c5993898-7e4e-4974-a8a6-22c5213dded3/"
        data-src="https://ucarecdn.com/c5993898-7e4e-4974-a8a6-22c5213dded3/"
        width="100%"
        alt="Alt Image"
        style="--objf:cover;--w:38px;--w-tablet:38px;--w-mobile:38px;--h:auto;--h-tablet:auto;--h-mobile:auto"
      />
    
  </picture>
      </div>
  </div>
    </div>
    </div>
   
    
    </div>
    </div>
   
    
    </div>
    </div>
  
            </section>
           
    


{% schema %}
  {
    
    "name": "Section 15",
    "tag": "section",
    "class": "gps-573039469586809972 gps gpsil",
    "settings": [{"type":"paragraph","content":"Section design by GemPages. [Click here to start design](https://admin.shopify.com/store/i0pixe-10/apps/gempages-cro/app/shopify/edit?pageType=GP_PRODUCT&editorId=572751048691811510&sectionId=573039469586809972)"},{"type":"select","label":"Section Preload","id":"section_preload","options":[{"label":"On","value":"true"},{"label":"Off","value":"false"},{"label":"Off Lazy Script","value":"off_lazy_script"}],"default":"false"},{"type":"text","label":"Checksum","id":"checksum"},{"type":"html","id":"ggQ3-1U74rr_text","label":"ggQ3-1U74rr_text","default":"<p>Subscribe Today</p>"},{"type":"html","id":"ggooBT7q7gh_text","label":"ggooBT7q7gh_text","default":"<p>Sign up for exclusive content, special prizes, and latest update</p>"},{"type":"html","id":"ggfvkX2QVVy_successMessage","label":"ggfvkX2QVVy_successMessage","default":"Thanks for contacting us. We'll get back to you as soon as possible."},{"type":"html","id":"ggfvkX2QVVy_errorMessage","label":"ggfvkX2QVVy_errorMessage","default":"Can’t send email. Please try again later."},{"type":"html","id":"ggN9SXHSpQf_placeholder","label":"ggN9SXHSpQf_placeholder","default":"Enter your email"},{"type":"html","id":"ggoDo9_9yAp_label","label":"ggoDo9_9yAp_label","default":"Subscribe"},{"type":"html","id":"ggsyQUdO5qq_text","label":"ggsyQUdO5qq_text","default":"<p><strong>About Us&nbsp;</strong></p>"},{"type":"html","id":"ggRATab67XW_label","label":"ggRATab67XW_label","default":"<p>About Us</p>"},{"type":"html","id":"gg4FT1jArF7_label","label":"gg4FT1jArF7_label","default":"<p>FAQ</p>"},{"type":"html","id":"ggr_svpW299_label","label":"ggr_svpW299_label","default":"<p>Terms &amp; Conditions</p>"},{"type":"html","id":"gggcxnhVXcR_label","label":"gggcxnhVXcR_label","default":"<p>Privacy Policy</p>"},{"type":"html","id":"ggUfkeB_Wxl_text","label":"ggUfkeB_Wxl_text","default":"<p><strong>Categories</strong></p>"},{"type":"html","id":"ggagJ7v5K9g_label","label":"ggagJ7v5K9g_label","default":"<p>Home</p>"},{"type":"html","id":"ggJKkJoW6X9_label","label":"ggJKkJoW6X9_label","default":"<p><u>3D Printing</u></p>"},{"type":"html","id":"ggFiKU4uExT_label","label":"ggFiKU4uExT_label","default":"<p><u>Accessories</u></p>"},{"type":"html","id":"gg5XQOqL3GM_label","label":"gg5XQOqL3GM_label","default":"<p><u>Support</u></p>"},{"type":"html","id":"ggo7PuYIFnB_text","label":"ggo7PuYIFnB_text","default":"<p><strong>Help</strong></p>"},{"type":"html","id":"ggrFgKAFWB2_label","label":"ggrFgKAFWB2_label","default":"Customer Service"},{"type":"html","id":"gg0v1ZTj8II_label","label":"gg0v1ZTj8II_label","default":"Returns & Exchanges"},{"type":"html","id":"gg92hVOZCa__label","label":"gg92hVOZCa__label","default":"FAQs"},{"type":"html","id":"ggxzan03qxn_label","label":"ggxzan03qxn_label","default":"Contact Us"},{"type":"html","id":"gg1LEIGFRdl_text","label":"gg1LEIGFRdl_text","default":"<p><strong>Visit</strong></p>"},{"type":"html","id":"ggdWNromoJb_text","label":"ggdWNromoJb_text","default":"<p>4th Floor, No. 4 Plant, 6898 Zhuhai Avenue, Hongqi Town, Jinwan District, Zhuhai City</p>"},{"type":"html","id":"gg4obQbt7bl_text","label":"gg4obQbt7bl_text","default":"<p>&nbsp;<EMAIL></p>"},{"type":"html","id":"gg8PgmiWhZM_childItem_0","label":"gg8PgmiWhZM_childItem_0","default":"Company"},{"type":"html","id":"gg8PgmiWhZM_childItem_1","label":"gg8PgmiWhZM_childItem_1","default":"Shop"},{"type":"html","id":"gg8PgmiWhZM_childItem_2","label":"gg8PgmiWhZM_childItem_2","default":"Help"},{"type":"html","id":"gg8PgmiWhZM_childItem_3","label":"gg8PgmiWhZM_childItem_3","default":"Visit"},{"type":"html","id":"ggvGc8VDsoz_label","label":"ggvGc8VDsoz_label","default":"About"},{"type":"html","id":"ggQq_kny8z__label","label":"ggQq_kny8z__label","default":"Events"},{"type":"html","id":"gggQOp0RgL9_label","label":"gggQOp0RgL9_label","default":"Rentals"},{"type":"html","id":"ggLr00DN-vM_label","label":"ggLr00DN-vM_label","default":"Features"},{"type":"html","id":"ggg6HZiOGVS_label","label":"ggg6HZiOGVS_label","default":"Men"},{"type":"html","id":"ggZuQowhjZ-_label","label":"ggZuQowhjZ-_label","default":"Women"},{"type":"html","id":"ggye8JvypHv_label","label":"ggye8JvypHv_label","default":"Footweat"},{"type":"html","id":"ggey-GpmVRK_label","label":"ggey-GpmVRK_label","default":"Brands"},{"type":"html","id":"ggiqPw9m64R_label","label":"ggiqPw9m64R_label","default":"Customer Service"},{"type":"html","id":"ggvfxs-sOuu_label","label":"ggvfxs-sOuu_label","default":"Returns & Exchanges"},{"type":"html","id":"ggaqfY_HoMF_label","label":"ggaqfY_HoMF_label","default":"FAQs"},{"type":"html","id":"gg-oCW7sNRJ_label","label":"gg-oCW7sNRJ_label","default":"Contact Us"},{"type":"html","id":"gg9ivC_4L2Y_text","label":"gg9ivC_4L2Y_text","default":"<p>261 NW 26th Street Miami. FL 33127</p>"},{"type":"html","id":"gg7OYNwF5UU_text","label":"gg7OYNwF5UU_text","default":"<p>999-999-999</p>"},{"type":"html","id":"ggrz15_xA7F_text","label":"ggrz15_xA7F_text","default":"<p><EMAIL></p>"},{"type":"html","id":"ggHn6yRi_jE_text","label":"ggHn6yRi_jE_text","default":"<p>Copyright © 2022 GemThemes. All Rights Reserved.</p>"}],
    "blocks": [{"type": "@app"}],
    "locales": {}
  }
{% endschema %}
