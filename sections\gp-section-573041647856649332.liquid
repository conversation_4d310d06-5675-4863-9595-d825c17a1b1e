

<style {% if section.settings.section_preload == "false" %} class="gps-link gps-style" type="text/disabled-css" {% endif %}>.gps-573041647856649332.gps.gpsil [style*="--bg:"]{background:var(--bg)}.gps-573041647856649332.gps.gpsil [style*="--hvr-bg:"]:hover{background:var(--hvr-bg)}.gps-573041647856649332.gps.gpsil [style*="--bga:"]{background-attachment:var(--bga)}.gps-573041647856649332.gps.gpsil [style*="--bgc:"]{background-color:var(--bgc)}.gps-573041647856649332.gps.gpsil [style*="--hvr-bgc:"]:hover{background-color:var(--hvr-bgc)}.gps-573041647856649332.gps.gpsil [style*="--bgi:"]{background-image:var(--bgi)}.gps-573041647856649332.gps.gpsil [style*="--bgp:"]{background-position:var(--bgp)}.gps-573041647856649332.gps.gpsil [style*="--bgr:"]{background-repeat:var(--bgr)}.gps-573041647856649332.gps.gpsil [style*="--bgs:"]{background-size:var(--bgs)}.gps-573041647856649332.gps.gpsil [style*="--b:"]{border:var(--b)}.gps-573041647856649332.gps.gpsil [style*="--hvr-b:"]:hover{border:var(--hvr-b)}.gps-573041647856649332.gps.gpsil [style*="--bb:"]{border-bottom:var(--bb)}.gps-573041647856649332.gps.gpsil [style*="--bbw:"]{border-bottom-width:var(--bbw)}.gps-573041647856649332.gps.gpsil [style*="--bc:"]{border-color:var(--bc)}.gps-573041647856649332.gps.gpsil [style*="--hvr-bc:"]:hover{border-color:var(--hvr-bc)}.gps-573041647856649332.gps.gpsil [style*="--bblr:"]{border-bottom-left-radius:var(--bblr)}.gps-573041647856649332.gps.gpsil [style*="--bbrr:"]{border-bottom-right-radius:var(--bbrr)}.gps-573041647856649332.gps.gpsil [style*="--radius:"]{border-radius:var(--radius)}.gps-573041647856649332.gps.gpsil [style*="--bs:"]{border-style:var(--bs)}.gps-573041647856649332.gps.gpsil [style*="--hvr-bs:"]:hover{border-style:var(--hvr-bs)}.gps-573041647856649332.gps.gpsil [style*="--bt:"]{border-top:var(--bt)}.gps-573041647856649332.gps.gpsil [style*="--btlr:"]{border-top-left-radius:var(--btlr)}.gps-573041647856649332.gps.gpsil [style*="--btrr:"]{border-top-right-radius:var(--btrr)}.gps-573041647856649332.gps.gpsil [style*="--bw:"]{border-width:var(--bw)}.gps-573041647856649332.gps.gpsil [style*="--hvr-bw:"]:hover{border-width:var(--hvr-bw)}.gps-573041647856649332.gps.gpsil [style*="--shadow:"]{box-shadow:var(--shadow)}.gps-573041647856649332.gps.gpsil [style*="--c:"]{color:var(--c)}.gps-573041647856649332.gps.gpsil [style*="--hvr-c:"]:hover{color:var(--hvr-c)}.gps-573041647856649332.gps.gpsil [style*="--cg:"]{-moz-column-gap:var(--cg);column-gap:var(--cg)}.gps-573041647856649332.gps.gpsil [style*="--ff:"]{font-family:var(--ff)}.gps-573041647856649332.gps.gpsil [style*="--size:"]{font-size:var(--size)}.gps-573041647856649332.gps.gpsil [style*="--weight:"]{font-weight:var(--weight)}.gps-573041647856649332.gps.gpsil [style*="--fs:"]{font-style:var(--fs)}.gps-573041647856649332.gps.gpsil [style*="--gtc:"]{grid-template-columns:var(--gtc)}.gps-573041647856649332.gps.gpsil [style*="--h:"]{height:var(--h)}.gps-573041647856649332.gps.gpsil [style*="--jc:"]{justify-content:var(--jc)}.gps-573041647856649332.gps.gpsil [style*="--ls:"]{letter-spacing:var(--ls)}.gps-573041647856649332.gps.gpsil [style*="--lh:"]{line-height:var(--lh)}.gps-573041647856649332.gps.gpsil [style*="--m:"]{margin:var(--m)}.gps-573041647856649332.gps.gpsil [style*="--mb:"]{margin-bottom:var(--mb)}.gps-573041647856649332.gps.gpsil [style*="--mr:"]{margin-right:var(--mr)}.gps-573041647856649332.gps.gpsil [style*="--mt:"]{margin-top:var(--mt)}.gps-573041647856649332.gps.gpsil [style*="--maxw:"]{max-width:var(--maxw)}.gps-573041647856649332.gps.gpsil [style*="--minw:"]{min-width:var(--minw)}.gps-573041647856649332.gps.gpsil [style*="--op:"]{opacity:var(--op)}.gps-573041647856649332.gps.gpsil [style*="--o:"]{order:var(--o)}.gps-573041647856649332.gps.gpsil [style*="--pc:"]{place-content:var(--pc)}.gps-573041647856649332.gps.gpsil [style*="--p:"]{padding:var(--p)}.gps-573041647856649332.gps.gpsil [style*="--pb:"]{padding-bottom:var(--pb)}.gps-573041647856649332.gps.gpsil [style*="--pl:"]{padding-left:var(--pl)}.gps-573041647856649332.gps.gpsil [style*="--pr:"]{padding-right:var(--pr)}.gps-573041647856649332.gps.gpsil [style*="--pt:"]{padding-top:var(--pt)}.gps-573041647856649332.gps.gpsil [style*="--ta:"]{text-align:var(--ta)}.gps-573041647856649332.gps.gpsil [style*="--ts:"]{text-shadow:var(--ts)}.gps-573041647856649332.gps.gpsil [style*="--tt:"]{text-transform:var(--tt)}.gps-573041647856649332.gps.gpsil [style*="--t:"]{transform:var(--t)}.gps-573041647856649332.gps.gpsil [style*="--w:"]{width:var(--w)}@media only screen and (max-width:1024px){.gps-573041647856649332.gps.gpsil [style*="--bbw-tablet:"]{border-bottom-width:var(--bbw-tablet)}.gps-573041647856649332.gps.gpsil [style*="--size-tablet:"]{font-size:var(--size-tablet)}.gps-573041647856649332.gps.gpsil [style*="--lh-tablet:"]{line-height:var(--lh-tablet)}.gps-573041647856649332.gps.gpsil [style*="--maxw-tablet:"]{max-width:var(--maxw-tablet)}.gps-573041647856649332.gps.gpsil [style*="--pb-tablet:"]{padding-bottom:var(--pb-tablet)}.gps-573041647856649332.gps.gpsil [style*="--pl-tablet:"]{padding-left:var(--pl-tablet)}.gps-573041647856649332.gps.gpsil [style*="--pr-tablet:"]{padding-right:var(--pr-tablet)}.gps-573041647856649332.gps.gpsil [style*="--pt-tablet:"]{padding-top:var(--pt-tablet)}.gps-573041647856649332.gps.gpsil [style*="--w-tablet:"]{width:var(--w-tablet)}}@media only screen and (max-width:767px){.gps-573041647856649332.gps.gpsil [style*="--bbw-mobile:"]{border-bottom-width:var(--bbw-mobile)}.gps-573041647856649332.gps.gpsil [style*="--size-mobile:"]{font-size:var(--size-mobile)}.gps-573041647856649332.gps.gpsil [style*="--gtc-mobile:"]{grid-template-columns:var(--gtc-mobile)}.gps-573041647856649332.gps.gpsil [style*="--lh-mobile:"]{line-height:var(--lh-mobile)}.gps-573041647856649332.gps.gpsil [style*="--mb-mobile:"]{margin-bottom:var(--mb-mobile)}.gps-573041647856649332.gps.gpsil [style*="--maxw-mobile:"]{max-width:var(--maxw-mobile)}.gps-573041647856649332.gps.gpsil [style*="--pl-mobile:"]{padding-left:var(--pl-mobile)}.gps-573041647856649332.gps.gpsil [style*="--pr-mobile:"]{padding-right:var(--pr-mobile)}.gps-573041647856649332.gps.gpsil [style*="--w-mobile:"]{width:var(--w-mobile)}}.gps-573041647856649332 .gp-g-heading-1{font-family:var(--g-h1-ff);font-size:var(--g-h1-size);font-style:var(--g-h1-fs);font-weight:var(--g-h1-weight);letter-spacing:var(--g-h1-ls);line-height:var(--g-h1-lh)}.gps-573041647856649332 .gp-g-paragraph-1{font-family:var(--g-p1-ff);font-size:var(--g-p1-size);font-style:var(--g-p1-fs);font-weight:var(--g-p1-weight);letter-spacing:var(--g-p1-ls);line-height:var(--g-p1-lh)}.gps-573041647856649332 .gp-relative{position:relative}.gps-573041647856649332 .gp-mx-auto{margin-left:auto;margin-right:auto}.gps-573041647856649332 .gp-mb-0{margin-bottom:0}.gps-573041647856649332 .gp-block{display:block}.gps-573041647856649332 .gp-flex{display:flex}.gps-573041647856649332 .gp-inline-flex{display:inline-flex}.gps-573041647856649332 .gp-grid{display:grid}.gps-573041647856649332 .\!gp-hidden{display:none!important}.gps-573041647856649332 .gp-hidden{display:none}.gps-573041647856649332 .gp-w-full{width:100%}.gps-573041647856649332 .gp-min-w-0{min-width:0}.gps-573041647856649332 .gp-max-w-full{max-width:100%}.gps-573041647856649332 .gp-flex-1{flex:1 1 0%}.gps-573041647856649332 .gp-flex-none{flex:none}.gps-573041647856649332 .gp-cursor-pointer{cursor:pointer}.gps-573041647856649332 .gp-grid-rows-\[1fr\]{grid-template-rows:1fr}.gps-573041647856649332 .gp-flex-row{flex-direction:row}.gps-573041647856649332 .gp-flex-col{flex-direction:column}.gps-573041647856649332 .gp-flex-wrap{flex-wrap:wrap}.gps-573041647856649332 .gp-items-center{align-items:center}.gps-573041647856649332 .gp-justify-start{justify-content:flex-start}.gps-573041647856649332 .gp-justify-center{justify-content:center}.gps-573041647856649332 .gp-gap-y-0{row-gap:0}.gps-573041647856649332 .gp-overflow-hidden{overflow:hidden}.gps-573041647856649332 .gp-whitespace-nowrap{white-space:nowrap}.gps-573041647856649332 .gp-p-4{padding:16px}.gps-573041647856649332 .gp-px-4{padding-left:16px;padding-right:16px}.gps-573041647856649332 .gp-py-2{padding-bottom:8px;padding-top:8px}.gps-573041647856649332 .gp-pb-0{padding-bottom:0}.gps-573041647856649332 .gp-transition-colors{transition-duration:.15s;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-573041647856649332 .gp-duration-200{transition-duration:.2s}.gps-573041647856649332 .gp-ease-in-out{transition-timing-function:cubic-bezier(.4,0,.2,1)}@media (max-width:1024px){.gps-573041647856649332 .tablet\:gp-block{display:block}.gps-573041647856649332 .tablet\:\!gp-hidden{display:none!important}.gps-573041647856649332 .tablet\:gp-hidden{display:none}}@media (max-width:767px){.gps-573041647856649332 .mobile\:gp-block{display:block}.gps-573041647856649332 .mobile\:\!gp-hidden{display:none!important}.gps-573041647856649332 .mobile\:gp-hidden{display:none}}.gps-573041647856649332 .\[\&_\*\]\:gp-max-w-full *{max-width:100%}.gps-573041647856649332 .\[\&_p\]\:gp-inline p{display:inline}.gps-573041647856649332 .\[\&_p\]\:after\:gp-whitespace-pre-wrap p:after{content:var(--tw-content);white-space:pre-wrap}.gps-573041647856649332 .\[\&_p\]\:after\:gp-content-\[\'\\A\'\] p:after{--tw-content:"\A";content:var(--tw-content)}</style>

       
      
            <section
              class="gp-mx-auto gp-max-w-full {% if section.settings.section_preload == "false" %}gps-lazy {% endif %}"
              style="--w:100%;--w-tablet:100%;--w-mobile:100%;--pl:0px;--pl-tablet:0px;--pl-mobile:0px;--pr:0px;--pr-tablet:0px;--pr-mobile:0px"
            >
              
    <div
      id="g112EyDtzk" data-id="g112EyDtzk"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pt:var(--g-s-4xl);--pl:15px;--pb:var(--g-s-4xl);--pr:15px;--pt-tablet:var(--g-s-4xl);--pl-tablet:15px;--pb-tablet:var(--g-s-4xl);--pr-tablet:15px;--cg:var(--g-s-2xl);--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:#FFFFFF;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="g112EyDtzk gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gUNw0qLIAC gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      parentTag="Col" id="g-2W1NEK7Q" data-id="g-2W1NEK7Q"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--cg:var(--g-s-2xl);--pc:start;--gtc:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="g-2W1NEK7Q gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gER5QfxAKs gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g2qW_ITuGe">
    <div
      parentTag="Col"
        class="g2qW_ITuGe "
        style="--ta:left;--mt:-50px;--mb:40px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-heading-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:center;--weight:bold;--c:var(--g-c-text-2, text-2);word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.gg2qW_ITuGe_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    <gp-tab data-id="giVUHA8wPM" gp-data='{"setting":{"borderTab":{"active":{"border":"solid","color":"#2352E7","isCustom":true,"width":"0px 0px 2px 0px"},"hover":{"border":"solid","borderLabel":"Style 1","borderType":"style-1","color":"#2352E7","isCustom":true,"isLink":true,"width":"0px 0px 2px 0px"},"normal":{"border":"solid","borderLabel":"Style 1","borderType":"style-1","color":"#EEEEEE","isCustom":true,"isLink":true,"width":"0px 0px 1px 0px"}},"childItem":["<p>Machine Parameters</p>","<p>Sensors</p>","<p>Software</p>","<p>Electrical Hardware</p>"],"labelAlign":{"desktop":"center"},"labelBgColor":{"active":"rgba(245, 245, 245, 0)","normal":"transparent"},"labelColor":{"active":"#2352E7","hover":"#2352E7","normal":"#242424"},"labelTypo":{"custom":{"desktop":{"fontSize":"19px","fontStyle":"normal","fontWeight":"700","letterSpacing":"0px","lineHeight":"130%"},"mobile":{"fontSize":"17px","letterSpacing":"0px","lineHeight":"150%"},"tablet":{"fontSize":"19px","lineHeight":"130%"}}},"labelTypoV2":{"type":"paragraph-1","custom":{"fontSize":{"desktop":"22px","mobile":"17px","tablet":"19px"},"fontStyle":"normal","fontWeight":"700","letterSpacing":"normal","lineHeight":{"desktop":"130%","mobile":"130%","tablet":"130%"}}},"labelWidth":{"desktop":"200px"},"panelAlign":{"desktop":"center"},"panelFullWidth":{"desktop":false,"mobile":true},"panelWidth":{"desktop":"680px","mobile":"15%"},"position":{"0":"t","1":"o","2":"p","desktop":"top"},"translate":"childItem"},"builderProps":{"uid":"giVUHA8wPM","builderData":{"advanced":{"border":{"desktop":{"normal":{"border":"none","borderType":"none","borderWidth":{},"color":"transparent","isCustom":false,"width":"0px"}}},"boxShadow":{"desktop":{"normal":{"angle":90,"blur":"4px","color":"rgba(18, 18, 18, 0.12)","distance":"2px","spread":"0px","type":"shadow-1"}}},"d":{"desktop":true,"mobile":true,"tablet":true},"hasBoxShadow":{"desktop":{"normal":false}},"op":{"desktop":"100%"},"rounded":{"desktop":{"normal":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"none"}}},"spacing-setting":{"desktop":{"margin":{"bottom":0},"padding":""},"mobile":{"margin":"","padding":""},"tablet":{"margin":"","padding":""}}},"childrens":["gOavi-wYOt","g2JS29TwpW","gooqDda_YS","gZ_kb9_B5k"],"label":"Tab","settings":{"borderTab":{"active":{"border":"solid","color":"#2352E7","isCustom":true,"width":"0px 0px 2px 0px"},"hover":{"border":"solid","borderLabel":"Style 1","borderType":"style-1","color":"#2352E7","isCustom":true,"isLink":true,"width":"0px 0px 2px 0px"},"normal":{"border":"solid","borderLabel":"Style 1","borderType":"style-1","color":"#EEEEEE","isCustom":true,"isLink":true,"width":"0px 0px 1px 0px"}},"childItem":["<p>Machine Parameters</p>","<p>Sensors</p>","<p>Software</p>","<p>Electrical Hardware</p>"],"labelAlign":{"desktop":"center"},"labelBgColor":{"active":"rgba(245, 245, 245, 0)","normal":"transparent"},"labelColor":{"active":"#2352E7","hover":"#2352E7","normal":"#242424"},"labelTypo":{"custom":{"desktop":{"fontSize":"19px","fontStyle":"normal","fontWeight":"700","letterSpacing":"0px","lineHeight":"130%"},"mobile":{"fontSize":"17px","letterSpacing":"0px","lineHeight":"150%"},"tablet":{"fontSize":"19px","lineHeight":"130%"}}},"labelTypoV2":{"type":"paragraph-1","custom":{"fontSize":{"desktop":"22px","mobile":"17px","tablet":"19px"},"fontStyle":"normal","fontWeight":"700","letterSpacing":"normal","lineHeight":{"desktop":"130%","mobile":"130%","tablet":"130%"}}},"labelWidth":{"desktop":"200px"},"panelAlign":{"desktop":"center"},"panelFullWidth":{"desktop":false,"mobile":true},"panelWidth":{"desktop":"680px","mobile":"15%"},"position":{"0":"t","1":"o","2":"p","desktop":"top"},"translate":"childItem"},"styles":{},"tag":"Tabs","uid":"giVUHA8wPM","type":"component"}}}'>
      <div
        
        data-id=""
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%"
        class="gp-flex giVUHA8wPM"
      >
        <style>
            .giVUHA8wPM .gp-navs-tab.left p,
            .giVUHA8wPM .gp-navs-tab.right p {
              word-wrap: break-word;
              white-space: break-spaces;
            }
            .giVUHA8wPM .wrap-width_full_mobile_top_true { width: 100%; }

        </style>
        <div
          class="gp-flex gp-w-full  gp-flex-col"
        >
          <div
            class="gp-flex"
            style="--jc:center"
          >
            <ul
              class="gp-tab-header-list gp-flex gp-flex-wrap  0:gp-flex-row 1:gp-flex-row 2:gp-flex-row gp-flex-row"
              style="--maxw:680px;--maxw-tablet:680px;--maxw-mobile:100%"
            >
              
                  <li
                    class="gp-navs-tab gp-flex gp-flex-1 gp-cursor-pointer gp-py-2 gp-px-4"
                    aria-hidden
                    style="--bs:unset, --hvr-bs:unset, --hvr-bgc:unset, --hvr-c:unset,--hvr-bw:unset, --hvr-bc:unset"
                    key="giVUHA8wPM"
                  >
                    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="">
    <div
      
        class=" "
        
      >
      <div  >
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-cursor-pointer gp-overflow-hidden gp-whitespace-nowrap"
          style="--w:100%;--ta:center;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--size:22px;--size-tablet:19px;--size-mobile:17px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggiVUHA8wPM_childItem_0 }}</div>
      </div>
    </div>
    </gp-text>
    
                  </li>
                
                  <li
                    class="gp-navs-tab gp-flex gp-flex-1 gp-cursor-pointer gp-py-2 gp-px-4"
                    aria-hidden
                    style="--bs:unset, --hvr-bs:unset, --hvr-bgc:unset, --hvr-c:unset,--hvr-bw:unset, --hvr-bc:unset"
                    key="giVUHA8wPM"
                  >
                    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="">
    <div
      
        class=" "
        
      >
      <div  >
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-cursor-pointer gp-overflow-hidden gp-whitespace-nowrap"
          style="--w:100%;--ta:center;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--size:22px;--size-tablet:19px;--size-mobile:17px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggiVUHA8wPM_childItem_1 }}</div>
      </div>
    </div>
    </gp-text>
    
                  </li>
                
                  <li
                    class="gp-navs-tab gp-flex gp-flex-1 gp-cursor-pointer gp-py-2 gp-px-4"
                    aria-hidden
                    style="--bs:unset, --hvr-bs:unset, --hvr-bgc:unset, --hvr-c:unset,--hvr-bw:unset, --hvr-bc:unset"
                    key="giVUHA8wPM"
                  >
                    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="">
    <div
      
        class=" "
        
      >
      <div  >
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-cursor-pointer gp-overflow-hidden gp-whitespace-nowrap"
          style="--w:100%;--ta:center;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--size:22px;--size-tablet:19px;--size-mobile:17px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggiVUHA8wPM_childItem_2 }}</div>
      </div>
    </div>
    </gp-text>
    
                  </li>
                
                  <li
                    class="gp-navs-tab gp-flex gp-flex-1 gp-cursor-pointer gp-py-2 gp-px-4"
                    aria-hidden
                    style="--bs:unset, --hvr-bs:unset, --hvr-bgc:unset, --hvr-c:unset,--hvr-bw:unset, --hvr-bc:unset"
                    key="giVUHA8wPM"
                  >
                    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="">
    <div
      
        class=" "
        
      >
      <div  >
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-cursor-pointer gp-overflow-hidden gp-whitespace-nowrap"
          style="--w:100%;--ta:center;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--size:22px;--size-tablet:19px;--size-mobile:17px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggiVUHA8wPM_childItem_3 }}</div>
      </div>
    </div>
    </gp-text>
    
                  </li>
                
            </ul>
          </div>
          <div
            class="gp-flex gp-flex-1 gp-min-w-0"
          >
            <div
              class="gp-tab-item-container gp-p-4 gp-pb-0"
              key="giVUHA8wPM"
              style="--w:100%"
            >
            
  <div
    
    style="--b:solid;--bc:#121212;--bw:0px;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-none)"
    class="gp-tab-item gp-child-item-giVUHA8wPM"
  >
    <div gp-el-wrapper style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-3xl);--mb-mobile:var(--g-s-2xl)" class="g2kyVk5e6W ">
      
    <div
    data-id="g2kyVk5e6W"
      class="gp-flex gp-justify-start"
    >
      <div
        style="--w:100px;--w-tablet:100px;--w-mobile:100px;--bs:solid;--bbw:1pxpx;--bbw-tablet:undefinedpx;--bbw-mobile:undefinedpx;--t:gp-rotate(0deg);--bc:rgba(125, 125, 125, 0)"
        class="gp-block tablet:gp-block mobile:gp-block"
      ></div>
      <div
        class="gp-items-center gp-hidden tablet:gp-hidden mobile:gp-hidden"
        style="--w:100px;--w-tablet:100px;--w-mobile:100px;--bc:rgba(125, 125, 125, 0);--t:gp-rotate(0deg)"
      >
        
    <div
      class="gp-hidden"
      style="--w:100px;--w-tablet:100px;--w-mobile:100px;--bc:rgba(125, 125, 125, 0);--bs:solid;--bbw:1pxpx;--bbw-tablet:undefinedpx;--bbw-mobile:undefinedpx;--minw:100px"
    ></div>
  
        
            <span
              class="gp-inline-flex gp-items-center gp-justify-center gp-flex-none"
              style="--mr:var(--g-s-xs);--w:20px;--h:20px;--t:gp-rotate(0);--c:var(--g-c-text-1, text-1)"
            >
              <svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M428.8 137.6h-86.177a115.52 115.52 0 0 0 2.176-22.4c0-47.914-35.072-83.2-92-83.2-45.314 0-57.002 48.537-75.707 78.784-7.735 12.413-16.994 23.317-25.851 33.253l-.131.146-.129.148C135.662 161.807 127.764 168 120.8 168h-2.679c-5.747-4.952-13.536-8-22.12-8H32c-17.673 0-32 12.894-32 28.8v230.4C0 435.106 14.327 448 32 448h64c8.584 0 16.373-3.048 22.12-8h2.679c28.688 0 67.137 40 127.2 40h21.299c62.542 0 98.8-38.658 99.94-91.145 12.482-17.813 18.491-40.785 15.985-62.791A93.148 93.148 0 0 0 393.152 304H428.8c45.435 0 83.2-37.584 83.2-83.2 0-45.099-38.101-83.2-83.2-83.2zm0 118.4h-91.026c12.837 14.669 14.415 42.825-4.95 61.05 11.227 19.646 1.687 45.624-12.925 53.625 6.524 39.128-10.076 61.325-50.6 61.325H248c-45.491 0-77.21-35.913-120-39.676V215.571c25.239-2.964 42.966-21.222 59.075-39.596 11.275-12.65 21.725-25.3 30.799-39.875C232.355 112.712 244.006 80 252.8 80c23.375 0 44 8.8 44 35.2 0 35.2-26.4 53.075-26.4 70.4h158.4c18.425 0 35.2 16.5 35.2 35.2 0 18.975-16.225 35.2-35.2 35.2zM88 384c0 13.255-10.745 24-24 24s-24-10.745-24-24 10.745-24 24-24 24 10.745 24 24z" /></svg>
            </span>
          
        
            <span
              class="gp-inline-flex gp-items-center gp-justify-center gp-flex-none gp-g-paragraph-1"
              style="--tt:horizontal;--c:var(--g-c-text-1, text-1);--mr:var(--g-s-s)"
            >
              Title
            </span>
          
        
    <div
      class="gp-flex"
      style="--w:100px;--w-tablet:100px;--w-mobile:100px;--bc:rgba(125, 125, 125, 0);--bs:solid;--bbw:1pxpx;--bbw-tablet:undefinedpx;--bbw-mobile:undefinedpx;--minw:100px"
    ></div>
  
      </div>
    </div>
  
      </div>
       
      
    <div
      parentTag="TabItem" id="gEGaaPAjnQ" data-id="gEGaaPAjnQ"
        style="--mb:var(--g-s-2xl);--d:grid;--d-tablet:grid;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--cg:8px;--pc:start;--gtc:minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--h:auto;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gEGaaPAjnQ gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="glRG_yhPnq gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g6WVsCBWwc">
    <div
      parentTag="Col"
        class="g6WVsCBWwc "
        style="--ta:left;--mb:-10px;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--size:21px;--size-tablet:16px;--size-mobile:14px;--lh:100%;--lh-tablet:180%;--lh-mobile:180%;--weight:bold;--c:#424242;word-break:break-word;--bgc:rgba(246, 246, 246, 0);--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.gg6WVsCBWwc_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gd7QoNjKCW gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="ggANmgN1vN">
    <div
      parentTag="Col"
        class="ggANmgN1vN "
        style="--ta:left;--mb:-100px;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--size:24px;--size-tablet:16px;--size-mobile:14px;--lh:100%;--lh-tablet:180%;--lh-mobile:180%;--weight:bold;--c:#424242;word-break:break-word;--bgc:rgba(246, 246, 246, 0);--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.gggANmgN1vN_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gFuz6Ta6Aw gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gQTDxN3dnk">
    <div
      parentTag="Col"
        class="gQTDxN3dnk "
        style="--ta:left;--mb:-100px;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--size:24px;--size-tablet:16px;--size-mobile:14px;--lh:100%;--lh-tablet:180%;--lh-mobile:180%;--weight:bold;--c:#424242;word-break:break-word;--bgc:rgba(246, 246, 246, 0);--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggQTDxN3dnk_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gblC6wB6hd gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g14sLQMrYY">
    <div
      parentTag="Col"
        class="g14sLQMrYY "
        style="--ta:left;--mb:-100px;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--size:24px;--size-tablet:16px;--size-mobile:14px;--lh:100%;--lh-tablet:180%;--lh-mobile:180%;--weight:bold;--c:#424242;word-break:break-word;--bgc:rgba(246, 246, 246, 0);--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.gg14sLQMrYY_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
       
      
    <div
      parentTag="TabItem" id="gtBRCQMjO2" data-id="gtBRCQMjO2"
        style="--mb:2px;--d:grid;--d-tablet:grid;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--cg:2px;--pc:start;--gtc:minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--h:auto;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gtBRCQMjO2 gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gZplLbRR0G gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gEY5zCKBvo">
    <div
      parentTag="Col"
        class="gEY5zCKBvo "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggEY5zCKBvo_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gYLiPqKRNf gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gQBhWdJMKH">
    <div
      parentTag="Col"
        class="gQBhWdJMKH "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggQBhWdJMKH_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gpqNiWpjE0 gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gZuKYY7LHG">
    <div
      parentTag="Col"
        class="gZuKYY7LHG "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggZuKYY7LHG_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gWcosjszK0 gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gnKjncwfID">
    <div
      parentTag="Col"
        class="gnKjncwfID "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggnKjncwfID_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
       
      
    <div
      parentTag="TabItem" id="gPnNA8tUzI" data-id="gPnNA8tUzI"
        style="--mb:2px;--d:grid;--d-tablet:grid;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--cg:2px;--pc:start;--gtc:minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--h:auto;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gPnNA8tUzI gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="g_Rg13xLit gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gIeQi_ROOF">
    <div
      parentTag="Col"
        class="gIeQi_ROOF "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggIeQi_ROOF_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gqbh8zJ0FW gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gCx0yDvdUY">
    <div
      parentTag="Col"
        class="gCx0yDvdUY "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggCx0yDvdUY_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gRm_vTxdVh gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gOzjtJNhfh">
    <div
      parentTag="Col"
        class="gOzjtJNhfh "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggOzjtJNhfh_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gaExXEmKYC gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gLN_wmr7cU">
    <div
      parentTag="Col"
        class="gLN_wmr7cU "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggLN_wmr7cU_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
       
      
    <div
      parentTag="TabItem" id="gdLJROPskT" data-id="gdLJROPskT"
        style="--mb:2px;--d:grid;--d-tablet:grid;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--cg:2px;--pc:start;--gtc:minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--h:auto;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gdLJROPskT gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gqg2oN4_qn gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gvtfSdZX-W">
    <div
      parentTag="Col"
        class="gvtfSdZX-W "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggvtfSdZX-W_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gHuypWqPhv gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gTMr5_HyVd">
    <div
      parentTag="Col"
        class="gTMr5_HyVd "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggTMr5_HyVd_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gyehgdEZau gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g3TfV3Uvik">
    <div
      parentTag="Col"
        class="g3TfV3Uvik "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.gg3TfV3Uvik_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gzncBGiEU- gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gRcGoQXvUT">
    <div
      parentTag="Col"
        class="gRcGoQXvUT "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggRcGoQXvUT_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
       
      
    <div
      parentTag="TabItem" id="gftR_yNRTK" data-id="gftR_yNRTK"
        style="--mb:2px;--d:grid;--d-tablet:grid;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--cg:2px;--pc:start;--gtc:minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--h:auto;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gftR_yNRTK gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gr24qN2BG7 gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gSVL2667uR">
    <div
      parentTag="Col"
        class="gSVL2667uR "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggSVL2667uR_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gVBOuS-iMo gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="goixylx3wD">
    <div
      parentTag="Col"
        class="goixylx3wD "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggoixylx3wD_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gxwI8xPI42 gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gcm6sJ5GwK">
    <div
      parentTag="Col"
        class="gcm6sJ5GwK "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggcm6sJ5GwK_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gNjEC35H3L gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="ggsqXg8k7v">
    <div
      parentTag="Col"
        class="ggsqXg8k7v "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.gggsqXg8k7v_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
       
      
    <div
      parentTag="TabItem" id="gR26A5Ymd-" data-id="gR26A5Ymd-"
        style="--mb:2px;--d:grid;--d-tablet:grid;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--cg:2px;--pc:start;--gtc:minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--h:auto;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gR26A5Ymd- gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gg44RdaNjv gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gGMdqEuQRy">
    <div
      parentTag="Col"
        class="gGMdqEuQRy "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggGMdqEuQRy_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gSNDLZ51e4 gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gWEfCf_Wt_">
    <div
      parentTag="Col"
        class="gWEfCf_Wt_ "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggWEfCf_Wt__text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gpfoQwqjX9 gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g2cgWH4DOz">
    <div
      parentTag="Col"
        class="g2cgWH4DOz "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.gg2cgWH4DOz_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="ggZaaFYe7l gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gAptm69qFX">
    <div
      parentTag="Col"
        class="gAptm69qFX "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggAptm69qFX_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
       
      
    <div
      parentTag="TabItem" id="g1EYvVDzvf" data-id="g1EYvVDzvf"
        style="--mb:2px;--d:grid;--d-tablet:grid;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--cg:2px;--pc:start;--gtc:minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--h:auto;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="g1EYvVDzvf gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gkxBL3Tq55 gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g5w-vBVvGx">
    <div
      parentTag="Col"
        class="g5w-vBVvGx "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.gg5w-vBVvGx_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gp4pLx2W9M gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g90OEhA6nf">
    <div
      parentTag="Col"
        class="g90OEhA6nf "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.gg90OEhA6nf_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gVDLgub7Kv gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gW861ZEqYT">
    <div
      parentTag="Col"
        class="gW861ZEqYT "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggW861ZEqYT_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="g3BKrZzNPZ gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gP_xOKipUG">
    <div
      parentTag="Col"
        class="gP_xOKipUG "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggP_xOKipUG_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
       
      
    <div
      parentTag="TabItem" id="g9uPM45opi" data-id="g9uPM45opi"
        style="--mb:2px;--d:grid;--d-tablet:grid;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--cg:2px;--pc:start;--gtc:minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--h:auto;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="g9uPM45opi gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gDjrRsxDU9 gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="grNaI7T5FU">
    <div
      parentTag="Col"
        class="grNaI7T5FU "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggrNaI7T5FU_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gbTn2n-dM3 gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gu8N5UomB-">
    <div
      parentTag="Col"
        class="gu8N5UomB- "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggu8N5UomB-_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="g-zHpsWWiw gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gsZmqpf8-W">
    <div
      parentTag="Col"
        class="gsZmqpf8-W "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggsZmqpf8-W_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gDp8rhefTk gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gw8rUe3Xfr">
    <div
      parentTag="Col"
        class="gw8rUe3Xfr "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggw8rUe3Xfr_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
       
      
    <div
      parentTag="TabItem" id="goCdpCouRX" data-id="goCdpCouRX"
        style="--mb:2px;--d:grid;--d-tablet:grid;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--cg:2px;--pc:start;--gtc:minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--h:auto;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="goCdpCouRX gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="g4i0RISP-h gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gBXFbt1d4H">
    <div
      parentTag="Col"
        class="gBXFbt1d4H "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggBXFbt1d4H_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gl9HX_GOqh gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="goDxU-MNFB">
    <div
      parentTag="Col"
        class="goDxU-MNFB "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggoDxU-MNFB_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gUsxqp3JDX gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gLi0q3Q2yo">
    <div
      parentTag="Col"
        class="gLi0q3Q2yo "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggLi0q3Q2yo_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gK-AEcVDd7 gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gtaN0_0Z3F">
    <div
      parentTag="Col"
        class="gtaN0_0Z3F "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggtaN0_0Z3F_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
       
      
    <div
      parentTag="TabItem" id="gxCcWiFnKW" data-id="gxCcWiFnKW"
        style="--mb:2px;--d:grid;--d-tablet:grid;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--cg:2px;--pc:start;--gtc:minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--h:auto;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gxCcWiFnKW gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gFEzH8HUmr gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gYb9SESEw1">
    <div
      parentTag="Col"
        class="gYb9SESEw1 "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggYb9SESEw1_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="glCuL8mkz- gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g-wliuXyxo">
    <div
      parentTag="Col"
        class="g-wliuXyxo "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.gg-wliuXyxo_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="g2ESyRq8HI gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gbzliZB5Z7">
    <div
      parentTag="Col"
        class="gbzliZB5Z7 "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggbzliZB5Z7_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="g5UGTmLw6h gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gxxfujcIgP">
    <div
      parentTag="Col"
        class="gxxfujcIgP "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggxxfujcIgP_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
       
      
    <div
      parentTag="TabItem" id="grlVcgPD1b" data-id="grlVcgPD1b"
        style="--mb:2px;--d:grid;--d-tablet:grid;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--cg:2px;--pc:start;--gtc:minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--h:auto;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="grlVcgPD1b gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="ghc1ZamoiH gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gOLSMPsGz9">
    <div
      parentTag="Col"
        class="gOLSMPsGz9 "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggOLSMPsGz9_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gOieEUIpa1 gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gfGFvPS7em">
    <div
      parentTag="Col"
        class="gfGFvPS7em "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggfGFvPS7em_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gOR8R057UM gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gnSyXrurLg">
    <div
      parentTag="Col"
        class="gnSyXrurLg "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggnSyXrurLg_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="g3z9Xc83OP gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g4Aue0oPr0">
    <div
      parentTag="Col"
        class="g4Aue0oPr0 "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.gg4Aue0oPr0_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
       
      
    <div
      parentTag="TabItem" id="gijbEECDMQ" data-id="gijbEECDMQ"
        style="--mb:2px;--d:grid;--d-tablet:grid;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--cg:2px;--pc:start;--gtc:minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--h:auto;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gijbEECDMQ gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gSVToaXHjp gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gqAiusMaBv">
    <div
      parentTag="Col"
        class="gqAiusMaBv "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggqAiusMaBv_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gvII6HyIGQ gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g3b2QhRgR8">
    <div
      parentTag="Col"
        class="g3b2QhRgR8 "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.gg3b2QhRgR8_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gn5b6q-Avu gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gxF-Ccvgp8">
    <div
      parentTag="Col"
        class="gxF-Ccvgp8 "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggxF-Ccvgp8_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gjKwKTTSQe gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gdClmtnSzZ">
    <div
      parentTag="Col"
        class="gdClmtnSzZ "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggdClmtnSzZ_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
       
      
    <div
      parentTag="TabItem" id="gnrWsG93LO" data-id="gnrWsG93LO"
        style="--mb:2px;--d:grid;--d-tablet:grid;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--cg:2px;--pc:start;--gtc:minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--h:auto;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gnrWsG93LO gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gzOj8kdOCh gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gKKcBo7q1C">
    <div
      parentTag="Col"
        class="gKKcBo7q1C "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggKKcBo7q1C_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gpC6JwPJEF gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gJG9zUHDTw">
    <div
      parentTag="Col"
        class="gJG9zUHDTw "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggJG9zUHDTw_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="g3veQ-o-Zf gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gFhSFMozwV">
    <div
      parentTag="Col"
        class="gFhSFMozwV "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggFhSFMozwV_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gH3OQArXOu gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g3AnHl4kFU">
    <div
      parentTag="Col"
        class="g3AnHl4kFU "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.gg3AnHl4kFU_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
       
      
    <div
      parentTag="TabItem" id="gQKgARoW2M" data-id="gQKgARoW2M"
        style="--mb:2px;--d:grid;--d-tablet:grid;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--cg:2px;--pc:start;--gtc:minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--h:auto;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gQKgARoW2M gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="g4oTkMmf2Q gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g-DeKoyPp7">
    <div
      parentTag="Col"
        class="g-DeKoyPp7 "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.gg-DeKoyPp7_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="g4ElPX0KWf gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g3A9DFQNa5">
    <div
      parentTag="Col"
        class="g3A9DFQNa5 "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.gg3A9DFQNa5_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="glMG8FPhZ7 gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gtJrQQLlrZ">
    <div
      parentTag="Col"
        class="gtJrQQLlrZ "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggtJrQQLlrZ_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gvBY9yU1hP gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gSn8OEDb0O">
    <div
      parentTag="Col"
        class="gSn8OEDb0O "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:175%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggSn8OEDb0O_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
  </div>
  
  <div
    
    style="--b:solid;--bc:#121212;--bw:0px;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-none)"
    class="gp-tab-item gp-child-item-giVUHA8wPM"
  >
    <div gp-el-wrapper style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-3xl);--mb-mobile:var(--g-s-2xl)" class="g_JvmLOIs6 ">
      
    <div
    data-id="g_JvmLOIs6"
      class="gp-flex gp-justify-start"
    >
      <div
        style="--w:100px;--w-tablet:100px;--w-mobile:100px;--bs:solid;--bbw:1pxpx;--bbw-tablet:undefinedpx;--bbw-mobile:undefinedpx;--t:gp-rotate(0deg);--bc:rgba(125, 125, 125, 0)"
        class="gp-block tablet:gp-block mobile:gp-block"
      ></div>
      <div
        class="gp-items-center gp-hidden tablet:gp-hidden mobile:gp-hidden"
        style="--w:100px;--w-tablet:100px;--w-mobile:100px;--bc:rgba(125, 125, 125, 0);--t:gp-rotate(0deg)"
      >
        
    <div
      class="gp-hidden"
      style="--w:100px;--w-tablet:100px;--w-mobile:100px;--bc:rgba(125, 125, 125, 0);--bs:solid;--bbw:1pxpx;--bbw-tablet:undefinedpx;--bbw-mobile:undefinedpx;--minw:100px"
    ></div>
  
        
            <span
              class="gp-inline-flex gp-items-center gp-justify-center gp-flex-none"
              style="--mr:var(--g-s-xs);--w:20px;--h:20px;--t:gp-rotate(0);--c:var(--g-c-text-1, text-1)"
            >
              <svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M428.8 137.6h-86.177a115.52 115.52 0 0 0 2.176-22.4c0-47.914-35.072-83.2-92-83.2-45.314 0-57.002 48.537-75.707 78.784-7.735 12.413-16.994 23.317-25.851 33.253l-.131.146-.129.148C135.662 161.807 127.764 168 120.8 168h-2.679c-5.747-4.952-13.536-8-22.12-8H32c-17.673 0-32 12.894-32 28.8v230.4C0 435.106 14.327 448 32 448h64c8.584 0 16.373-3.048 22.12-8h2.679c28.688 0 67.137 40 127.2 40h21.299c62.542 0 98.8-38.658 99.94-91.145 12.482-17.813 18.491-40.785 15.985-62.791A93.148 93.148 0 0 0 393.152 304H428.8c45.435 0 83.2-37.584 83.2-83.2 0-45.099-38.101-83.2-83.2-83.2zm0 118.4h-91.026c12.837 14.669 14.415 42.825-4.95 61.05 11.227 19.646 1.687 45.624-12.925 53.625 6.524 39.128-10.076 61.325-50.6 61.325H248c-45.491 0-77.21-35.913-120-39.676V215.571c25.239-2.964 42.966-21.222 59.075-39.596 11.275-12.65 21.725-25.3 30.799-39.875C232.355 112.712 244.006 80 252.8 80c23.375 0 44 8.8 44 35.2 0 35.2-26.4 53.075-26.4 70.4h158.4c18.425 0 35.2 16.5 35.2 35.2 0 18.975-16.225 35.2-35.2 35.2zM88 384c0 13.255-10.745 24-24 24s-24-10.745-24-24 10.745-24 24-24 24 10.745 24 24z" /></svg>
            </span>
          
        
            <span
              class="gp-inline-flex gp-items-center gp-justify-center gp-flex-none gp-g-paragraph-1"
              style="--tt:horizontal;--c:var(--g-c-text-1, text-1);--mr:var(--g-s-s)"
            >
              Title
            </span>
          
        
    <div
      class="gp-flex"
      style="--w:100px;--w-tablet:100px;--w-mobile:100px;--bc:rgba(125, 125, 125, 0);--bs:solid;--bbw:1pxpx;--bbw-tablet:undefinedpx;--bbw-mobile:undefinedpx;--minw:100px"
    ></div>
  
      </div>
    </div>
  
      </div>
       
      
    <div
      parentTag="TabItem" id="gxJ7arndi-" data-id="gxJ7arndi-"
        style="--mb:var(--g-s-2xl);--d:grid;--d-tablet:grid;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--cg:8px;--pc:start;--gtc:minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--h:auto;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gxJ7arndi- gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gjSdO456SP gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gd0cINZWiF">
    <div
      parentTag="Col"
        class="gd0cINZWiF "
        style="--ta:left;--mb:-20px;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--size:21px;--size-tablet:16px;--size-mobile:14px;--lh:100%;--lh-tablet:180%;--lh-mobile:180%;--weight:bold;--c:#424242;word-break:break-word;--bgc:rgba(246, 246, 246, 0);--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggd0cINZWiF_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gZLgUkHdG5 gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gmpM98VTs5">
    <div
      parentTag="Col"
        class="gmpM98VTs5 "
        style="--ta:left;--mb:-100px;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--size:24px;--size-tablet:16px;--size-mobile:14px;--lh:100%;--lh-tablet:180%;--lh-mobile:180%;--weight:bold;--c:#424242;word-break:break-word;--bgc:rgba(246, 246, 246, 0);--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggmpM98VTs5_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gmgu4F8xDK gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gq1puUXiVV">
    <div
      parentTag="Col"
        class="gq1puUXiVV "
        style="--ta:left;--mb:-100px;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--size:24px;--size-tablet:16px;--size-mobile:14px;--lh:100%;--lh-tablet:180%;--lh-mobile:180%;--weight:bold;--c:#424242;word-break:break-word;--bgc:rgba(246, 246, 246, 0);--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggq1puUXiVV_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gh58w4bVSv gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gjHy46wr2O">
    <div
      parentTag="Col"
        class="gjHy46wr2O "
        style="--ta:left;--mb:-100px;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--size:24px;--size-tablet:16px;--size-mobile:14px;--lh:100%;--lh-tablet:180%;--lh-mobile:180%;--weight:bold;--c:#424242;word-break:break-word;--bgc:rgba(246, 246, 246, 0);--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggjHy46wr2O_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
       
      
    <div
      parentTag="TabItem" id="gbKZxFV8u8" data-id="gbKZxFV8u8"
        style="--mb:2px;--d:grid;--d-tablet:grid;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--cg:2px;--pc:start;--gtc:minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--h:auto;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gbKZxFV8u8 gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gJobuSLpu5 gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gjA49vAcJ8">
    <div
      parentTag="Col"
        class="gjA49vAcJ8 "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggjA49vAcJ8_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gEAKe8_txc gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gUx7LJwODi">
    <div
      parentTag="Col"
        class="gUx7LJwODi "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggUx7LJwODi_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gE4u8IybzR gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gxCOs7OB1W">
    <div
      parentTag="Col"
        class="gxCOs7OB1W "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggxCOs7OB1W_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="g2CNDF7CIh gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g2QVSSRXUV">
    <div
      parentTag="Col"
        class="g2QVSSRXUV "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.gg2QVSSRXUV_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
       
      
    <div
      parentTag="TabItem" id="geqXqaqRkT" data-id="geqXqaqRkT"
        style="--mb:2px;--d:grid;--d-tablet:grid;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--cg:2px;--pc:start;--gtc:minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--h:auto;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="geqXqaqRkT gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gLdLbo7D8d gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gtsP_k_4HS">
    <div
      parentTag="Col"
        class="gtsP_k_4HS "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggtsP_k_4HS_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="g_CxjF87Kc gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gaTmWG7TFi">
    <div
      parentTag="Col"
        class="gaTmWG7TFi "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggaTmWG7TFi_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gnT0pyqpg9 gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g5IM_UsgGo">
    <div
      parentTag="Col"
        class="g5IM_UsgGo "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.gg5IM_UsgGo_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gEkNQ_xvl4 gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gZfLQ8GvQk">
    <div
      parentTag="Col"
        class="gZfLQ8GvQk "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggZfLQ8GvQk_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
       
      
    <div
      parentTag="TabItem" id="gMAF4vH8uR" data-id="gMAF4vH8uR"
        style="--mb:2px;--d:grid;--d-tablet:grid;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--cg:2px;--pc:start;--gtc:minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--h:auto;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gMAF4vH8uR gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gCS_JdR2H0 gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g5l979t2_F">
    <div
      parentTag="Col"
        class="g5l979t2_F "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.gg5l979t2_F_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gxI2-x7XYB gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g8SGtHF5xw">
    <div
      parentTag="Col"
        class="g8SGtHF5xw "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.gg8SGtHF5xw_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="ghe_NLCrlk gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gNSgDvdjgV">
    <div
      parentTag="Col"
        class="gNSgDvdjgV "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggNSgDvdjgV_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gE9GZ-5Xj8 gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g36TcJNtZW">
    <div
      parentTag="Col"
        class="g36TcJNtZW "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.gg36TcJNtZW_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
       
      
    <div
      parentTag="TabItem" id="glWepOA0z2" data-id="glWepOA0z2"
        style="--mb:2px;--d:grid;--d-tablet:grid;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--cg:2px;--pc:start;--gtc:minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--h:auto;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="glWepOA0z2 gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gSCc504Dwe gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gBskgHm-Kf">
    <div
      parentTag="Col"
        class="gBskgHm-Kf "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggBskgHm-Kf_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gtAECFoJdK gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gYQFK6Zq-g">
    <div
      parentTag="Col"
        class="gYQFK6Zq-g "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggYQFK6Zq-g_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start;--d-mobile:none"
      class="gxekgnjjB4 gp-relative gp-flex gp-flex-col"
    >
      
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start;--d-mobile:none"
      class="geVpywtNkS gp-relative gp-flex gp-flex-col"
    >
      
    </div>
    </div>
   
    
  </div>
  
  <div
    
    style="--b:solid;--bc:#121212;--bw:0px;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-none)"
    class="gp-tab-item gp-child-item-giVUHA8wPM"
  >
    <div gp-el-wrapper style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-3xl);--mb-mobile:var(--g-s-2xl)" class="g_zw7mmI9y ">
      
    <div
    data-id="g_zw7mmI9y"
      class="gp-flex gp-justify-start"
    >
      <div
        style="--w:100px;--w-tablet:100px;--w-mobile:100px;--bs:solid;--bbw:1pxpx;--bbw-tablet:undefinedpx;--bbw-mobile:undefinedpx;--t:gp-rotate(0deg);--bc:rgba(125, 125, 125, 0)"
        class="gp-block tablet:gp-block mobile:gp-block"
      ></div>
      <div
        class="gp-items-center gp-hidden tablet:gp-hidden mobile:gp-hidden"
        style="--w:100px;--w-tablet:100px;--w-mobile:100px;--bc:rgba(125, 125, 125, 0);--t:gp-rotate(0deg)"
      >
        
    <div
      class="gp-hidden"
      style="--w:100px;--w-tablet:100px;--w-mobile:100px;--bc:rgba(125, 125, 125, 0);--bs:solid;--bbw:1pxpx;--bbw-tablet:undefinedpx;--bbw-mobile:undefinedpx;--minw:100px"
    ></div>
  
        
            <span
              class="gp-inline-flex gp-items-center gp-justify-center gp-flex-none"
              style="--mr:var(--g-s-xs);--w:20px;--h:20px;--t:gp-rotate(0);--c:var(--g-c-text-1, text-1)"
            >
              <svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M428.8 137.6h-86.177a115.52 115.52 0 0 0 2.176-22.4c0-47.914-35.072-83.2-92-83.2-45.314 0-57.002 48.537-75.707 78.784-7.735 12.413-16.994 23.317-25.851 33.253l-.131.146-.129.148C135.662 161.807 127.764 168 120.8 168h-2.679c-5.747-4.952-13.536-8-22.12-8H32c-17.673 0-32 12.894-32 28.8v230.4C0 435.106 14.327 448 32 448h64c8.584 0 16.373-3.048 22.12-8h2.679c28.688 0 67.137 40 127.2 40h21.299c62.542 0 98.8-38.658 99.94-91.145 12.482-17.813 18.491-40.785 15.985-62.791A93.148 93.148 0 0 0 393.152 304H428.8c45.435 0 83.2-37.584 83.2-83.2 0-45.099-38.101-83.2-83.2-83.2zm0 118.4h-91.026c12.837 14.669 14.415 42.825-4.95 61.05 11.227 19.646 1.687 45.624-12.925 53.625 6.524 39.128-10.076 61.325-50.6 61.325H248c-45.491 0-77.21-35.913-120-39.676V215.571c25.239-2.964 42.966-21.222 59.075-39.596 11.275-12.65 21.725-25.3 30.799-39.875C232.355 112.712 244.006 80 252.8 80c23.375 0 44 8.8 44 35.2 0 35.2-26.4 53.075-26.4 70.4h158.4c18.425 0 35.2 16.5 35.2 35.2 0 18.975-16.225 35.2-35.2 35.2zM88 384c0 13.255-10.745 24-24 24s-24-10.745-24-24 10.745-24 24-24 24 10.745 24 24z" /></svg>
            </span>
          
        
            <span
              class="gp-inline-flex gp-items-center gp-justify-center gp-flex-none gp-g-paragraph-1"
              style="--tt:horizontal;--c:var(--g-c-text-1, text-1);--mr:var(--g-s-s)"
            >
              Title
            </span>
          
        
    <div
      class="gp-flex"
      style="--w:100px;--w-tablet:100px;--w-mobile:100px;--bc:rgba(125, 125, 125, 0);--bs:solid;--bbw:1pxpx;--bbw-tablet:undefinedpx;--bbw-mobile:undefinedpx;--minw:100px"
    ></div>
  
      </div>
    </div>
  
      </div>
       
      
    <div
      parentTag="TabItem" id="gUnNYyl3Xc" data-id="gUnNYyl3Xc"
        style="--mb:var(--g-s-2xl);--d:grid;--d-tablet:grid;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--cg:8px;--pc:start;--gtc:minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--h:auto;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gUnNYyl3Xc gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gJgQzV_vLl gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g8zaptF_LJ">
    <div
      parentTag="Col"
        class="g8zaptF_LJ "
        style="--ta:left;--mb:-20px;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--size:21px;--size-tablet:16px;--size-mobile:14px;--lh:100%;--lh-tablet:180%;--lh-mobile:180%;--weight:bold;--c:#424242;word-break:break-word;--bgc:rgba(246, 246, 246, 0);--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.gg8zaptF_LJ_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gswySeQdDl gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g2sCTdiHfX">
    <div
      parentTag="Col"
        class="g2sCTdiHfX "
        style="--ta:left;--mb:-100px;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--size:24px;--size-tablet:16px;--size-mobile:14px;--lh:100%;--lh-tablet:180%;--lh-mobile:180%;--weight:bold;--c:#424242;word-break:break-word;--bgc:rgba(246, 246, 246, 0);--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.gg2sCTdiHfX_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gr2Y70OsUD gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gPxWLV0M8J">
    <div
      parentTag="Col"
        class="gPxWLV0M8J "
        style="--ta:left;--mb:-100px;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--size:24px;--size-tablet:16px;--size-mobile:14px;--lh:100%;--lh-tablet:180%;--lh-mobile:180%;--weight:bold;--c:#424242;word-break:break-word;--bgc:rgba(246, 246, 246, 0);--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggPxWLV0M8J_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gIQ67Wx87Z gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gjAKRg_cQL">
    <div
      parentTag="Col"
        class="gjAKRg_cQL "
        style="--ta:left;--mb:-100px;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--size:24px;--size-tablet:16px;--size-mobile:14px;--lh:100%;--lh-tablet:180%;--lh-mobile:180%;--weight:bold;--c:#424242;word-break:break-word;--bgc:rgba(246, 246, 246, 0);--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggjAKRg_cQL_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
       
      
    <div
      parentTag="TabItem" id="gvWVBEUz8A" data-id="gvWVBEUz8A"
        style="--mb:2px;--d:grid;--d-tablet:grid;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--cg:2px;--pc:start;--gtc:minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--h:auto;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gvWVBEUz8A gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="ggLoObyFAh gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g4CbbVWhi1">
    <div
      parentTag="Col"
        class="g4CbbVWhi1 "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.gg4CbbVWhi1_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gs45F4oMtM gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gCOx2gxMzK">
    <div
      parentTag="Col"
        class="gCOx2gxMzK "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggCOx2gxMzK_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="g4J_dKFX-9 gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g4N7ElvBN3">
    <div
      parentTag="Col"
        class="g4N7ElvBN3 "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.gg4N7ElvBN3_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gHK1fusMWY gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gy0dR2elmo">
    <div
      parentTag="Col"
        class="gy0dR2elmo "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggy0dR2elmo_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
       
      
    <div
      parentTag="TabItem" id="gddtJltrxM" data-id="gddtJltrxM"
        style="--mb:2px;--d:grid;--d-tablet:grid;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--cg:2px;--pc:start;--gtc:minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--h:auto;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gddtJltrxM gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="geQx3N3PiO gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g7N2hPBze4">
    <div
      parentTag="Col"
        class="g7N2hPBze4 "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:525%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.gg7N2hPBze4_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gI8QD71Bs4 gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gqkO1_0GLx">
    <div
      parentTag="Col"
        class="gqkO1_0GLx "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:525%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggqkO1_0GLx_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gRWU4MZDWK gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gB-YPK5jPA">
    <div
      parentTag="Col"
        class="gB-YPK5jPA "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:525%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggB-YPK5jPA_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="g5bTEsrcfA gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gHFZd8xcO8">
    <div
      parentTag="Col"
        class="gHFZd8xcO8 "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:175%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggHFZd8xcO8_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
  </div>
  
  <div
    
    style="--b:solid;--bc:#121212;--bw:0px;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-none)"
    class="gp-tab-item gp-child-item-giVUHA8wPM"
  >
    <div gp-el-wrapper style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-3xl);--mb-mobile:var(--g-s-2xl)" class="gmrro3g43W ">
      
    <div
    data-id="gmrro3g43W"
      class="gp-flex gp-justify-start"
    >
      <div
        style="--w:100px;--w-tablet:100px;--w-mobile:100px;--bs:solid;--bbw:1pxpx;--bbw-tablet:undefinedpx;--bbw-mobile:undefinedpx;--t:gp-rotate(0deg);--bc:rgba(125, 125, 125, 0)"
        class="gp-block tablet:gp-block mobile:gp-block"
      ></div>
      <div
        class="gp-items-center gp-hidden tablet:gp-hidden mobile:gp-hidden"
        style="--w:100px;--w-tablet:100px;--w-mobile:100px;--bc:rgba(125, 125, 125, 0);--t:gp-rotate(0deg)"
      >
        
    <div
      class="gp-hidden"
      style="--w:100px;--w-tablet:100px;--w-mobile:100px;--bc:rgba(125, 125, 125, 0);--bs:solid;--bbw:1pxpx;--bbw-tablet:undefinedpx;--bbw-mobile:undefinedpx;--minw:100px"
    ></div>
  
        
            <span
              class="gp-inline-flex gp-items-center gp-justify-center gp-flex-none"
              style="--mr:var(--g-s-xs);--w:20px;--h:20px;--t:gp-rotate(0);--c:var(--g-c-text-1, text-1)"
            >
              <svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M428.8 137.6h-86.177a115.52 115.52 0 0 0 2.176-22.4c0-47.914-35.072-83.2-92-83.2-45.314 0-57.002 48.537-75.707 78.784-7.735 12.413-16.994 23.317-25.851 33.253l-.131.146-.129.148C135.662 161.807 127.764 168 120.8 168h-2.679c-5.747-4.952-13.536-8-22.12-8H32c-17.673 0-32 12.894-32 28.8v230.4C0 435.106 14.327 448 32 448h64c8.584 0 16.373-3.048 22.12-8h2.679c28.688 0 67.137 40 127.2 40h21.299c62.542 0 98.8-38.658 99.94-91.145 12.482-17.813 18.491-40.785 15.985-62.791A93.148 93.148 0 0 0 393.152 304H428.8c45.435 0 83.2-37.584 83.2-83.2 0-45.099-38.101-83.2-83.2-83.2zm0 118.4h-91.026c12.837 14.669 14.415 42.825-4.95 61.05 11.227 19.646 1.687 45.624-12.925 53.625 6.524 39.128-10.076 61.325-50.6 61.325H248c-45.491 0-77.21-35.913-120-39.676V215.571c25.239-2.964 42.966-21.222 59.075-39.596 11.275-12.65 21.725-25.3 30.799-39.875C232.355 112.712 244.006 80 252.8 80c23.375 0 44 8.8 44 35.2 0 35.2-26.4 53.075-26.4 70.4h158.4c18.425 0 35.2 16.5 35.2 35.2 0 18.975-16.225 35.2-35.2 35.2zM88 384c0 13.255-10.745 24-24 24s-24-10.745-24-24 10.745-24 24-24 24 10.745 24 24z" /></svg>
            </span>
          
        
            <span
              class="gp-inline-flex gp-items-center gp-justify-center gp-flex-none gp-g-paragraph-1"
              style="--tt:horizontal;--c:var(--g-c-text-1, text-1);--mr:var(--g-s-s)"
            >
              Title
            </span>
          
        
    <div
      class="gp-flex"
      style="--w:100px;--w-tablet:100px;--w-mobile:100px;--bc:rgba(125, 125, 125, 0);--bs:solid;--bbw:1pxpx;--bbw-tablet:undefinedpx;--bbw-mobile:undefinedpx;--minw:100px"
    ></div>
  
      </div>
    </div>
  
      </div>
       
      
    <div
      parentTag="TabItem" id="gJ3NU9yxjs" data-id="gJ3NU9yxjs"
        style="--mb:var(--g-s-2xl);--d:grid;--d-tablet:grid;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--cg:8px;--pc:start;--gtc:minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--h:auto;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gJ3NU9yxjs gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gn6eA5qJ52 gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g9i4-ebW9G">
    <div
      parentTag="Col"
        class="g9i4-ebW9G "
        style="--ta:left;--mb:-20px;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--size:21px;--size-tablet:16px;--size-mobile:14px;--lh:100%;--lh-tablet:180%;--lh-mobile:180%;--weight:bold;--c:#424242;word-break:break-word;--bgc:rgba(246, 246, 246, 0);--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.gg9i4-ebW9G_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gInc55WZD5 gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gJ5XR2HSUU">
    <div
      parentTag="Col"
        class="gJ5XR2HSUU "
        style="--ta:left;--mb:-100px;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--size:24px;--size-tablet:16px;--size-mobile:14px;--lh:100%;--lh-tablet:180%;--lh-mobile:180%;--weight:bold;--c:#424242;word-break:break-word;--bgc:rgba(246, 246, 246, 0);--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggJ5XR2HSUU_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gv71RWtDe2 gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g30c4N-pMI">
    <div
      parentTag="Col"
        class="g30c4N-pMI "
        style="--ta:left;--mb:-100px;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--size:24px;--size-tablet:16px;--size-mobile:14px;--lh:100%;--lh-tablet:180%;--lh-mobile:180%;--weight:bold;--c:#424242;word-break:break-word;--bgc:rgba(246, 246, 246, 0);--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.gg30c4N-pMI_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gVd2nmHwER gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gRYTOsTSOw">
    <div
      parentTag="Col"
        class="gRYTOsTSOw "
        style="--ta:left;--mb:-100px;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--size:24px;--size-tablet:16px;--size-mobile:14px;--lh:100%;--lh-tablet:180%;--lh-mobile:180%;--weight:bold;--c:#424242;word-break:break-word;--bgc:rgba(246, 246, 246, 0);--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggRYTOsTSOw_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
       
      
    <div
      parentTag="TabItem" id="gWvAq19P2X" data-id="gWvAq19P2X"
        style="--mb:2px;--d:grid;--d-tablet:grid;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--cg:2px;--pc:start;--gtc:minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--h:auto;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gWvAq19P2X gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gjqX6QNE6c gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gr0Pgk6xIo">
    <div
      parentTag="Col"
        class="gr0Pgk6xIo "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggr0Pgk6xIo_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gs3ibeR6M_ gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gX9Nszr0v-">
    <div
      parentTag="Col"
        class="gX9Nszr0v- "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggX9Nszr0v-_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gfXsOSor6n gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gI9EaUKkOM">
    <div
      parentTag="Col"
        class="gI9EaUKkOM "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggI9EaUKkOM_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gGhtj82jmh gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gmgcdwPvJV">
    <div
      parentTag="Col"
        class="gmgcdwPvJV "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggmgcdwPvJV_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
       
      
    <div
      parentTag="TabItem" id="gbu3sUtlML" data-id="gbu3sUtlML"
        style="--mb:2px;--d:grid;--d-tablet:grid;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--cg:2px;--pc:start;--gtc:minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--h:auto;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gbu3sUtlML gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gUXN-1xHDo gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gprJQ4f7cZ">
    <div
      parentTag="Col"
        class="gprJQ4f7cZ "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggprJQ4f7cZ_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gGXPIDBjxm gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gNJpDyDw00">
    <div
      parentTag="Col"
        class="gNJpDyDw00 "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggNJpDyDw00_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gl2e9UD3fU gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="ggmvz5N_7U">
    <div
      parentTag="Col"
        class="ggmvz5N_7U "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.gggmvz5N_7U_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gPkzEpFd3o gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gi-hwxptGO">
    <div
      parentTag="Col"
        class="gi-hwxptGO "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:175%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggi-hwxptGO_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
       
      
    <div
      parentTag="TabItem" id="grwsBqFHBg" data-id="grwsBqFHBg"
        style="--mb:2px;--d:grid;--d-tablet:grid;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--cg:2px;--pc:start;--gtc:minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--h:auto;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="grwsBqFHBg gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gE1PiXJfRb gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g1QyyTfvQe">
    <div
      parentTag="Col"
        class="g1QyyTfvQe "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.gg1QyyTfvQe_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="g7sQvcW6O7 gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gIgNVy8l7C">
    <div
      parentTag="Col"
        class="gIgNVy8l7C "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--ls:Auto;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:175%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggIgNVy8l7C_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gpu4HBYtlc gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g0WeP03yv7">
    <div
      parentTag="Col"
        class="g0WeP03yv7 "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.gg0WeP03yv7_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gcZju2M5gR gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gT8ka6p_vL">
    <div
      parentTag="Col"
        class="gT8ka6p_vL "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggT8ka6p_vL_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
  </div>
  
            </div>
          </div>
        </div>
      </div>
    </gp-tab>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-tab.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
    </div>
    </div>
   
    
    </div>
    </div>
  
            </section>
           
    


{% schema %}
  {
    
    "name": "Section 5",
    "tag": "section",
    "class": "gps-573041647856649332 gps gpsil",
    "settings": [{"type":"paragraph","content":"Section design by GemPages. [Click here to start design](https://admin.shopify.com/store/i0pixe-10/apps/gempages-cro/app/shopify/edit?pageType=GP_PRODUCT&editorId=572751048691811510&sectionId=573041647856649332)"},{"type":"select","label":"Section Preload","id":"section_preload","options":[{"label":"On","value":"true"},{"label":"Off","value":"false"},{"label":"Off Lazy Script","value":"off_lazy_script"}],"default":"false"},{"type":"text","label":"Checksum","id":"checksum"},{"type":"html","id":"gg2qW_ITuGe_text","label":"gg2qW_ITuGe_text","default":"IR3 V2 3D Prnter Specifications"},{"type":"html","id":"ggiVUHA8wPM_childItem_0","label":"ggiVUHA8wPM_childItem_0","default":"<p>Machine Parameters</p>"},{"type":"html","id":"ggiVUHA8wPM_childItem_1","label":"ggiVUHA8wPM_childItem_1","default":"<p>Sensors</p>"},{"type":"html","id":"ggiVUHA8wPM_childItem_2","label":"ggiVUHA8wPM_childItem_2","default":"<p>Software</p>"},{"type":"html","id":"ggiVUHA8wPM_childItem_3","label":"ggiVUHA8wPM_childItem_3","default":"<p>Electrical Hardware</p>"},{"type":"html","id":"gg6WVsCBWwc_text","label":"gg6WVsCBWwc_text","default":"Parameter"},{"type":"html","id":"gggANmgN1vN_text","label":"gggANmgN1vN_text","default":"Specification"},{"type":"html","id":"ggQTDxN3dnk_text","label":"ggQTDxN3dnk_text","default":"Parameter"},{"type":"html","id":"gg14sLQMrYY_text","label":"gg14sLQMrYY_text","default":"Specification"},{"type":"html","id":"ggEY5zCKBvo_text","label":"ggEY5zCKBvo_text","default":"Printing Technology"},{"type":"html","id":"ggQBhWdJMKH_text","label":"ggQBhWdJMKH_text","default":"FDM&nbsp;"},{"type":"html","id":"ggZuKYY7LHG_text","label":"ggZuKYY7LHG_text","default":"Layer Thickness&nbsp;"},{"type":"html","id":"ggnKjncwfID_text","label":"ggnKjncwfID_text","default":"0.1-0.3mm"},{"type":"html","id":"ggIeQi_ROOF_text","label":"ggIeQi_ROOF_text","default":"Machine Structure"},{"type":"html","id":"ggCx0yDvdUY_text","label":"ggCx0yDvdUY_text","default":"Full metal frame&nbsp;"},{"type":"html","id":"ggOzjtJNhfh_text","label":"ggOzjtJNhfh_text","default":"Print Speed"},{"type":"html","id":"ggLN_wmr7cU_text","label":"ggLN_wmr7cU_text","default":"≤400mm/s"},{"type":"html","id":"ggvtfSdZX-W_text","label":"ggvtfSdZX-W_text","default":"Motion Structure"},{"type":"html","id":"ggTMr5_HyVd_text","label":"ggTMr5_HyVd_text","default":"CoreXY"},{"type":"html","id":"gg3TfV3Uvik_text","label":"gg3TfV3Uvik_text","default":"Print Acceleration"},{"type":"html","id":"ggRcGoQXvUT_text","label":"ggRcGoQXvUT_text","default":"≤20000mm/s²"},{"type":"html","id":"ggSVL2667uR_text","label":"ggSVL2667uR_text","default":"Filament Diameter"},{"type":"html","id":"ggoixylx3wD_text","label":"ggoixylx3wD_text","default":"1.75mm"},{"type":"html","id":"ggcm6sJ5GwK_text","label":"ggcm6sJ5GwK_text","default":"Maximum Nozzle Temperature"},{"type":"html","id":"gggsqXg8k7v_text","label":"gggsqXg8k7v_text","default":"300°C"},{"type":"html","id":"ggGMdqEuQRy_text","label":"ggGMdqEuQRy_text","default":"Motor Type"},{"type":"html","id":"ggWEfCf_Wt__text","label":"ggWEfCf_Wt__text","default":"5:1 Dual gear reduction extruder motor"},{"type":"html","id":"gg2cgWH4DOz_text","label":"gg2cgWH4DOz_text","default":"Maximum Heated &nbsp;Bed Temperature"},{"type":"html","id":"ggAptm69qFX_text","label":"ggAptm69qFX_text","default":"90°C"},{"type":"html","id":"gg5w-vBVvGx_text","label":"gg5w-vBVvGx_text","default":"Nozzle Material"},{"type":"html","id":"gg90OEhA6nf_text","label":"gg90OEhA6nf_text","default":"Hardened steel"},{"type":"html","id":"ggW861ZEqYT_text","label":"ggW861ZEqYT_text","default":"Nozzle Heating Time"},{"type":"html","id":"ggP_xOKipUG_text","label":"ggP_xOKipUG_text","default":"40s"},{"type":"html","id":"ggrNaI7T5FU_text","label":"ggrNaI7T5FU_text","default":"Nozzle Size"},{"type":"html","id":"ggu8N5UomB-_text","label":"ggu8N5UomB-_text","default":"Standard 0.4mm"},{"type":"html","id":"ggsZmqpf8-W_text","label":"ggsZmqpf8-W_text","default":"Heated Bed Heating Time"},{"type":"html","id":"ggw8rUe3Xfr_text","label":"ggw8rUe3Xfr_text","default":"90s"},{"type":"html","id":"ggBXFbt1d4H_text","label":"ggBXFbt1d4H_text","default":"Print Volume&nbsp;"},{"type":"html","id":"ggoDxU-MNFB_text","label":"ggoDxU-MNFB_text","default":"250×250×∞mm(X*Y*Z)"},{"type":"html","id":"ggLi0q3Q2yo_text","label":"ggLi0q3Q2yo_text","default":"Maximum Flow Rate"},{"type":"html","id":"ggtaN0_0Z3F_text","label":"ggtaN0_0Z3F_text","default":"26mm³/s"},{"type":"html","id":"ggYb9SESEw1_text","label":"ggYb9SESEw1_text","default":"Product Dimensions"},{"type":"html","id":"gg-wliuXyxo_text","label":"gg-wliuXyxo_text","default":"676×436×510mm"},{"type":"html","id":"ggbzliZB5Z7_text","label":"ggbzliZB5Z7_text","default":"Print Platform&nbsp;"},{"type":"html","id":"ggxxfujcIgP_text","label":"ggxxfujcIgP_text","default":"PEI metal build surface"},{"type":"html","id":"ggOLSMPsGz9_text","label":"ggOLSMPsGz9_text","default":"Package Dimensions"},{"type":"html","id":"ggfGFvPS7em_text","label":"ggfGFvPS7em_text","default":"770×510×320mm"},{"type":"html","id":"ggnSyXrurLg_text","label":"ggnSyXrurLg_text","default":"Compatible Materials"},{"type":"html","id":"gg4Aue0oPr0_text","label":"gg4Aue0oPr0_text","default":"PLA/PETG/TPU/ ABS/ASA, etc."},{"type":"html","id":"ggqAiusMaBv_text","label":"ggqAiusMaBv_text","default":"Net Weight"},{"type":"html","id":"gg3b2QhRgR8_text","label":"gg3b2QhRgR8_text","default":"16.5kg"},{"type":"html","id":"ggxF-Ccvgp8_text","label":"ggxF-Ccvgp8_text","default":"Operating Environment &nbsp;Temperature"},{"type":"html","id":"ggdClmtnSzZ_text","label":"ggdClmtnSzZ_text","default":"10-40°C"},{"type":"html","id":"ggKKcBo7q1C_text","label":"ggKKcBo7q1C_text","default":"Gross Weight"},{"type":"html","id":"ggJG9zUHDTw_text","label":"ggJG9zUHDTw_text","default":"21kg"},{"type":"html","id":"ggFhSFMozwV_text","label":"ggFhSFMozwV_text","default":"Noise Level"},{"type":"html","id":"gg3AnHl4kFU_text","label":"gg3AnHl4kFU_text","default":"54dB"},{"type":"html","id":"gg-DeKoyPp7_text","label":"gg-DeKoyPp7_text","default":"Print Precision"},{"type":"html","id":"gg3A9DFQNa5_text","label":"gg3A9DFQNa5_text","default":"±0.1mm"},{"type":"html","id":"ggtJrQQLlrZ_text","label":"ggtJrQQLlrZ_text","default":"Print Methods"},{"type":"html","id":"ggSn8OEDb0O_text","label":"ggSn8OEDb0O_text","default":"USB drive/Local network/I nternet network"},{"type":"html","id":"ggd0cINZWiF_text","label":"ggd0cINZWiF_text","default":"Feature"},{"type":"html","id":"ggmpM98VTs5_text","label":"ggmpM98VTs5_text","default":"Support"},{"type":"html","id":"ggq1puUXiVV_text","label":"ggq1puUXiVV_text","default":"Feature"},{"type":"html","id":"ggjHy46wr2O_text","label":"ggjHy46wr2O_text","default":"Support"},{"type":"html","id":"ggjA49vAcJ8_text","label":"ggjA49vAcJ8_text","default":"Printing Vibration Compensation"},{"type":"html","id":"ggUx7LJwODi_text","label":"ggUx7LJwODi_text","default":"Yes"},{"type":"html","id":"ggxCOs7OB1W_text","label":"ggxCOs7OB1W_text","default":"Filament Runout Detection"},{"type":"html","id":"gg2QVSSRXUV_text","label":"gg2QVSSRXUV_text","default":"Yes"},{"type":"html","id":"ggtsP_k_4HS_text","label":"ggtsP_k_4HS_text","default":"Material Shortage Detection"},{"type":"html","id":"ggaTmWG7TFi_text","label":"ggaTmWG7TFi_text","default":"Yes"},{"type":"html","id":"gg5IM_UsgGo_text","label":"gg5IM_UsgGo_text","default":"Clogging Detection"},{"type":"html","id":"ggZfLQ8GvQk_text","label":"ggZfLQ8GvQk_text","default":"Yes"},{"type":"html","id":"gg5l979t2_F_text","label":"gg5l979t2_F_text","default":"Auto Leveling"},{"type":"html","id":"gg8SGtHF5xw_text","label":"gg8SGtHF5xw_text","default":"Yes"},{"type":"html","id":"ggNSgDvdjgV_text","label":"ggNSgDvdjgV_text","default":"LED Lighting"},{"type":"html","id":"gg36TcJNtZW_text","label":"gg36TcJNtZW_text","default":"Yes"},{"type":"html","id":"ggBskgHm-Kf_text","label":"ggBskgHm-Kf_text","default":"Camera"},{"type":"html","id":"ggYQFK6Zq-g_text","label":"ggYQFK6Zq-g_text","default":"Yes"},{"type":"html","id":"gg8zaptF_LJ_text","label":"gg8zaptF_LJ_text","default":"Feature"},{"type":"html","id":"gg2sCTdiHfX_text","label":"gg2sCTdiHfX_text","default":"Specification"},{"type":"html","id":"ggPxWLV0M8J_text","label":"ggPxWLV0M8J_text","default":"Feature"},{"type":"html","id":"ggjAKRg_cQL_text","label":"ggjAKRg_cQL_text","default":"Specification"},{"type":"html","id":"gg4CbbVWhi1_text","label":"gg4CbbVWhi1_text","default":"Slicing Software"},{"type":"html","id":"ggCOx2gxMzK_text","label":"ggCOx2gxMzK_text","default":"Ideamaker/ Ideaformer Cura"},{"type":"html","id":"gg4N7ElvBN3_text","label":"gg4N7ElvBN3_text","default":"Operating Systems"},{"type":"html","id":"ggy0dR2elmo_text","label":"ggy0dR2elmo_text","default":"Windows/MacOS/Linux"},{"type":"html","id":"gg7N2hPBze4_text","label":"gg7N2hPBze4_text","default":"Output File Format"},{"type":"html","id":"ggqkO1_0GLx_text","label":"ggqkO1_0GLx_text","default":".gcode"},{"type":"html","id":"ggB-YPK5jPA_text","label":"ggB-YPK5jPA_text","default":"input Formats"},{"type":"html","id":"ggHFZd8xcO8_text","label":"ggHFZd8xcO8_text","default":".stl/.obj/.3mf/.step/ .stp/.iges/.igs/.oltp/ .jpg/.jpeg/.png/.bmp"},{"type":"html","id":"gg9i4-ebW9G_text","label":"gg9i4-ebW9G_text","default":"Parameter"},{"type":"html","id":"ggJ5XR2HSUU_text","label":"ggJ5XR2HSUU_text","default":"Specification"},{"type":"html","id":"gg30c4N-pMI_text","label":"gg30c4N-pMI_text","default":"Parameter"},{"type":"html","id":"ggRYTOsTSOw_text","label":"ggRYTOsTSOw_text","default":"Specification"},{"type":"html","id":"ggr0Pgk6xIo_text","label":"ggr0Pgk6xIo_text","default":"Input Voltage"},{"type":"html","id":"ggX9Nszr0v-_text","label":"ggX9Nszr0v-_text","default":"110VAC/220VAC, 50/60Hz"},{"type":"html","id":"ggI9EaUKkOM_text","label":"ggI9EaUKkOM_text","default":"Memory&nbsp;"},{"type":"html","id":"ggmgcdwPvJV_text","label":"ggmgcdwPvJV_text","default":"16GB-SD, 1GB DDR3"},{"type":"html","id":"ggprJQ4f7cZ_text","label":"ggprJQ4f7cZ_text","default":"aximum Power&nbsp;"},{"type":"html","id":"ggNJpDyDw00_text","label":"ggNJpDyDw00_text","default":"800W"},{"type":"html","id":"gggmvz5N_7U_text","label":"gggmvz5N_7U_text","default":"User Interface&nbsp;"},{"type":"html","id":"ggi-hwxptGO_text","label":"ggi-hwxptGO_text","default":"4.3-inch touchscreen with 800×480 resolution"},{"type":"html","id":"gg1QyyTfvQe_text","label":"gg1QyyTfvQe_text","default":"Main Controller&nbsp;"},{"type":"html","id":"ggIgNVy8l7C_text","label":"ggIgNVy8l7C_text","default":"64-bit 1.5GHz Quad-core Cortex-A53 processor"},{"type":"html","id":"gg0WeP03yv7_text","label":"gg0WeP03yv7_text","default":"Printing Firmware Klipper"},{"type":"html","id":"ggT8ka6p_vL_text","label":"ggT8ka6p_vL_text","default":"Klipper"}],
    "blocks": [{"type": "@app"}],
    "locales": {}
  }
{% endschema %}
