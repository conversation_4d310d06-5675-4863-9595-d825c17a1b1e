{%- liquid
  assign main_menu = linklists[section.settings.main_menu_link_list]

  assign logo_alignment = 'left'
  if section.settings.main_menu_alignment == 'center-left' or section.settings.main_menu_alignment == 'center-split' or section.settings.main_menu_alignment == 'center' or section.settings.main_menu_alignment == 'center-drawer'
    assign logo_alignment = 'center'
  endif

  assign template_name = template | replace: '.', ' ' | truncatewords: 2, '' | handle

  assign sticky_header = false
  assign overlay_header = false

  if section.settings.header_sticky
    assign sticky_header = true
  endif

  if template_name == 'index' and section.settings.sticky_index
    assign overlay_header = true
  endif
  if template_name contains 'collection' and collection.image and section.settings.sticky_collection
    assign overlay_header = true
  endif
-%}

{%- render 'drawer-menu', section: section, main_menu: main_menu, logo_alignment: logo_alignment -%}
{%- render 'cart-drawer' -%}

{%- style -%}
  .site-nav__link,
  .site-nav__dropdown-link {
    font-size: {{ settings.type_navigation_size }}px;
  }

  {%- if settings.type_navigation_size < 18 -%}
    .site-nav__link {
      padding-left: 8px;
      padding-right: 8px;
    }
  {%- endif -%}

  {%- if settings.color_header == settings.color_body_bg or settings.color_body_bg contains settings.color_header -%}
    .site-header {
      border-bottom: 1px solid;
      border-bottom-color: {{ settings.color_borders }};
    }
  {%- endif -%}
{%- endstyle -%}

<div data-section-id="{{ section.id }}" data-section-type="header">
  <div id="HeaderWrapper" class="header-wrapper{% if overlay_header %} header-wrapper--overlay is-light{% endif %}">
    <header
      id="SiteHeader"
      class="site-header{% if settings.type_navigation_style %} site-header--heading-style{% endif %}"
      data-sticky="{{ sticky_header }}"
      data-overlay="{{ overlay_header }}">
      <div class="page-width">
        <div
          class="header-layout header-layout--{{ section.settings.main_menu_alignment }}"
          data-logo-align="{{ logo_alignment }}">

          {%- if logo_alignment == 'left' -%}
            <div class="header-item header-item--logo">
              {%- render 'header-logo-block', section: section -%}
            </div>
          {%- endif -%}

          {%- if logo_alignment == 'left' and section.settings.main_menu_alignment != 'left-drawer' -%}
            <div class="header-item header-item--navigation{% if section.settings.main_menu_alignment == 'left-center' %} text-center{% endif %}" role="navigation" aria-label="Primary">
              {%- render 'header-desktop-nav', section: section, main_menu: main_menu, hover_menu: section.settings.hover_menu -%}
            </div>
          {%- endif -%}

          {%- if logo_alignment == 'center' -%}
            <div class="header-item header-item--left header-item--navigation" role="navigation" aria-label="Primary">
              {%- if section.settings.main_menu_alignment == 'center' or section.settings.main_menu_alignment == 'center-split' -%}
                {%- if settings.search_enable -%}
                  <div class="site-nav medium-down--hide">
                    <a href="{{ routes.search_url }}" class="site-nav__link site-nav__link--icon js-search-header js-no-transition">
                      <svg aria-hidden="true" focusable="false" role="presentation" class="icon icon-search" viewBox="0 0 64 64"><title>icon-search</title><path d="M47.16 28.58A18.58 18.58 0 1 1 28.58 10a18.58 18.58 0 0 1 18.58 18.58ZM54 54 41.94 42"/></svg>
                      <span class="icon__fallback-text">{{ 'general.search.title' | t }}</span>
                    </a>
                  </div>
                {%- endif -%}
              {%- endif -%}

              {%- if section.settings.main_menu_alignment == 'center-left' -%}
                {%- render 'header-desktop-nav', section: section, main_menu: main_menu, hover_menu: section.settings.hover_menu -%}
              {%- endif -%}

              <div class="site-nav{% unless section.settings.main_menu_alignment == 'center-drawer' %} large-up--hide{% endunless %}">
                <button
                  type="button"
                  class="site-nav__link site-nav__link--icon js-drawer-open-nav"
                  aria-controls="NavDrawer">
                  <svg aria-hidden="true" focusable="false" role="presentation" class="icon icon-hamburger" viewBox="0 0 64 64"><title>icon-hamburger</title><path d="M7 15h51M7 32h43M7 49h51"/></svg>
                  <span class="icon__fallback-text">{{ 'general.drawers.navigation' | t }}</span>
                </button>
              </div>
            </div>

            {%- if section.settings.main_menu_alignment == 'center-split' -%}
              {%- render 'header-split-nav', main_menu: main_menu, section: section, hover_menu: section.settings.hover_menu-%}
            {%- endif -%}

            {%- if section.settings.main_menu_alignment != 'center-split' -%}
              <div class="header-item header-item--logo">
                {%- render 'header-logo-block', section: section -%}
              </div>
            {%- endif -%}
          {%- endif -%}

          <div class="header-item header-item--icons">
            {%- render 'header-icons', section: section -%}
          </div>
        </div>

        {%- if section.settings.main_menu_alignment == 'center' -%}
          <div class="text-center" role="navigation" aria-label="Primary">
            {%- render 'header-desktop-nav', section: section, main_menu: main_menu, hover_menu: section.settings.hover_menu -%}
          </div>
        {%- endif -%}
      </div>
      <div class="site-header__search-container">
        <div class="site-header__search">
          <div class="page-width">
            {% render 'predictive-search', context: 'header' %}
          </div>
        </div>
      </div>
    </header>
  </div>
</div>

{%- if template_name == 'index' and section.settings.sticky_index -%}
  {%- style -%}
    /* Offset first hero's text to make room for overlaid navigation */
    .shopify-section:first-child .hero__text-content.vertical-center {
      padding-top: 110px;
    }
    @media screen and (max-width: 768px) {
      .shopify-section:first-child .hero__text-content.vertical-center {
        padding-top: 70px;
      }
    }
  {%- endstyle -%}
{%- endif -%}

{% schema %}
{
  "name": "t:sections.header.name",
  "settings": [
    {
      "type": "header",
      "content": "t:sections.header.settings.header_logo"
    },
    {
      "type": "image_picker",
      "id": "logo",
      "label": "t:sections.header.settings.logo.label"
    },
    {
      "type": "image_picker",
      "id": "logo-inverted",
      "label": "t:sections.header.settings.logo-inverted.label",
      "info": "t:sections.header.settings.logo-inverted.info"
    },
    {
      "type": "range",
      "id": "desktop_logo_width",
      "label": "t:sections.header.settings.desktop_logo_width.label",
      "default": 200,
      "min": 100,
      "max": 400,
      "step": 10,
      "unit": "px"
    },
    {
      "type": "range",
      "id": "mobile_logo_width",
      "label": "t:sections.header.settings.mobile_logo_width.label",
      "default": 140,
      "min": 60,
      "max": 200,
      "step": 10,
      "unit": "px",
      "info": "t:sections.header.settings.mobile_logo_width.info"
    },
    {
      "type": "link_list",
      "id": "main_menu_link_list",
      "label": "t:sections.header.settings.main_menu_link_list.label",
      "default": "main-menu"
    },
    {
      "type": "checkbox",
      "id": "hover_menu",
      "label": "t:sections.header.settings.hover_menu.label",
      "default": true
    },
    {
      "type": "select",
      "id": "main_menu_alignment",
      "label": "t:sections.header.settings.main_menu_alignment.label",
      "default": "left-center",
      "options": [
        {
          "value": "left",
          "label": "t:sections.header.settings.main_menu_alignment.options.left.label"
        },
        {
          "value": "left-center",
          "label": "t:sections.header.settings.main_menu_alignment.options.left-center.label"
        },
        {
          "value": "left-drawer",
          "label": "t:sections.header.settings.main_menu_alignment.options.left-drawer.label"
        },
        {
          "value": "center-left",
          "label": "t:sections.header.settings.main_menu_alignment.options.center-left.label"
        },
        {
          "value": "center-split",
          "label": "t:sections.header.settings.main_menu_alignment.options.center-split.label"
        },
        {
          "value": "center",
          "label": "t:sections.header.settings.main_menu_alignment.options.center.label"
        },
        {
          "value": "center-drawer",
          "label": "t:sections.header.settings.main_menu_alignment.options.center-drawer.label"
        }
      ]
    },
    {
      "type": "checkbox",
      "id": "header_sticky",
      "label": "t:sections.header.settings.header_sticky.label",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "sticky_index",
      "label": "t:sections.header.settings.sticky_index.label",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "sticky_collection",
      "label": "t:sections.header.settings.sticky_collection.label",
      "info": "t:sections.header.settings.sticky_collection.info",
      "default": false
    }
  ],
  "blocks": [
    {
      "type": "megamenu",
      "name": "t:sections.header.blocks.mega_menu.name",
      "settings": [
        {
          "type": "text",
          "id": "menu_item",
          "label": "t:sections.header.blocks.mega_menu.settings.menu_item.label",
          "info": "t:sections.header.blocks.mega_menu.settings.menu_item.info"
        },
        {
          "type": "header",
          "content": "t:sections.header.blocks.mega_menu.settings.header_promotion_1"
        },
        {
          "type": "image_picker",
          "id": "promo_image_1",
          "label": "t:sections.header.blocks.mega_menu.settings.promo_image_1.label"
        },
        {
          "type": "text",
          "id": "promo_heading_1",
          "label": "t:sections.header.blocks.mega_menu.settings.promo_heading_1.label"
        },
        {
          "type": "text",
          "id": "promo_text_1",
          "label": "t:sections.header.blocks.mega_menu.settings.promo_text_1.label"
        },
        {
          "type": "url",
          "id": "promo_url_1",
          "label": "t:sections.header.blocks.mega_menu.settings.promo_url_1.label"
        },
        {
          "type": "header",
          "content": "t:sections.header.blocks.mega_menu.settings.header_promotion_2"
        },
        {
          "type": "image_picker",
          "id": "promo_image_2",
          "label": "t:sections.header.blocks.mega_menu.settings.promo_image_2.label"
        },
        {
          "type": "text",
          "id": "promo_heading_2",
          "label": "t:sections.header.blocks.mega_menu.settings.promo_heading_2.label"
        },
        {
          "type": "text",
          "id": "promo_text_2",
          "label": "t:sections.header.blocks.mega_menu.settings.promo_text_2.label"
        },
        {
          "type": "url",
          "id": "promo_url_2",
          "label": "t:sections.header.blocks.mega_menu.settings.promo_url_2.label"
        }
      ]
    }
  ],
  "default": {
    "settings": {}
  }
}
{% endschema %}
