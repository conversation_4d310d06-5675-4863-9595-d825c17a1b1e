{% comment %}
  IR3 All-in-One Hero Section for Ideaformer IR3 V2 3D Printer
  File: sections/ir3-all.liquid
{% endcomment %}

{{ 'IR3-hero-section-1.css' | asset_url | stylesheet_tag }}

<section
  class="hero-section ir3-all-section"
  id="ir3-all-{{ section.id }}"
  style="margin-top: {{ section.settings.margin_top | default: 0 }}px; margin-bottom: {{ section.settings.margin_bottom | default: 0 }}px;"
>
  <!-- Hero Container -->
  <div class="hero-container">
    <!-- Enhanced Background Layers -->
    <div class="background-layer">
      <div class="gradient-overlay"></div>
      <div class="animated-gradient"></div>
      <div class="grid-pattern"></div>
      <div class="tech-lines"></div>
      <div class="floating-shapes">
        <div class="shape shape-1"></div>
        <div class="shape shape-2"></div>
        <div class="shape shape-3"></div>
      </div>
      <div class="particle-field"></div>
    </div>

    <!-- Content Layer -->
    <div class="content-layer">
      <div class="container">
        <div class="hero-content">
          <!-- Title Group -->
          <div class="title-group animate-fade-in" data-delay="0">
            {% if section.settings.model_tag != blank %}
              <span class="pre-title animate-slide-in">{{ section.settings.model_tag }}</span>
            {% endif %}
            
            {% if section.settings.main_title_line1 != blank %}
              <h1 class="main-title glitch" data-text="{{ section.settings.main_title_line1 }}">
                {{ section.settings.main_title_line1 }}
              </h1>
            {% endif %}
            
            {% if section.settings.main_title_line2 != blank %}
              <h2 class="sub-title">
                {{ section.settings.main_title_line2 }}
              </h2>
            {% endif %}
            
            {% if section.settings.subtitle != blank %}
              <p class="tagline animate-fade-in" data-delay="0.3">
                {{ section.settings.subtitle }}
              </p>
            {% endif %}
          </div>

          <!-- Feature Pills -->
          <div class="feature-pills animate-fade-in" data-delay="0.4">
            {% if section.settings.feature_strip_1 != blank %}
              <span class="pill">{{ section.settings.feature_strip_1 }}</span>
            {% endif %}
            {% if section.settings.feature_strip_2 != blank %}
              <span class="pill">{{ section.settings.feature_strip_2 }}</span>
            {% endif %}
            {% if section.settings.feature_strip_3 != blank %}
              <span class="pill">{{ section.settings.feature_strip_3 }}</span>
            {% endif %}
          </div>

          <!-- Product Features -->
          <div class="product-features animate-fade-in" data-delay="0.5">
            {% if section.settings.feature_1 != blank %}
              <div class="feature-item">
                <span class="feature-icon">✓</span>
                <span class="feature-text">{{ section.settings.feature_1 }}</span>
              </div>
            {% endif %}
            {% if section.settings.feature_2 != blank %}
              <div class="feature-item">
                <span class="feature-icon">✓</span>
                <span class="feature-text">{{ section.settings.feature_2 }}</span>
              </div>
            {% endif %}
            {% if section.settings.feature_3 != blank %}
              <div class="feature-item">
                <span class="feature-icon">✓</span>
                <span class="feature-text">{{ section.settings.feature_3 }}</span>
              </div>
            {% endif %}
          </div>

          <!-- CTA Buttons -->
          <div class="cta-group animate-fade-in" data-delay="0.6">
            {% if section.settings.primary_cta_text != blank %}
              <a href="{{ section.settings.primary_cta_link | default: '#' }}" class="btn btn-primary magnetic-button">
                {{ section.settings.primary_cta_text }}
              </a>
            {% endif %}
            {% if section.settings.secondary_cta_text != blank %}
              <a href="{{ section.settings.secondary_cta_link | default: '#' }}" class="btn btn-secondary magnetic-button">
                {{ section.settings.secondary_cta_text }}
              </a>
            {% endif %}
          </div>

          <!-- Scroll Indicator -->
          {% if section.settings.scroll_text != blank %}
            <div class="scroll-indicator animate-float" data-delay="0.8">
              <span class="scroll-text">{{ section.settings.scroll_text }}</span>
              <div class="scroll-arrow">↓</div>
            </div>
          {% endif %}
        </div>

        <!-- Product Image -->
        {% if section.settings.product_image != blank %}
          <div class="product-showcase animate-fade-in" data-delay="0.7">
            <div class="product-image-container">
              <img
                src="{{ section.settings.product_image | img_url: '800x' }}"
                alt="{{ section.settings.product_alt_text | default: 'Ideaformer IR3 V2 3D Printer' }}"
                class="product-image animate-float"
                loading="lazy"
              >
            </div>
          </div>
        {% endif %}
      </div>
    </div>

    <!-- Background Video (if provided) -->
    {% if section.settings.background_video != blank %}
      <div class="video-background">
        <video autoplay muted loop playsinline>
          <source src="{{ section.settings.background_video }}" type="video/mp4">
        </video>
      </div>
    {% endif %}
  </div>
</section>

<script src="{{ 'IR3-hero-section-1.js' | asset_url }}" defer></script>

{% schema %}
{
  "name": "IR3 All-in-One Hero",
  "settings": [
    {
      "type": "header",
      "content": "Title Settings"
    },
    {
      "type": "text",
      "id": "model_tag",
      "label": "Model Tag",
      "default": "PROFESSIONAL SERIES"
    },
    {
      "type": "text",
      "id": "main_title_line1",
      "label": "Main Title Line 1",
      "default": "INFINITE POSSIBILITIES"
    },
    {
      "type": "text",
      "id": "main_title_line2",
      "label": "Main Title Line 2",
      "default": "CONVEYOR BELT 3D PRINTING"
    },
    {
      "type": "textarea",
      "id": "subtitle",
      "label": "Subtitle",
      "default": "Break the Z-axis limit. Start your continuous printing journey."
    },
    {
      "type": "header",
      "content": "Product Image"
    },
    {
      "type": "image_picker",
      "id": "product_image",
      "label": "Product Image"
    },
    {
      "type": "text",
      "id": "product_alt_text",
      "label": "Product Image Alt Text",
      "default": "Ideaformer IR3 V2 3D Printer"
    },
    {
      "type": "header",
      "content": "Features"
    },
    {
      "type": "text",
      "id": "feature_1",
      "label": "Feature 1",
      "default": "Auto Leveling System"
    },
    {
      "type": "text",
      "id": "feature_2",
      "label": "Feature 2",
      "default": "Metal Conveyor Belt"
    },
    {
      "type": "text",
      "id": "feature_3",
      "label": "Feature 3",
      "default": "400+ mm/s Speed"
    },
    {
      "type": "header",
      "content": "Feature Strip"
    },
    {
      "type": "text",
      "id": "feature_strip_1",
      "label": "Feature Strip 1",
      "default": "Infinite Z-axis"
    },
    {
      "type": "text",
      "id": "feature_strip_2",
      "label": "Feature Strip 2",
      "default": "Batch Production"
    },
    {
      "type": "text",
      "id": "feature_strip_3",
      "label": "Feature Strip 3",
      "default": "100% Auto-leveling"
    },
    {
      "type": "header",
      "content": "Call to Action"
    },
    {
      "type": "text",
      "id": "primary_cta_text",
      "label": "Primary CTA Text",
      "default": "Explore IR3 V2"
    },
    {
      "type": "url",
      "id": "primary_cta_link",
      "label": "Primary CTA Link"
    },
    {
      "type": "text",
      "id": "secondary_cta_text",
      "label": "Secondary CTA Text",
      "default": "View Specifications"
    },
    {
      "type": "url",
      "id": "secondary_cta_link",
      "label": "Secondary CTA Link"
    },
    {
      "type": "header",
      "content": "Additional Settings"
    },
    {
      "type": "text",
      "id": "scroll_text",
      "label": "Scroll Text",
      "default": "Scroll to explore"
    },
    {
      "type": "url",
      "id": "background_video",
      "label": "Background Video URL"
    },
    {
      "type": "range",
      "id": "margin_top",
      "label": "Top Margin",
      "min": 0,
      "max": 100,
      "step": 5,
      "default": 0,
      "unit": "px"
    },
    {
      "type": "range",
      "id": "margin_bottom",
      "label": "Bottom Margin",
      "min": 0,
      "max": 100,
      "step": 5,
      "default": 0,
      "unit": "px"
    }
  ],
  "presets": [
    {
      "name": "IR3 All-in-One Hero",
      "category": "Hero"
    }
  ]
}
{% endschema %}
