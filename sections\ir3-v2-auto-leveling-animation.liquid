{%- style -%}
  /* 使用Shopify托管字体 */
  @font-face {
    font-family: 'Montserrat';
    font-weight: 400;
    font-style: normal;
    src: url("{{ 'montserrat-regular.woff2' | asset_url }}") format("woff2"),
         url("{{ 'montserrat-regular.woff' | asset_url }}") format("woff");
    font-display: swap;
  }

  @font-face {
    font-family: 'Montserrat';
    font-weight: 700;
    font-style: normal;
    src: url("{{ 'montserrat-bold.woff2' | asset_url }}") format("woff2"),
         url("{{ 'montserrat-bold.woff' | asset_url }}") format("woff");
    font-display: swap;
  }

  @font-face {
    font-family: 'Montserrat';
    font-weight: 400;
    font-style: italic;
    src: url("{{ 'montserrat-italic.woff2' | asset_url }}") format("woff2"),
         url("{{ 'montserrat-italic.woff' | asset_url }}") format("woff");
    font-display: swap;
  }

  @font-face {
    font-family: 'Open Sans';
    font-weight: 400;
    font-style: normal;
    src: url("{{ 'open-sans-regular.woff2' | asset_url }}") format("woff2"),
         url("{{ 'open-sans-regular.woff' | asset_url }}") format("woff");
    font-display: swap;
  }

  @font-face {
    font-family: 'Open Sans';
    font-weight: 700;
    font-style: normal;
    src: url("{{ 'open-sans-bold.woff2' | asset_url }}") format("woff2"),
         url("{{ 'open-sans-bold.woff' | asset_url }}") format("woff");
    font-display: swap;
  }

  @font-face {
    font-family: 'Open Sans';
    font-weight: 400;
    font-style: italic;
    src: url("{{ 'open-sans-italic.woff2' | asset_url }}") format("woff2"),
         url("{{ 'open-sans-italic.woff' | asset_url }}") format("woff");
    font-display: swap;
  }
  /* 强制重置section的margin */
  #shopify-section-{{ section.id }} {
    margin: 0 !important;
    padding: 0 !important;
  }

  .ir3-auto-leveling {
    position: relative;
    width: 100%;
    overflow: hidden;
    margin: 0 !important;
    margin-bottom: {{ section.settings.bottom_spacing }}px !important;
  }

  .ir3-auto-leveling__video-wrapper {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
  }

  .ir3-auto-leveling__video-container {
    position: relative;
    width: {% if section.settings.full_width %}100%{% else %}{{ section.settings.container_width }}px{% endif %};
    {% if section.settings.use_viewport_height %}
      height: 100vh;
      min-height: {{ section.settings.section_height_desktop }}px;
    {% else %}
      height: {{ section.settings.section_height_desktop }}px;
    {% endif %}
    margin: 0 auto;
    overflow: hidden;
    background-color: {{ section.settings.background_color }};
    {% if section.settings.video_fill_mode == 'fullscreen' %}
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      z-index: 9;
    {% endif %}
  }

  .ir3-auto-leveling__video {
    position: absolute;
    width: 100%;
    height: 100%;
    {% if section.settings.video_fill_mode == 'contain' %}
      object-fit: contain;
    {% elsif section.settings.video_fill_mode == 'cover' %}
      object-fit: cover;
    {% elsif section.settings.video_fill_mode == 'fullscreen' %}
      object-fit: cover;
      width: 100vw;
      height: 100vh;
    {% else %}
      object-fit: fill;
    {% endif %}
  }

  /* 视频主标题和描述 */
  .ir3-auto-leveling__main-title-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    z-index: 13;
    width: 90%;
    max-width: 900px;
    opacity: 0;
    pointer-events: none;
    transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  .ir3-auto-leveling__main-title {
    font-size: {{ section.settings.video_title_size }}px;
    font-weight: {{ section.settings.video_title_weight }};
    font-family: {{ section.settings.video_title_font_family }};
    font-style: {{ section.settings.video_title_font_style }};
    color: {{ section.settings.video_title_color }};
    margin-bottom: 20px;
    line-height: 1.2;
    letter-spacing: {{ section.settings.video_title_letter_spacing }}em;
    text-transform: {% if section.settings.video_title_uppercase %}uppercase{% else %}none{% endif %};
    text-shadow:
      0 2px 4px rgba(0,0,0,0.1),
      0 4px 8px rgba(0,0,0,0.1),
      0 8px 16px rgba(0,0,0,0.1);
    animation: mainTitleGlow 3s ease-in-out infinite alternate;
  }

  @keyframes mainTitleGlow {
    0% {
      filter: brightness(1);
    }
    100% {
      filter: brightness(1.1);
    }
  }

  .ir3-auto-leveling__main-description {
    font-size: {{ section.settings.video_description_size }}px;
    font-family: {{ section.settings.video_description_font_family }};
    font-style: {{ section.settings.video_description_font_style }};
    color: {{ section.settings.video_description_color }};
    line-height: 1.6;
    font-weight: {{ section.settings.video_description_weight }};
    opacity: 0.9;
    max-width: 700px;
    margin: 0 auto;
    text-shadow: 0 1px 3px rgba(0,0,0,0.2);
  }

  .ir3-auto-leveling__main-title-container.show {
    opacity: 1;
    animation: mainTitleFadeIn 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  }

  .ir3-auto-leveling__main-title-container.hide {
    animation: mainTitleFadeOut 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  }

  @keyframes mainTitleFadeIn {
    0% {
      opacity: 0;
      transform: translate(-50%, -40%) scale(0.9);
      filter: blur(10px);
    }
    50% {
      filter: blur(5px);
    }
    100% {
      opacity: 1;
      transform: translate(-50%, -50%) scale(1);
      filter: blur(0);
    }
  }

  @keyframes mainTitleFadeOut {
    0% {
      opacity: 1;
      transform: translate(-50%, -50%) scale(1);
      filter: blur(0);
    }
    100% {
      opacity: 0;
      transform: translate(-50%, -60%) scale(0.95);
      filter: blur(10px);
    }
  }

  /* 调试网格系统 */
  .ir3-auto-leveling__debug-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 20;
    {% unless section.settings.show_debug_markers %}display: none;{% endunless %}
  }

  .ir3-auto-leveling__debug-grid {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
      repeating-linear-gradient(0deg, rgba(255,255,255,0.1) 0, rgba(255,255,255,0.1) 1px, transparent 1px, transparent 10%),
      repeating-linear-gradient(90deg, rgba(255,255,255,0.1) 0, rgba(255,255,255,0.1) 1px, transparent 1px, transparent 10%);
  }

  .ir3-auto-leveling__debug-grid-major {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
      repeating-linear-gradient(0deg, rgba(255,255,255,0.3) 0, rgba(255,255,255,0.3) 2px, transparent 2px, transparent 20%),
      repeating-linear-gradient(90deg, rgba(255,255,255,0.3) 0, rgba(255,255,255,0.3) 2px, transparent 2px, transparent 20%);
  }

  .ir3-auto-leveling__debug-labels {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    font-size: 10px;
    color: rgba(255,255,255,0.8);
    font-family: monospace;
  }

  .ir3-auto-leveling__debug-label {
    position: absolute;
    background: rgba(0,0,0,0.7);
    padding: 2px 4px;
    border-radius: 2px;
    font-size: 10px;
    white-space: nowrap;
  }

  /* 调试信息面板 */
  .ir3-auto-leveling__debug-panel {
    position: fixed;
    top: 20px;
    right: 20px;
    background: rgba(0,0,0,0.9);
    color: #fff;
    padding: 20px;
    border-radius: 8px;
    font-family: monospace;
    font-size: 12px;
    z-index: 100;
    min-width: 250px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.5);
    {% unless section.settings.show_debug_markers %}display: none;{% endunless %}
  }

  .ir3-auto-leveling__debug-panel h4 {
    margin: 0 0 10px 0;
    font-size: 14px;
    color: #ffd700;
    border-bottom: 1px solid rgba(255,255,255,0.3);
    padding-bottom: 5px;
  }

  .ir3-auto-leveling__debug-info {
    margin: 5px 0;
    display: flex;
    justify-content: space-between;
  }

  .ir3-auto-leveling__debug-info label {
    color: #888;
  }

  .ir3-auto-leveling__debug-info span {
    color: #0ff;
    font-weight: bold;
  }

  /* 鼠标跟踪器 */
  .ir3-auto-leveling__mouse-tracker {
    position: absolute;
    width: 1px;
    height: 100%;
    background: rgba(255,0,0,0.5);
    pointer-events: none;
    z-index: 21;
    {% unless section.settings.show_debug_markers %}display: none;{% endunless %}
  }

  .ir3-auto-leveling__mouse-tracker-h {
    position: absolute;
    width: 100%;
    height: 1px;
    background: rgba(255,0,0,0.5);
    pointer-events: none;
    z-index: 21;
    {% unless section.settings.show_debug_markers %}display: none;{% endunless %}
  }

  .ir3-auto-leveling__mouse-coords {
    position: absolute;
    background: rgba(255,0,0,0.9);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-family: monospace;
    pointer-events: none;
    z-index: 22;
    transform: translate(10px, -30px);
    {% unless section.settings.show_debug_markers %}display: none;{% endunless %}
  }

  /* 时间轴标记 */
  .ir3-auto-leveling__timeline-markers {
    position: absolute;
    bottom: 40px;
    left: 0;
    width: 100%;
    height: 30px;
    z-index: 21;
    {% unless section.settings.show_debug_markers %}display: none;{% endunless %}
  }

  .ir3-auto-leveling__timeline-marker {
    position: absolute;
    top: 0;
    width: 2px;
    height: 20px;
    background: rgba(255,255,0,0.6);
  }

  .ir3-auto-leveling__timeline-label {
    position: absolute;
    top: 22px;
    font-size: 10px;
    color: rgba(255,255,0,0.8);
    font-family: monospace;
    transform: translateX(-50%);
  }

  /* 高亮点调试信息 */
  .ir3-auto-leveling__hotspot-debug {
    position: absolute;
    top: -25px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0,0,0,0.8);
    color: #0ff;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
    font-family: monospace;
    white-space: nowrap;
    pointer-events: none;
    {% unless section.settings.show_debug_markers %}display: none;{% endunless %}
  }

  .ir3-auto-leveling__progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: {{ section.settings.progress_height }}px;
    width: 0%;
    background: {{ section.settings.progress_gradient_start }};
    background: linear-gradient(90deg, {{ section.settings.progress_gradient_start }} 0%, {{ section.settings.progress_gradient_end }} 100%);
    z-index: 10;
    transition: width 0.1s linear;
    box-shadow: 0 0 20px rgba({{ section.settings.progress_gradient_end | color_to_rgb | append: ',0.6' }});
    {% unless section.settings.show_progress %}display: none;{% endunless %}
  }

  .ir3-auto-leveling__progress::before {
    content: '';
    position: absolute;
    top: -2px;
    left: 0;
    right: 0;
    height: 2px;
    background: rgba(255, 255, 255, 0.5);
    animation: shimmer 2s infinite;
  }

  @keyframes shimmer {
    0% { opacity: 0.3; }
    50% { opacity: 0.8; }
    100% { opacity: 0.3; }
  }

  .ir3-auto-leveling__hotspots {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 11;
    {% unless section.settings.show_hotspots %}display: none;{% endunless %}
  }

  .ir3-auto-leveling__hotspot {
    position: absolute;
    width: {{ section.settings.hotspot_size }}px;
    height: {{ section.settings.hotspot_size }}px;
    border-radius: 50%;
    background: {{ section.settings.hotspot_bg_color }};
    border: 2px solid {{ section.settings.hotspot_border_color }};
    transform: translate(-50%, -50%);
    opacity: 0;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 20px rgba({{ section.settings.hotspot_glow_color | color_to_rgb | append: ',0.4' }}),
                inset 0 0 10px rgba({{ section.settings.hotspot_glow_color | color_to_rgb | append: ',0.2' }});
  }

  .ir3-auto-leveling__hotspot::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 6px;
    height: 6px;
    background: {{ section.settings.hotspot_center_color }};
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: pulse-center 2s infinite;
  }

  .ir3-auto-leveling__hotspot::after {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    border: 2px solid {{ section.settings.hotspot_border_color }};
    border-radius: 50%;
    opacity: 0;
    animation: ripple 2s infinite;
  }

  @keyframes pulse-center {
    0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 1; }
    50% { transform: translate(-50%, -50%) scale(1.5); opacity: 0.7; }
  }

  @keyframes ripple {
    0% {
      transform: scale(0.8);
      opacity: 1;
    }
    100% {
      transform: scale(2);
      opacity: 0;
    }
  }

  .ir3-auto-leveling__hotspot:hover {
    transform: translate(-50%, -50%) scale(1.3) rotate(90deg);
    border-color: {{ section.settings.hotspot_hover_color }};
    box-shadow: 0 6px 30px rgba({{ section.settings.hotspot_glow_color | color_to_rgb | append: ',0.6' }}),
                inset 0 0 20px rgba({{ section.settings.hotspot_glow_color | color_to_rgb | append: ',0.4' }});
  }

  .ir3-auto-leveling__hotspot.active {
    animation: hotspot-active 2s infinite;
  }

  @keyframes hotspot-active {
    0%, 100% {
      transform: translate(-50%, -50%) scale(1) rotate(0deg);
      box-shadow: 0 4px 20px rgba({{ section.settings.hotspot_glow_color | color_to_rgb | append: ',0.4' }}),
                  inset 0 0 10px rgba({{ section.settings.hotspot_glow_color | color_to_rgb | append: ',0.2' }});
    }
    25% {
      transform: translate(-50%, -50%) scale(1.1) rotate(90deg);
    }
    50% {
      transform: translate(-50%, -50%) scale(1) rotate(180deg);
      box-shadow: 0 8px 40px rgba({{ section.settings.hotspot_glow_color | color_to_rgb | append: ',0.8' }}),
                  inset 0 0 20px rgba({{ section.settings.hotspot_glow_color | color_to_rgb | append: ',0.4' }});
    }
    75% {
      transform: translate(-50%, -50%) scale(1.1) rotate(270deg);
    }
  }

  .ir3-auto-leveling__text-container {
    {% if section.settings.text_display_position == 'inside_bottom' %}
      position: absolute;
      bottom: 40px;
      left: 50%;
      transform: translateX(-50%);
      width: 90%;
      max-width: 800px;
      text-align: center;
      z-index: 12;
    {% elsif section.settings.text_display_position == 'outside_bottom' %}
      position: relative;
      width: 100%;
      padding: 40px 20px;
      text-align: center;
    {% else %}
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      z-index: 12;
    {% endif %}
  }

  .ir3-auto-leveling__text-item {
    {% if section.settings.text_display_position == 'next_to_hotspot' %}
      position: absolute;
      background: {{ section.settings.text_bg_gradient_start }};
      background: linear-gradient(135deg,
        {{ section.settings.text_bg_gradient_start | replace: 'rgb', 'rgba' | replace: ')', ',' }}{{ section.settings.text_bg_opacity }}) 0%,
        {{ section.settings.text_bg_gradient_end | replace: 'rgb', 'rgba' | replace: ')', ',' }}{{ section.settings.text_bg_opacity }}) 100%);
      padding: 24px 28px;
      padding-top: 36px;
      border-radius: {{ section.settings.text_border_radius }}px;
      width: 360px;
      max-width: 90vw;
      pointer-events: auto;
      border-left: 4px solid {{ section.settings.text_accent_color }};
      box-shadow:
        0 10px 40px rgba(0,0,0,0.15),
        0 4px 12px rgba(0,0,0,0.1),
        0 0 100px rgba({{ section.settings.text_accent_color | color_to_rgb | append: ',0.05' }});
      backdrop-filter: blur({{ section.settings.text_backdrop_blur }}px) saturate(180%);
      -webkit-backdrop-filter: blur({{ section.settings.text_backdrop_blur }}px) saturate(180%);
      overflow: visible;
      z-index: 15;
      transform-origin: center center;
      transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    {% else %}
      margin: 0 auto 20px;
      max-width: 700px;
      {% if section.settings.text_display_position == 'inside_bottom' %}
        background: {{ section.settings.text_bg_gradient_start }};
        background: linear-gradient(135deg,
          {{ section.settings.text_bg_gradient_start | replace: 'rgb', 'rgba' | replace: ')', ',' }}{{ section.settings.text_bg_opacity }}) 0%,
          {{ section.settings.text_bg_gradient_end | replace: 'rgb', 'rgba' | replace: ')', ',' }}{{ section.settings.text_bg_opacity }}) 100%);
        padding: 28px 36px;
        padding-top: 40px;
        border-radius: {{ section.settings.text_border_radius }}px;
        box-shadow:
          0 12px 48px rgba(0,0,0,0.18),
          0 4px 16px rgba(0,0,0,0.12),
          0 0 120px rgba({{ section.settings.text_accent_color | color_to_rgb | append: ',0.08' }});
        backdrop-filter: blur({{ section.settings.text_backdrop_blur }}px) saturate(180%);
        -webkit-backdrop-filter: blur({{ section.settings.text_backdrop_blur }}px) saturate(180%);
        border-top: 3px solid {{ section.settings.text_accent_color }};
        position: relative;
        overflow: visible;
        transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      {% else %}
        padding: 20px;
      {% endif %}
    {% endif %}
    opacity: 0;
  }

  {% if section.settings.text_display_position == 'next_to_hotspot' %}
  .ir3-auto-leveling__text-item.active {
    opacity: 1;
    animation: textItemSlideIn 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  }

  .ir3-auto-leveling__text-item:not(.active) {
    opacity: 0;
    pointer-events: none;
  }

  @keyframes textItemSlideIn {
    0% {
      opacity: 0;
      transform: translateY(20px) scale(0.95);
      filter: blur(5px);
    }
    50% {
      filter: blur(0);
    }
    100% {
      opacity: 1;
      transform: translateY(0) scale(1);
      filter: blur(0);
    }
  }
  {% endif %}

  .ir3-auto-leveling__text-item:hover {
    {% if section.settings.text_display_position == 'next_to_hotspot' %}
      transform: translateY(-4px);
      box-shadow:
        0 15px 50px rgba(0,0,0,0.2),
        0 5px 15px rgba(0,0,0,0.15),
        0 0 120px rgba({{ section.settings.text_accent_color | color_to_rgb | append: ',0.1' }});
    {% elsif section.settings.text_display_position == 'inside_bottom' %}
      transform: translateY(-2px);
      box-shadow:
        0 15px 55px rgba(0,0,0,0.22),
        0 5px 20px rgba(0,0,0,0.15),
        0 0 140px rgba({{ section.settings.text_accent_color | color_to_rgb | append: ',0.1' }});
    {% endif %}
  }

  .ir3-auto-leveling__title {
    font-size: {{ section.settings.title_size }}px;
    font-weight: 700;
    color: {{ section.settings.title_color }};
    margin-bottom: 12px;
    line-height: 1.3;
    letter-spacing: -0.02em;
    text-shadow: 0 1px 2px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
  }

  .ir3-auto-leveling__title::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: {{ section.settings.text_accent_color }};
    transition: width 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  .ir3-auto-leveling__text-item:hover .ir3-auto-leveling__title {
    transform: translateX(4px);
    color: {{ section.settings.title_hover_color | default: section.settings.title_color }};
  }

  .ir3-auto-leveling__text-item:hover .ir3-auto-leveling__title::after {
    width: 100%;
  }

  .ir3-auto-leveling__description {
    font-size: {{ section.settings.description_size }}px;
    color: {{ section.settings.description_color }};
    line-height: 1.7;
    font-weight: 400;
    opacity: 0.95;
    transition: all 0.3s ease;
  }

  .ir3-auto-leveling__text-item:hover .ir3-auto-leveling__description {
    opacity: 1;
    color: {{ section.settings.description_hover_color | default: section.settings.description_color }};
  }

  .ir3-auto-leveling__step-number {
    position: absolute;
    top: -16px;
    right: 20px;
    width: 32px;
    height: 32px;
    background: {{ section.settings.text_accent_color }};
    color: #1a1a1a;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 14px;
    box-shadow:
      0 2px 8px rgba({{ section.settings.text_accent_color | color_to_rgb | append: ',0.4' }}),
      0 0 20px rgba({{ section.settings.text_accent_color | color_to_rgb | append: ',0.2' }});
    animation: step-bounce 2s infinite;
    z-index: 1;
    border: 1px solid rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
  }

  .ir3-auto-leveling__text-item:hover .ir3-auto-leveling__step-number {
    transform: translateY(-2px) scale(1.1);
    box-shadow:
      0 4px 12px rgba({{ section.settings.text_accent_color | color_to_rgb | append: ',0.5' }}),
      0 0 30px rgba({{ section.settings.text_accent_color | color_to_rgb | append: ',0.3' }});
  }

  @keyframes step-bounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-4px); }
  }

  .ir3-auto-leveling__text-item.fade-in {
    animation: advancedFadeIn 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  }

  .ir3-auto-leveling__text-item.fade-out {
    animation: advancedFadeOut 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  }

  @keyframes advancedFadeIn {
    0% {
      opacity: 0;
      filter: blur(4px);
      transform: translateY(20px) scale(0.95);
    }
    50% {
      filter: blur(0px);
    }
    100% {
      opacity: 1;
      filter: blur(0px);
      transform: translateY(0) scale(1);
    }
  }

  @keyframes advancedFadeOut {
    0% {
      opacity: 1;
      filter: blur(0px);
      transform: translateY(0) scale(1);
    }
    100% {
      opacity: 0;
      filter: blur(4px);
      transform: translateY(-20px) scale(0.95);
    }
  }

  .ir3-auto-leveling__markers {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    {% unless section.settings.show_debug_markers %}display: none;{% endunless %}
  }

  .ir3-auto-leveling__marker {
    position: absolute;
    width: 2px;
    height: 100%;
    background-color: rgba(255, 0, 0, 0.5);
  }

  .ir3-auto-leveling__marker--start {
    left: 0;
    background-color: rgba(0, 255, 0, 0.5);
  }

  .ir3-auto-leveling__marker--end {
    right: 0;
    background-color: rgba(0, 0, 255, 0.5);
  }

  .ir3-auto-leveling--pinned {
    height: calc(100vh + 500vh);
  }

  @media only screen and (max-width: 768px) {
    .ir3-auto-leveling__video-container {
      {% if section.settings.use_viewport_height %}
        height: 100vh;
        min-height: {{ section.settings.section_height_mobile }}px;
      {% else %}
        height: {{ section.settings.section_height_mobile }}px;
      {% endif %}
      width: 100%;
    }

    /* 移动端：确保section有足够的高度支持完整的滚动动画 */
    .ir3-auto-leveling--pinned {
      min-height: calc(100vh + 400vh); /* 移动端使用较小的额外高度 */
    }

    /* 移动端热点文字优化 */
    .ir3-auto-leveling__text-item {
      max-width: 280px; /* 限制文字宽度 */
      font-size: 14px; /* 稍小的字体 */
      padding: 12px 16px; /* 调整内边距 */
      margin: 8px; /* 增加外边距避免贴边 */

      /* 确保文字框不会超出视口 */
      left: auto !important;
      right: 10px;
      transform: none !important;
    }

    .ir3-auto-leveling__text-item h3 {
      font-size: 16px; /* 移动端标题字体 */
      margin-bottom: 8px;
    }

    .ir3-auto-leveling__text-item p {
      font-size: 13px; /* 移动端描述字体 */
      line-height: 1.4;
    }

    /* 移动端热点大小调整 */
    .ir3-auto-leveling__hotspot {
      width: 20px;
      height: 20px;
    }

    .ir3-auto-leveling__hotspot::before {
      width: 8px;
      height: 8px;
    }

    .ir3-auto-leveling__main-title {
      font-size: {{ section.settings.video_title_size | times: 0.75 }}px;
    }

    .ir3-auto-leveling__main-description {
      font-size: {{ section.settings.video_description_size | times: 0.85 }}px;
    }

    .ir3-auto-leveling__title {
      font-size: {{ section.settings.title_size | times: 0.85 }}px;
    }

    .ir3-auto-leveling__description {
      font-size: {{ section.settings.description_size | times: 0.85 }}px;
    }

    .ir3-auto-leveling__text-item {
      {% if section.settings.text_display_position == 'next_to_hotspot' %}
        width: 280px;
        padding: 20px 24px;
        padding-top: 32px;
      {% endif %}
    }

    .ir3-auto-leveling__hotspot {
      width: {{ section.settings.hotspot_size | times: 0.8 }}px;
      height: {{ section.settings.hotspot_size | times: 0.8 }}px;
    }

    .ir3-auto-leveling__step-number {
      width: 28px;
      height: 28px;
      font-size: 12px;
      top: -14px;
      right: 16px;
    }

    .ir3-auto-leveling--pinned {
      height: calc(100vh + 400vh);
    }

    .ir3-auto-leveling__debug-panel {
      top: 10px;
      right: 10px;
      padding: 15px;
      font-size: 11px;
      min-width: 200px;
    }
  }
{%- endstyle -%}

<div
  class="ir3-auto-leveling {% if section.settings.lock_scroll %}ir3-auto-leveling--pinned{% endif %}"
  id="ir3-auto-leveling-{{ section.id }}"
  data-section-id="{{ section.id }}"
  data-section-type="ir3-auto-leveling"
>
  <div class="ir3-auto-leveling__video-wrapper">
    <div class="ir3-auto-leveling__video-container" id="ir3-video-container-{{ section.id }}">
      <video
        class="ir3-auto-leveling__video"
        id="ir3-video-{{ section.id }}"
        muted
        playsinline
        {% if section.settings.loop_video %}
          loop
        {% endif %}
        data-desktop-src="{{ section.settings.video_url }}"
        data-mobile-src="{{ section.settings.mobile_video_url }}"
      ></video>

      <!-- 视频主标题和描述 -->
      {% if section.settings.show_video_title %}
        {% if section.settings.video_title != blank or section.settings.video_description != blank %}
          <div class="ir3-auto-leveling__main-title-container" id="ir3-main-title-{{ section.id }}">
            {% if section.settings.video_title != blank %}
              <h2 class="ir3-auto-leveling__main-title">{{ section.settings.video_title }}</h2>
            {% endif %}
            {% if section.settings.video_description != blank %}
              <p class="ir3-auto-leveling__main-description">{{ section.settings.video_description }}</p>
            {% endif %}
          </div>
        {% endif %}
      {% endif %}

      {% if section.settings.show_debug_markers %}
        <!-- 调试网格覆盖层 -->
        <div class="ir3-auto-leveling__debug-overlay">
          <!-- 细网格 (10%) -->
          <div class="ir3-auto-leveling__debug-grid"></div>
          <!-- 粗网格 (20%) -->
          <div class="ir3-auto-leveling__debug-grid-major"></div>
          <!-- 坐标标签 -->
          <div class="ir3-auto-leveling__debug-labels" id="ir3-debug-labels-{{ section.id }}"></div>
        </div>

        <!-- 鼠标跟踪器 -->
        <div class="ir3-auto-leveling__mouse-tracker" id="ir3-mouse-tracker-v-{{ section.id }}"></div>
        <div class="ir3-auto-leveling__mouse-tracker-h" id="ir3-mouse-tracker-h-{{ section.id }}"></div>
        <div class="ir3-auto-leveling__mouse-coords" id="ir3-mouse-coords-{{ section.id }}">X: 0%, Y: 0%</div>

        <!-- 时间轴标记 -->
        <div class="ir3-auto-leveling__timeline-markers" id="ir3-timeline-markers-{{ section.id }}"></div>
      {% endif %}

      {% if section.settings.show_hotspots %}
        <div class="ir3-auto-leveling__hotspots" id="ir3-hotspots-{{ section.id }}">
          {% for i in (1..6) %}
            {% assign hotspot_enabled = 'hotspot_' | append: i | append: '_enabled' %}
            {% assign hotspot_x = 'hotspot_' | append: i | append: '_x' %}
            {% assign hotspot_y = 'hotspot_' | append: i | append: '_y' %}
            {% assign hotspot_start_time = 'hotspot_' | append: i | append: '_start_time' %}
            {% assign hotspot_end_time = 'hotspot_' | append: i | append: '_end_time' %}
            {% assign hotspot_title = 'hotspot_' | append: i | append: '_title' %}
            {% assign hotspot_description = 'hotspot_' | append: i | append: '_description' %}

            {% if section.settings[hotspot_enabled] %}
              <div
                class="ir3-auto-leveling__hotspot"
                id="ir3-hotspot-{{ section.id }}-{{ i }}"
                style="left: {{ section.settings[hotspot_x] }}%; top: {{ section.settings[hotspot_y] }}%;"
                data-hotspot-id="{{ i }}"
                data-start-time="{{ section.settings[hotspot_start_time] }}"
                data-end-time="{{ section.settings[hotspot_end_time] }}"
                data-x="{{ section.settings[hotspot_x] }}"
                data-y="{{ section.settings[hotspot_y] }}"
                data-mobile-x="{{ section.settings[hotspot_mobile_x] }}"
                data-mobile-y="{{ section.settings[hotspot_mobile_y] }}"
              >
                {% if section.settings.show_debug_markers %}
                  <div class="ir3-auto-leveling__hotspot-debug">
                    #{{ i }} [{{ section.settings[hotspot_start_time] }}-{{ section.settings[hotspot_end_time] }}%]
                  </div>
                {% endif %}
              </div>
            {% endif %}
          {% endfor %}
        </div>
      {% endif %}

      {% if section.settings.show_progress %}
        <div class="ir3-auto-leveling__progress" id="ir3-progress-{{ section.id }}"></div>
      {% endif %}

      {% if section.settings.show_debug_markers %}
        <div class="ir3-auto-leveling__markers">
          <div class="ir3-auto-leveling__marker ir3-auto-leveling__marker--start"></div>
          <div class="ir3-auto-leveling__marker ir3-auto-leveling__marker--end"></div>
        </div>
      {% endif %}

      {% if section.settings.text_display_position == 'next_to_hotspot' %}
        <div class="ir3-auto-leveling__text-container" id="ir3-text-container-{{ section.id }}">
          {% for i in (1..6) %}
            {% assign hotspot_enabled = 'hotspot_' | append: i | append: '_enabled' %}
            {% assign hotspot_show_text = 'hotspot_' | append: i | append: '_show_text' %}
            {% assign hotspot_x = 'hotspot_' | append: i | append: '_x' %}
            {% assign hotspot_y = 'hotspot_' | append: i | append: '_y' %}
            {% assign hotspot_title = 'hotspot_' | append: i | append: '_title' %}
            {% assign hotspot_description = 'hotspot_' | append: i | append: '_description' %}

            {% if section.settings[hotspot_enabled] and section.settings[hotspot_show_text] %}
              <div
                class="ir3-auto-leveling__text-item"
                id="ir3-text-item-{{ section.id }}-{{ i }}"
                data-hotspot-id="{{ i }}"
                data-x="{{ section.settings[hotspot_x] }}"
                data-y="{{ section.settings[hotspot_y] }}"
              >
                {% if section.settings.show_step_numbers %}
                  <div class="ir3-auto-leveling__step-number">{{ i }}</div>
                {% endif %}
                {% if section.settings[hotspot_title] != blank %}
                  <div class="ir3-auto-leveling__title">{{ section.settings[hotspot_title] }}</div>
                {% endif %}
                {% if section.settings[hotspot_description] != blank %}
                  <div class="ir3-auto-leveling__description">{{ section.settings[hotspot_description] }}</div>
                {% endif %}
              </div>
            {% endif %}
          {% endfor %}
        </div>
      {% endif %}
    </div>

    {% if section.settings.text_display_position != 'next_to_hotspot' %}
      <div class="ir3-auto-leveling__text-container" id="ir3-text-container-{{ section.id }}">
        {% for i in (1..6) %}
          {% assign hotspot_enabled = 'hotspot_' | append: i | append: '_enabled' %}
          {% assign hotspot_show_text = 'hotspot_' | append: i | append: '_show_text' %}
          {% assign hotspot_title = 'hotspot_' | append: i | append: '_title' %}
          {% assign hotspot_description = 'hotspot_' | append: i | append: '_description' %}

          {% if section.settings[hotspot_enabled] and section.settings[hotspot_show_text] %}
            <div
              class="ir3-auto-leveling__text-item"
              id="ir3-text-item-{{ section.id }}-{{ i }}"
              data-hotspot-id="{{ i }}"
            >
              {% if section.settings.show_step_numbers %}
                <div class="ir3-auto-leveling__step-number">{{ i }}</div>
              {% endif %}
              {% if section.settings[hotspot_title] != blank %}
                <div class="ir3-auto-leveling__title">{{ section.settings[hotspot_title] }}</div>
              {% endif %}
              {% if section.settings[hotspot_description] != blank %}
                <div class="ir3-auto-leveling__description">{{ section.settings[hotspot_description] }}</div>
              {% endif %}
            </div>
          {% endif %}
        {% endfor %}
      </div>
    {% endif %}
  </div>

  <!-- 调试信息面板 -->
  {% if section.settings.show_debug_markers %}
    <div class="ir3-auto-leveling__debug-panel" id="ir3-debug-panel-{{ section.id }}">
      <h4>🔧 调试信息</h4>
      <div class="ir3-auto-leveling__debug-info">
        <label>鼠标位置:</label>
        <span id="ir3-debug-mouse-{{ section.id }}">X: 0%, Y: 0%</span>
      </div>
      <div class="ir3-auto-leveling__debug-info">
        <label>视频进度:</label>
        <span id="ir3-debug-progress-{{ section.id }}">0%</span>
      </div>
      <div class="ir3-auto-leveling__debug-info">
        <label>时间码:</label>
        <span id="ir3-debug-time-{{ section.id }}">0.00s / 0.00s</span>
      </div>
      <div class="ir3-auto-leveling__debug-info">
        <label>滚动位置:</label>
        <span id="ir3-debug-scroll-{{ section.id }}">0%</span>
      </div>
      <div class="ir3-auto-leveling__debug-info">
        <label>活动高亮点:</label>
        <span id="ir3-debug-active-{{ section.id }}">无</span>
      </div>
      <div class="ir3-auto-leveling__debug-info">
        <label>容器尺寸:</label>
        <span id="ir3-debug-size-{{ section.id }}">0x0</span>
      </div>
    </div>
  {% endif %}
</div>

<script>
      document.addEventListener('DOMContentLoaded', function() {
        initIR3Animation('{{ section.id }}');
      });
      
      document.addEventListener('IR3AnimationReady', function() {
        initIR3Animation('{{ section.id }}');
      });
      
      function initIR3Animation(sectionId) {
        if (window['ir3AnimationInit' + sectionId]) {
          return;
        }
        
        if (typeof gsap === 'undefined' || typeof ScrollTrigger === 'undefined') {
          console.error('GSAP或ScrollTrigger未加载，IR3动画无法初始化');
          return;
        }
        
        window['ir3AnimationInit' + sectionId] = true;
        
        const section = document.getElementById('ir3-auto-leveling-' + sectionId);
        const videoContainer = document.getElementById('ir3-video-container-' + sectionId);
        const video = document.getElementById('ir3-video-' + sectionId);
        const hotspots = document.querySelectorAll('#ir3-hotspots-' + sectionId + ' .ir3-auto-leveling__hotspot');
        const textItems = document.querySelectorAll('#ir3-text-container-' + sectionId + ' .ir3-auto-leveling__text-item');
        const progressBar = document.getElementById('ir3-progress-' + sectionId);
        const mainTitleContainer = document.getElementById('ir3-main-title-' + sectionId);
        const isLockScroll = {{ section.settings.lock_scroll }};
        const showDebugMarkers = {{ section.settings.show_debug_markers }};
        const showVideoTitle = {{ section.settings.show_video_title }};
        const videoTitleStartTime = {{ section.settings.video_title_start_time }} / 100;
        const videoTitleEndTime = {{ section.settings.video_title_end_time }} / 100;

        // 响应式视频和热点配置
        const isMobile = window.innerWidth <= 768;
        const desktopVideoSrc = video.getAttribute('data-desktop-src');
        const mobileVideoSrc = video.getAttribute('data-mobile-src');

        // 设置正确的视频源
        video.src = isMobile ? mobileVideoSrc : desktopVideoSrc;

        // 添加导航跳转检测变量
        let isNavigationJump = false;
        let navigationJumpTimeout = null;

        // 初始化时隐藏所有高亮点
        function resetHotspots() {
          hotspots.forEach(function(hotspot) {
            hotspot.classList.remove('active');
            gsap.set(hotspot, { opacity: 0 });
          });

          textItems.forEach(function(textItem) {
            textItem.classList.remove('active', 'fade-in');
            textItem.classList.add('fade-out');
            gsap.set(textItem, { opacity: 0 });
          });
        }

        // 检测导航跳转
        function detectNavigationJump() {
          isNavigationJump = true;
          resetHotspots();

          // 清除之前的超时
          if (navigationJumpTimeout) {
            clearTimeout(navigationJumpTimeout);
          }

          // 设置延迟，允许正常的滚动动画
          navigationJumpTimeout = setTimeout(function() {
            isNavigationJump = false;
          }, 800); // 800ms延迟
        }

        // 监听页面导航事件
        document.addEventListener('pageNavigationJump', detectNavigationJump);

        // 监听滚动事件来检测快速跳转
        let lastScrollTime = Date.now();
        let lastScrollTop = window.pageYOffset;

        window.addEventListener('scroll', function() {
          const currentTime = Date.now();
          const currentScrollTop = window.pageYOffset;
          const scrollDistance = Math.abs(currentScrollTop - lastScrollTop);
          const timeDiff = currentTime - lastScrollTime;

          // 如果滚动距离很大且时间很短，可能是导航跳转
          if (scrollDistance > 300 && timeDiff < 50) {
            detectNavigationJump();
          }

          lastScrollTime = currentTime;
          lastScrollTop = currentScrollTop;
        }, { passive: true });

        // 初始化时重置状态
        resetHotspots();

        // 更新热点位置函数
        function updateHotspotPositions() {
          const currentIsMobile = window.innerWidth <= 768;
          hotspots.forEach(hotspot => {
            const desktopX = hotspot.getAttribute('data-x');
            const desktopY = hotspot.getAttribute('data-y');
            const mobileX = hotspot.getAttribute('data-mobile-x');
            const mobileY = hotspot.getAttribute('data-mobile-y');

            if (currentIsMobile && mobileX && mobileY) {
              hotspot.style.left = mobileX + '%';
              hotspot.style.top = mobileY + '%';
            } else {
              hotspot.style.left = desktopX + '%';
              hotspot.style.top = desktopY + '%';
            }
          });
        }

        // 初始化热点位置
        updateHotspotPositions();
        
        // 调试元素
        let debugElements = {};
        if (showDebugMarkers) {
          debugElements = {
            mouseTrackerV: document.getElementById('ir3-mouse-tracker-v-' + sectionId),
            mouseTrackerH: document.getElementById('ir3-mouse-tracker-h-' + sectionId),
            mouseCoords: document.getElementById('ir3-mouse-coords-' + sectionId),
            debugMouse: document.getElementById('ir3-debug-mouse-' + sectionId),
            debugProgress: document.getElementById('ir3-debug-progress-' + sectionId),
            debugTime: document.getElementById('ir3-debug-time-' + sectionId),
            debugScroll: document.getElementById('ir3-debug-scroll-' + sectionId),
            debugActive: document.getElementById('ir3-debug-active-' + sectionId),
            debugSize: document.getElementById('ir3-debug-size-' + sectionId),
            debugLabels: document.getElementById('ir3-debug-labels-' + sectionId),
            timelineMarkers: document.getElementById('ir3-timeline-markers-' + sectionId)
          };
          
          // 创建网格标签
          createGridLabels();
          
          // 创建时间轴标记
          createTimelineMarkers();
          
          // 鼠标跟踪
          setupMouseTracking();
          
          // 更新容器尺寸信息
          updateContainerSize();
        }
        
        function createGridLabels() {
          if (!debugElements.debugLabels) return;
          
          // 创建坐标标签 (每20%一个)
          for (let x = 0; x <= 100; x += 20) {
            for (let y = 0; y <= 100; y += 20) {
              const label = document.createElement('div');
              label.className = 'ir3-auto-leveling__debug-label';
              label.textContent = `${x},${y}`;
              label.style.left = x + '%';
              label.style.top = y + '%';
              
              // 调整边缘标签位置
              if (x === 100) label.style.transform = 'translateX(-100%)';
              if (y === 100) label.style.transform = 'translateY(-100%)';
              if (x === 100 && y === 100) label.style.transform = 'translate(-100%, -100%)';
              
              debugElements.debugLabels.appendChild(label);
            }
          }
        }
        
        function createTimelineMarkers() {
          if (!debugElements.timelineMarkers) return;
          
          // 创建时间轴标记 (每10%一个)
          for (let i = 0; i <= 100; i += 10) {
            const marker = document.createElement('div');
            marker.className = 'ir3-auto-leveling__timeline-marker';
            marker.style.left = i + '%';
            
            const label = document.createElement('div');
            label.className = 'ir3-auto-leveling__timeline-label';
            label.textContent = i + '%';
            label.style.left = i + '%';
            
            debugElements.timelineMarkers.appendChild(marker);
            debugElements.timelineMarkers.appendChild(label);
          }
        }
        
        function setupMouseTracking() {
          if (!showDebugMarkers) return;
          
          videoContainer.addEventListener('mousemove', function(e) {
            const rect = videoContainer.getBoundingClientRect();
            const x = ((e.clientX - rect.left) / rect.width * 100).toFixed(1);
            const y = ((e.clientY - rect.top) / rect.height * 100).toFixed(1);
            
            // 更新鼠标跟踪线
            if (debugElements.mouseTrackerV) {
              debugElements.mouseTrackerV.style.left = x + '%';
            }
            if (debugElements.mouseTrackerH) {
              debugElements.mouseTrackerH.style.top = y + '%';
            }
            
            // 更新鼠标坐标显示
            const coordsText = `X: ${x}%, Y: ${y}%`;
            if (debugElements.mouseCoords) {
              debugElements.mouseCoords.textContent = coordsText;
              debugElements.mouseCoords.style.left = e.clientX - rect.left + 'px';
              debugElements.mouseCoords.style.top = e.clientY - rect.top + 'px';
            }
            
            // 更新调试面板
            if (debugElements.debugMouse) {
              debugElements.debugMouse.textContent = coordsText;
            }
          });
          
          // 鼠标离开时隐藏跟踪器
          videoContainer.addEventListener('mouseleave', function() {
            if (debugElements.mouseTrackerV) debugElements.mouseTrackerV.style.display = 'none';
            if (debugElements.mouseTrackerH) debugElements.mouseTrackerH.style.display = 'none';
            if (debugElements.mouseCoords) debugElements.mouseCoords.style.display = 'none';
          });
          
          videoContainer.addEventListener('mouseenter', function() {
            if (debugElements.mouseTrackerV) debugElements.mouseTrackerV.style.display = 'block';
            if (debugElements.mouseTrackerH) debugElements.mouseTrackerH.style.display = 'block';
            if (debugElements.mouseCoords) debugElements.mouseCoords.style.display = 'block';
          });
        }
        
        function updateContainerSize() {
          if (!debugElements.debugSize) return;
          
          const rect = videoContainer.getBoundingClientRect();
          debugElements.debugSize.textContent = `${Math.round(rect.width)}x${Math.round(rect.height)}`;
        }
        
        video.addEventListener('error', function(e) {
          console.error('视频加载失败', e);
          videoContainer.innerHTML = '<div style="padding: 20px; text-align: center; color: red;">视频加载失败，请检查URL是否正确</div>';
        });
        
        video.addEventListener('loadedmetadata', function() {
          setupScrollTrigger();
        });
        
        if (video.readyState >= 2) {
          setupScrollTrigger();
        }

        // 监听窗口大小变化，重新计算移动端状态
        let resizeTimeout;
        window.addEventListener('resize', function() {
          clearTimeout(resizeTimeout);
          resizeTimeout = setTimeout(function() {
            const newIsMobile = window.innerWidth <= 768;
            const currentVideoSrc = video.src;
            const expectedVideoSrc = newIsMobile ? mobileVideoSrc : desktopVideoSrc;

            // 如果设备类型改变，更新视频源
            if (currentVideoSrc !== expectedVideoSrc) {
              const currentTime = video.currentTime;
              video.src = expectedVideoSrc;
              video.currentTime = currentTime;
            }

            // 更新热点位置
            updateHotspotPositions();

            // 重新刷新ScrollTrigger以适应新的屏幕尺寸
            ScrollTrigger.refresh();
          }, 250);
        });
    
        function positionTextItem(textItem, hotspot) {
          const containerRect = videoContainer.getBoundingClientRect();
          const hotspotX = parseFloat(hotspot.dataset.x);
          const hotspotY = parseFloat(hotspot.dataset.y);
          const textWidth = 360;
          const textHeight = 150;
          const hotspotSize = {{ section.settings.hotspot_size }};
          
          const offsetPercentX = (hotspotSize / 2 + 20) / containerRect.width * 100;
          const offsetPercentY = (hotspotSize / 2 + 20) / containerRect.height * 100;
          
          let position = 'right';
          let textX = hotspotX;
          let textY = hotspotY;
          let transform = 'translate(0, -50%)';
          
          if (hotspotX + offsetPercentX + (textWidth / containerRect.width * 100) > 95) {
            position = 'left';
            textX = hotspotX - offsetPercentX - (textWidth / containerRect.width * 100);
            transform = 'translate(0, -50%)';
          } else {
            textX = hotspotX + offsetPercentX;
          }
          
          if (hotspotY > 75) {
            position = position === 'left' ? 'top-left' : 'top-right';
            textY = hotspotY - offsetPercentY - (textHeight / containerRect.height * 100);
            transform = position.includes('left') ? 'translate(0, 0)' : 'translate(0, 0)';
          } else if (hotspotY < 25) {
            position = position === 'left' ? 'bottom-left' : 'bottom-right';
            textY = hotspotY + offsetPercentY;
            transform = position.includes('left') ? 'translate(0, 0)' : 'translate(0, 0)';
          }
          
          textItem.style.left = textX + '%';
          textItem.style.top = textY + '%';
          textItem.style.transform = transform;
          textItem.setAttribute('data-position', position);
        }
        
        function setupScrollTrigger() {
          gsap.registerPlugin(ScrollTrigger);
    
          if ('{{ section.settings.video_fill_mode }}' === 'fullscreen') {
            document.body.style.paddingBottom = window.innerHeight + 'px';
          }
    
          if ('{{ section.settings.text_display_position }}' === 'next_to_hotspot') {
            textItems.forEach(function(textItem) {
              const hotspotId = textItem.dataset.hotspotId;
              const hotspot = document.getElementById('ir3-hotspot-' + sectionId + '-' + hotspotId);
              if (hotspot) {
                positionTextItem(textItem, hotspot);
              }
            });
          }
          
          const trigger = ScrollTrigger.create({
            trigger: section,
            start: 'top bottom',
            end: 'bottom top',
            scrub: {{ section.settings.scroll_smoothness }},
            markers: {{ section.settings.show_debug_markers }},
            onUpdate: function(self) {
              if (video.duration) {
                let progress = Math.max(0, Math.min(self.progress, 1));
                const videoProgress = progress * video.duration;
                video.currentTime = videoProgress;
                
                if (progressBar) {
                  progressBar.style.width = (progress * 100) + '%';
                }
                
                // 更新调试信息
                if (showDebugMarkers && debugElements.debugProgress) {
                  debugElements.debugProgress.textContent = (progress * 100).toFixed(1) + '%';
                  debugElements.debugTime.textContent = 
                    videoProgress.toFixed(2) + 's / ' + video.duration.toFixed(2) + 's';
                  debugElements.debugScroll.textContent = (self.progress * 100).toFixed(1) + '%';
                }
                
                // 处理视频标题显示
                if (showVideoTitle && mainTitleContainer) {
                  if (progress >= videoTitleStartTime && progress <= videoTitleEndTime) {
                    if (!mainTitleContainer.classList.contains('show')) {
                      mainTitleContainer.classList.remove('hide');
                      mainTitleContainer.classList.add('show');
                    }
                  } else {
                    if (mainTitleContainer.classList.contains('show')) {
                      mainTitleContainer.classList.remove('show');
                      mainTitleContainer.classList.add('hide');
                    }
                  }
                }
                
                // 更新活动高亮点信息
                let activeHotspots = [];
                
                if (hotspots.length > 0 && !isNavigationJump) {
                  hotspots.forEach(function(hotspot) {
                    const startTime = parseFloat(hotspot.dataset.startTime) / 100;
                    const endTime = parseFloat(hotspot.dataset.endTime) / 100;
                    const hotspotId = hotspot.dataset.hotspotId;

                    const textItem = document.getElementById('ir3-text-item-' + sectionId + '-' + hotspotId);

                    if (progress >= startTime && progress <= endTime) {
                      activeHotspots.push('#' + hotspotId);

                      if (!hotspot.classList.contains('active')) {
                        hotspot.classList.add('active');
                        gsap.to(hotspot, {
                          opacity: 1,
                          duration: 0.4,
                          ease: "power2.out"
                        });
                      }
                      
                      if (textItem && !textItem.classList.contains('active')) {
                        textItem.classList.add('active');
                        textItem.classList.remove('fade-out');
                        textItem.classList.add('fade-in');
                        if ('{{ section.settings.text_display_position }}' === 'next_to_hotspot') {
                          positionTextItem(textItem, hotspot);
                        }
                        gsap.to(textItem, { 
                          opacity: 1, 
                          duration: 0.5, 
                          ease: "power2.out" 
                        });
                      }
                    } else {
                      if (hotspot.classList.contains('active')) {
                        hotspot.classList.remove('active');
                        gsap.to(hotspot, { 
                          opacity: 0, 
                          duration: 0.3, 
                          ease: "power2.in" 
                        });
                      }
                      
                      if (textItem && textItem.classList.contains('active')) {
                        textItem.classList.remove('active');
                        textItem.classList.remove('fade-in');
                        textItem.classList.add('fade-out');
                        gsap.to(textItem, { 
                          opacity: 0, 
                          duration: 0.3, 
                          ease: "power2.in" 
                        });
                      }
                    }
                  });
                }
                
                // 更新活动高亮点显示
                if (showDebugMarkers && debugElements.debugActive) {
                  debugElements.debugActive.textContent = 
                    activeHotspots.length > 0 ? activeHotspots.join(', ') : '无';
                }
              }
            }
          });
          
          if (isLockScroll) {
            const viewportHeight = window.innerHeight;
            const sectionHeight = section.offsetHeight;

            // 移动端使用pinSpacing: true 来避免重叠问题
            const isMobile = window.innerWidth <= 768;

            const pinTrigger = ScrollTrigger.create({
              trigger: section,
              start: 'top top',
              end: () => `+=${sectionHeight - viewportHeight}`,
              pin: videoContainer,
              pinSpacing: isMobile ? true : false,
              anticipatePin: 1,
              markers: {{ section.settings.show_debug_markers }}
            });
          }
          
          let resizeTimer;
          window.addEventListener('resize', function() {
            clearTimeout(resizeTimer);
            resizeTimer = setTimeout(function() {
              ScrollTrigger.refresh(true);
              
              if (showDebugMarkers) {
                updateContainerSize();
              }
              
              if ('{{ section.settings.text_display_position }}' === 'next_to_hotspot') {
                textItems.forEach(function(textItem) {
                  if (textItem.classList.contains('active')) {
                    const hotspotId = textItem.dataset.hotspotId;
                    const hotspot = document.getElementById('ir3-hotspot-' + sectionId + '-' + hotspotId);
                    if (hotspot) {
                      positionTextItem(textItem, hotspot);
                    }
                  }
                });
              }
            }, 150);
          });
        }
      }
</script>

{% schema %}
{
  "name": "IR3 V2 智能调平动画",
  "settings": [
    {
      "type": "header",
      "content": "视频设置"
    },
    {
      "type": "text",
      "id": "video_url",
      "label": "视频URL (桌面端)",
      "default": "https://cdn.shopify.com/videos/c/o/v/9910e33e66834553a74d1f72c12e3156.mp4",
      "info": "输入MP4格式视频的URL。建议视频长度不超过30秒，分辨率为1280x720。"
    },
    {
      "type": "text",
      "id": "mobile_video_url",
      "label": "视频URL (移动端)",
      "default": "https://cdn.shopify.com/videos/c/o/v/9763914c5c314974a7179de15511fbe5.mp4",
      "info": "移动端专用的竖屏视频URL。建议分辨率为720x1280或9:16比例。"
    },
    {
      "type": "checkbox",
      "id": "full_width",
      "label": "全宽显示",
      "default": true,
      "info": "是否将视频容器拉伸至100%宽度"
    },
    {
      "type": "range",
      "id": "container_width",
      "min": 500,
      "max": 1600,
      "step": 50,
      "default": 1200,
      "label": "容器宽度",
      "unit": "px",
      "info": "当不是全宽显示时的容器宽度"
    },
    {
      "type": "select",
      "id": "video_fill_mode",
      "label": "视频填充方式",
      "options": [
        {
          "value": "contain",
          "label": "包含 (保持纵横比)"
        },
        {
          "value": "cover",
          "label": "覆盖 (可能裁剪)"
        },
        {
          "value": "fill",
          "label": "填充 (可能变形)"
        },
        {
          "value": "fullscreen",
          "label": "全屏 (占满整个屏幕)"
        }
      ],
      "default": "contain"
    },
    {
      "type": "checkbox",
      "id": "loop_video",
      "label": "循环播放视频",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "use_viewport_height",
      "label": "使用视口高度 (100vh)",
      "default": true,
      "info": "启用后视频将占满整个屏幕高度，禁用后使用下方的固定高度设置"
    },
    {
      "type": "range",
      "id": "section_height_desktop",
      "min": 400,
      "max": 1200,
      "step": 50,
      "default": 600,
      "label": "部分高度 (桌面)",
      "unit": "px"
    },
    {
      "type": "range",
      "id": "section_height_mobile",
      "min": 250,
      "max": 800,
      "step": 50,
      "default": 400,
      "label": "部分高度 (移动)",
      "unit": "px"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "背景颜色",
      "default": "#0a0a0a"
    },
    {
      "type": "header",
      "content": "视频标题设置"
    },
    {
      "type": "checkbox",
      "id": "show_video_title",
      "label": "显示视频标题",
      "default": true
    },
    {
      "type": "text",
      "id": "video_title",
      "label": "视频标题",
      "default": "IR3 智能调平系统"
    },
    {
      "type": "textarea",
      "id": "video_description",
      "label": "视频描述",
      "default": "6点精准检测，0.01mm超高精度，让每一次打印都完美无瑕"
    },
    {
      "type": "range",
      "id": "video_title_size",
      "min": 24,
      "max": 72,
      "step": 2,
      "default": 48,
      "label": "标题字体大小",
      "unit": "px"
    },
    {
      "type": "range",
      "id": "video_description_size",
      "min": 14,
      "max": 28,
      "step": 1,
      "default": 20,
      "label": "描述字体大小",
      "unit": "px"
    },
    {
      "type": "color",
      "id": "video_title_color",
      "label": "标题颜色",
      "default": "#FFFFFF"
    },
    {
      "type": "color",
      "id": "video_description_color",
      "label": "描述颜色",
      "default": "rgba(255, 255, 255, 0.85)"
    },
    {
      "type": "range",
      "id": "video_description_weight",
      "min": 300,
      "max": 700,
      "step": 100,
      "default": 400,
      "label": "描述字重"
    },
    {
      "type": "select",
      "id": "video_description_font_family",
      "label": "描述字体",
      "default": "'Open Sans', sans-serif",
      "options": [
        {
          "value": "'Helvetica Neue', Helvetica, Arial, sans-serif",
          "label": "Helvetica Neue (系统字体)"
        },
        {
          "value": "Georgia, 'Times New Roman', serif",
          "label": "Georgia (系统字体)"
        },
        {
          "value": "'Open Sans', sans-serif",
          "label": "Open Sans (自定义字体)"
        },
        {
          "value": "'Montserrat', sans-serif",
          "label": "Montserrat (自定义字体)"
        },
        {
          "value": "var(--typeHeaderPrimary),var(--typeHeaderFallback)",
          "label": "商店主题标题字体"
        },
        {
          "value": "var(--typeBodyPrimary),var(--typeBodyFallback)",
          "label": "商店主题正文字体"
        }
      ]
    },
    {
      "type": "select",
      "id": "video_description_font_style",
      "label": "描述字体样式",
      "default": "normal",
      "options": [
        {
          "value": "normal",
          "label": "正常"
        },
        {
          "value": "italic",
          "label": "斜体"
        }
      ]
    },
    {
      "type": "range",
      "id": "video_title_weight",
      "min": 400,
      "max": 900,
      "step": 100,
      "default": 700,
      "label": "标题字重"
    },
    {
      "type": "range",
      "id": "video_title_letter_spacing",
      "min": -0.1,
      "max": 0.2,
      "step": 0.1,
      "default": 0.0,
      "label": "标题字间距",
      "unit": "em"
    },
    {
      "type": "checkbox",
      "id": "video_title_uppercase",
      "label": "标题大写",
      "default": false
    },
    {
      "type": "select",
      "id": "video_title_font_family",
      "label": "标题字体",
      "default": "'Montserrat', sans-serif",
      "options": [
        {
          "value": "'Helvetica Neue', Helvetica, Arial, sans-serif",
          "label": "Helvetica Neue (系统字体)"
        },
        {
          "value": "Georgia, 'Times New Roman', serif",
          "label": "Georgia (系统字体)"
        },
        {
          "value": "'Open Sans', sans-serif",
          "label": "Open Sans (自定义字体)"
        },
        {
          "value": "'Montserrat', sans-serif",
          "label": "Montserrat (自定义字体)"
        },
        {
          "value": "var(--typeHeaderPrimary),var(--typeHeaderFallback)",
          "label": "商店主题标题字体"
        },
        {
          "value": "var(--typeBodyPrimary),var(--typeBodyFallback)",
          "label": "商店主题正文字体"
        }
      ]
    },
    {
      "type": "select",
      "id": "video_title_font_style",
      "label": "标题字体样式",
      "default": "normal",
      "options": [
        {
          "value": "normal",
          "label": "正常"
        },
        {
          "value": "italic",
          "label": "斜体"
        }
      ]
    },
    {
      "type": "range",
      "id": "video_title_start_time",
      "min": 0,
      "max": 100,
      "step": 1,
      "default": 0,
      "label": "标题开始显示时间",
      "unit": "%",
      "info": "基于视频进度的百分比"
    },
    {
      "type": "range",
      "id": "video_title_end_time",
      "min": 0,
      "max": 100,
      "step": 1,
      "default": 15,
      "label": "标题结束显示时间",
      "unit": "%",
      "info": "基于视频进度的百分比"
    },
    {
      "type": "header",
      "content": "高亮点样式"
    },
    {
      "type": "checkbox",
      "id": "show_hotspots",
      "label": "显示高亮点",
      "default": true
    },
    {
      "type": "range",
      "id": "hotspot_size",
      "min": 20,
      "max": 40,
      "step": 2,
      "default": 28,
      "label": "高亮点大小",
      "unit": "px"
    },
    {
      "type": "color",
      "id": "hotspot_bg_color",
      "label": "高亮点背景色",
      "default": "rgba(255, 255, 255, 0.15)"
    },
    {
      "type": "color",
      "id": "hotspot_border_color",
      "label": "高亮点边框色",
      "default": "#FFFFFF"
    },
    {
      "type": "color",
      "id": "hotspot_center_color",
      "label": "高亮点中心色",
      "default": "#FFFFFF"
    },
    {
      "type": "color",
      "id": "hotspot_glow_color",
      "label": "高亮点发光色",
      "default": "#FFFFFF"
    },
    {
      "type": "color",
      "id": "hotspot_hover_color",
      "label": "高亮点悬停色",
      "default": "#F0F0F0"
    },
    {
      "type": "header",
      "content": "文本显示设置"
    },
    {
      "type": "select",
      "id": "text_display_position",
      "label": "文本显示位置",
      "options": [
        {
          "value": "inside_bottom",
          "label": "视频内部底部"
        },
        {
          "value": "outside_bottom",
          "label": "视频外部底部"
        },
        {
          "value": "next_to_hotspot",
          "label": "高亮点旁边"
        }
      ],
      "default": "next_to_hotspot"
    },
    {
      "type": "checkbox",
      "id": "show_step_numbers",
      "label": "显示步骤编号",
      "default": true
    },
    {
      "type": "header",
      "content": "文本样式"
    },
    {
      "type": "range",
      "id": "title_size",
      "min": 16,
      "max": 36,
      "step": 1,
      "default": 24,
      "label": "标题字体大小",
      "unit": "px"
    },
    {
      "type": "range",
      "id": "description_size",
      "min": 12,
      "max": 24,
      "step": 1,
      "default": 16,
      "label": "描述字体大小",
      "unit": "px"
    },
    {
      "type": "color",
      "id": "title_color",
      "label": "标题颜色",
      "default": "#1a1a1a"
    },
    {
      "type": "color",
      "id": "title_hover_color",
      "label": "标题悬停颜色",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "description_color",
      "label": "描述颜色",
      "default": "#4a5568"
    },
    {
      "type": "color",
      "id": "description_hover_color",
      "label": "描述悬停颜色",
      "default": "#2d3748"
    },
    {
      "type": "color",
      "id": "text_bg_gradient_start",
      "label": "文本背景渐变起始色",
      "default": "rgba(255, 255, 255, 0.98)"
    },
    {
      "type": "color",
      "id": "text_bg_gradient_end",
      "label": "文本背景渐变结束色",
      "default": "rgba(249, 250, 251, 0.95)"
    },
    {
      "type": "range",
      "id": "text_bg_opacity",
      "min": 0.5,
      "max": 1.0,
      "step": 0.1,
      "default": 0.9,
      "label": "文本背景透明度"
    },
    {
      "type": "range",
      "id": "text_backdrop_blur",
      "min": 0,
      "max": 30,
      "step": 2,
      "default": 20,
      "label": "背景模糊度",
      "unit": "px"
    },
    {
      "type": "color",
      "id": "text_accent_color",
      "label": "文本强调色",
      "default": "#FFFFFF"
    },
    {
      "type": "range",
      "id": "text_border_radius",
      "min": 8,
      "max": 24,
      "step": 2,
      "default": 16,
      "label": "文本框圆角",
      "unit": "px"
    },
    {
      "type": "header",
      "content": "进度条设置"
    },
    {
      "type": "checkbox",
      "id": "show_progress",
      "label": "显示进度条",
      "default": false
    },
    {
      "type": "range",
      "id": "progress_height",
      "min": 2,
      "max": 8,
      "step": 1,
      "default": 4,
      "label": "进度条高度",
      "unit": "px"
    },
    {
      "type": "color",
      "id": "progress_gradient_start",
      "label": "进度条渐变起始色",
      "default": "#FFFFFF"
    },
    {
      "type": "color",
      "id": "progress_gradient_end",
      "label": "进度条渐变结束色",
      "default": "#F0F0F0"
    },
    {
      "type": "paragraph",
      "content": "每个调平点都有独立的启用开关和文字显示开关，可以单独控制每个点的显示状态和文字信息的显示"
    },
    {
      "type": "header",
      "content": "调平点 1 - 传送带前端"
    },
    {
      "type": "checkbox",
      "id": "hotspot_1_enabled",
      "label": "启用调平点 1",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "hotspot_1_show_text",
      "label": "显示调平点 1 文字",
      "default": true
    },
    {
      "type": "range",
      "id": "hotspot_1_x",
      "min": 0,
      "max": 100,
      "step": 1,
      "default": 25,
      "label": "X位置",
      "unit": "%"
    },
    {
      "type": "range",
      "id": "hotspot_1_y",
      "min": 0,
      "max": 100,
      "step": 1,
      "default": 40,
      "label": "Y位置 (桌面端)",
      "unit": "%"
    },
    {
      "type": "range",
      "id": "hotspot_1_mobile_x",
      "min": 0,
      "max": 100,
      "step": 1,
      "default": 50,
      "label": "X位置 (移动端)",
      "unit": "%"
    },
    {
      "type": "range",
      "id": "hotspot_1_mobile_y",
      "min": 0,
      "max": 100,
      "step": 1,
      "default": 30,
      "label": "Y位置 (移动端)",
      "unit": "%"
    },
    {
      "type": "range",
      "id": "hotspot_1_start_time",
      "min": 0,
      "max": 100,
      "step": 1,
      "default": 5,
      "label": "开始显示时间",
      "unit": "%"
    },
    {
      "type": "range",
      "id": "hotspot_1_end_time",
      "min": 0,
      "max": 100,
      "step": 1,
      "default": 20,
      "label": "结束显示时间",
      "unit": "%"
    },
    {
      "type": "text",
      "id": "hotspot_1_title",
      "label": "标题",
      "default": "初始位置检测"
    },
    {
      "type": "textarea",
      "id": "hotspot_1_description",
      "label": "描述",
      "default": "打印头移动至传送带前端，准备开始智能调平程序"
    },
    {
      "type": "header",
      "content": "调平点 2 - 第一次接触"
    },
    {
      "type": "checkbox",
      "id": "hotspot_2_enabled",
      "label": "启用调平点 2",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "hotspot_2_show_text",
      "label": "显示调平点 2 文字",
      "default": true
    },
    {
      "type": "range",
      "id": "hotspot_2_x",
      "min": 0,
      "max": 100,
      "step": 1,
      "default": 35,
      "label": "X位置",
      "unit": "%"
    },
    {
      "type": "range",
      "id": "hotspot_2_y",
      "min": 0,
      "max": 100,
      "step": 1,
      "default": 45,
      "label": "Y位置 (桌面端)",
      "unit": "%"
    },
    {
      "type": "range",
      "id": "hotspot_2_mobile_x",
      "min": 0,
      "max": 100,
      "step": 1,
      "default": 50,
      "label": "X位置 (移动端)",
      "unit": "%"
    },
    {
      "type": "range",
      "id": "hotspot_2_mobile_y",
      "min": 0,
      "max": 100,
      "step": 1,
      "default": 35,
      "label": "Y位置 (移动端)",
      "unit": "%"
    },
    {
      "type": "range",
      "id": "hotspot_2_start_time",
      "min": 0,
      "max": 100,
      "step": 1,
      "default": 20,
      "label": "开始显示时间",
      "unit": "%"
    },
    {
      "type": "range",
      "id": "hotspot_2_end_time",
      "min": 0,
      "max": 100,
      "step": 1,
      "default": 35,
      "label": "结束显示时间",
      "unit": "%"
    },
    {
      "type": "text",
      "id": "hotspot_2_title",
      "label": "标题",
      "default": "精准探测接触"
    },
    {
      "type": "textarea",
      "id": "hotspot_2_description",
      "label": "描述",
      "default": "打印头缓慢下降，探测传送带表面的精确高度位置"
    },
    {
      "type": "header",
      "content": "调平点 3 - Y轴偏移测量"
    },
    {
      "type": "checkbox",
      "id": "hotspot_3_enabled",
      "label": "启用调平点 3",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "hotspot_3_show_text",
      "label": "显示调平点 3 文字",
      "default": true
    },
    {
      "type": "range",
      "id": "hotspot_3_x",
      "min": 0,
      "max": 100,
      "step": 1,
      "default": 50,
      "label": "X位置",
      "unit": "%"
    },
    {
      "type": "range",
      "id": "hotspot_3_y",
      "min": 0,
      "max": 100,
      "step": 1,
      "default": 50,
      "label": "Y位置 (桌面端)",
      "unit": "%"
    },
    {
      "type": "range",
      "id": "hotspot_3_mobile_x",
      "min": 0,
      "max": 100,
      "step": 1,
      "default": 50,
      "label": "X位置 (移动端)",
      "unit": "%"
    },
    {
      "type": "range",
      "id": "hotspot_3_mobile_y",
      "min": 0,
      "max": 100,
      "step": 1,
      "default": 40,
      "label": "Y位置 (移动端)",
      "unit": "%"
    },
    {
      "type": "range",
      "id": "hotspot_3_start_time",
      "min": 0,
      "max": 100,
      "step": 1,
      "default": 35,
      "label": "开始显示时间",
      "unit": "%"
    },
    {
      "type": "range",
      "id": "hotspot_3_end_time",
      "min": 0,
      "max": 100,
      "step": 1,
      "default": 50,
      "label": "结束显示时间",
      "unit": "%"
    },
    {
      "type": "text",
      "id": "hotspot_3_title",
      "label": "标题",
      "default": "Y轴偏移计算"
    },
    {
      "type": "textarea",
      "id": "hotspot_3_description",
      "label": "描述",
      "default": "系统记录并计算Y轴的偏移量，准备进行补偿调整"
    },
    {
      "type": "header",
      "content": "调平点 4 - 多点验证"
    },
    {
      "type": "checkbox",
      "id": "hotspot_4_enabled",
      "label": "启用调平点 4",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "hotspot_4_show_text",
      "label": "显示调平点 4 文字",
      "default": true
    },
    {
      "type": "range",
      "id": "hotspot_4_x",
      "min": 0,
      "max": 100,
      "step": 1,
      "default": 65,
      "label": "X位置",
      "unit": "%"
    },
    {
      "type": "range",
      "id": "hotspot_4_y",
      "min": 0,
      "max": 100,
      "step": 1,
      "default": 45,
      "label": "Y位置 (桌面端)",
      "unit": "%"
    },
    {
      "type": "range",
      "id": "hotspot_4_mobile_x",
      "min": 0,
      "max": 100,
      "step": 1,
      "default": 50,
      "label": "X位置 (移动端)",
      "unit": "%"
    },
    {
      "type": "range",
      "id": "hotspot_4_mobile_y",
      "min": 0,
      "max": 100,
      "step": 1,
      "default": 45,
      "label": "Y位置 (移动端)",
      "unit": "%"
    },
    {
      "type": "range",
      "id": "hotspot_4_start_time",
      "min": 0,
      "max": 100,
      "step": 1,
      "default": 50,
      "label": "开始显示时间",
      "unit": "%"
    },
    {
      "type": "range",
      "id": "hotspot_4_end_time",
      "min": 0,
      "max": 100,
      "step": 1,
      "default": 65,
      "label": "结束显示时间",
      "unit": "%"
    },
    {
      "type": "text",
      "id": "hotspot_4_title",
      "label": "标题",
      "default": "多点精度验证"
    },
    {
      "type": "textarea",
      "id": "hotspot_4_description",
      "label": "描述",
      "default": "沿传送带进行多次接触测试，确保调平精度达到0.01mm"
    },
    {
      "type": "header",
      "content": "调平点 5 - 实时补偿"
    },
    {
      "type": "checkbox",
      "id": "hotspot_5_enabled",
      "label": "启用调平点 5",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "hotspot_5_show_text",
      "label": "显示调平点 5 文字",
      "default": true
    },
    {
      "type": "range",
      "id": "hotspot_5_x",
      "min": 0,
      "max": 100,
      "step": 1,
      "default": 75,
      "label": "X位置",
      "unit": "%"
    },
    {
      "type": "range",
      "id": "hotspot_5_y",
      "min": 0,
      "max": 100,
      "step": 1,
      "default": 50,
      "label": "Y位置 (桌面端)",
      "unit": "%"
    },
    {
      "type": "range",
      "id": "hotspot_5_mobile_x",
      "min": 0,
      "max": 100,
      "step": 1,
      "default": 50,
      "label": "X位置 (移动端)",
      "unit": "%"
    },
    {
      "type": "range",
      "id": "hotspot_5_mobile_y",
      "min": 0,
      "max": 100,
      "step": 1,
      "default": 50,
      "label": "Y位置 (移动端)",
      "unit": "%"
    },
    {
      "type": "range",
      "id": "hotspot_5_start_time",
      "min": 0,
      "max": 100,
      "step": 1,
      "default": 65,
      "label": "开始显示时间",
      "unit": "%"
    },
    {
      "type": "range",
      "id": "hotspot_5_end_time",
      "min": 0,
      "max": 100,
      "step": 1,
      "default": 80,
      "label": "结束显示时间",
      "unit": "%"
    },
    {
      "type": "text",
      "id": "hotspot_5_title",
      "label": "标题",
      "default": "智能补偿应用"
    },
    {
      "type": "textarea",
      "id": "hotspot_5_description",
      "label": "描述",
      "default": "系统自动应用Y轴补偿值，实现完美的打印平面"
    },
    {
      "type": "header",
      "content": "调平点 6 - 完成确认"
    },
    {
      "type": "checkbox",
      "id": "hotspot_6_enabled",
      "label": "启用调平点 6",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "hotspot_6_show_text",
      "label": "显示调平点 6 文字",
      "default": true
    },
    {
      "type": "range",
      "id": "hotspot_6_x",
      "min": 0,
      "max": 100,
      "step": 1,
      "default": 85,
      "label": "X位置",
      "unit": "%"
    },
    {
      "type": "range",
      "id": "hotspot_6_y",
      "min": 0,
      "max": 100,
      "step": 1,
      "default": 45,
      "label": "Y位置 (桌面端)",
      "unit": "%"
    },
    {
      "type": "range",
      "id": "hotspot_6_mobile_x",
      "min": 0,
      "max": 100,
      "step": 1,
      "default": 50,
      "label": "X位置 (移动端)",
      "unit": "%"
    },
    {
      "type": "range",
      "id": "hotspot_6_mobile_y",
      "min": 0,
      "max": 100,
      "step": 1,
      "default": 55,
      "label": "Y位置 (移动端)",
      "unit": "%"
    },
    {
      "type": "range",
      "id": "hotspot_6_start_time",
      "min": 0,
      "max": 100,
      "step": 1,
      "default": 80,
      "label": "开始显示时间",
      "unit": "%"
    },
    {
      "type": "range",
      "id": "hotspot_6_end_time",
      "min": 0,
      "max": 100,
      "step": 1,
      "default": 95,
      "label": "结束显示时间",
      "unit": "%"
    },
    {
      "type": "text",
      "id": "hotspot_6_title",
      "label": "标题",
      "default": "调平完成确认"
    },
    {
      "type": "textarea",
      "id": "hotspot_6_description",
      "label": "描述",
      "default": "6点智能调平完成，系统已准备进行高精度45°角打印"
    },
    {
      "type": "header",
      "content": "滚动控制"
    },
    {
      "type": "checkbox",
      "id": "lock_scroll",
      "label": "锁定滚动",
      "default": false,
      "info": "锁定页面滚动直到动画完成"
    },
    {
      "type": "range",
      "id": "scroll_smoothness",
      "min": 0,
      "max": 3,
      "step": 0.1,
      "default": 0.5,
      "label": "滚动平滑度",
      "info": "值越大，动画越平滑"
    },
    {
      "type": "checkbox",
      "id": "show_debug_markers",
      "label": "显示调试标记",
      "default": false,
      "info": "显示坐标网格、时间轴标记和调试信息面板"
    },
    {
      "type": "range",
      "id": "bottom_spacing",
      "min": 0,
      "max": 100,
      "step": 10,
      "default": 30,
      "label": "底部间距",
      "unit": "px"
    }
  ],
  "presets": [
    {
      "name": "IR3 V2 智能调平动画",
      "category": "视频"
    }
  ]
}
{% endschema %}
