{% comment %}
  Page Navigation Component
  Fixed left-side navigation with scroll monitoring and smooth scrolling
{% endcomment %}

<div class="page-navigation" id="page-navigation">
  <!-- Mobile Navigation Trigger Button -->
  <button class="mobile-nav-trigger" id="mobile-nav-trigger" aria-label="Open Navigation Menu">
    <div class="trigger-progress" id="trigger-progress"></div>
    <div class="trigger-icon">
      <div class="trigger-dot"></div>
      <div class="trigger-dot"></div>
      <div class="trigger-dot"></div>
    </div>
  </button>

  <!-- Mobile Navigation Overlay -->
  <div class="mobile-nav-overlay" id="mobile-nav-overlay"></div>

  <nav class="page-nav-container" id="page-nav-container">
    <!-- Mobile Close Button -->
    <button class="mobile-nav-close" id="mobile-nav-close" aria-label="Close Navigation Menu">
      <span class="close-icon">×</span>
    </button>

    <div class="page-nav-header">
      <h3 class="page-nav-title">{{ section.settings.nav_title | default: 'SECTIONS' }}</h3>
    </div>

    <ul class="page-nav-list" id="nav-list">
      <!-- Navigation items will be dynamically generated based on page sections -->
    </ul>

    {% if section.settings.show_progress %}
      <div class="page-nav-progress">
        <div class="progress-bar" id="progress-bar"></div>
      </div>
    {% endif %}
  </nav>
</div>

<!-- Scroll Lock Tooltip -->
<div class="scroll-lock-tooltip" id="scroll-lock-tooltip">
  <div class="tooltip-content">
    <div class="tooltip-icon" id="tooltip-icon">🔒</div>
    <div class="tooltip-text">
      <div class="tooltip-title" id="tooltip-title">Interactive Section</div>
      <div class="tooltip-description" id="tooltip-description">
        This section requires interaction to unlock. Scroll through all content to continue.
      </div>
    </div>
    <div class="tooltip-arrow"></div>
  </div>
</div>

<style>
  .page-navigation {
    position: fixed;
    {% if section.settings.position == 'right' %}
      right: 2rem;
    {% else %}
      left: 2rem;
    {% endif %}
    top: 50%;
    transform: translateY(-50%);
    z-index: 1000;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    opacity: 0;
    transition: opacity 0.5s ease;
  }

  .page-nav-container {
    background: rgba(10, 10, 10, 0.85) !important;
    backdrop-filter: blur(15px);
    border-radius: 12px;
    padding: 1rem 0.75rem;
    box-shadow:
      0 4px 20px rgba(0, 0, 0, 0.3),
      0 1px 3px rgba(0, 0, 0, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    min-width: 160px;
    max-width: 200px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
  }

  .page-nav-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(0, 255, 136, 0.3), transparent);
  }

  .page-nav-container:hover {
    transform: translateY(-1px);
    box-shadow:
      0 8px 30px rgba(0, 0, 0, 0.4),
      0 2px 8px rgba(0, 0, 0, 0.3),
      inset 0 1px 0 rgba(0, 255, 136, 0.2);
    border-color: rgba(0, 255, 136, 0.2);
  }

  .page-nav-header {
    margin-bottom: 0.75rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  .page-nav-title {
    font-size: 0.75rem;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.6);
    margin: 0;
    text-transform: uppercase;
    letter-spacing: 1px;
    text-align: center;
  }

  .page-nav-list {
    list-style: none;
    margin: 0;
    padding: 0;
  }

  .page-nav-item {
    margin-bottom: 0.5rem;
  }

  .page-nav-link {
    display: block;
    padding: 0.5rem 0.75rem;
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    border-radius: 6px;
    font-size: 0.75rem;
    font-weight: 400;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    line-height: 1.3;
    letter-spacing: 0.02em;
    text-align: center;
  }

  .page-nav-link::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    width: 2px;
    height: 0;
    background: linear-gradient(135deg, #00ff88 0%, #0099ff 100%);
    transform: translateY(-50%);
    transition: height 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 1px;
  }

  .page-nav-link::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 255, 136, 0.1);
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 6px;
  }

  .page-nav-link:hover {
    color: rgba(255, 255, 255, 0.9);
    background: rgba(0, 255, 136, 0.15);
  }

  .page-nav-link:hover::after {
    opacity: 1;
  }

  .page-nav-link:hover::before {
    height: 60%;
  }

  .page-nav-link.active {
    background: rgba(0, 255, 136, 0.2);
    color: #00ff88;
    font-weight: 500;
    box-shadow:
      0 2px 8px rgba(0, 255, 136, 0.3),
      inset 0 1px 0 rgba(0, 255, 136, 0.2);
  }

  .page-nav-link.active::before {
    height: 80%;
  }

  .page-nav-link.active::after {
    opacity: 1;
  }



  /* Scroll Lock Tooltip */
  .scroll-lock-tooltip {
    position: fixed;
    z-index: 1002;
    background: rgba(20, 20, 20, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 12px;
    padding: 1rem;
    max-width: 280px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(0, 255, 136, 0.3);
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.4),
      0 2px 8px rgba(0, 0, 0, 0.2),
      inset 0 1px 0 rgba(0, 255, 136, 0.1);
  }

  .scroll-lock-tooltip.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }

  .tooltip-content {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .tooltip-icon {
    font-size: 1.25rem;
    flex-shrink: 0;
    margin-top: 0.125rem;
  }

  .tooltip-text {
    flex: 1;
  }

  .tooltip-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: #ffaa00;
    margin-bottom: 0.25rem;
    line-height: 1.3;
  }

  .tooltip-description {
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.4;
  }

  .tooltip-arrow {
    position: absolute;
    width: 8px;
    height: 8px;
    background: rgba(20, 20, 20, 0.95);
    border: 1px solid rgba(0, 255, 136, 0.3);
    border-top: none;
    border-left: none;
    transform: rotate(45deg);
  }

  .tooltip-arrow.left {
    left: -4px;
    top: 50%;
    transform: translateY(-50%) rotate(135deg);
  }

  .tooltip-arrow.right {
    right: -4px;
    top: 50%;
    transform: translateY(-50%) rotate(-45deg);
  }

  .tooltip-arrow.bottom {
    bottom: -4px;
    left: 50%;
    transform: translateX(-50%) rotate(45deg);
  }

  .page-nav-progress {
    margin-top: 0.75rem;
    padding-top: 0.75rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
  }

  .progress-bar {
    height: 2px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 1px;
    overflow: hidden;
    position: relative;
  }

  .progress-bar::after {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    background: linear-gradient(90deg, #00ff88 0%, #0099ff 100%);
    border-radius: 1px;
    transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    width: var(--progress, 0%);
    box-shadow: 0 0 8px rgba(0, 255, 136, 0.5);
  }

  .progress-bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(0, 255, 136, 0.4), transparent);
    animation: shimmer 3s infinite;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .page-nav-container:hover .progress-bar::before {
    opacity: 1;
  }

  @keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
  }

  /* Responsive Design */
  @media (max-width: 1200px) {
    .page-navigation {
      {% if section.settings.position == 'right' %}
        right: 1.5rem;
      {% else %}
        left: 1.5rem;
      {% endif %}
    }
  }

  /* Mobile trigger button */
  .mobile-nav-trigger {
    display: none;
    position: fixed;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    width: 48px;
    height: 48px;
    background: rgba(10, 10, 10, 0.9);
    backdrop-filter: blur(15px);
    border-radius: 50%;
    border: 2px solid rgba(0, 255, 136, 0.6);
    cursor: pointer;
    z-index: 1001;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  }

  /* Hide trigger button when mobile nav is active */
  .mobile-nav-trigger.nav-open {
    opacity: 0;
    pointer-events: none;
    transform: translateY(-50%) scale(0.8);
  }

  .mobile-nav-trigger:hover {
    transform: translateY(-50%) scale(1.05);
    border-color: rgba(0, 255, 136, 0.8);
    box-shadow: 0 4px 20px rgba(0, 255, 136, 0.2);
  }

  .trigger-progress {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: conic-gradient(from 0deg, rgba(0, 255, 136, 0.3) 0%, rgba(0, 255, 136, 0.3) var(--progress, 0%), transparent var(--progress, 0%));
    border-radius: 50%;
    transition: all 0.3s ease;
  }

  .trigger-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    gap: 3px;
    z-index: 1;
  }

  .trigger-dot {
    width: 4px;
    height: 4px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    transition: all 0.3s ease;
  }

  .mobile-nav-trigger:hover .trigger-dot {
    background: #00ff88;
  }

  /* Mobile overlay */
  .mobile-nav-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.4);
    z-index: 1000;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
  }

  .mobile-nav-overlay.active {
    opacity: 1;
    pointer-events: all;
  }

  /* Mobile close button */
  .mobile-nav-close {
    display: none;
    position: absolute;
    top: 0.75rem;
    right: 0.75rem;
    width: 24px;
    height: 24px;
    background: none;
    border: none;
    cursor: pointer;
    color: rgba(255, 255, 255, 0.6);
    font-size: 18px;
    line-height: 1;
    transition: color 0.3s ease;
  }

  .mobile-nav-close:hover {
    color: #00ff88;
  }

  .close-icon {
    display: block;
    transform: rotate(0deg);
    transition: transform 0.3s ease;
  }

  .mobile-nav-close:hover .close-icon {
    transform: rotate(90deg);
  }

  @media (max-width: 1024px) {
    .page-navigation {
      {% if section.settings.position == 'right' %}
        right: 1rem;
      {% else %}
        left: 1rem;
      {% endif %}
    }

    .page-nav-container {
      min-width: 180px;
      max-width: 200px;
      padding: 1.25rem 0.875rem;
      border-radius: 16px;
    }

    .page-nav-link {
      padding: 0.75rem 1rem;
      font-size: 0.8125rem;
    }
  }

  @media (max-width: 768px) {
    .page-navigation {
      position: fixed;
      left: 0;
      top: 0;
      bottom: 0;
      transform: none;
      width: 100%;
      max-width: none;
      z-index: 1001;
      pointer-events: none;
    }

    .mobile-nav-trigger {
      display: block !important;
      pointer-events: all !important;
      opacity: 1 !important;
      visibility: visible !important;
    }

    .mobile-nav-close {
      display: block;
    }

    .mobile-nav-overlay {
      display: block;
    }

    .page-nav-container {
      position: fixed;
      left: -280px;
      top: 50%;
      transform: translateY(-50%);
      width: 260px;
      height: auto;
      max-height: 80vh;
      padding: 1.5rem 1rem;
      border-radius: 0 16px 16px 0;
      background: rgba(10, 10, 10, 0.95) !important;
      backdrop-filter: blur(20px);
      box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.4),
        0 4px 16px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.1) !important;
      border-left: none;
      transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      overflow-y: auto;
      pointer-events: all;
      opacity: 0;
      z-index: 1002;
    }

    .page-nav-container.active {
      left: 0;
      opacity: 1;
    }

    .mobile-nav-close {
      display: block;
    }

    .page-nav-header {
      margin-bottom: 1.5rem;
      padding-bottom: 1rem;
      border-bottom: 1px solid rgba(0, 255, 136, 0.2);
      position: relative;
    }

    .page-nav-title {
      font-size: 0.875rem;
      text-align: center;
      color: rgba(255, 255, 255, 0.8);
    }

    .page-nav-list {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;
    }

    .page-nav-item {
      margin-bottom: 0;
    }

    .page-nav-link {
      text-align: left;
      padding: 0.75rem 1rem;
      border-radius: 8px;
      font-size: 0.875rem;
      font-weight: 400;
      color: rgba(255, 255, 255, 0.8);
      background: transparent;
      border: none;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      overflow: hidden;
      line-height: 1.4;
      letter-spacing: 0.02em;
    }

    .page-nav-link:hover {
      color: rgba(255, 255, 255, 0.95);
      background: rgba(0, 255, 136, 0.1);
      transform: translateX(4px);
    }

    .page-nav-link.active {
      background: rgba(0, 255, 136, 0.2);
      color: #00ff88;
      font-weight: 500;
      transform: translateX(4px);
      box-shadow:
        0 2px 8px rgba(0, 255, 136, 0.3),
        inset 0 1px 0 rgba(0, 255, 136, 0.2);
    }

    .mobile-nav-overlay {
      display: block;
    }

    .mobile-nav-overlay.active {
      pointer-events: all;
    }

    .page-nav-progress {
      margin-top: 1rem;
      padding-top: 1rem;
      border-top: 1px solid rgba(255, 255, 255, 0.1);
    }

    .scroll-lock-tooltip {
      max-width: calc(100vw - 2rem);
      font-size: 0.875rem;
    }

    .tooltip-title {
      font-size: 1rem;
    }

    .tooltip-description {
      font-size: 0.875rem;
    }
  }



  /* Force dark theme to match page design */

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    .page-nav-container,
    .page-nav-link,
    .page-nav-link::before,
    .page-nav-link::after,
    .progress-bar::after {
      transition: none;
    }

    .page-nav-container:hover {
      transform: none;
    }

    .page-nav-link:hover,
    .page-nav-link.active {
      transform: none;
    }

    @keyframes shimmer {
      0%, 100% { transform: translateX(0); }
    }
  }
</style>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const pageNavigation = new PageNavigation();
    pageNavigation.init();

    // Make navigation available globally for debugging
    window.pageNavigation = pageNavigation;
    window.debugNavigation = function() {
      console.log('🔍 Navigation Debug Info:');
      console.log('- Sections found:', pageNavigation.sections.length);
      console.log('- Sections:', pageNavigation.sections.map(s => s.name));
      console.log('- Scroll lock active:', pageNavigation.isScrollLockActive());
      console.log('- Current locked component:', pageNavigation.currentLockedComponent);
      console.log('- Current section:', pageNavigation.currentSection?.name || 'none');
      console.log('- Body classes:', Array.from(document.body.classList));
      console.log('- Body style position:', document.body.style.position);
      console.log('- Pin spacers found:', document.querySelectorAll('.pin-spacer').length);
    };

    console.log('✅ Page Navigation initialized. Use debugNavigation() for debug info.');
  });

  class PageNavigation {
    constructor() {
      this.navList = document.getElementById('nav-list');
      this.progressBar = document.getElementById('progress-bar');
      this.sections = [];
      this.currentSection = null;
      this.observer = null;

      // Mobile navigation elements
      this.mobileNavTrigger = document.getElementById('mobile-nav-trigger');
      this.mobileNavContainer = document.getElementById('page-nav-container');
      this.mobileNavOverlay = document.getElementById('mobile-nav-overlay');
      this.mobileNavClose = document.getElementById('mobile-nav-close');
      this.triggerProgress = document.getElementById('trigger-progress');
      this.isMobileNavOpen = false;
    }

    init() {
      // Wait for DOM to be fully loaded
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
          this.initializeNavigation();
        });
      } else {
        this.initializeNavigation();
      }
    }

    initializeNavigation() {
      this.generateNavItems();
      this.setupScrollObserver();
      this.bindEvents();
      this.setupMobileNavigation();
      this.updateNavigationVisibility();
    }

    updateNavigationVisibility() {
      // Hide navigation if no sections found or if setting is enabled
      const hideOnMobile = {{ section.settings.hide_on_mobile | json }};
      const isMobile = window.innerWidth <= 768;

      if (this.sections.length === 0 || (hideOnMobile && isMobile)) {
        document.getElementById('page-navigation').style.display = 'none';
        return;
      }

      // Show navigation with fade-in effect
      const nav = document.getElementById('page-navigation');
      nav.style.opacity = '0';
      nav.style.display = 'block';

      setTimeout(() => {
        nav.style.transition = 'opacity 0.5s ease';
        nav.style.opacity = '1';
      }, 100);
    }

    generateNavItems() {
      // Get page sections from the template data with scroll lock info
      const pageSections = [
        { id: 'ir3_hero_section_1_M3ezat', name: 'Hero Section', hasScrollLock: false },
        { id: 'ir3_v2_key_features_N7xLp9', name: 'Key Features', hasScrollLock: true, lockType: 'interactive' },
        { id: 'ir3_v2_auto_leveling_animation_7wqmHp', name: 'Smart Leveling', hasScrollLock: true, lockType: 'video' },
        { id: 'ir3_tech_innovation_Kx9mN2', name: 'Tech Innovation', hasScrollLock: false },
        { id: 'ir3_video_scroll_demo_Kx8mN3', name: 'Klipper Firmware', hasScrollLock: false },
        { id: 'ir3_performance_video_scroll_Pf4mN5', name: 'Performance', hasScrollLock: false },
        { id: 'smart_features_Sm4rtF3', name: 'Smart Features', hasScrollLock: true, lockType: 'interactive' },
        { id: 'ir3_batch_printing_video_Bp8mN9', name: 'Batch Printing', hasScrollLock: false },
        { id: 'ir3_long_object_printing_Lg9mN4', name: 'Long Object Printing', hasScrollLock: false }
      ];

      let validSectionIndex = 0;

      pageSections.forEach((section) => {
        // Try multiple selectors to find the section element
        let sectionElement = document.querySelector(`#shopify-section-${section.id}`) ||
                           document.querySelector(`[data-section-id="${section.id}"]`) ||
                           document.querySelector(`[id*="${section.id}"]`) ||
                           document.querySelector(`section[class*="${section.id}"]`);

        // If still not found, try to find by section type
        if (!sectionElement) {
          const sectionType = section.id.split('_')[0];
          sectionElement = document.querySelector(`[data-section-type*="${sectionType}"]`);
        }

        if (sectionElement) {
          this.sections.push({
            element: sectionElement,
            id: section.id,
            name: section.name,
            index: validSectionIndex,
            hasScrollLock: section.hasScrollLock,
            lockType: section.lockType
          });

          const navItem = this.createNavItem(section, validSectionIndex);
          this.navList.appendChild(navItem);
          validSectionIndex++;
        } else {
          console.warn(`Section not found: ${section.id}`);
        }
      });

      // If no sections found, try to auto-detect sections
      if (this.sections.length === 0) {
        this.autoDetectSections();
      }
    }

    autoDetectSections() {
      // Auto-detect sections based on common Shopify section patterns
      const sectionElements = document.querySelectorAll('[id^="shopify-section-"], section[class*="section"], .section');

      sectionElements.forEach((element, index) => {
        if (element.id && element.id.includes('shopify-section-')) {
          const sectionId = element.id.replace('shopify-section-', '');
          const sectionName = this.generateSectionName(sectionId);

          this.sections.push({
            element: element,
            id: sectionId,
            name: sectionName,
            index: index
          });

          const navItem = this.createNavItem({ id: sectionId, name: sectionName }, index);
          this.navList.appendChild(navItem);
        }
      });
    }

    generateSectionName(sectionId) {
      // Convert section ID to readable name
      return sectionId
        .replace(/[-_]/g, ' ')
        .replace(/\b\w/g, l => l.toUpperCase())
        .replace(/\s+/g, ' ')
        .trim();
    }

    createNavItem(section, index) {
      const li = document.createElement('li');
      li.className = 'page-nav-item';

      const link = document.createElement('a');
      link.href = `#${section.id}`;
      link.className = 'page-nav-link';
      link.textContent = section.name;
      link.dataset.sectionId = section.id;
      link.dataset.index = index;
      link.dataset.hasScrollLock = section.hasScrollLock || false;
      link.dataset.lockType = section.lockType || '';

      // Add scroll lock class for identification (no visual indicator)
      if (section.hasScrollLock) {
        link.classList.add('has-scroll-lock');
      }

      li.appendChild(link);
      return li;
    }

    setupScrollObserver() {
      const options = {
        root: null,
        rootMargin: '-20% 0px -60% 0px',
        threshold: [0, 0.25, 0.5, 0.75, 1]
      };

      this.observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            this.setActiveSection(entry.target);
          }
        });
      }, options);

      this.sections.forEach(section => {
        this.observer.observe(section.element);
      });
    }

    setActiveSection(element) {
      const sectionData = this.sections.find(s => s.element === element);
      if (!sectionData) return;

      // Remove active class from all links
      document.querySelectorAll('.page-nav-link').forEach(link => {
        link.classList.remove('active');
      });

      // Add active class to current link
      const activeLink = document.querySelector(`[data-section-id="${sectionData.id}"]`);
      if (activeLink) {
        activeLink.classList.add('active');
      }

      // Update progress bar
      const progress = ((sectionData.index + 1) / this.sections.length) * 100;
      this.progressBar.style.setProperty('--progress', `${progress}%`);

      this.currentSection = sectionData;
    }

    bindEvents() {
      this.navList.addEventListener('click', (e) => {
        e.preventDefault();

        if (e.target.classList.contains('page-nav-link')) {
          const sectionId = e.target.dataset.sectionId;
          console.log('🔍 Navigation clicked:', sectionId);

          // Check if any scroll lock is currently active
          const isLocked = this.isScrollLockActive();
          console.log('🔒 Scroll lock status:', isLocked);

          if (isLocked) {
            console.log('⚠️ Navigation blocked due to scroll lock');
            // Show tooltip explaining user needs to complete current section
            this.showScrollLockBlockTooltip(e.target);
            return; // Don't navigate
          }

          console.log('✅ Proceeding with navigation to:', sectionId);
          // Normal navigation
          this.scrollToSection(sectionId);
        }
      });

      // Handle window resize
      let resizeTimeout;
      window.addEventListener('resize', () => {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(() => {
          this.updateNavigationVisibility();
        }, 250);
      });

      // Handle visibility change (tab switching)
      document.addEventListener('visibilitychange', () => {
        if (!document.hidden) {
          // Refresh observer when tab becomes visible
          this.refreshObserver();
        }
      });
    }

    refreshObserver() {
      if (this.observer) {
        this.observer.disconnect();
        this.setupScrollObserver();
      }
    }

    scrollToSection(sectionId) {
      console.log('🎯 scrollToSection called with:', sectionId);
      const section = this.sections.find(s => s.id === sectionId);
      if (!section) {
        console.error('❌ Section not found:', sectionId);
        return;
      }

      console.log('✅ Section found:', section.name);
      const targetElement = section.element;

      // Calculate header offset dynamically
      const header = document.querySelector('header, .header, [class*="header"]');
      const headerHeight = header ? header.offsetHeight : 0;
      const additionalOffset = 20; // Extra spacing
      const totalOffset = headerHeight + additionalOffset;

      // Get target position
      const elementPosition = targetElement.getBoundingClientRect().top;
      const offsetPosition = elementPosition + window.pageYOffset - totalOffset;

      // 触发导航跳转事件，通知其他组件
      document.dispatchEvent(new CustomEvent('pageNavigationJump', {
        detail: { sectionId: sectionId, targetElement: targetElement }
      }));

      // Add visual feedback
      this.addScrollingFeedback(sectionId);

      // Smooth scroll with custom easing
      console.log('🚀 Starting smooth scroll to position:', offsetPosition);
      this.smoothScrollTo(offsetPosition, 800);
    }

    addScrollingFeedback(sectionId) {
      // Add temporary visual feedback to the clicked nav item
      const navLink = document.querySelector(`[data-section-id="${sectionId}"]`);
      if (navLink) {
        navLink.style.transform = 'scale(0.95)';
        navLink.style.transition = 'transform 0.1s ease';

        setTimeout(() => {
          navLink.style.transform = '';
          navLink.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
        }, 100);
      }
    }

    smoothScrollTo(targetPosition, duration = 800) {
      const startPosition = window.pageYOffset;
      const distance = targetPosition - startPosition;
      let startTime = null;

      // Custom easing function (ease-in-out-cubic)
      const easeInOutCubic = (t) => {
        return t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1;
      };

      const animation = (currentTime) => {
        if (startTime === null) startTime = currentTime;
        const timeElapsed = currentTime - startTime;
        const progress = Math.min(timeElapsed / duration, 1);
        const easedProgress = easeInOutCubic(progress);

        window.scrollTo(0, startPosition + distance * easedProgress);

        if (progress < 1) {
          requestAnimationFrame(animation);
        }
      };

      requestAnimationFrame(animation);
    }

    isScrollLockActive() {
      let lockReasons = [];
      let lockedComponent = null;

      // Method 1: Check body scroll-locked class (ir3-v2-key-features and smart-features)
      if (document.body.classList.contains('scroll-locked')) {
        lockReasons.push('body.scroll-locked');

        // Try to identify which component is locked
        const keyFeaturesSection = document.querySelector('[data-section-type="ir3-v2-key-features"]');
        const smartFeaturesSection = document.querySelector('[data-section-type="smart-features"]');

        if (keyFeaturesSection) {
          const rect = keyFeaturesSection.getBoundingClientRect();
          if (rect.top <= 100 && rect.bottom >= window.innerHeight * 0.3) {
            lockedComponent = { name: 'Key Features', type: 'interactive', element: keyFeaturesSection };
          }
        }

        if (smartFeaturesSection && !lockedComponent) {
          const rect = smartFeaturesSection.getBoundingClientRect();
          if (rect.top <= 100 && rect.bottom >= window.innerHeight * 0.3) {
            lockedComponent = { name: 'Smart Features', type: 'interactive', element: smartFeaturesSection };
          }
        }
      }

      // Method 2: Check body fixed positioning
      if (document.body.style.position === 'fixed' && document.body.style.overflow === 'hidden') {
        lockReasons.push('body fixed positioning');
      }

      // Method 3: Check for GSAP ScrollTrigger pins (ir3-v2-auto-leveling-animation)
      const autoLevelingSection = document.querySelector('[data-section-type="ir3-auto-leveling"]');
      if (autoLevelingSection) {
        const rect = autoLevelingSection.getBoundingClientRect();
        const isInViewport = rect.top <= 50 && rect.bottom >= window.innerHeight * 0.5;

        // Check if this section has pinning active
        const pinnedContainer = autoLevelingSection.querySelector('.ir3-auto-leveling__video-container');
        if (pinnedContainer && isInViewport) {
          const computedStyle = window.getComputedStyle(pinnedContainer);
          if (computedStyle.position === 'fixed' || pinnedContainer.style.position === 'fixed') {
            lockReasons.push('auto-leveling video pinned');
            lockedComponent = { name: 'Smart Leveling', type: 'video', element: autoLevelingSection };
          }
        }

        // Also check for pin-spacer elements (GSAP creates these)
        const pinSpacers = document.querySelectorAll('.pin-spacer');
        pinSpacers.forEach(spacer => {
          if (spacer.contains(autoLevelingSection) || autoLevelingSection.contains(spacer)) {
            if (isInViewport) {
              lockReasons.push('GSAP pin-spacer detected');
              lockedComponent = { name: 'Smart Leveling', type: 'video', element: autoLevelingSection };
            }
          }
        });
      }

      const isLocked = lockReasons.length > 0;

      // Store the locked component for use in tooltips
      this.currentLockedComponent = lockedComponent;

      if (isLocked) {
        console.log('🔒 Scroll lock detected:', lockReasons.join(', '));
        if (lockedComponent) {
          console.log('📍 Locked component:', lockedComponent.name, `(${lockedComponent.type})`);
        }
      } else {
        console.log('🔓 No scroll lock detected');
      }

      return isLocked;
    }

    getCurrentLockedSection() {
      // Return the locked component identified in isScrollLockActive()
      if (this.currentLockedComponent) {
        return this.currentLockedComponent;
      }

      // Fallback: try to identify based on viewport position
      const lockedSections = [
        { id: 'ir3_v2_key_features_N7xLp9', name: 'Key Features', type: 'interactive' },
        { id: 'ir3_v2_auto_leveling_animation_7wqmHp', name: 'Smart Leveling', type: 'video' },
        { id: 'smart_features_Sm4rtF3', name: 'Smart Features', type: 'interactive' }
      ];

      for (const sectionInfo of lockedSections) {
        const section = this.sections.find(s => s.id === sectionInfo.id);
        if (section) {
          const rect = section.element.getBoundingClientRect();
          const viewportHeight = window.innerHeight;

          // Check if section is currently in viewport and likely locked
          if (rect.top <= 100 && rect.bottom >= viewportHeight * 0.5) {
            return {
              name: sectionInfo.name,
              type: sectionInfo.type,
              element: section.element
            };
          }
        }
      }

      return null;
    }

    showScrollLockBlockTooltip(targetElement) {
      const tooltip = document.getElementById('scroll-lock-tooltip');
      const tooltipIcon = document.getElementById('tooltip-icon');
      const tooltipTitle = document.getElementById('tooltip-title');
      const tooltipDescription = document.getElementById('tooltip-description');

      if (!tooltip) return;

      const currentLockedSection = this.getCurrentLockedSection();
      const targetSectionName = targetElement.textContent.replace(/[🎬🔒]/g, '').trim();

      // Update tooltip content for scroll lock blocking
      tooltipIcon.textContent = '⚠️';
      tooltipTitle.textContent = 'Navigation Blocked';

      if (currentLockedSection) {
        const lockedSectionName = currentLockedSection.name;
        const lockType = currentLockedSection.type;

        if (lockType === 'video') {
          tooltipDescription.textContent = `"${lockedSectionName}" is currently active. Please use your mouse wheel to scroll through the entire video content, then you can navigate to "${targetSectionName}".`;
        } else if (lockType === 'interactive') {
          tooltipDescription.textContent = `"${lockedSectionName}" is currently active. Please use your mouse wheel to browse through all interactive features, then you can navigate to "${targetSectionName}".`;
        }
      } else {
        tooltipDescription.textContent = `An interactive section is currently active. Please use your mouse wheel to scroll through all content, then you can navigate to "${targetSectionName}".`;
      }

      // Position tooltip
      this.positionTooltip(tooltip, targetElement);

      // Show tooltip
      tooltip.classList.add('show');

      // Auto hide after delay
      setTimeout(() => {
        this.hideScrollLockTooltip();
      }, 1500); // Quick tooltip display for better UX
    }

    hideScrollLockTooltip() {
      const tooltip = document.getElementById('scroll-lock-tooltip');
      if (tooltip) {
        tooltip.classList.remove('show');
      }
    }

    positionTooltip(tooltip, targetElement) {
      const targetRect = targetElement.getBoundingClientRect();
      const tooltipRect = tooltip.getBoundingClientRect();
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;

      // Default position: right of target
      let left = targetRect.right + 10;
      let top = targetRect.top + (targetRect.height / 2) - (tooltipRect.height / 2);
      let arrowClass = 'left';

      // Check if tooltip would go off screen on the right
      if (left + tooltipRect.width > viewportWidth - 20) {
        // Position on left instead
        left = targetRect.left - tooltipRect.width - 10;
        arrowClass = 'right';
      }

      // Check if tooltip would go off screen on the left
      if (left < 20) {
        // Position below target
        left = targetRect.left + (targetRect.width / 2) - (tooltipRect.width / 2);
        top = targetRect.bottom + 10;
        arrowClass = 'bottom';
      }

      // Ensure tooltip stays within viewport bounds
      left = Math.max(20, Math.min(left, viewportWidth - tooltipRect.width - 20));
      top = Math.max(20, Math.min(top, viewportHeight - tooltipRect.height - 20));

      // Apply position
      tooltip.style.left = left + 'px';
      tooltip.style.top = top + 'px';

      // Update arrow position
      const arrow = tooltip.querySelector('.tooltip-arrow');
      if (arrow) {
        arrow.className = `tooltip-arrow ${arrowClass}`;
      }
    }

    setupMobileNavigation() {
      // Mobile navigation event handlers
      if (this.mobileNavTrigger) {
        this.mobileNavTrigger.addEventListener('click', () => {
          this.openMobileNav();
        });
      }

      if (this.mobileNavClose) {
        this.mobileNavClose.addEventListener('click', () => {
          this.closeMobileNav();
        });
      }

      if (this.mobileNavOverlay) {
        this.mobileNavOverlay.addEventListener('click', () => {
          this.closeMobileNav();
        });
      }

      // Handle escape key
      document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && this.isMobileNavOpen) {
          this.closeMobileNav();
        }
      });

      // Close mobile nav when clicking on navigation links
      if (this.navList) {
        this.navList.addEventListener('click', (e) => {
          if (e.target.classList.contains('page-nav-link') && this.isMobileNavOpen) {
            this.closeMobileNav();
          }
        });
      }
    }

    openMobileNav() {
      if (this.mobileNavContainer) {
        this.mobileNavContainer.classList.add('active');
      }
      if (this.mobileNavOverlay) {
        this.mobileNavOverlay.classList.add('active');
      }
      if (this.mobileNavTrigger) {
        this.mobileNavTrigger.classList.add('nav-open');
      }
      this.isMobileNavOpen = true;

      // Prevent body scroll
      document.body.style.overflow = 'hidden';
    }

    closeMobileNav() {
      if (this.mobileNavContainer) {
        this.mobileNavContainer.classList.remove('active');
      }
      if (this.mobileNavOverlay) {
        this.mobileNavOverlay.classList.remove('active');
      }
      if (this.mobileNavTrigger) {
        this.mobileNavTrigger.classList.remove('nav-open');
      }
      this.isMobileNavOpen = false;

      // Restore body scroll
      document.body.style.overflow = '';
    }
  }
</script>

{% schema %}
{
  "name": "Page Navigation",
  "tag": "section",
  "class": "section",
  "settings": [
    {
      "type": "header",
      "content": "Navigation Settings"
    },
    {
      "type": "text",
      "id": "nav_title",
      "label": "Navigation Title",
      "default": "SECTIONS",
      "info": "Title displayed at the top of the navigation"
    },
    {
      "type": "checkbox",
      "id": "show_progress",
      "label": "Show Progress Bar",
      "default": true,
      "info": "Display reading progress at the bottom"
    },
    {
      "type": "checkbox",
      "id": "hide_on_mobile",
      "label": "Hide on Mobile",
      "default": false,
      "info": "Hide navigation on mobile devices"
    },
    {
      "type": "header",
      "content": "Visual Settings"
    },
    {
      "type": "select",
      "id": "position",
      "label": "Position",
      "options": [
        {
          "value": "left",
          "label": "Left Side"
        },
        {
          "value": "right",
          "label": "Right Side"
        }
      ],
      "default": "left",
      "info": "Choose navigation position"
    },
    {
      "type": "range",
      "id": "opacity",
      "min": 0.7,
      "max": 1.0,
      "step": 0.1,

      "label": "Background Opacity",
      "default": 0.9
    }
  ],
  "presets": [
    {
      "name": "Page Navigation",
      "settings": {
        "nav_title": "SECTIONS",
        "show_progress": true,
        "hide_on_mobile": false,
        "position": "left",
        "opacity": 0.9
      }
    }
  ]
}
{% endschema %}
