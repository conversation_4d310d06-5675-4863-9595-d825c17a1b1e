<div data-section-id="{{ section.id }}" data-section-type="password-header">
  <div class="header-wrapper{% if section.settings.overlay_header %} header-wrapper--overlay is-light{% endif %}">
    {%- unless shop.password_message == blank -%}
      <div class="announcement">
        <span class="announcement__text">{{ shop.password_message }}</span>
      </div>
    {%- endunless -%}

    <header class="site-header site-header--password" role="banner">
      <div class="page-width">
        <div class="password-page__header__grid">
          <div class="password-page__header__grid-item password-page__logo">
            <h1 itemscope itemtype="http://schema.org/Organization" class="h2">
              {%- if section.settings.logo != blank -%}
                {%- style -%}
                  .password-page__logo h1 {
                    height: {{ section.settings.mobile_logo_height }}px;
                  }
                  @media only screen and (min-width: 769px) {
                    .password-page__logo h1 {
                      height: {{ section.settings.desktop_logo_height }}px;
                    }
                  }
                {%- endstyle -%}

                {% comment %} Desktop logo {% endcomment %}
                {%- assign width = section.settings.desktop_logo_height | times: section.settings.logo.aspect_ratio | times: 2 -%}
                {%- assign height = section.settings.desktop_logo_height -%}
                {%- capture sizes -%}{{ section.settings.desktop_logo_height | times: section.settings.logo.aspect_ratio }}px{%- endcapture -%}
                {%- capture widths -%}{{ section.settings.desktop_logo_height | times: section.settings.logo.aspect_ratio }}, {{ section.settings.desktop_logo_height | times: section.settings.logo.aspect_ratio | times: 2 }}{%- endcapture -%}
                {%- capture style -%}max-width: {{ section.settings.desktop_logo_height | times: section.settings.logo.aspect_ratio }}px;max-height: {{ section.settings.desktop_logo_height }}px;{%- endcapture -%}

                {% comment %} Mobile logo {% endcomment %}
                {%- assign mobile_width = section.settings.mobile_logo_height | times: section.settings.logo.aspect_ratio | times: 2 -%}
                {%- assign mobile_height = section.settings.mobile_logo_height -%}
                {%- capture mobile_sizes -%}{{ section.settings.mobile_logo_height | times: section.settings.logo.aspect_ratio }}px{%- endcapture -%}
                {%- capture mobile_widths -%}{{ section.settings.mobile_logo_height | times: section.settings.logo.aspect_ratio }}, {{ section.settings.mobile_logo_height | times: section.settings.logo.aspect_ratio | times: 2 }}{%- endcapture -%}
                {%- capture mobile_style -%}max-width: {{ section.settings.mobile_logo_height | times: section.settings.logo.aspect_ratio }}px;max-height: {{ section.settings.mobile_logo_height }}px;{%- endcapture -%}

                {%- render 'image-element',
                  img: section.settings.logo,
                  img_width: width,
                  img_height: height,
                  sizes: sizes,
                  widths: widths,
                  style: style,
                  classes: 'small--hide',
                  loading: 'eager',
                  alt: section.settings.logo.alt | default: shop.name,
                  itemprop: 'logo',
                -%}
                {%- render 'image-element',
                  img: section.settings.logo,
                  img_width: mobile_width,
                  img_height: mobile_height,
                  sizes: mobile_sizes,
                  widths: mobile_widths,
                  style: mobile_style,
                  classes: 'medium-up--hide',
                  loading: 'eager',
                  alt: section.settings.logo.alt | default: shop.name,
                -%}
              {%- else -%}
                <span>{{ shop.name }}</span>
              {%- endif -%}
            </h1>
          </div>

          <div class="password-page__header__grid-item password-page__login">
            <a href="#LoginModal" class="js-modal-open-login-modal password-login">
              <span class="password__lock">
                <svg aria-hidden="true" focusable="false" role="presentation" class="icon icon-lock" viewBox="0 0 64 64"><path d="M19.45 23.6v-3.2c0-7 5.89-11.75 12.55-11.75 7.21 0 12.55 5.49 12.55 11.75v3.2M10 23.6h44v31.75H10zm22 13.87v7.47"/><circle cx="32" cy="35.87" r="1.6"/></svg>
              </span>
              {{ 'general.password_page.password_link' | t }}
            </a>
          </div>
        </div>
      </div>
    </header>
  </div>
</div>

{% schema %}
{
  "name": "t:sections.password-header.name",
  "class": "password-header-section",
  "settings": [
    {
      "type": "checkbox",
      "id": "overlay_header",
      "label": "t:sections.password-header.settings.overlay_header.label",
      "default": true
    },
    {
      "type": "image_picker",
      "id": "logo",
      "label": "t:sections.password-header.settings.logo.label"
    },
    {
      "type": "range",
      "id": "desktop_logo_height",
      "label": "t:sections.password-header.settings.desktop_logo_height.label",
      "default": 50,
      "min": 20,
      "max": 120,
      "unit": "px"
    },
    {
      "type": "range",
      "id": "mobile_logo_height",
      "label": "t:sections.password-header.settings.mobile_logo_height.label",
      "default": 50,
      "min": 20,
      "max": 120,
      "unit": "px"
    }
  ]
}
{% endschema %}
