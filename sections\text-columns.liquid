{%- if section.settings.divider -%}<div class="section--divider">{%- endif -%}

<div class="page-width">
  {%- if section.settings.title != blank -%}
    <div class="section-header text-{{ section.settings.align_text }}">
      <h2>{{ section.settings.title }}</h2>
    </div>
  {% endif %}

  <div class="text-column__grid">
    {%- for block in section.blocks -%}
      <div class="text-{{ section.settings.align_text }}" {{ block.shopify_attributes }} data-aos="row-of-3">
        {%- if block.settings.enable_image -%}
          <div style="{% if section.settings.align_text == 'center' %}margin: 0 auto;{% endif %} max-width: {{ block.settings.image_width }}px;">
            {% if block.settings.button_link != blank %}
              <a href="{{ block.settings.button_link }}">
            {% endif %}
            {% if block.settings.image != blank %}

              <div class="image-wrap text-spacing {% if block.settings.image_mask != 'none' %}svg-mask svg-mask--{{ block.settings.image_mask }}{% endif %}" style="height: 0; padding-bottom: {{ 100 | divided_by: block.settings.image.aspect_ratio }}%;">
                {%- render 'image-element',
                  img: block.settings.image,
                  sizeVariable: column_width,
                  widths: '180, 360, 540, 720, 900, 1080',
                -%}
              </div>
            {% else %}
              <div class="image-wrap text-spacing {% if block.settings.image_mask != 'none' %}svg-mask svg-mask--{{ block.settings.image_mask }}{% endif %}" style="height: 300px">{{ 'image' | placeholder_svg_tag: 'placeholder-svg' }}</div>
            {% endif %}
            {% if block.settings.button_link != blank %}
              </a>
            {% endif %}
          </div>
        {% endif %}
        {%- if block.settings.title != blank -%}
          <h3>{{ block.settings.title }}</h3>
        {% endif %}
        {% if block.settings.text != blank %}
          <div class="rte-setting text-spacing">{{ block.settings.text }}</div>
        {% endif %}
        {% if block.settings.button_label != blank %}
          <a href="{{ block.settings.button_link }}" class="btn btn--secondary btn--small">
            {{ block.settings.button_label }}
          </a>
        {% endif %}
      </div>
    {%- endfor -%}
  </div>
</div>

{%- if section.settings.divider -%}</div>{%- endif -%}

{% schema %}
{
  "name": "t:sections.text-columns.name",
  "class": "index-section",
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "t:sections.text-columns.settings.title.label"
    },
    {
      "type": "select",
      "id": "align_text",
      "label": "t:sections.text-columns.settings.align_text.label",
      "default": "center",
      "options": [
        {
          "value": "left",
          "label": "t:sections.text-columns.settings.align_text.options.left.label"
        },
        {
          "value": "center",
          "label": "t:sections.text-columns.settings.align_text.options.center.label"
        }
      ]
    },
    {
      "type": "checkbox",
      "id": "divider",
      "label": "t:sections.text-columns.settings.divider.label",
      "default": false
    }
  ],
  "blocks": [
    {
      "type": "text_block",
      "name": "t:sections.text-columns.blocks.column.name",
      "settings": [
        {
          "type": "checkbox",
          "id": "enable_image",
          "label": "t:sections.text-columns.blocks.column.settings.enable_image.label",
          "default": true
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "t:sections.text-columns.blocks.column.settings.image.label"
        },
        {
          "type": "select",
          "id": "image_mask",
          "label": "t:common.image_mask.label",
          "default": "none",
          "options": [
            {
              "value": "none",
              "label": "t:common.image_mask.options.none.label"
            },
            {
              "value": "portrait",
              "label": "t:common.image_mask.options.portrait.label"
            },
            {
              "value": "landscape",
              "label": "t:common.image_mask.options.landscape.label"
            },
            {
              "value": "square",
              "label": "t:common.image_mask.options.square.label"
            },
            {
              "value": "rounded",
              "label": "t:common.image_mask.options.rounded.label"
            },
            {
              "value": "rounded-wave",
              "label": "t:common.image_mask.options.rounded-wave.label"
            },
            {
              "value": "rounded-top",
              "label": "t:common.image_mask.options.rounded-top.label"
            },
            {
              "value": "star",
              "label": "t:common.image_mask.options.star.label"
            },
            {
              "value": "splat-1",
              "label": "t:common.image_mask.options.splat-1.label"
            },
            {
              "value": "splat-2",
              "label": "t:common.image_mask.options.splat-2.label"
            },
            {
              "value": "splat-3",
              "label": "t:common.image_mask.options.splat-3.label"
            },
            {
              "value": "splat-4",
              "label": "t:common.image_mask.options.splat-4.label"
            }
          ]
        },
        {
          "type": "range",
          "id": "image_width",
          "label": "t:sections.text-columns.blocks.column.settings.image_width.label",
          "default": 650,
          "min": 60,
          "max": 650,
          "step": 10,
          "unit": "px"
        },
        {
          "type": "text",
          "id": "title",
          "label": "t:sections.text-columns.blocks.column.settings.title.label",
          "default": "Example title"
        },
        {
          "type": "richtext",
          "id": "text",
          "label": "t:sections.text-columns.blocks.column.settings.text.label",
          "default": "<p>Use this section to explain a set of product features, to link to a series of pages, or to answer common questions about your products. Add images for emphasis.</p>"
        },
        {
          "type": "text",
          "id": "button_label",
          "label": "t:sections.text-columns.blocks.column.settings.button_label.label",
          "default": "Optional button"
        },
        {
          "type": "url",
          "id": "button_link",
          "label": "t:sections.text-columns.blocks.column.settings.button_link.label"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "t:sections.text-columns.presets.text_columns_with_images.name",
      "blocks": [
        {
          "type": "text_block"
        },
        {
          "type": "text_block"
        },
        {
          "type": "text_block"
        }
      ]
    }
  ],
  "disabled_on": {
    "groups": ["custom.popups"]
  }
}
{% endschema %}
