{% comment %}
- collection: Liquid 'collection' or 'search' object
- enable_sort: boolean
- collection_tags_style
{% endcomment %}

{%- liquid
  assign current_filter_size = 0
  for filter in collection.filters
    assign current_filter_size = current_filter_size | plus: filter.active_values.size
  endfor
-%}

<div class="collection-filter">
  <div class="grid grid--uniform">

    <div class="grid__item small--one-half medium-up--one-half">
      <button
        id="FilterDrawerTrigger"
        type="button"
        class="collection-filter__item--drawer js-drawer-open-collection-filters btn btn--tertiary{% unless current_filter_size == 0 %} btn--tertiary-active{% endunless %}"
        aria-controls="FilterDrawer"
      >
        <svg aria-hidden="true" focusable="false" role="presentation" class="icon icon-filter" viewBox="0 0 64 64"><title>icon-filter</title><path d="M48 42h10m-10 0a5 5 0 1 1-5-5 5 5 0 0 1 5 5ZM7 42h31M16 22H6m10 0a5 5 0 1 1 5 5 5 5 0 0 1-5-5Zm41 0H26"/></svg>
        {{ 'collections.filters.title_tags' | t }}
        {%- if current_filter_size > 0 -%}
          ({{ current_filter_size }})
        {%- endif -%}
      </button>
    </div>

    {%- if enable_sort -%}
      <div class="grid__item small--one-half medium-up--one-half text-right{% if collection_tags_style == 'dropdown' %} small--one-half medium-up--one-half{% endif %} collection-filter__item--sort">
        {%- assign sort_by = collection.sort_by | default: collection.default_sort_by -%}
        <label for="SortBy" class="hidden-label">{{ 'collections.sorting.title' | t }}</label>
        <select name="SortBy" id="SortBy" data-default-sortby="{{ collection.default_sort_by }}">
          <option value="title-ascending"{% if sort_by == collection.default_sort_by %} selected="selected"{% endif %}>{{ 'collections.sorting.title' | t }}</option>
          {%- for option in collection.sort_options -%}
            <option value="{{ option.value }}" {% if option.value == sort_by %}selected="selected"{% endif %}>{{ option.name }}</option>
          {%- endfor -%}
        </select>
      </div>
    {%- endif -%}

  </div>
</div>