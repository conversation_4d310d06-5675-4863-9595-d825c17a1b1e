{%- liquid
  unless background_position
    assign background_position = 'top center'
  endunless

  if collection == blank
    assign collection_title = 'home_page.onboarding.collection_title' | t
  else
    assign collection_title = collection.title | escape
  endif

  if collection.image
    assign collection_image = collection.image
  else
    assign collection_image = collection.products.first.featured_media.preview_image
  endif
-%}

<div class="grid__item {{ gridView }} skrim__item" data-aos="skrim__animation" {{ block.shopify_attributes }}>
  <a href="{{ collection.url }}" class="skrim__link skrim__item-content">
    {%- if collection == blank or collection_image == blank -%}
      {% capture placeholder %}collection-{% cycle 1, 2, 3, 4, 5, 6 %}{% endcapture %}
      <div class="skrim__overlay grid__image">{{ placeholder | placeholder_svg_tag: 'placeholder-svg' }}</div>
    {%- else -%}
      <div class="skrim__overlay grid__image">
        {% assign indicator = block.id %}
        {% if block == blank %}
          {% assign indicator = collection.id %}
        {% endif %}

        {% style %}
          .grid__image--{{ indicator }}{
            object-position: {{ background_position }};
          }
        {% endstyle %}

        {% assign classes = 'image-fit grid__image--' | append: indicator %}
        {%- render 'image-element',
          img: collection_image,
          img_width: 2400,
          classes: classes,
          sizes: sizes,
          sizeVariable: sizeVariable,
          fallback: fallback,
        -%}
      </div>
    {%- endif -%}

    <div class="skrim__title">
      <div class="skrim__underline-me">
        {{ collection_title }}
      </div>
    </div>
  </a>
</div>
