{%- if section.settings.enable_sidebar -%}
  <div id="CollectionSidebar">
    {%- render 'sidebar-drawer', section: section, collection: collection -%}
  </div>
{%- endif -%}

{%- if section.settings.enable_sidebar == false or section.settings.filter_style == 'drawer' or collection.filters.size == 0 -%}
  {% comment %}
    Override grid styles if sidebar is disabled
  {% endcomment %}
  {%- style -%}
    .collection-content .grid__item--sidebar { width: 0; }
    .collection-content .grid__item--content { width: 100%; }
    .grid__item--sidebar { position: static; overflow: hidden; }
  {%- endstyle -%}
{%- endif -%}

{%- if section.settings.enable_sidebar == false or collection.filters.size == 0 -%}
  {% comment %}
    Disable sidebar & filter features
  {% endcomment %}
  {%- style -%}
    .collection-filter__item--drawer {
      display: none;
    }
  {%- endstyle -%}
{%- endif -%}

{%- unless section.settings.enable_sort -%}
  {%- style -%}
    .collection-filter__item--sort {
      display: none;
    }
  {%- endstyle -%}
{%- endunless -%}
