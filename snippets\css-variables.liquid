{%- style -%}
  :root {
    --typeHeaderPrimary: {{ settings.type_header_font_family.family }};
    --typeHeaderFallback: {{ settings.type_header_font_family.fallback_families }};
    --typeHeaderSize: {{ settings.type_header_base_size | default: '32' | append: 'px' }};
    --typeHeaderWeight: {{ settings.type_header_font_family.weight }};
    --typeHeaderLineHeight: {{ settings.type_header_line_height | default: 1.4 }};
    --typeHeaderSpacing: {{ settings.type_header_spacing | default: '25' | divided_by: 1000.00 | append: 'em' }};

    --typeBasePrimary:{{ settings.type_base_font_family.family }};
    --typeBaseFallback:{{ settings.type_base_font_family.fallback_families }};
    --typeBaseSize: {{ settings.type_base_size | default: '16' | append: 'px' }};
    --typeBaseWeight: {{ settings.type_base_font_family.weight }};
    --typeBaseLineHeight: {{ settings.type_base_line_height | default: 1.6 }};
    --typeBaseSpacing: {{ settings.type_base_spacing | default: '50' | divided_by: 1000.00 | append: 'em' }};

    --iconWeight: {{ settings.icon_weight | default: '5px' }};
    --iconLinecaps: {{ settings.icon_linecaps | default: 'miter' }};

    --animateImagesStyle: {{ settings.animate_images_style | default: 'zoom-fade'}};
    --animateImagesStyleSmall: {{ settings.animate_images_style | default: 'zoom-fade' | append: '-small' }};
    --animateSectionsBackgroundStyle: {{ settings.animate_sections_background_style | default: 'zoom-fade'}};
    --animateSectionsTextStyle: {{ settings.animate_sections_text_style | default: 'rise-up'}};
    --animateSectionsTextStyleAppendOut: {{ settings.animate_sections_text_style | default: 'rise-up' | append: '-out'}};

    --colorAnnouncement: {{ settings.color_announcement | default: "#1c1d1d" }};
    --colorAnnouncementText: {{ settings.color_announcement_text | default: "#ffffff" }};

    --colorBody: {{ settings.color_body_bg | default: "#fff" }};
    --colorBodyAlpha05: {{ settings.color_body_bg | default: "#1c1d1d" | color_modify: 'alpha', 0.05 }};
    --colorBodyDim: {{ settings.color_body_bg | default: "#ffffff" | color_darken: 5 }};
    --colorBodyLightDim: {{ settings.color_body_bg | default: "#ffffff" | color_darken: 2 }};
    --colorBodyMediumDim: {{ settings.color_body_bg | default: "#ffffff" | color_darken: 4 }};

    --colorBorder: {{ settings.color_borders | default: "#1c1d1d" }};

    --colorBtnPrimary: {{ settings.color_button | default: "#000000" }};
    --colorBtnPrimaryDim: {{ settings.color_button | default: "#000000" | color_darken: 5 }};
    --colorBtnPrimaryText: {{ settings.color_button_text | default: "#ffffff" }};

    --colorCartDot: {{ settings.color_cart_dot | default: "#ff4f33" }};

    --colorDrawers: {{ settings.color_drawer_background | default: "#1c1d1d" }};
    --colorDrawersDim: {{ settings.color_drawer_background | default: "#1c1d1d" | color_darken: 5 }};
    --colorDrawerBorder: {{ settings.color_drawer_border | default: "#343535" }};
    --colorDrawerText: {{ settings.color_drawer_text | default: "#fff" }};
    --colorDrawerTextDarken: {{ settings.color_drawer_text | default: "#fff" | color_darken: 15 }};
    --colorDrawerButton: {{ settings.color_drawer_button | default: "#a26b25" }};
    --colorDrawerButtonText: {{ settings.color_drawer_button_text | default: "#fff" }};

    --colorFooter: {{ settings.color_footer | default: "#111111" }};
    --colorFooterText: {{ settings.color_footer_text | default: "#ffffff" }};
    --colorFooterTextAlpha01: {{ settings.color_footer_text | default: '#fff' | color_modify: 'alpha', 0.1 }};
    --colorFooterTextAlpha012: {{ settings.color_footer_text | default: '#fff' | color_modify: 'alpha', 0.12 }};
    --colorFooterTextAlpha06: {{ settings.color_footer_text | default: '#fff' | color_modify: 'alpha', 0.6 }};

    --colorHeroText: {{ settings.color_image_text | default: "#ffffff" }};

    --colorInputBg: {{ settings.color_body_bg | default: "#1c1d1d" }};
    --colorInputBgDim: {{ settings.color_body_bg | default: "#1c1d1d" | color_darken: 5 }};
    --colorInputBgDark: {{ settings.color_body_bg | default: "#1c1d1d" | color_darken: 10 }};
    --colorInputText: {{ settings.color_body_text | default: "#1c1d1d" }};

    --colorLink: {{ settings.color_body_text | default: "#1c1d1d" }};

    --colorModalBg: {{ settings.color_modal_overlays | default: "#000000" | color_modify: 'alpha', 0.6 }};

    --colorNav: {{ settings.color_header | default: "#ffffff" }};
    --colorNavText: {{ settings.color_header_text | default: "#000000" }};

    --colorSalePrice: {{ settings.color_sale_price | default: "#1c1d1d" }};
    --colorSaleTag: {{ settings.color_sale_tag | default: "#1c1d1d" }};
    --colorSaleTagText: {{ settings.color_sale_tag_text | default: "#ffffff" }};

    --colorTextBody: {{ settings.color_body_text | default: "#1c1d1d" }};
    --colorTextBodyAlpha0025: {{ settings.color_body_text | default: "#1c1d1d" | color_modify: 'alpha', 0.025 }};
    --colorTextBodyAlpha005: {{ settings.color_body_text | default: "#1c1d1d" | color_modify: 'alpha', 0.05 }};
    --colorTextBodyAlpha008: {{ settings.color_body_text | default: '#1c1d1d' | color_modify: 'alpha', 0.08 }};
    --colorTextBodyAlpha01: {{ settings.color_body_text | default: "#1c1d1d" | color_modify: 'alpha', 0.1 }};
    --colorTextBodyAlpha035: {{ settings.color_body_text | default: "#1c1d1d" | color_modify: 'alpha', 0.35 }};

    --colorSmallImageBg: {{ settings.color_small_image_bg | default: "#eee" }};
    --colorLargeImageBg: {{ settings.color_large_image_bg | default: "#1c1d1d" }};

    --urlIcoSelect: url({{ 'ico-select.svg' | asset_url | split: '?' | first }});
    --urlIcoSelectFooter: url({{ 'ico-select-footer.svg' | asset_url | split: '?' | first }});
    --urlIcoSelectWhite: url({{ 'ico-select-white.svg' | asset_url | split: '?' | first }});

    --urlButtonArrowPng: url({{ 'button-arrow.png' | asset_url | split: '?' | first }});
    --urlButtonArrow2xPng: url({{ 'button-arrow-2x.png' | asset_url | split: '?' | first }});
    --urlButtonArrowBlackPng: url({{ 'button-arrow-black.png' | asset_url | split: '?' | first }});
    --urlButtonArrowBlack2xPng: url({{ 'button-arrow-black-2x.png' | asset_url | split: '?' | first }});

    --sizeChartMargin: 25px 0;
    --sizeChartIconMargin: 5px;

    --newsletterReminderPadding: 30px 35px;

    /*Shop Pay Installments*/
    --color-body-text: {{ settings.color_body_text | default: "#1c1d1d" }};
    --color-body: {{ settings.color_body_bg | default: "#fff" }};
    --color-bg: {{ settings.color_body_bg | default: "#fff" }};
  }

  {% comment %}
    Scrims (fade on top of images)
  {% endcomment %}
  {%- assign colorImage1Alpha = settings.color_image_1_opacity | divided_by: 100.0 -%}
  {%- assign colorImage2Alpha = settings.color_image_2_opacity | divided_by: 100.0 -%}
  .collection-hero__content:before,
  .hero__image-wrapper:before,
  .hero__media:before {
    background-image: linear-gradient(to bottom, {{ settings.color_image_2 | color_modify: 'alpha', colorImage2Alpha }} 0%, {{ settings.color_image_2 | color_modify: 'alpha', colorImage2Alpha }} 40%, {{ settings.color_image_1 | color_modify: 'alpha', colorImage1Alpha }} 100%);
  }

  .skrim__item-content .skrim__overlay:after {
    background-image: linear-gradient(to bottom, {{ settings.color_image_2 | color_modify: 'alpha', colorImage2Alpha }} 30%, {{ settings.color_image_1 | color_modify: 'alpha', colorImage1Alpha }} 100%);
  }

  .placeholder-content {
    background-image: linear-gradient(100deg, {{ settings.color_small_image_bg }} 40%, {{ settings.color_small_image_bg | color_darken: 3 }} 63%, {{ settings.color_small_image_bg }} 79%);
  }
{%- endstyle -%}
