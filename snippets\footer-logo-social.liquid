<div class="footer__logo-social">
  {%- if block.settings.logo -%}
    {%- style -%}
      .footer__logo a {
        height: {{ block.settings.desktop_logo_height }}px;
      }
    {%- endstyle -%}
    <div class="footer__logo">
      <a href="{{ routes.root_url }}">
        {%- assign width = block.settings.desktop_logo_height | times: block.settings.logo.aspect_ratio | times: 2 -%}
        {%- assign height = block.settings.desktop_logo_height -%}
        {%- capture sizes -%}{{ block.settings.desktop_logo_height | times: block.settings.logo.aspect_ratio }}px{%- endcapture -%}
        {%- capture widths -%}{{ block.settings.desktop_logo_height | times: block.settings.logo.aspect_ratio }}, {{ block.settings.desktop_logo_height | times: block.settings.logo.aspect_ratio | times: 2 }}{%- endcapture -%}
        {%- capture style -%}max-width: {{ block.settings.desktop_logo_height | times: block.settings.logo.aspect_ratio }}px;max-height: {{ block.settings.desktop_logo_height }}px;{%- endcapture -%}

        {%- render 'image-element',
          img: block.settings.logo,
          img_width: width,
          img_height: height,
          sizes: sizes,
          widths: widths,
          style: style,
          alt: block.settings.logo.alt | default: shop.name,
        -%}
      </a>
    </div>
  {%- endif -%}

  {% render 'social-icons', additional_classes: 'footer__social' %}

</div>
