{% comment %}
  Renders gift card recipient form.
  Accepts:
  - product: {Object} product object.
  - form: {Object} the product form object.
  - section: {Object} section to which this snippet belongs.

  Usage:
  {% render 'gift-card-recipient-form', product: product, form: form, section: section %}
{% endcomment %}

{%- assign gift_card_recipient_control_flag = 'properties[__shopify_send_gift_card_to_recipient]' -%}
<recipient-form
  class="recipient-form"
  data-section-id="{{ section.id }}"
  data-product-variant-id="{{ product.selected_or_first_available_variant.id }}"
>
  <div class="recipient-form__checkbox-wrapper">
    <input
      id="Recipient-Checkbox-{{ section.id }}"
      type="checkbox"
      name="{{ gift_card_recipient_control_flag }}"
    >
    <label class="checkbox-label" for="Recipient-Checkbox-{{ section.id }}">{{ 'recipient.form.checkbox' | t }}
    </label>
  </div>
  <div class="recipient-fields">
    <div class="recipient-fields__field">
      <div class="field">
        <input
          class="field__input input-full"
          id="Recipient-email-{{ section.id }}"
          type="email"
          placeholder="{{ 'recipient.form.email' | t }}"
          name="properties[Recipient email]"
          value="{{ form.email }}"
          {% if form.errors contains 'email' %}
            aria-invalid="true"
            aria-describedby="RecipientForm-email-error-{{ section.id }}"
          {% endif %}
        >
        <label class="field__label" for="Recipient-email-{{ section.id }}">
          <span class="recipient-email-label required">{{ 'recipient.form.email_label' | t }}</span>
        </label>
      </div>
    </div>

    <div class="recipient-fields__field">
      <div class="field">
        <input
          class="field__input input-full"
          autocomplete="name"
          type="text"
          id="Recipient-name-{{ section.id }}"
          name="properties[Recipient name]"
          placeholder="{{ 'recipient.form.name' | t }}"
          value="{{ form.name }}"
          {% if form.errors contains 'name' %}
            aria-invalid="true"
            aria-describedby="RecipientForm-name-error-{{ section.id }}"
          {% endif %}
        >
        <label class="field__label" for="Recipient-name-{{ section.id }}">
          {{- 'recipient.form.name_label' | t -}}
        </label>
      </div>
    </div>

    <div class="recipient-fields__field">
      {%- assign max_chars_message = 200 -%}
      {%- assign max_chars_message_rendered = 'recipient.form.max_characters' | t: max_chars: max_chars_message -%}
      {%- assign message_label_rendered = 'recipient.form.message_label' | t -%}
      <div class="field">
        <textarea
          rows="10"
          id="Recipient-message-{{ section.id }}"
          class="text-area field__input input-full"
          name="properties[Message]"
          maxlength="{{ max_chars_message }}"
          placeholder="{{ 'recipient.form.message' | t }}"
          aria-label="{{ message_label_rendered }} {{ max_chars_message_rendered }}"
          {% if form.errors contains 'message' %}
            aria-invalid="true"
            aria-describedby="RecipientForm-message-error-{{ section.id }}"
          {% endif %}
        >{{ form.message }}</textarea>
        <label class="form__label field__label" for="Recipient-message-{{ section.id }}">
          {{ message_label_rendered }}
        </label>
      </div>

      <label class="form__label recipient-form-field-label recipient-form-field-label--space-between">
        <span>{{ max_chars_message_rendered }}</span>
      </label>
    </div>

    <div class="field">
      <input
        class="field__input text-body input-full"
        autocomplete="send_on"
        type="date"
        id="Recipient-send-on-{{ section.id }}"
        name="properties[Send on]"
        placeholder="{{ 'recipient.form.send_on_label' | t }}"
        pattern="\d{4}-\d{2}-\d{2}"
        value="{{ form.send_on }}"
        {% if form.errors contains 'send_on' %}
          aria-invalid="true"
          aria-describedby="RecipientForm-send_on-error-{{ section.id }}"
        {% endif %}
      >
      <label class="form__label field__label" for="Recipient-send-on-{{ section.id }}">
        {{ 'recipient.form.send_on_label' | t }}
      </label>
    </div>
  </div>
  <input
    type="hidden"
    name="{{ gift_card_recipient_control_flag }}"
    value="if_present"
    id="Recipient-Control-{{ section.id }}"
    disabled
  >
</recipient-form>
