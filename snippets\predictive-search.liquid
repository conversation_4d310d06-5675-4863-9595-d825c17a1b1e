{%- liquid
  assign dark_search_bg = false
  assign lightness = settings.color_header_search | color_brightness

  if lightness <= 100
    assign dark_search_bg = true
  endif
-%}

<predictive-search data-context="{{ context }}" data-enabled="{{ settings.predictive_search_enabled }}" data-dark="{{ dark_search_bg }}">
  <div class="predictive__screen" data-screen></div>
  <form action="{{ routes.search_url }}" method="get" role="search">
    <label for="Search" class="hidden-label">Search</label>
    <div class="search__input-wrap">
      <input
        class="search__input"
        id="Search"
        type="search"
        name="q"
        value="{{ search.terms | escape }}"
        role="combobox"
        aria-expanded="false"
        aria-owns="predictive-search-results"
        aria-controls="predictive-search-results"
        aria-haspopup="listbox"
        aria-autocomplete="list"
        autocorrect="off"
        autocomplete="off"
        autocapitalize="off"
        spellcheck="false"
        placeholder="{{ 'general.search.placeholder' | t }}"
        tabindex="0"
      >
      <input name="options[prefix]" type="hidden" value="last">
      <button class="btn--search" type="submit">
        <svg aria-hidden="true" focusable="false" role="presentation" class="icon icon-search" viewBox="0 0 64 64"><defs><style>.cls-1{fill:none;stroke:#000;stroke-miterlimit:10;stroke-width:2px}</style></defs><path class="cls-1" d="M47.16 28.58A18.58 18.58 0 1 1 28.58 10a18.58 18.58 0 0 1 18.58 18.58zM54 54L41.94 42"/></svg>
        <span class="icon__fallback-text">{{ 'general.search.submit' | t }}</span>
      </button>
    </div>

    <button class="btn--close-search">
      <svg aria-hidden="true" focusable="false" role="presentation" class="icon icon-close" viewBox="0 0 64 64"><defs><style>.cls-1{fill:none;stroke:#000;stroke-miterlimit:10;stroke-width:2px}</style></defs><path class="cls-1" d="M19 17.61l27.12 27.13m0-27.13L19 44.74"/></svg>
    </button>
    <div id="predictive-search" class="search__results" tabindex="-1"></div>
  </form>
</predictive-search>
