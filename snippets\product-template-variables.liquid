{%- liquid
  assign current_variant = product.selected_or_first_available_variant
  assign days_price_valid_until = 10 | times: 86400
  assign gtin_option = 'gtin'
  assign is_barcode_available = false
  if current_variant.barcode != blank
    assign is_barcode_available = true
    assign gtin_string_length = current_variant.barcode | size
    if gtin_string_length == 8 or gtin_string_length == 12 or gtin_string_length == 13 or gtin_string_length == 14
      assign is_valid_gtin_length = true
      assign gtin_option = gtin_option | append: gtin_string_length
    endif
  endif
-%}

<script type="application/ld+json">
  {
    "@context": "http://schema.org",
    "@type": "Product",
    "offers": [
      {%- for variant in product.variants -%}
        {
          "@type" : "Offer",
          {%- if variant.sku != blank -%}
            "sku": {{ variant.sku | json }},
          {%- endif -%}
          "availability" : "http://schema.org/{% if variant.available %}InStock{% else %}OutOfStock{% endif %}",
          "price" : {{ variant.price | divided_by: 100.00 | json }},
          "priceCurrency" : {{ cart.currency.iso_code | json }},
          "priceValidUntil": "{{ 'now' | date: '%s' | plus: days_price_valid_until | date: '%Y-%m-%d' }}",
          "url" : {{ shop.url | append: variant.url | json }}
        }{% unless forloop.last %},{% endunless %}
      {%- endfor -%}
    ],
    "brand": {{ product.vendor | json }},
    "sku": {{ current_variant.sku | json }},
    "name": {{ product.title | json }},
    "description": {{ product.description | strip_html | json }},
    "category": "",
    "url": "{{ shop.url }}{{ product.url }}",
    {%- if is_barcode_available and is_valid_gtin_length %}
      "{{gtin_option}}": {{ current_variant.barcode | json }},
      "productId": {{ current_variant.barcode | json }},
    {%- elsif is_barcode_available %}
      "mpn": {{ current_variant.barcode | json }},
      "productId": {{ current_variant.barcode | json }},
    {%- endif -%}
    "image": {
      "@type": "ImageObject",
      "url": "https:{{ product.featured_media.preview_image | img_url: '1024x1024' }}",
      "image": "https:{{ product.featured_media.preview_image | img_url: '1024x1024' }}",
      "name": {{ product.title | json }},
      "width": 1024,
      "height": 1024
    }
  }
</script>
