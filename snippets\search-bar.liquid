{%- liquid
  unless search_btn_style
    assign search_btn_style = 'btn--secondary'
  endunless
-%}

<form action="{{ routes.search_url }}" method="get" class="input-group search-bar {{ search_bar_location }}" role="search">
  <input type="hidden" name="type" value="{{ settings.search_type }}">
  <input type="hidden" name="options[prefix]" value="last">
  <input type="search" name="q" value="{{ search.terms | escape | replace: '*', '' }}" placeholder="{{ 'general.search.placeholder' | t }}" class="input-group-field" aria-label="{{ 'general.search.placeholder' | t }}">
  <span class="input-group-btn">
    <label for="search-bar-submit" class="hidden-label">{{ 'general.search.submit' | t }}</label>
    <button type="submit" id="search-bar-submit" class="{{ search_btn_style }}">
      <svg aria-hidden="true" focusable="false" role="presentation" class="icon icon-search" viewBox="0 0 64 64"><title>icon-search</title><path d="M47.16 28.58A18.58 18.58 0 1 1 28.58 10a18.58 18.58 0 0 1 18.58 18.58ZM54 54 41.94 42"/></svg>
      <span class="icon__fallback-text">{{ 'general.search.submit' | t }}</span>
    </button>
  </span>
</form>
