{% comment %}
  JS-load cart markup without bloat of a full layout.

  This is used in both the mini cart drawer and cart page.
  When a quantity is changed, this file is scraped and data-products
    is fully replaced to account for possible cart discounts changing

  The cart-wide discount div also replaces what is originally in the cart
    as it can change anytime a cart-item changes
{% endcomment %}
{% layout none %}

<div class="cart__items"
  data-count="{{ cart.item_count }}"
  {% if cart.total_discounts > 0 %}
    data-cart-savings="{{ cart.total_discounts }}"
  {% endif %}
  data-cart-subtotal="{{ cart.total_price }}">
  {% for item in cart.items %}
    {%- render 'cart-item', product: item, sizeVariable: '90px', fallback: '60px', -%}
  {% endfor %}
</div>
<div class="cart__discounts{% if cart.cart_level_discount_applications == blank %} hide{% endif %}">
  <div class="cart__item-sub cart__item-row">
    <div>{{ 'cart.general.discounts' | t }}</div>
    <div class="text-right">
      {% for cart_discount in cart.cart_level_discount_applications %}
        <div>
          {{ cart_discount.title }} (-{{ cart_discount.total_allocated_amount | money }})
        </div>
      {% endfor %}
    </div>
  </div>
</div>
