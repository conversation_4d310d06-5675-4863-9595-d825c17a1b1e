<div class="page-width page-content">
  <div class="grid">
    <div class="grid__item medium-up--one-third medium-up--push-one-third text-center">

      <div class="form-vertical">
        {% form 'reset_customer_password' %}

          <header class="section-header">
            <h1 class="section-header__title">{{ 'customer.reset_password.title' | t }}</h1>
            <p>{{ 'customer.reset_password.subtext' | t: email: email }}</p>
          </header>

          {{ form.errors | default_errors }}

          <label for="ResetPassword" class="hidden-label">{{ 'customer.reset_password.password' | t }}</label>
          <input type="password" value="" name="customer[password]" id="ResetPassword" class="input-full{% if form.errors contains 'password' %} error{% endif %}" placeholder="{{ 'customer.reset_password.password' | t }}">

          <label for="PasswordConfirmation" class="hidden-label">{{ 'customer.reset_password.password_confirm' | t }}</label>
          <input type="password" value="" name="customer[password_confirmation]" id="PasswordConfirmation" class="input-full{% if form.errors contains 'password_confirmation' %} error{% endif %}" placeholder="{{ 'customer.reset_password.password_confirm' | t }}">

          <div class="text-center">
            <label for="reset-password-submit" class="hidden-label">{{ 'customer.reset_password.submit' | t }}</label>
            <input type="submit" id="reset-password-submit" class="btn btn--full btn--no-animate" value="{{ 'customer.reset_password.submit' | t }}">
          </div>

        {% endform %}
      </div>

    </div>
  </div>
</div>
