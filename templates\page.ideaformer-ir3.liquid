<!-- templates/page.ideaformer-ir3.liquid -->
<!-- 在 Shopify 后台创建新页面，选择这个模板 -->

<style>
  /* 重置样式 */
  .ideaformer-landing * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  .ideaformer-landing {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background: #0a0a0a;
    color: #fff;
    overflow-x: hidden;
  }

  /* 加载屏幕 */
  .if-loader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #000;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity 0.5s;
  }

  .if-loader-content {
    text-align: center;
  }

  .if-loader-logo {
    font-size: 3rem;
    font-weight: bold;
    background: linear-gradient(45deg, #00ff88, #0099ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: if-pulse 1.5s ease-in-out infinite;
  }

  @keyframes if-pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
  }

  /* 自定义导航栏 */
  .if-nav {
    position: fixed;
    top: 0;
    width: 100%;
    padding: 20px 50px;
    background: rgba(10, 10, 10, 0.9);
    backdrop-filter: blur(10px);
    z-index: 1000;
    transition: all 0.3s;
  }

  .if-nav.scrolled {
    padding: 15px 50px;
    background: rgba(10, 10, 10, 0.95);
  }

  .if-nav-container {
    max-width: 1400px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .if-logo {
    font-size: 1.8rem;
    font-weight: bold;
    background: linear-gradient(45deg, #00ff88, #0099ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .if-nav-links {
    display: flex;
    gap: 30px;
    list-style: none;
  }

  .if-nav-links a {
    color: #fff;
    text-decoration: none;
    transition: color 0.3s;
    font-size: 1rem;
  }

  .if-nav-links a:hover {
    color: #00ff88;
  }

  /* 英雄区域 */
  .if-hero {
    height: 100vh;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    background: radial-gradient(circle at center, #111 0%, #000 100%);
    overflow: hidden;
    margin-top: -70px;
    padding-top: 70px;
  }

  .if-hero-bg {
    position: absolute;
    width: 200%;
    height: 200%;
    top: -50%;
    left: -50%;
    background-image:
      repeating-linear-gradient(0deg, transparent, transparent 2px, rgba(0, 255, 136, 0.03) 2px, rgba(0, 255, 136, 0.03) 4px),
      repeating-linear-gradient(90deg, transparent, transparent 2px, rgba(0, 153, 255, 0.03) 2px, rgba(0, 153, 255, 0.03) 4px);
    animation: if-grid-move 20s linear infinite;
  }

  @keyframes if-grid-move {
    0% { transform: translate(0, 0); }
    100% { transform: translate(50px, 50px); }
  }

  .if-hero-content {
    text-align: center;
    z-index: 1;
    max-width: 1200px;
    padding: 0 20px;
  }

  .if-hero-title {
    font-size: clamp(2.5rem, 6vw, 5rem);
    font-weight: 900;
    margin-bottom: 20px;
    opacity: 0;
    transform: translateY(50px);
    animation: if-fadeInUp 1s forwards 0.5s;
  }

  .if-hero-subtitle {
    font-size: clamp(1.2rem, 3vw, 2rem);
    color: #888;
    margin-bottom: 40px;
    opacity: 0;
    transform: translateY(50px);
    animation: if-fadeInUp 1s forwards 0.7s;
  }

  @keyframes if-fadeInUp {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .if-hero-cta {
    display: inline-block;
    padding: 15px 40px;
    background: linear-gradient(45deg, #00ff88, #0099ff);
    color: #000;
    text-decoration: none;
    font-weight: bold;
    border-radius: 50px;
    transition: all 0.3s;
    opacity: 0;
    transform: translateY(50px);
    animation: if-fadeInUp 1s forwards 0.9s;
  }

  .if-hero-cta:hover {
    transform: scale(1.05);
    box-shadow: 0 10px 30px rgba(0, 255, 136, 0.3);
  }

  /* 3D模型展示区 */
  .if-model-showcase {
    height: 100vh;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #050505;
  }

  .if-model-container {
    width: 80%;
    max-width: 800px;
    height: 600px;
    position: relative;
    opacity: 0;
    transform: scale(0.8);
    transition: all 1s;
  }

  .if-model-container.visible {
    opacity: 1;
    transform: scale(1);
  }

  .if-model-3d {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #1a1a1a, #0a0a0a);
    border-radius: 20px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 255, 136, 0.1);
  }

  /* 使用产品图片替代3D模型 */
  .if-model-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
    padding: 40px;
  }

  /* 特性区域 */
  .if-features {
    padding: 100px 0;
    background: #000;
  }

  .if-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
  }

  .if-section-title {
    text-align: center;
    font-size: clamp(2rem, 4vw, 3.5rem);
    margin-bottom: 80px;
    opacity: 0;
    transform: translateY(50px);
    transition: all 0.8s;
  }

  .if-section-title.visible {
    opacity: 1;
    transform: translateY(0);
  }

  .if-features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 40px;
  }

  .if-feature-card {
    background: linear-gradient(135deg, #0a0a0a, #1a1a1a);
    padding: 40px;
    border-radius: 20px;
    border: 1px solid #222;
    opacity: 0;
    transform: translateY(50px);
    transition: all 0.5s;
  }

  .if-feature-card.visible {
    opacity: 1;
    transform: translateY(0);
  }

  .if-feature-card:hover {
    border-color: #00ff88;
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 255, 136, 0.1);
  }

  .if-feature-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(45deg, #00ff88, #0099ff);
    border-radius: 15px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
  }

  .if-feature-title {
    font-size: 1.5rem;
    margin-bottom: 15px;
  }

  .if-feature-description {
    color: #888;
    line-height: 1.6;
  }

  /* 规格区域 */
  .if-specs {
    padding: 100px 0;
    background: #050505;
  }

  .if-specs-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: center;
  }

  .if-specs-content h3 {
    font-size: 2.5rem;
    margin-bottom: 30px;
    background: linear-gradient(45deg, #00ff88, #0099ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .if-spec-item {
    display: flex;
    align-items: center;
    margin-bottom: 25px;
    opacity: 0;
    transform: translateX(-50px);
    transition: all 0.5s;
  }

  .if-spec-item.visible {
    opacity: 1;
    transform: translateX(0);
  }

  .if-spec-label {
    flex: 1;
    color: #888;
  }

  .if-spec-value {
    flex: 1;
    font-weight: bold;
    font-size: 1.1rem;
  }

  /* CTA区域 */
  .if-cta {
    padding: 150px 0;
    background: radial-gradient(circle at center, #0a0a0a, #000);
    text-align: center;
  }

  .if-cta-title {
    font-size: clamp(2rem, 5vw, 4rem);
    margin-bottom: 30px;
    opacity: 0;
    transform: scale(0.8);
    transition: all 0.8s;
  }

  .if-cta-title.visible {
    opacity: 1;
    transform: scale(1);
  }

  .if-cta-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
    opacity: 0;
    transform: translateY(50px);
    transition: all 0.8s 0.2s;
  }

  .if-cta-buttons.visible {
    opacity: 1;
    transform: translateY(0);
  }

  .if-cta-button {
    padding: 20px 50px;
    border-radius: 50px;
    text-decoration: none;
    font-weight: bold;
    font-size: 1.1rem;
    transition: all 0.3s;
    display: inline-block;
  }

  .if-cta-primary {
    background: linear-gradient(45deg, #00ff88, #0099ff);
    color: #000;
  }

  .if-cta-primary:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 255, 136, 0.4);
  }

  .if-cta-secondary {
    border: 2px solid #00ff88;
    color: #00ff88;
  }

  .if-cta-secondary:hover {
    background: #00ff88;
    color: #000;
    transform: translateY(-5px);
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .if-nav {
      padding: 15px 20px;
    }

    .if-nav-links {
      display: none;
    }

    .if-hero-title {
      font-size: 2.5rem;
    }

    .if-features-grid {
      grid-template-columns: 1fr;
    }

    .if-specs-container {
      grid-template-columns: 1fr;
    }
  }

  /* 滚动进度条 */
  .if-progress-bar {
    position: fixed;
    top: 0;
    left: 0;
    height: 3px;
    background: linear-gradient(45deg, #00ff88, #0099ff);
    z-index: 9999;
    transition: width 0.1s;
  }
</style>

<div class="ideaformer-landing">
  <!-- 加载屏幕 -->
  <div class="if-loader" id="ifLoader">
    <div class="if-loader-content">
      <div class="if-loader-logo">IDEAFORMER</div>
    </div>
  </div>

  <!-- 滚动进度条 -->
  <div class="if-progress-bar" id="ifProgressBar"></div>

  <!-- 自定义导航栏 -->
  <nav class="if-nav" id="ifNavbar">
    <div class="if-nav-container">
      <div class="if-logo">IDEAFORMER IR3 V2</div>
      <ul class="if-nav-links">
        <li><a href="#home">首页</a></li>
        <li><a href="#model">产品展示</a></li>
        <li><a href="#features">核心特性</a></li>
        <li><a href="#specs">技术规格</a></li>
        <li><a href="{{ routes.cart_url }}">购物车</a></li>
      </ul>
    </div>
  </nav>

  <!-- 英雄区域 -->
  <section class="if-hero" id="home">
    <div class="if-hero-bg"></div>
    <div class="if-hero-content">
      <h1 class="if-hero-title">{{ page.metafields.custom.hero_title | default: '突破极限，无限可能' }}</h1>
      <p class="if-hero-subtitle">
        {{ page.metafields.custom.hero_subtitle | default: 'IDEAFORMER IR3 V2 传送带3D打印机' }}
      </p>
      <a href="#features" class="if-hero-cta">探索更多</a>
    </div>
  </section>

  <!-- 3D模型展示 -->
  <section class="if-model-showcase" id="model">
    <div class="if-model-container">
      <div class="if-model-3d">
        {% if page.metafields.custom.product_image %}
          <img
            src="{{ page.metafields.custom.product_image | image_url: width: 800 }}"
            alt="IR3 V2"
            class="if-model-image"
          >
        {% else %}
          <!-- 如果没有设置图片，显示占位内容 -->
          <div style="display: flex; align-items: center; justify-content: center; height: 100%; font-size: 3rem; color: #333;">
            产品图片
          </div>
        {% endif %}
      </div>
    </div>
  </section>

  <!-- 核心特性 -->
  <section class="if-features" id="features">
    <div class="if-container">
      <h2 class="if-section-title">革新性能，引领未来</h2>
      <div class="if-features-grid">
        <div class="if-feature-card">
          <div class="if-feature-icon">∞</div>
          <h3 class="if-feature-title">无限Z轴打印</h3>
          <p class="if-feature-description">250×250×∞mm打印尺寸，突破传统限制，实现超长物件打印和批量生产</p>
        </div>

        <div class="if-feature-card">
          <div class="if-feature-icon">⚡</div>
          <h3 class="if-feature-title">极速打印</h3>
          <p class="if-feature-description">采用Klipper固件，最高400mm/s打印速度，20000mm/s²加速度，效率提升300%</p>
        </div>

        <div class="if-feature-card">
          <div class="if-feature-icon">🎯</div>
          <h3 class="if-feature-title">100%自动调平</h3>
          <p class="if-feature-description">智能传感器自动检测，无需手动调节，一键完成精准调平</p>
        </div>

        <div class="if-feature-card">
          <div class="if-feature-icon">🛡️</div>
          <h3 class="if-feature-title">金属传送带</h3>
          <p class="if-feature-description">PEI涂层金属带，耐用性提升500%，支持多种材料，附着力更强</p>
        </div>

        <div class="if-feature-card">
          <div class="if-feature-icon">📱</div>
          <h3 class="if-feature-title">智能控制</h3>
          <p class="if-feature-description">WiFi连接，手机远程监控，4.3寸电容触摸屏，操作更便捷</p>
        </div>

        <div class="if-feature-card">
          <div class="if-feature-icon">🔧</div>
          <h3 class="if-feature-title">断料检测</h3>
          <p class="if-feature-description">智能检测耗材状态，自动暂停续打，保证打印成功率</p>
        </div>
      </div>
    </div>
  </section>

  <!-- 技术规格 -->
  <section class="if-specs" id="specs">
    <div class="if-container">
      <h2 class="if-section-title">技术参数</h2>
      <div class="if-specs-container">
        <div class="if-specs-content">
          <h3>性能参数</h3>
          <div class="if-spec-item">
            <span class="if-spec-label">打印尺寸</span>
            <span class="if-spec-value">250×250×∞mm</span>
          </div>
          <div class="if-spec-item">
            <span class="if-spec-label">打印速度</span>
            <span class="if-spec-value">最高400mm/s</span>
          </div>
          <div class="if-spec-item">
            <span class="if-spec-label">加速度</span>
            <span class="if-spec-value">20000mm/s²</span>
          </div>
          <div class="if-spec-item">
            <span class="if-spec-label">层厚精度</span>
            <span class="if-spec-value">0.05-0.4mm</span>
          </div>
          <div class="if-spec-item">
            <span class="if-spec-label">喷嘴温度</span>
            <span class="if-spec-value">最高290°C</span>
          </div>
          <div class="if-spec-item">
            <span class="if-spec-label">热床温度</span>
            <span class="if-spec-value">最高110°C</span>
          </div>
          <div class="if-spec-item">
            <span class="if-spec-label">支持材料</span>
            <span class="if-spec-value">PLA/PETG/ABS/TPU等</span>
          </div>
        </div>
        <div class="if-specs-visual">
          <!-- 简化的视觉效果 -->
          <div style="text-align: center;">
            <div style="font-size: 4rem; font-weight: bold; background: linear-gradient(45deg, #00ff88, #0099ff); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">
              IR3 V2
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- CTA区域 -->
  <section class="if-cta" id="contact">
    <div class="if-container">
      <h2 class="if-cta-title">开启无限打印新时代</h2>
      <div class="if-cta-buttons">
        {% assign product = all_products[page.metafields.custom.product_handle] %}
        {% if product %}
          <a href="{{ product.url }}" class="if-cta-button if-cta-primary">立即购买</a>
        {% else %}
          <a href="/products" class="if-cta-button if-cta-primary">立即购买</a>
        {% endif %}
        <a href="/pages/contact" class="if-cta-button if-cta-secondary">了解更多</a>
      </div>
    </div>
  </section>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // 页面加载完成后隐藏加载屏幕
    setTimeout(function() {
      var loader = document.getElementById('ifLoader');
      if (loader) {
        loader.style.opacity = '0';
        setTimeout(function() {
          loader.style.display = 'none';
        }, 500);
      }
    }, 1000);

    // 滚动进度条
    window.addEventListener('scroll', function() {
      var scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      var scrollHeight = document.documentElement.scrollHeight - document.documentElement.clientHeight;
      var scrollPercent = (scrollTop / scrollHeight) * 100;
      var progressBar = document.getElementById('ifProgressBar');
      if (progressBar) {
        progressBar.style.width = scrollPercent + '%';
      }
    });

    // 导航栏滚动效果
    window.addEventListener('scroll', function() {
      var navbar = document.getElementById('ifNavbar');
      if (navbar) {
        if (window.scrollY > 50) {
          navbar.classList.add('scrolled');
        } else {
          navbar.classList.remove('scrolled');
        }
      }
    });

    // 滚动动画
    var observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    };

    var observer = new IntersectionObserver(function(entries) {
      entries.forEach(function(entry) {
        if (entry.isIntersecting) {
          entry.target.classList.add('visible');
        }
      });
    }, observerOptions);

    // 观察需要动画的元素
    document.querySelectorAll('.if-section-title, .if-model-container, .if-feature-card, .if-spec-item, .if-cta-title, .if-cta-buttons').forEach(function(el) {
      observer.observe(el);
    });

    // 平滑滚动
    document.querySelectorAll('a[href^="#"]').forEach(function(anchor) {
      anchor.addEventListener('click', function(e) {
        e.preventDefault();
        var targetId = this.getAttribute('href');
        var target = document.querySelector(targetId);
        if (target) {
          target.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }
      });
    });
  });
</script>
