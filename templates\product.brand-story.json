/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "main": {
      "type": "main-product",
      "blocks": {
        "price": {
          "type": "price",
          "settings": {}
        },
        "separator": {
          "type": "separator",
          "settings": {}
        },
        "variant_picker": {
          "type": "variant_picker",
          "settings": {
            "variant_labels": true,
            "picker_type": "button",
            "product_dynamic_variants_enable": true,
            "color_swatches": false
          }
        },
        "sales_point": {
          "type": "sales_point",
          "settings": {
            "icon": "shield",
            "text": "Lifetime Customer Service"
          }
        },
        "inventory_status": {
          "type": "inventory_status",
          "settings": {
            "inventory_threshold": 10,
            "inventory_transfers_enable": true
          }
        },
        "buy_buttons": {
          "type": "buy_buttons",
          "settings": {
            "show_dynamic_checkout": true,
            "surface_pickup_enable": true,
            "show_gift_card_recipient": false
          }
        },
        "description": {
          "type": "description",
          "settings": {
            "is_tab": false
          }
        },
        "tab": {
          "type": "tab",
          "settings": {
            "title": "Shipping information",
            "content": "<h5>If you have any quality, size or usage issues, please feel free to contact our customer service, or send an <NAME_EMAIL></h5>",
            "page": ""
          }
        },
        "contact": {
          "type": "contact",
          "settings": {
            "title": "Ask a question",
            "phone": false
          }
        },
        "share": {
          "type": "share",
          "settings": {}
        }
      },
      "block_order": [
        "price",
        "separator",
        "variant_picker",
        "sales_point",
        "inventory_status",
        "buy_buttons",
        "description",
        "tab",
        "contact",
        "share"
      ],
      "settings": {
        "sku_enable": false,
        "image_size": "medium",
        "product_zoom_enable": true,
        "thumbnail_position": "beside",
        "thumbnail_height": "flexible",
        "thumbnail_arrows": false,
        "enable_video_looping": true,
        "product_video_style": "muted"
      }
    },
    "featured_collections_J7BLGd": {
      "type": "featured-collections",
      "blocks": {
        "collection_TpGR3x": {
          "type": "collection",
          "settings": {
            "collection": "frontpage",
            "focal_point": "center center",
            "size": "wide"
          }
        },
        "collection_wP36xQ": {
          "type": "collection",
          "settings": {
            "collection": "3d-printer-build-plate",
            "focal_point": "center center",
            "size": "square-small"
          }
        },
        "collection_TJ3RKg": {
          "type": "collection",
          "settings": {
            "collection": "3d-printer-hotend-kit",
            "focal_point": "center center",
            "size": "square-small"
          }
        },
        "collection_mG8aDi": {
          "type": "collection",
          "settings": {
            "collection": "3d-printer-nozzles",
            "focal_point": "center center",
            "size": "square-small"
          }
        },
        "collection_pAfb3G": {
          "type": "collection",
          "settings": {
            "collection": "other-accessories",
            "focal_point": "center center",
            "size": "square-small"
          }
        },
        "collection_3dbdqT": {
          "type": "collection",
          "settings": {
            "collection": "ir3-v2-printer-accessories",
            "focal_point": "center center",
            "size": "wide"
          }
        }
      },
      "block_order": [
        "collection_TpGR3x",
        "collection_wP36xQ",
        "collection_TJ3RKg",
        "collection_mG8aDi",
        "collection_pAfb3G",
        "collection_3dbdqT"
      ],
      "name": "t:sections.featured-collections.presets.collection_list.name",
      "settings": {
        "title": "Customers Also Viewed",
        "divider": true,
        "enable_gutter": true
      }
    },
    "sub": {
      "type": "product-full-width",
      "disabled": true,
      "settings": {
        "max_width": true
      }
    },
    "background-image-text": {
      "type": "background-image-text",
      "disabled": true,
      "settings": {
        "subtitle": "Impressive",
        "title": "Large image with text box",
        "text": "<p>Pair large text with a full-width image to draw attention to an important detail of your brand or product line.</p>",
        "button_label": "",
        "button_link": "",
        "layout": "left",
        "height": 550,
        "parallax": false,
        "parallax_direction": "top",
        "lazyload_images": true
      }
    },
    "text-and-image": {
      "type": "text-and-image",
      "disabled": true,
      "settings": {
        "image_mask": "none",
        "image2_mask": "none",
        "subtitle": "",
        "title": "Image with text",
        "text": "<p>Pair large text with an image to tell a story, explain a detail about your product, or describe a new promotion.</p>",
        "button_label": "",
        "button_link": "",
        "button_style": "primary",
        "align_text": "left",
        "image_width": "50",
        "layout": "right",
        "divider": false,
        "top_padding": true,
        "bottom_padding": true,
        "lazyload_images": true
      }
    },
    "text-and-image2": {
      "type": "text-and-image",
      "disabled": true,
      "settings": {
        "image_mask": "none",
        "image2_mask": "none",
        "subtitle": "",
        "title": "Image with text",
        "text": "<p>Pair large text with an image to tell a story, explain a detail about your product, or describe a new promotion.</p>",
        "button_label": "",
        "button_link": "",
        "button_style": "primary",
        "align_text": "left",
        "image_width": "50",
        "layout": "left",
        "divider": false,
        "top_padding": true,
        "bottom_padding": true,
        "lazyload_images": true
      }
    },
    "testimonials": {
      "type": "testimonials",
      "blocks": {
        "testimonials-0": {
          "type": "testimonial",
          "settings": {
            "icon": "5-stars",
            "testimonial": "<p>Add customer reviews and testimonials to showcase your store’s happy customers.</p>",
            "author": "Author name",
            "author_info": "Los Angeles, CA"
          }
        },
        "testimonials-1": {
          "type": "testimonial",
          "settings": {
            "icon": "5-stars",
            "testimonial": "<p>Add customer reviews and testimonials to showcase your store’s happy customers.</p>",
            "author": "Author name",
            "author_info": "Los Angeles, CA"
          }
        },
        "testimonials-2": {
          "type": "testimonial",
          "settings": {
            "icon": "5-stars",
            "testimonial": "<p>Add customer reviews and testimonials to showcase your store’s happy customers.</p>",
            "author": "Author name",
            "author_info": "Los Angeles, CA"
          }
        }
      },
      "block_order": [
        "testimonials-0",
        "testimonials-1",
        "testimonials-2"
      ],
      "disabled": true,
      "settings": {
        "title": "Don't take our word for it",
        "round_images": true,
        "color_background": "#f9f9f9",
        "color_text": "#1c1d1d"
      }
    },
    "product-recommendations": {
      "type": "product-recommendations",
      "settings": {
        "product_recommendations_heading": "You may also like",
        "related_count": 6,
        "products_per_row": 3
      }
    },
    "collection-return": {
      "type": "collection-return",
      "settings": {}
    },
    "newsletter_J6dckQ": {
      "type": "newsletter",
      "blocks": {
        "title_yYmK6L": {
          "type": "title",
          "settings": {
            "title": "Sign up and save",
            "heading_size": "h2"
          }
        },
        "text_NBLnRk": {
          "type": "text",
          "settings": {
            "text": "<p>Subscribe to get special offers, free giveaways, and once-in-a-lifetime deals.</p>"
          }
        },
        "form_RHPyVf": {
          "type": "form",
          "settings": {}
        }
      },
      "block_order": [
        "title_yYmK6L",
        "text_NBLnRk",
        "form_RHPyVf"
      ],
      "name": "t:sections.newsletter.presets.email_signup.name",
      "settings": {
        "align_text": "center",
        "image_width": "33",
        "image_mask": "none",
        "image_position": "right",
        "color_background": "#f9f9f9",
        "color_text": "#1c1d1d",
        "top_padding": true,
        "bottom_padding": true
      }
    }
  },
  "order": [
    "main",
    "featured_collections_J7BLGd",
    "sub",
    "background-image-text",
    "text-and-image",
    "text-and-image2",
    "testimonials",
    "product-recommendations",
    "collection-return",
    "newsletter_J6dckQ"
  ]
}
