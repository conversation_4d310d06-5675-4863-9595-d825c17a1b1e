<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ScrollTrigger Pin Test</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
        }
        
        .section {
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
        }
        
        .section-1 { background: #ff6b6b; }
        .section-2 { background: #4ecdc4; }
        .section-3 { background: #45b7d1; }
        .section-4 { background: #96ceb4; }
        
        .pinned-section {
            height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: white;
        }
        
        .feature-counter {
            font-size: 1.5rem;
            margin-bottom: 20px;
        }
        
        .feature-buttons {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .feature-btn {
            padding: 10px 20px;
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            cursor: pointer;
            border-radius: 5px;
            transition: all 0.3s ease;
        }
        
        .feature-btn:hover,
        .feature-btn.active {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        
        .status {
            margin-top: 20px;
            padding: 10px 20px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 5px;
            font-size: 1rem;
        }
        
        .completion-indicator {
            position: fixed;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 15px 25px;
            border-radius: 20px;
            opacity: 0;
            transition: opacity 0.5s ease;
            z-index: 1000;
        }
        
        .completion-indicator.visible {
            opacity: 1;
        }
    </style>
</head>
<body>
    <div class="section section-1">
        <h1>Scroll down to test pinning</h1>
    </div>
    
    <div class="pinned-section" id="pinned-section">
        <div class="feature-counter">
            Feature <span id="current-feature">1</span> of 3
        </div>
        
        <div class="feature-buttons">
            <button class="feature-btn active" data-feature="0">Feature 1</button>
            <button class="feature-btn" data-feature="1">Feature 2</button>
            <button class="feature-btn" data-feature="2">Feature 3</button>
        </div>
        
        <div class="status" id="status">
            Viewed: <span id="viewed-count">1</span>/3 features
        </div>
        
        <div style="margin-top: 20px; font-size: 1rem; opacity: 0.8;">
            Use scroll wheel or buttons to navigate
        </div>
    </div>
    
    <div class="section section-2">
        <h1>Section after pinned area</h1>
    </div>
    
    <div class="section section-3">
        <h1>Another section</h1>
    </div>
    
    <div class="section section-4">
        <h1>Final section</h1>
    </div>
    
    <div class="completion-indicator" id="completion-indicator">
        All features viewed! Scroll to continue
    </div>

    <script>
        gsap.registerPlugin(ScrollTrigger);
        
        // Test implementation similar to the refactored IR3 component
        const pinnedSection = document.getElementById('pinned-section');
        const features = ['Feature 1', 'Feature 2', 'Feature 3'];
        let currentIndex = 0;
        let viewedFeatures = new Set([0]); // Start with first feature viewed
        let hasViewedAllFeatures = false;
        let scrollTriggerInstance = null;
        let scrollCooldown = false;
        let lastScrollTime = 0;
        
        // Update UI
        function updateUI() {
            document.getElementById('current-feature').textContent = currentIndex + 1;
            document.getElementById('viewed-count').textContent = viewedFeatures.size;
            
            // Update button states
            document.querySelectorAll('.feature-btn').forEach((btn, index) => {
                btn.classList.toggle('active', index === currentIndex);
            });
        }
        
        // Change feature
        function changeFeature(index) {
            if (index < 0) index = features.length - 1;
            if (index >= features.length) index = 0;
            
            currentIndex = index;
            viewedFeatures.add(index);
            
            updateUI();
            checkAllFeaturesViewed();
            
            console.log('Changed to feature', index, 'Viewed:', Array.from(viewedFeatures));
        }
        
        // Check if all features viewed
        function checkAllFeaturesViewed() {
            if (viewedFeatures.size >= features.length && !hasViewedAllFeatures) {
                hasViewedAllFeatures = true;
                console.log('All features viewed! Updating ScrollTrigger...');
                
                // Show completion indicator
                const indicator = document.getElementById('completion-indicator');
                indicator.classList.add('visible');
                
                setTimeout(() => {
                    indicator.classList.remove('visible');
                }, 3000);
                
                // Update ScrollTrigger to allow exit
                if (scrollTriggerInstance) {
                    scrollTriggerInstance.vars.end = "+=50vh";
                    scrollTriggerInstance.refresh();
                    
                    setTimeout(() => {
                        if (scrollTriggerInstance) {
                            scrollTriggerInstance.kill();
                            scrollTriggerInstance = null;
                            console.log('ScrollTrigger unpinned');
                        }
                    }, 2000);
                }
            }
        }
        
        // Handle scroll while pinned
        function handlePinnedScroll() {
            if (!pinnedSection.hasScrollListener) {
                pinnedSection.hasScrollListener = true;
                
                pinnedSection.addEventListener('wheel', (e) => {
                    if (scrollCooldown || hasViewedAllFeatures) return;
                    
                    e.preventDefault();
                    e.stopPropagation();
                    
                    const delta = e.deltaY;
                    const now = Date.now();
                    
                    if (now - lastScrollTime < 400) return;
                    lastScrollTime = now;
                    scrollCooldown = true;
                    
                    console.log('Scroll detected:', delta > 0 ? 'down' : 'up');
                    
                    if (delta > 0) {
                        // Scroll down - next feature
                        if (currentIndex < features.length - 1) {
                            changeFeature(currentIndex + 1);
                        } else {
                            checkAllFeaturesViewed();
                        }
                    } else {
                        // Scroll up - previous feature
                        if (currentIndex > 0) {
                            changeFeature(currentIndex - 1);
                        }
                    }
                    
                    setTimeout(() => {
                        scrollCooldown = false;
                    }, 1000);
                }, { passive: false });
            }
        }
        
        // Create ScrollTrigger pin
        function createScrollPin() {
            console.log('Creating ScrollTrigger pin...');
            
            return ScrollTrigger.create({
                trigger: pinnedSection,
                start: "top top",
                end: () => hasViewedAllFeatures ? "+=50vh" : "+=9999vh",
                pin: true,
                pinSpacing: false,
                anticipatePin: 1,
                onUpdate: (self) => {
                    if (!hasViewedAllFeatures) {
                        handlePinnedScroll();
                    }
                },
                onRefresh: () => {
                    console.log('ScrollTrigger refreshed');
                },
                markers: true // Enable markers for debugging
            });
        }
        
        // Button event listeners
        document.querySelectorAll('.feature-btn').forEach((btn, index) => {
            btn.addEventListener('click', () => {
                changeFeature(index);
            });
        });
        
        // Initialize
        function initialize() {
            updateUI();
            scrollTriggerInstance = createScrollPin();
            console.log('Test initialized');
        }
        
        // Start when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initialize);
        } else {
            initialize();
        }
    </script>
</body>
</html>
