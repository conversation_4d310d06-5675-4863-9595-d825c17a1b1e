<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>过渡效果测试</title>
    <style>
        body {
            margin: 0;
            padding: 50px;
            background: #1a1a1a;
            font-family: Arial, sans-serif;
        }
        
        .test-container {
            width: 350px;
            height: 350px;
            margin: 0 auto;
            position: relative;
            border-radius: 20px;
            overflow: hidden;
            cursor: pointer;
        }
        
        .interactive-media-container {
            position: relative;
            width: 100%;
            height: 100%;
            overflow: hidden;
        }
        
        .static-image,
        .interactive-video {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 20px;
            transition: opacity 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .static-image {
            opacity: 1;
            z-index: 2;
        }
        
        .interactive-video {
            opacity: 0;
            z-index: 1;
            pointer-events: none;
        }
        
        .interactive-media-container:hover .static-image {
            opacity: 0;
        }
        
        .interactive-media-container:hover .interactive-video {
            opacity: 1;
        }
        
        .info {
            color: white;
            text-align: center;
            margin-top: 20px;
        }
        
        .status {
            margin-top: 10px;
            padding: 10px;
            background: rgba(255,255,255,0.1);
            border-radius: 5px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <h1 style="color: white; text-align: center;">交互式视频过渡效果测试</h1>
    
    <div class="test-container">
        <div class="interactive-media-container">
            <img class="static-image" 
                 src="https://cdn.shopify.com/s/files/1/0762/6113/0493/files/gd02.png?v=1752307804" 
                 alt="Static Image">
            <video class="interactive-video" 
                   muted loop playsinline preload="metadata">
                <source src="https://cdn.shopify.com/videos/c/o/v/cb900f6a2ca640b796528b4cce8d6f79.mp4" 
                        type="video/mp4">
            </video>
        </div>
    </div>
    
    <div class="info">
        <p>将鼠标悬停在图片上查看过渡效果</p>
        <div class="status" id="status">等待交互...</div>
    </div>

    <script>
        const container = document.querySelector('.interactive-media-container');
        const video = document.querySelector('.interactive-video');
        const image = document.querySelector('.static-image');
        const status = document.getElementById('status');
        
        // 监听过渡事件
        image.addEventListener('transitionstart', () => {
            status.textContent = '图片过渡开始...';
        });
        
        image.addEventListener('transitionend', () => {
            status.textContent = '图片过渡完成 - 透明度: ' + window.getComputedStyle(image).opacity;
        });
        
        video.addEventListener('transitionstart', () => {
            status.textContent = '视频过渡开始...';
        });
        
        video.addEventListener('transitionend', () => {
            status.textContent = '视频过渡完成 - 透明度: ' + window.getComputedStyle(video).opacity;
        });
        
        // 鼠标事件
        container.addEventListener('mouseenter', () => {
            console.log('鼠标进入');
            video.currentTime = 0;
            video.play().catch(error => {
                console.warn('视频播放失败:', error);
                status.textContent = '视频播放失败: ' + error.message;
            });
        });
        
        container.addEventListener('mouseleave', () => {
            console.log('鼠标离开');
            video.pause();
            video.currentTime = 0;
        });
        
        // 视频加载事件
        video.addEventListener('loadeddata', () => {
            console.log('视频加载成功');
            status.textContent = '视频加载成功，可以测试过渡效果';
        });
        
        video.addEventListener('error', (e) => {
            console.error('视频加载错误:', e);
            status.textContent = '视频加载错误';
        });
        
        // 显示当前样式信息
        setInterval(() => {
            const videoOpacity = window.getComputedStyle(video).opacity;
            const imageOpacity = window.getComputedStyle(image).opacity;
            console.log('当前透明度 - 图片:', imageOpacity, '视频:', videoOpacity);
        }, 1000);
    </script>
</body>
</html>
